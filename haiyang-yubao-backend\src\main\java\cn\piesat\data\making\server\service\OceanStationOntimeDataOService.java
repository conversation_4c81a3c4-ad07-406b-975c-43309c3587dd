package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationOntimeDataO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点数据-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationOntimeDataOService extends IService<OceanStationOntimeDataO> {

    List<OceanStationOntimeDataO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




