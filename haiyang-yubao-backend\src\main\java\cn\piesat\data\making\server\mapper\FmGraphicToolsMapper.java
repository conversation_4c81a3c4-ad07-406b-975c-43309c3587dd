package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicToolsDTO;
import cn.piesat.data.making.server.entity.FmGraphicTools;
import cn.piesat.data.making.server.vo.FmGraphicToolsVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:39
 */
@Mapper(componentModel = "spring")
public interface FmGraphicToolsMapper {

    FmGraphicToolsMapper INSTANCE = Mappers.getMapper(FmGraphicToolsMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicToolsVO entityToVo(FmGraphicTools entity);

    /**
     * dto-->entity
     */
    FmGraphicTools dtoToEntity(FmGraphicToolsDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicToolsVO> entityListToVoList(List<FmGraphicTools> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicTools> dtoListToEntityList(List<FmGraphicToolsDTO> dtoList);
}
