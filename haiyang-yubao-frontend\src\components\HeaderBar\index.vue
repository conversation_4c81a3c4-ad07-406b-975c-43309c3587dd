<template>
  <div class="header-bar" :class="props.className">
    <div v-if="props.className === 'system'" class="logo" @click="toHome">
      <!-- <slot name="logo"></slot> -->
      <div class="logo-img">
        <img src="src/assets/images/common/forecast.png" alt="" />
      </div>
      <div class="logo-title">
        <h2 class="system-name"></h2>
        <div class="sub-title"></div>
      </div>
    </div>

    <slot name="title"></slot>
    <div v-if="props.className === 'system'" class="menu-list">
      <template v-for="(item, index) in menuList" :key="item.path">
        <template
          v-if="item.children && item.children.length > 0 && item.meta.dropmenu"
        >
          <n-dropdown
            class="ht-dropdown-menu"
            trigger="hover"
            :options="getOptions(item.children)"
            @select="
              (val:any, option:any) => {
                handleMenuSelect(val, option, item)
              }
            "
          >
            <div
              class="menu-item"
              :class="currentPath?.includes(item.path) ? 'active' : ''"
              @click="changeTab(item, index)"
            >
              {{ item.meta.title }}
            </div>
          </n-dropdown>
        </template>
        <template v-else>
          <div
            class="menu-item"
            :class="currentPath?.includes(item.path) ? 'active' : ''"
            @click="changeTab(item, index)"
          >
            {{ item.meta.title }}
          </div>
        </template>
      </template>
    </div>
    <div class="header-right d-flex flex-align-center">
      <qx-button
        class="border nav-menu-icon"
        :class="isHover ? 'active' : ''"
        @mouseover="onMouseEnter"
        @mouseleave="onMouseLeave"
        >导航菜单

        <i class="icon" :class="isHover ? 'icon-down rotate' : 'icon-down'"></i>
      </qx-button>

      <nav-menu :visible="isHover" @change="changeHandler" />

      <i class="icon icon-notice1"></i>
      <n-dropdown
        trigger="hover"
        :options="options"
        class="user-dropdown"
        @select="handleSelect"
      >
        <div class="drop-down">
          <i class="icon icon-user1"></i>
          {{ username }}
          <i class="icon icon-down"></i>
        </div>
      </n-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts" name="header-bar">
import Api from 'src/requests/authRequest'
import PerApi from 'src/requests/pers'
import { SelectOption, SelectGroupOption, useMessage } from 'naive-ui'
import type { DropdownOption } from 'naive-ui'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import microApp from '@micro-zoe/micro-app'
import { QxButton } from '../QxButton'
import { NavMenu } from './index'

const router = useRouter()
const routes = router.getRoutes()
const route = useRoute()
const username = ref('admin')
const message = useMessage()
const currentPath = ref<string>(route.path)
const isHover = ref(false)

const menuList = ref<any>([])
const props = defineProps({
  className: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default() {
      return ''
    }
  }
})

function getMenuList() {
  const systemName = props.name
  const menu: any = routes.filter(item => {
    return item.name == systemName
  })

  if (menu && menu.length) {
    menuList.value = menu[0].children

    currentPath.value = findActivePath(menuList.value, route.path)
  }
}

function findActivePath(menu: any[], path: string) {
  for (let item of menu) {
    if (item.path === path) {
      return item.meta.activePath
    }
    if (item.children) {
      let result: any = findActivePath(item.children, path)
      if (result) {
        return result
      }
    }
  }
  return null
}

onMounted(() => {
  getMenuList()

  if (config.permissionSwitch) {
    getUserPermission()
    // if (config.checklogin) {
    //   console.log(ssoClient,'ssoClient')
    //   ssoClient.init({
    //     "passport_baseURL": config.passportUrl,
    //     check_callback: (res:any) => {
    //       console.log(res, "res----checklogin");
    //       if (res.code == 401) {
    //         location.href = config.loginUrl + "?redirect_url=" + location.href;
    //       }
    //     }
    //   });
    // }
  }

  getUserInfo()
})

function changeTab(item: any, index: number) {
  if (!item.children || !item.meta.dropmenu) {
    router.push(item.path)
  }

  currentPath.value = item.path
}
function getOptions(children: any[]) {
  return children?.map(item => {
    return {
      label: item.meta?.title ?? '',
      value: item.path,
      key: item.path
    }
  })
}

function handleMenuSelect(value: string, option: DropdownOption, item: any) {
  currentPath.value = item.path
  router.push(value)
}

const options = ref<Array<SelectOption | SelectGroupOption>>([
  {
    label: '修改密码',
    key: 1
  },
  {
    label: '退出登录',
    key: 2
  }
])

function handleSelect(val: number) {
  if (val === 2) {
    Api.logout()
      .then(() => {
        location.href = config.loginUrl
      })
      .catch(e => {
        let { msg } = e?.response?.data
        message.error(msg || '退出登录失败')
      })
  }
}

/**
 * @description 请求权限编码
 */
function getUserPermission() {
  PerApi.getPersList()
    .then((res: any) => {
      const stringPermissions = res?.stringPermissions
      const tenantInfo = { name: '海南海洋灾害一体化', code: '0', id: '0' }
      //添加全局变量
      microApp.setGlobalData({
        permission: stringPermissions,
        loginExpireCode: config.loginExpireCode,
        userMultipleType: config.userMultipleType,
        tenantInfo
      })
    })
    .catch(() => {})
}
/**
 * @description 获取用户信息
 */
function getUserInfo() {
  PerApi.getUserInfo()
    .then((res: any) => {
      username.value = res?.realName
    })
    .catch(() => {})
}

let timer = ref<any>(null)
const isChange = ref(false)
function onMouseEnter() {
  if (timer.value != null) {
    clearTimeout(timer.value)
    timer.value = null
  }

  isHover.value = true
}
function onMouseLeave() {
  if (isChange.value) return
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }

  timer.value = setTimeout(() => {
    isHover.value = false
  }, 300)
}

function changeHandler(val: boolean) {
  isChange.value = val
  isHover.value = val
}

function toHome() {
  location.href = config.homeUrl
}

onBeforeUnmount(() => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
})
</script>

<style lang="scss">
.header-bar {
  width: 100%;
  height: 79px;
  background: url(src/assets/images/login/bg.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 23px;
  .logo,
  .drop-down {
    width: 121px;
  }
  .logo-img {
    width: 46px;
    height: 46px;
    // border-radius:50%;
    // overflow: hidden;
    margin-right: 10px;
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }
  .header-right {
    .icon-notice1 {
      margin: 0 25px;
    }
  }
  .drop-down {
    margin-top: -25px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-right: 19px;
    cursor: pointer;
    .icon {
      margin-right: 6px;
    }
  }

  .menu-list {
    display: flex;
    align-items: center;
    margin-left: -100px;
    .menu-item {
      padding: 0 21px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #ffffff;
      text-align: left;
      cursor: pointer;
      &.active {
        color: #2bffe6;
      }
    }
  }
}
.ht-dropdown-menu.n-dropdown-menu {
  width: 160px;
  text-align: center;
  background: #0f77e3;
  box-sizing: border-box;
  padding: 15px;
  .n-dropdown-option-body__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
  }
  .n-dropdown-option {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    &:nth-last-child(1) {
      border: none;
    }
    .n-dropdown-option-body {
      align-items: center;
    }
  }
  .n-dropdown-option-body--pending::before {
    background: transparent !important;
  }
}
.user-dropdown.n-dropdown-menu {
  .n-dropdown-option-body {
    align-items: center;
  }
}
.nav-menu-icon {
  display: flex;
  align-items: center;
  .icon-down {
    width: 14px;
    height: 14px;
  }
  i.icon {
    margin-left: 4px;
  }
  &.active {
    border: 1px solid #00c2ce;
    color: #00c2ce;
    .icon {
      color: #00c2ce;
    }
  }
}
</style>
