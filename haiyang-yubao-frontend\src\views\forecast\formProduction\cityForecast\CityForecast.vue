<template>
  <div class="sea-area-forecast">
    <div class="sea-area-aside">
      <n-data-table
        v-loading="loading"
        class="qx-table"
        :data="areaTableData"
        :columns="columns"
        :single-line="false"
        :max-height="610"
      ></n-data-table>
    </div>
    <div class="sea-area-content">
      <div class="table-wrap">
        <n-data-table
          class="qx-table"
          :data="contentTable"
          :columns="contentColumns"
          :single-line="false"
          :max-height="610"
        ></n-data-table>
      </div>
      <div class="text-area-wrap">
        <div v-if="bridge?.collect().tabIndex === 1" class="update-btn-wrap">
          <n-button @click="updateParagraph">更新</n-button>
        </div>
        <n-input
          v-model:value="paragraph"
          type="textarea"
          show-count
          ref="inputRef"
          placeholder="请输入预报文字，完成后点击文本框右上角的更新按钮"
          class="my-text"
        >
          <template #count>
            <div>
              <span>共{{ cptParagraphStatistics.all }}字</span>
              <span>（</span>
              <span>汉字{{ cptParagraphStatistics.chinese }}字</span>
              <span>）</span>
              <span>，</span>
              <span>选中{{ cptParagraphStatistics.selected }}字</span>
              <span>（</span>
              <span>汉字{{ cptParagraphStatistics.selectedChinese }}字</span>
              <span>）</span>
            </div>
          </template>
        </n-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h, PropType, computed, inject } from 'vue'
import type { DataTableColumn, NInput } from 'naive-ui'
import { useMessage } from 'naive-ui'
import { useContentTable } from 'src/views/forecast/formProduction/seaAreaForecastHooks/useContentTable'
import { useParagraph } from 'src/views/forecast/formProduction/cityForecast/useParagraph'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import {
  bridgeKey,
  IForecastProductBridge
} from 'src/views/forecast/formProduction/forecastProductHooks/types'
import { useParagraphStatistics } from 'src/utils/vue-hooks/useParagraphStatistics/useParagraphStatistics'
import { useAreaTable } from 'src/views/forecast/formProduction/cityForecast/useAreaTable'

const bridge = inject<IBridge<IForecastProductBridge>>(bridgeKey)
const message = useMessage()
const loading = ref(false)

const props = defineProps<{
  tableData: { name: string }[]
}>()
// const { cptTableData } = useCityName(() => props.tableData)
const { areaTableData } = useAreaTable()

const indexArr = computed(() =>
  props.tableData.map(_ => {
    const find = areaTableData.value.find(i => _.name === i.name)!
    return {
      name: find?.name || '-',
      index: find?.index || 0
    }
  })
)

const columns: DataTableColumn[] = [
  {
    title: '序号',
    width: 60,
    key: 'num',
    render(row: any, index: number) {
      return index + 1
    }
  },
  {
    title: '区域城市',
    key: 'name'
  }
]

const { contentColumns, contentTable } = useContentTable({
  numberArr: indexArr,
  setParagraph: (text: string) => setParagraph(text)
})

const { paragraph, updateParagraph, setParagraph } = useParagraph({
  areaGetter() {
    return areaTableData.value.map((item, i) => {
      return {
        name: item.name,
        index: i + 1,
        city: item.name || '-'
      }
    })
  },
  contentArr() {
    return contentTable.value
  }
})
defineExpose({
  getParagraph() {
    return paragraph.value
  },
  getTableData() {
    contentTable.value.forEach((item, i) => {
      const indexArray = item.index.split(',').map(_ => Number(_) - 1)
      Reflect.set(
        item,
        '$names',
        props.tableData
          .filter((_, j) => indexArray.includes(j))
          .map(obj => obj.name)
      )
    })
    return contentTable.value
  }
})

const inputRef = ref<InstanceType<typeof NInput> | null>(null)
const { cptParagraphStatistics } = useParagraphStatistics(paragraph, inputRef)
</script>

<style scoped lang="scss">
.sea-area-forecast {
  display: flex;
  box-sizing: border-box;
  padding: 20px;

  .sea-area-aside {
    width: 280px;
    flex-shrink: 0;
    margin-right: 20px;
  }

  .sea-area-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .table-wrap {
      flex: 1;
    }

    .text-area-wrap {
      flex: 0 0 200px;
      position: relative;

      .update-btn-wrap {
        position: absolute;
        top: 0;
        right: 0;
        margin-top: 3px;
        margin-right: 3px;
        z-index: 1;
        //display: none;
      }

      .my-text {
        height: 100%;
      }
    }

    .text-area-wrap:has(.my-text:focus-within) {
      .update-btn-wrap {
        display: initial;
      }
    }
  }
}
</style>
