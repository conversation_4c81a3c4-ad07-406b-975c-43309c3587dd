<template>
  <div class="aside-menu" :class="props.collapsed ? 'collapsed' : ''">
    <i class="icon icon-collapsed" @click="changeCollapsed"></i>
    <qx-menu :options="menuData" @node-click="clickMenuItem" />
  </div>
</template>

<script setup lang="ts" name="aside-menu">
import { ref, watch, h } from 'vue'
import { useRouter, useRoute, RouteRecordRaw } from 'vue-router'
import type { MenuOption } from 'naive-ui'
import { QxMenu } from 'src/components/QxMenu'
import eventBus from 'src/utils/eventBus'

const router = useRouter()
const routes = router.getRoutes()
const route = useRoute()
const menuData = ref<any[]>([])
const isCollapsed = ref<boolean>(false)

const props = defineProps({
  collapsed: {
    type: Boolean,
    default() {
      return false
    }
  },
  path: {
    type: String,
    default() {
      return ''
    }
  }
})

watch(
  () => props.collapsed,
  val => {
    isCollapsed.value = val
  },
  { immediate: true }
)

watch(
  () => route.path,
  () => {
    getAsideMenu()
  }
)

// 获取侧边栏菜单
function getAsideMenu() {
  // 获取当前路由路径
  const path = route.path
  // 获取当前路由路径的第一级目录
  let parentRouteName = path.split('/')[1]
  if (route.meta?.parentName) {
    parentRouteName = route.meta.parentName as string
  }
  // 根据第一级目录查找父级路由节点
  const result = findParentNodeByName(routes, parentRouteName)
  // 生成侧边栏菜单
  menuData.value = generatorMenu(result?.children ?? [])
}

// 根据名称查找父节点
function findParentNodeByName(
  data: RouteRecordRaw[],
  name: string
): RouteRecordRaw | null {
  // 遍历数据
  for (const node of data) {
    // 如果节点有子节点，则递归查找子节点
    if (node.children) {
      const parentNode = findParentNodeByName(node.children, name)
      // 如果找到了父节点，则返回
      if (parentNode) {
        return parentNode
      }
    }
    // 如果节点的名称与目标名称相同，则返回该节点)
    if (node.name === name) {
      return node
    }
  }
  // 如果没有找到父节点，则返回null
  return null
}

// 生成菜单
function generatorMenu(routerMap: Array<any>) {
  // 遍历路由数组
  return routerMap.map(item => {
    // 创建当前菜单对象
    const currentMenu = {
      ...item,
      ...item.meta,
      label: item.meta?.title,
      key: item.path,
      icon: () => {
        return renderMenuIcon(item)
      }
    }
    // 是否有子菜单，并递归处理
    if (item.children && item.children.length > 0) {
      currentMenu.type = 'group'
      currentMenu.children = generatorMenu(item.children)
    }
    return currentMenu
  })
}

getAsideMenu()

const emit = defineEmits(['collapsed'])

function changeCollapsed() {
  emit('collapsed', !isCollapsed.value)
  eventBus.emit('collapsed', !isCollapsed.value)
}

// 渲染菜单图标
function renderMenuIcon(options: MenuOption) {
  // 从options中解构出meta中的icon
  const {
    meta: { icon }
  } = options as { meta: { icon: string } }
  // 返回一个i标签，class为icon
  return h('i', { class: icon })
}

// 定义一个泛型函数，用于点击菜单项
function clickMenuItem<T extends MenuOption>(item: T) {
  // 跳转到菜单项的路径
  router.push(item.path as string)
}
</script>

<style lang="scss">
.aside-menu {
  width: 200px;
  background: #ffffff;
  box-sizing: border-box;
  padding: 13px 4px 0;
  flex-shrink: 0;
  animation: changeWidthReverse linear 0.3s;
  &.collapsed {
    width: 50px;
    animation: changeWidth linear 0.3s;
    // transform: changeWidth;
  }
  .icon-collapsed {
    margin-left: 11px;
    margin-bottom: 13px;
    cursor: pointer;
  }
  .n-layout {
    height: calc(100% - 46px);
  }
  .n-menu-item-content__icon > i.icon {
    width: 20px;
    height: 20px;
  }
  .n-menu-item-content--selected {
    .n-menu-item-content-header {
      font-weight: 600;
    }
  }
}

@keyframes changeWidth {
  0% {
    width: 200px;
  }
  100% {
    width: 50px;
  }
}
@keyframes changeWidthReverse {
  0% {
    width: 50px;
  }
  100% {
    width: 200px;
  }
}
</style>
