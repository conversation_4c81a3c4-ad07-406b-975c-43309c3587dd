<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-26 17:32:55
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-31 10:18:22
 * @FilePath: /hainan-jianzai-web/src/views/analysis/components/exportDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <qx-dialog
    title="导出站点选择"
    :visible="dialogVisible"
    width="360px"
    class="export-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <n-form
        ref="formRef"
        label-width="auto"
        label-placement="left"
        class="qx-form"
        require-mark-placement="left"
        :model="form"
        :rules="rules"
      >
        <n-form-item label="数据时间" path="dataTime">
          <n-date-picker
            v-model:formatted-value="form.dataTime"
            type="daterange"
            clearable
          />
        </n-form-item>
        <n-form-item label="潮汐基面" path="datum">
          <n-select
            v-model:value="form.datum"
            :options="baseSelectList"
            placeholder="请选择"
          />
        </n-form-item>
        <n-form-item label="站点" path="stationIds">
          <n-select
            v-model:value="form.stationIds"
            multiple
            :options="stationList"
            label-field="name"
            value-field="id"
          />
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="onClose">取消</qx-button>
        <qx-button class="primary" @click="onDialogSave">导出</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { QxDialog } from 'src/components/QxDialog'
import { ref, computed, reactive, watch } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Analysis from 'src/requests/analysis'
import { useMessage } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'

const message = useMessage()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:visible', 'export'])

const formRef = ref<FormInst | null>(null)
const dialogVisible = computed(() => props.visible)
const form = ref({
  dataTime: null,
  datum: null,
  stationIds: null
})

const rules: FormRules = {
  dataTime: {
    type: 'array',
    required: true,
    message: '请选择数据时间',
    trigger: ['blur', 'change']
  },
  datum: {
    required: true,
    message: '请选择潮汐基面',
    trigger: ['blur', 'change']
  },
  stationId: {
    type: 'array',
    required: true,
    message: '请选择站点',
    trigger: ['blur', 'change']
  }
}

const baseSelectList = [
  {
    label: '85基面',
    value: 'datum'
  },
  {
    label: '水尺零点',
    value: 'gauge_zero'
  },
  {
    label: '潮汐表基面',
    value: 'tide_datum'
  }
]

let stationList = ref<any[]>([])

function onClose() {
  emit('update:visible', false)
}

function getStationList() {
  Analysis.getStationListofStormSurge()
    .then(res => {
      stationList.value = res as any
    })
    .catch(e => {
      console.error(e, 'eee')
    })
}

watch(
  () => props.visible,
  (val: boolean) => {
    if (val) {
      getStationList()
    }
  },
  {
    immediate: true
  }
)

function onDialogSave() {
  formRef.value?.validate(errors => {
    if (!errors) {
      emit('export', form.value)
    }
  })
}
</script>

<style lang="scss">
.export-dialog {
  .qx-form {
    box-sizing: border-box;
    padding: 14px 20px 6px;
  }
}
</style>
