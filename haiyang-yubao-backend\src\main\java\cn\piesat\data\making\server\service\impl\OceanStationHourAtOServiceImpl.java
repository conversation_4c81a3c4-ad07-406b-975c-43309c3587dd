package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourAtODao;
import cn.piesat.data.making.server.entity.OceanStationHourAtO;
import cn.piesat.data.making.server.service.OceanStationHourAtOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-气温-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourAtOServiceImpl extends ServiceImpl<OceanStationHourAtODao, OceanStationHourAtO>
        implements OceanStationHourAtOService {

    @Resource
    private OceanStationHourAtODao oceanStationHourAtODao;

    @Override
    public List<OceanStationHourAtO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourAtODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourAtODao.getMaxCreateTime();
    }
}





