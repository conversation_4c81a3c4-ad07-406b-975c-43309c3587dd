package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmGraphicTemplateBDTO;
import cn.piesat.data.making.server.dto.FmGraphicTmplateSignDTO;
import cn.piesat.data.making.server.entity.AlarmLevel;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import cn.piesat.data.making.server.service.AlarmLevelService;
import cn.piesat.data.making.server.service.FmGraphicTemplateBService;
import cn.piesat.data.making.server.service.SeaWaveAlarmService;
import cn.piesat.data.making.server.service.StormSurgeAlarmService;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.vo.AlarmInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部接口-警报
 */
@RestController
@RequestMapping("/outside/alarm")
public class AlarmController {

    @Autowired
    private AlarmLevelService alarmLevelService;
    @Autowired
    private SeaWaveAlarmService seaWaveAlarmService;
    @Autowired
    private StormSurgeAlarmService stormSurgeAlarmService;
    @Autowired
    private FmGraphicTemplateBService fmGraphicTemplateBService;

    /**
     * 根据警报id、类别获取警报信息
     *
     * @param id       警报id
     * @param category 类别：1风暴潮 2海浪
     * @return
     */
    @GetMapping("/info/{id}/{category}")
    public AlarmInfoVO selectOne(@PathVariable String id, @PathVariable Integer category) {
        AlarmInfoVO alarmInfo = new AlarmInfoVO();
        alarmInfo.setAlarmSource("海南省海洋预报台");
        if (1 == category) {
            StormSurgeAlarm alarm = this.stormSurgeAlarmService.getById(Long.valueOf(id));
            alarmInfo.setAlarmTitle(alarm.getTitle());
            AlarmLevel alarmLevel = alarmLevelService.getById(Long.valueOf(alarm.getAlarmLevel()));
            alarmInfo.setAlarmLevel(alarmLevel.getLevelColor());
            alarmInfo.setAlarmTime(this.getAlarmTime("FBCJBZZ"));
            alarmInfo.setAlarmContent(alarm.getAlarmContent());
            alarmInfo.setAlarmImages(alarm.getAlarmImages());
            alarmInfo.setReleaseTime(alarm.getReleaseTime());
            alarmInfo.setAlarmArea(alarm.getAlarmArea());
        }
        if (2 == category) {
            SeaWaveAlarm alarm = this.seaWaveAlarmService.getById(Long.valueOf(id));
            alarmInfo.setAlarmTitle(alarm.getTitle());
            AlarmLevel alarmLevel = alarmLevelService.getById(Long.valueOf(alarm.getAlarmLevel()));
            alarmInfo.setAlarmLevel(alarmLevel.getLevelColor());
            alarmInfo.setAlarmTime(this.getAlarmTime("HLJBZZ"));
            alarmInfo.setAlarmContent(alarm.getAlarmContent());
            alarmInfo.setAlarmImages(alarm.getAlarmImages());
            alarmInfo.setReleaseTime(alarm.getReleaseTime());
            alarmInfo.setAlarmArea(alarm.getAlarmArea());
        }
        return alarmInfo;
    }

    /**
     * 下载文件
     *
     * @param filePath 文件路径
     * @return
     **/
    @GetMapping("/download")
    public void downloadFile(@RequestParam("filePath") String filePath, HttpServletResponse response) {
        int index = filePath.lastIndexOf(File.separator);
        String fileName = filePath.substring(index + 1);
        FileUtil.downloadFile(filePath, fileName, response);
    }

    /**
     * 预报时效
     **/
    private String getAlarmTime(String templateType) {
        //开启状态的警报制作图形模板
        FmGraphicTemplateBDTO graphicTemplate = new FmGraphicTemplateBDTO();
        graphicTemplate.setProductType("2");
        graphicTemplate.setStatus(true);
        graphicTemplate.setTemplateType(templateType);
        List<FmGraphicTemplateB> templateList = fmGraphicTemplateBService.getListAll(graphicTemplate);
        if (CollectionUtils.isEmpty(templateList)) {
            return null;
        }
        FmGraphicTemplateB template = templateList.get(0);
        List<FmGraphicTmplateSignDTO> timeSignList =
                fmGraphicTemplateBService.getById(template.getId()).getSign().stream().filter(sign -> sign.getName().contains("预报时效：")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timeSignList)) {
            return null;
        }
        return timeSignList.get(0).getName().substring(5);
    }
}

