package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmPushResult;
import cn.piesat.data.making.server.service.impl.FmSenderEmailServiceImpl;
import cn.piesat.data.making.server.service.impl.FmSenderFaxServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmSenderEmailServiceImplTest {

    @Autowired
    private FmSenderEmailServiceImpl fmSenderEmailServiceImpl;

    @Autowired
    private FmSenderFaxServiceImpl fmSenderFaxServiceImpl;

    @Test
    public void testSender(){
        FmPushResult fmPushResult = new FmPushResult();
        fmSenderEmailServiceImpl.sender(fmPushResult);
    }

    @Test
    public void testSenderFax(){
        FmPushResult fmPushResult = new FmPushResult();
        fmPushResult.setPushChannelId("123456");
        fmPushResult.setPushChannel("fax");
        fmPushResult.setPushMsg("1951190793490272258.docx");
        fmPushResult.setPushContent("1951190793490272258.docx");
        fmPushResult.setId(1L);
        fmSenderFaxServiceImpl.sender(fmPushResult);
    }
}
