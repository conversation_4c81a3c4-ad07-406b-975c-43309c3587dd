package cn.piesat.data.making.server.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

public class SeaWaveMessageDTO {
    private Long id;
    /**
     * 标题
     */
    @NotEmpty(message = "标题不能为空")
    private String title;
    /**
     * 发布时间
     */
    @NotNull(message = "发布时间不能为空")
    private Date releaseTime;
    /**
     * 编号
     */
    @NotEmpty(message = "编号不能为空")
    private String number;
    /**
     * 签发人
     */
    @NotNull(message = "签发人不能为空")
    private Long signUser;
    /**
     * 制作人
     */
    @NotNull(message = "制作人不能为空")
    private Long makeUser;
    /**
     * 警报内容
     */
    @NotEmpty(message = "警报内容不能为空")
    private String alarmContent;
    /**
     * 短信
     */
    @NotEmpty(message = "短信不能为空")
    private String smsContent;
    /**
     * 台风编号
     */
    private String typhoonNo;
    /**
     * 台风时间
     */
    private Date typhoonTime;
    /**
     * 签发人
     */
    @NotEmpty(message = "签发人名不能为空")
    private String signUserName;
    /**
     * 制作人
     */
    @NotEmpty(message = "制作人名不能为空")
    private String makeUserName;

    /**
     * 源头类型  1冷空气 2台风
     */
    @NotNull(message = "源头类型不能为空")
    private Integer sourceType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public Long getSignUser() {
        return signUser;
    }

    public void setSignUser(Long signUser) {
        this.signUser = signUser;
    }

    public Long getMakeUser() {
        return makeUser;
    }

    public void setMakeUser(Long makeUser) {
        this.makeUser = makeUser;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public String getMakeUserName() {
        return makeUserName;
    }

    public void setMakeUserName(String makeUserName) {
        this.makeUserName = makeUserName;
    }

    public String getTyphoonNo() {
        return typhoonNo;
    }

    public void setTyphoonNo(String typhoonNo) {
        this.typhoonNo = typhoonNo;
    }

    public Date getTyphoonTime() {
        return typhoonTime;
    }

    public void setTyphoonTime(Date typhoonTime) {
        this.typhoonTime = typhoonTime;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }
}
