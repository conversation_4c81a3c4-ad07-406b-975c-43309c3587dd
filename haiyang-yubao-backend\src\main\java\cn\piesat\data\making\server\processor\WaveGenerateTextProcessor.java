package cn.piesat.data.making.server.processor;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.AreaDao;
import cn.piesat.data.making.server.dto.GenerateWordDTO;
import cn.piesat.data.making.server.dto.generate.*;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.enums.AlarmTemplateCodeEnum;
import cn.piesat.data.making.server.enums.WaveLevelEnum;
import cn.piesat.data.making.server.fegin.AlgorithmExecClient;
import cn.piesat.data.making.server.service.AlarmLevelService;
import cn.piesat.data.making.server.service.AlarmProductTemplateService;
import cn.piesat.data.making.server.service.FmForecastProductDataService;
import cn.piesat.data.making.server.service.FmTyphoonBService;
import cn.piesat.data.making.server.utils.GenerateFileUtil;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class WaveGenerateTextProcessor extends DefaultGenerateTextProcessor{

    @Autowired
    private AlarmLevelService alarmLevelService;
    @Autowired
    private AreaDao areaDao;
    @Autowired
    private AlarmProductTemplateService alarmProductTemplateService;
    @Autowired
    private FmTyphoonBService fmTyphoonBService;
    @Autowired
    private FmForecastProductDataService fmForecastProductDataService;

    @Override
    protected Map<String, Object> getParam(GenerateWordDTO generateWordDTO) throws IOException {
        WaveGenerateText generateText = (WaveGenerateText)generateWordDTO;
        Date ncFileDate = ((WaveGenerateText) generateWordDTO).getNcFileDate();

        //计算预报时间
        DateTime startTime = DateUtil.offsetHour(ncFileDate, generateText.getForecastStartTime());
        DateTime endTime = DateUtil.offsetHour(ncFileDate, generateText.getForecastEndTime());

        //查询台风
        FmTyphoonB fmTyphoonB = null;
        if(GenerateWordDTO.TYPHOON.equals(generateText.getSourceType()) || GenerateWordDTO.BOTH.equals(generateText.getSourceType())){
            //查询台风
            fmTyphoonB = fmTyphoonBService.getInfo(generateWordDTO.getTyphoonNo());
        }
        //查询预警等级
        AlarmLevel alarmLevel = Optional.ofNullable(alarmLevelService.getById(generateText.getAlarmLevelId())).orElseThrow(()-> new BusinessException("警报级比不能为空!"));
        //查询海区、海域数据
        LambdaQueryWrapper<Area> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Area::getId,generateText.getAreaIds());
        wrapper.select(Area::getId,Area::getName,Area::getAreaTypeCode);
        List<Area> areaStationList = areaDao.selectList(wrapper);
        Map<Long, Area> areaMap = areaStationList.stream().collect(Collectors.toMap(Area::getId, obj -> obj));
        //查询对应原始文件解析的预报数据
        WaveForecastDataDTO waveForecastDataDTO = new WaveForecastDataDTO(generateText.getAreaIds(), ncFileDate, startTime, endTime);
        List<FmForecastProductData> waveDataList = fmForecastProductDataService.getWaveDataList(waveForecastDataDTO);

        // 构建全局 Map，保留所有对象，但计算最小值时过滤 minValue="0.0"
        Map<Long, WaveMinMaxDTO> globalStatsMap = waveDataList.stream()
                .collect(Collectors.groupingBy(
                        FmForecastProductData::getAreaId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    // 计算最小值：过滤 minValue="0.0" 后取最小
                                   String min = list.stream()
                                            .filter(obj -> !"0.0".equals(obj.getMinValue()))  // 仅过滤 minValue="0.0"
                                            .map(FmForecastProductData::getMinValue)
                                            .min(Comparator.comparingDouble(Double::parseDouble)).orElse("0.1");


                                    // 计算最大值：不过滤任何数据
                                    String max = list.stream()
                                            .map(FmForecastProductData::getMaxValue)
                                            .max(Comparator.comparingDouble(Double::parseDouble)).get();



                                    return new WaveMinMaxDTO(Double.parseDouble(min),Double.parseDouble(max));
                                }
                        )
                ));


        //拼接
        List<WaveFreemarkerDTO.AreaWaveDTO> areaWaveDTOS = globalStatsMap.entrySet().stream().map(entry -> {
            Long areaId = entry.getKey();
            WaveMinMaxDTO minMaxDTO = entry.getValue();

            WaveFreemarkerDTO.AreaWaveDTO areaWaveDTO = new WaveFreemarkerDTO.AreaWaveDTO();
            Area areaStation = areaMap.get(areaId);

            String min = WaveLevelEnum.classifyWave(minMaxDTO.getMin());
            String max = WaveLevelEnum.classifyWave(minMaxDTO.getMax());
            areaWaveDTO.setAreaTypeCode(areaStation.getAreaTypeCode());
            areaWaveDTO.setName(areaStation.getName());
            areaWaveDTO.setMax(minMaxDTO.getMax());
            areaWaveDTO.setMin(minMaxDTO.getMin());
            areaWaveDTO.setMaxZh(max);
            areaWaveDTO.setMinZh(min);
            return areaWaveDTO;
        }).collect(Collectors.toList());


        Map<String, List<WaveFreemarkerDTO.AreaWaveDTO>> areaTypeMap = areaWaveDTOS.stream().collect(Collectors.groupingBy(WaveFreemarkerDTO.AreaWaveDTO::getAreaTypeCode));
        List<WaveFreemarkerDTO.AreaWaveDTO> area = areaTypeMap.getOrDefault("area", Collections.emptyList());
        List<WaveFreemarkerDTO.AreaWaveDTO> region = areaTypeMap.getOrDefault("region", Collections.emptyList());
        List<WaveFreemarkerDTO.AreaWaveDTO> resort = areaTypeMap.getOrDefault("resort", Collections.emptyList());
        List<WaveFreemarkerDTO.AreaWaveDTO> island = areaTypeMap.getOrDefault("island", Collections.emptyList());

        List<WaveFreemarkerDTO.AreaWaveDTO> list = Stream.of(region, resort, island).flatMap(Collection::stream).collect(Collectors.toList());

        Map<String,Object> param = new HashMap<>();
        param.put("time",DateUtil.format(generateWordDTO.getReleaseTime(),"yyyy年MM月dd日HH时"));
        param.put("sourceType",generateWordDTO.getSourceType());
        param.put("forecastStartTime",DateUtil.format(startTime,"dd日HH时"));
        param.put("forecastEndTime",DateUtil.format(endTime,"dd日HH时"));
        String typhoonName = "";
        if (fmTyphoonB != null && StringUtils.isNotBlank(fmTyphoonB.getName())) {
            typhoonName = fmTyphoonB.getName();
        }
        // 源头类型  1冷空气 2台风 3冷空气和台风
        String sourceTypeName;
        if (generateWordDTO.getSourceType() == 1) {
            sourceTypeName = "冷空气";
        } else if (generateWordDTO.getSourceType() == 2) {
            sourceTypeName = "台风" + typhoonName;
        } else if (generateWordDTO.getSourceType() == 3) {
            sourceTypeName = "冷空气和台风" + typhoonName;
        } else {
            sourceTypeName = "未知类型";
        }
        param.put("typhoonName", sourceTypeName);
        param.put("areaList",area);
        param.put("regionList",list);
        param.put("levelColor",alarmLevel.getLevelColor());
        param.put("levelRoman",alarmLevel.getLevelRoman());

        return param;
    }

    @Override
    public GenerateTextVO generate(GenerateWordDTO generateWordDTO, boolean isAlarm) {
        try {
            Map<String, Object> param = this.getParam(generateWordDTO);
            AlarmProductTemplate contentTemplate;
            AlarmProductTemplate smsTemplate;
            if(isAlarm){
                //查询内容、短信模板
                contentTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.SeaWaveAlarmContent);
                smsTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.SeaWaveSMS);
            }else {
                contentTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.SeaWaveMSGAlarmContent);
                smsTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.SeaWaveMSGSMS);
            }
            Preconditions.checkArgument(contentTemplate!=null,"内容模板为空");
            Preconditions.checkArgument(smsTemplate!=null,"短信模板为空");
            return this.templateReplace(param,contentTemplate.getTemplateContent(),smsTemplate.getTemplateContent());
        } catch (Exception e) {
            logger.error("文字生成失败",e);
            throw new BusinessException("文字生成异常："+e.getMessage());
        }
    }
}
