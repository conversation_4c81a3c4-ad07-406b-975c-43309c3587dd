package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.SeaWaveMessageDTO;
import cn.piesat.data.making.server.processor.WaveGenerateText;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.piesat.data.making.server.entity.SeaWaveMessage;

import java.util.Date;
import java.util.Map;

/**
 * (SeaWaveMessageB)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-11 16:59:02
 */
public interface SeaWaveMessageService extends IService<SeaWaveMessage> {

    PageResult<SeaWaveMessage> pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime);

    void checkNumber(String number);

    SeaWaveMessage selectByNumber(String number);

    SeaWaveMessage saveInfo(SeaWaveMessageDTO seaWaveMessageDTO);

    SeaWaveMessage updateInfo(SeaWaveMessageDTO seaWaveMessageDTO);

    GenerateTextVO generateText(WaveGenerateText waveAlarmGenerateText);

    void release(SeaWaveMessageDTO seaWaveMessageDTO);

}

