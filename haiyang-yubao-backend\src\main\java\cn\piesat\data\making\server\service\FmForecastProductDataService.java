package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.generate.WaveForecastDataDTO;
import cn.piesat.data.making.server.entity.FmForecastProductData;
import cn.piesat.data.making.server.vo.AreaVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-09-27 11:22:38
 */
@Service
public interface FmForecastProductDataService extends IService<FmForecastProductData> {

    void saveResult(String filepath) throws Exception;

    List<FmForecastProductData> getList(FmForecastProductData fmForecastProductData);

    Date getLastForecastStartTime(String dataSource);

    /**
     * 更新修订值
     **/
    void updateModifyValue(List<FmForecastProductData> list);

    void saveTideData(List<AreaVO> areaList, Date startTime, Date endTime);

    List<FmForecastProductData> getWaveDataList(WaveForecastDataDTO waveForecastDataDTO);

    List<Date> waveStartTimeList(String dataSource, String productCode, Integer num);
}
