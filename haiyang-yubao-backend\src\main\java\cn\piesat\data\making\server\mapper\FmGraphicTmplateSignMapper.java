package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicTmplateSignDTO;
import cn.piesat.data.making.server.entity.FmGraphicTmplateSign;
import cn.piesat.data.making.server.vo.FmGraphicTmplateSignVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-10 09:45:47
 */
@Mapper(componentModel = "spring")
public interface FmGraphicTmplateSignMapper {

    FmGraphicTmplateSignMapper INSTANCE = Mappers.getMapper(FmGraphicTmplateSignMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicTmplateSignVO entityToVo(FmGraphicTmplateSign entity);

    /**
     * dto-->entity
     */
    FmGraphicTmplateSign dtoToEntity(FmGraphicTmplateSignDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicTmplateSignVO> entityListToVoList(List<FmGraphicTmplateSign> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicTmplateSign> dtoListToEntityList(List<FmGraphicTmplateSignDTO> dtoList);
}
