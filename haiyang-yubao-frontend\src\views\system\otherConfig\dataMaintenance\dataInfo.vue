<template>
  <div class="data-detail">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>数据详情</h3>
      </div>
    </div>
    <div class="table-container">
      <div class="table-fileter d-flex align-items-center">
        <label for="">数据名称</label>
        <n-input
          class="qx-input"
          placeholder="请选择"
          readonly
          v-model:value="dataValue"
        />
      </div>

      <n-data-table :data="tableData" :columns="columns" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted,toRef, onBeforeUnmount } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import eventBus from 'src/utils/eventBus'

const dataValue = ref<string>('')

// 监听树形菜单选中事件
eventBus.on('selectedTree', data => {
  dataValue.value = data.label
})

const tableData = ref<any[]>([
  {
    lon: '名称',
    lat: '航线',
    index: 1
  }
])

interface RowData {
  title: string
  lon: string
  lat: string
  index: number
}

function createColumns(): DataTableColumns<RowData> {
  return [
    {
      title: '序号',
      key: 'index'
    },
    {
      title: '经度',
      key: 'lon'
    },
    {
      title: '纬度',
      key: 'lat'
    },
    {
      title: '操作',
      key: 'tags',
      render(row) {
        return h(
          'span',
          { class: 'operate-btn', onClick: () => onDelete(row) },
          '删除'
        )
      }
    }
  ]
}

const columns = createColumns()

function onDelete(row: RowData) {
  console.log(row, 'row')
}

onBeforeUnmount(()=>{
  eventBus.off('selectedTree')
})
</script>

<style lang="scss">
.data-detail {
  position: absolute;
  top: 20px;
  left: 282px;
  z-index: 1;
  height: calc(100% - 40px);
  background: #fff;
  width: 360px;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  z-index: 1;
  .aside-header {
    .title-wrap {
      padding: 18px 0 18px 26px;
    }
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .table-fileter {
    box-sizing: border-box;
    padding: 0 32px;
    margin: 10px 0;
    label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 12px;
      color: #222222;
      line-height: 32px;
      // width: 80px;
      flex-shrink: 0;
      text-align: right;
      margin-right: 15px;
    }
  }
  .table-container {
    .n-data-table-th__title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
    .n-data-table-td {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 12px;
      color: #222222;
      line-height: 14px;
    }
  }
}
</style>