import Api from 'src/requests/alarm'
import { ReferenceTreeManager } from './referenceTreeManager'
import { computed, Ref, ref, watch } from 'vue'
import { IRegionData, IRegionTree } from 'src/requests/alarm.type'
import {
  EReferenceRegionType,
  RegionTableMerged
} from './useReferenceStation.type'
import { DataTableBaseColumn, NSelect, NButton } from 'naive-ui'
import moment from 'moment'

export function useReferenceStation() {
  // 是否显示站
  const referenceStationVisible = ref(false)
  // 树列表
  const regionTreeData = ref<IRegionTree[]>([])
  const loadingRegionTreeData = ref<boolean>(false)
  // 当前站数据级别
  const regionLevel = ref(EReferenceRegionType.CITY_LEVEL)
  // 站列表
  const regionList = ref<IRegionTree[]>([])
  const regionSelected = ref<string[]>([])
  const regionTableData = ref<RegionTableMerged[]>([])
  // 参考站时间
  const stationTime = ref<[string, string]>([
    moment().subtract(1, 'days').format('YYYY-MM-DD 00:00:00'),
    moment().format('YYYY-MM-DD 00:00:00')
  ])

  watch(referenceStationVisible, async val => {
    if (!val) {
      return
    }
    void (await updateRegionTreeData(regionTreeData, loadingRegionTreeData))
    const treeManager = new ReferenceTreeManager(regionTreeData.value)
    if (regionLevel.value === EReferenceRegionType.CITY_LEVEL) {
      // 市县级
      regionList.value = treeManager.getCityRootNode().childList || []
    }
  })

  watch(regionSelected, async (val, oldValue) => {
    // 旧的有新的没有 (删除)
    const filter = oldValue.filter(i => !val.includes(i))
    filter.forEach(id => {
      const find = regionTableData.value.find(i => i.id === id)
      if (find && regionTableData.value.indexOf(find) !== -1) {
        regionTableData.value.splice(regionTableData.value.indexOf(find))
      }
    })
    // 新的有旧的没有 (新增)
    const idArr = val.filter(i => !oldValue.includes(i))
    idArr.forEach(id => {
      const find = regionList.value.find(region => region.id === id)
      if (find) {
        regionTableData.value.push({
          date: '',
          height: 0,
          level: 0,
          levelDesc: '',
          regionName: '',
          stationId: '',
          stationName: '',
          tide: '',
          tideTime: '',
          warnHeight: 0,
          ...find
        })
      }
    })
  })

  return {
    updateRegionTreeData: () =>
      updateRegionTreeData(regionTreeData, loadingRegionTreeData),
    getReferenceStationLevelList,
    regionLevel,
    referenceStationVisible,
    regionList,
    regionSelected,
    loadingRegionTreeData,
    regionTableData,
    regionColumns: getRegionColumns(regionTableData, regionSelected),
    selectAllRegion: () => selectAllRegion(regionSelected, regionList),
    stationTime,
    queryStationDataV2: () => {
      queryStationData(stationTime.value, regionTableData)
    }
  }
}

/**
 * 请求参考站数据
 * @param stationTreeData
 */
async function updateRegionTreeData(
  stationTreeData: Ref<IRegionTree[]>,
  loadingStationTreeData: Ref<boolean>
) {
  stationTreeData.value = []

  loadingStationTreeData.value = true
  try {
    const res = await Api.getStationTree({
      name: '', // 所有节点
      flag: 'station',
      code: 'oceanStation'
    })
    stationTreeData.value = res
    const treeManager = new ReferenceTreeManager(res)
    const cityRootNode = treeManager.getCityRootNode()
    console.log(cityRootNode)
  } catch (e) {
    stationTreeData.value = []
  } finally {
    loadingStationTreeData.value = false
  }
}

function selectAllRegion(
  regionSelected: Ref<string[]>,
  regionList: Ref<IRegionTree[]>
) {
  regionSelected.value = regionList.value.map(i => i.code)
}

/**
 * 获取参考站类型列表
 */
function getReferenceStationLevelList() {
  return [
    {
      name: '市县级',
      value: EReferenceRegionType.CITY_LEVEL
    },
    {
      name: '乡镇级',
      value: EReferenceRegionType.TOWNSHIP_LEVEL
    },
    {
      name: '岸段级',
      value: EReferenceRegionType.COASTAL_LEVEL
    }
  ]
}

function getRegionColumns(
  regionTableData: Ref<RegionTableMerged[]>,
  regionSelected: Ref<string[]>
): DataTableBaseColumn<any>[] {
  return [
    {
      title: '影响地区',
      key: 'name'
    },
    {
      title: '参考站',
      key: 'stationName',
      render(rowData: RegionTableMerged) {
        console.log(rowData)
        return (
          <NSelect
            value={rowData.stationId}
            onUpdateValue={val => (rowData.stationId = val)}
            options={rowData.childList || ([] as any)}
            valueField={'id'}
            labelField={'name'}></NSelect>
        )
      }
    },
    {
      title: '日期',
      key: 'date'
    },
    {
      title: '高潮时',
      key: 'tide'
    },
    {
      title: '高潮值(cm)',
      key: 'height'
    },
    {
      title: '警戒潮位(cm)',
      key: 'warnHeight'
    },
    {
      title: '预警级别',
      key: 'levelDesc'
    }
    // {
    //   title: '操作',
    //   key: '',
    //   render(rowData: RegionTableMerged, index: number) {
    //     const _ = () => {
    //       const indexOf = regionSelected.value.indexOf(rowData.stationId)
    //       regionSelected.value.splice(indexOf, 1)
    //     }
    //     return (
    //       <NButton onClick={_}>
    //         删除
    //       </NButton>
    //     )
    //   }
    // }
  ]
}

/**
 * 查询参考站数据 (查询按钮点击)
 */
function queryStationData(
  stationTime: [string, string],
  regionTableData: Ref<RegionTableMerged[]>
) {
  const idArr = regionTableData.value.map(i => i.stationId)
  Api.getStationData({
    ids: idArr.join(','),
    startTime: stationTime[0],
    endTime: stationTime[1],
    datum: 'datum'
  })
    .then((res: IRegionData[]) => {
      res.forEach((i: IRegionData) => {
        regionTableData.value.find(item => {
          i.id = item.id
          Object.assign(item, i)
        })
      })
    })
    .catch(() => {})
}
