package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 深海大型锚系浮标原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("group_ocean_large_deepsea_mooringbuoy_o")
public class GroupOceanLargeDeepseaMooringbuoyO implements Serializable {

    private static final long serialVersionUID = -17416218088786577L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 浮标站ID
     **/
    @JsonProperty("BuoyInfo_Id")
    @TableField("buoyinfo_id")
    private String buoyinfoId;
    /**
     * 浮标类型
     **/
    @JsonProperty("BuoyInfo_Type")
    @TableField("buoyinfo_type")
    private String buoyinfoType;
    /**
     * 浮标名称
     **/
    @JsonProperty("BuoyInfo_Name")
    @TableField("buoyinfo_name")
    private String buoyinfoName;
    /**
     * 浮标编号
     **/
    @JsonProperty("BuoyInfo_No")
    @TableField("buoyinfo_no")
    private String buoyinfoNo;
    /**
     * 浮标种类
     **/
    @JsonProperty("BuoyInfo_Kind")
    @TableField("buoyinfo_kind")
    private String buoyinfoKind;
    /**
     * 经度
     **/
    @JsonProperty("longitude")
    @TableField("longitude")
    private String longitude;
    /**
     * 经度(标准格式)
     **/
    @JsonProperty("longitude_standard")
    @TableField("longitude_standard")
    private String longitudeStandard;
    /**
     * 经度质控符
     **/
    @JsonProperty("longitude_Qc2")
    @TableField("longitude_qc2")
    private Long longitudeQc2;
    /**
     * 纬度
     **/
    @JsonProperty("latitude")
    @TableField("latitude")
    private String latitude;
    /**
     * 纬度(标准格式)
     **/
    @JsonProperty("latitude_standard")
    @TableField("latitude_standard")
    private String latitudeStandard;
    /**
     * 纬度质控符
     **/
    @JsonProperty("latitude_Qc2")
    @TableField("latitude_qc2")
    private Integer latitudeQc2;
    /**
     * 监测时间（原始未处理）
     **/
    @JsonProperty("MonitoringTimeString")
    @TableField("monitoringtimestring")
    private String monitoringtimestring;
    /**
     * 监测时间质控符
     **/
    @JsonProperty("MonitoringTimeString_Qc2")
    @TableField("monitoringtimestring_qc2")
    private Integer monitoringtimestringQc2;
    /**
     * 监测时间
     **/
    @JsonProperty("MonitoringTime")
    @TableField("monitoringtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringtime;
    /**
     * 监测时间质控符
     **/
    @JsonProperty("MonitoringTime_Qc2")
    @TableField("monitoringtime_qc2")
    private Integer monitoringtimeQc2;
    /**
     * 浮标运行状态
     **/
    @JsonProperty("RunningStatus_Style")
    @TableField("runningstatus_style")
    private String runningstatusStyle;
    /**
     * 浮标运行模式
     **/
    @JsonProperty("RunningStatus_Status")
    @TableField("runningstatus_status")
    private String runningstatusStatus;
    /**
     * 浮标电池电压
     **/
    @JsonProperty("RunningStatus_DY")
    @TableField("runningstatus_dy")
    private String runningstatusDy;
    /**
     * 浮标电池电压质控符
     **/
    @JsonProperty("RunningStatus_DY_Qc2")
    @TableField("runningstatus_dy_qc2")
    private Integer runningstatusDyQc2;
    /**
     * 浮标姿态斜度
     **/
    @JsonProperty("RunningStatus_lean")
    @TableField("runningstatus_lean")
    private String runningstatusLean;
    /**
     * 浮标姿态斜度质控符
     **/
    @JsonProperty("RunningStatus_lean_Qc2")
    @TableField("runningstatus_lean_qc2")
    private Integer runningstatusLeanQc2;
    /**
     * 浮标姿态方位
     **/
    @JsonProperty("RunningStatus_azimuth")
    @TableField("runningstatus_azimuth")
    private String runningstatusAzimuth;
    /**
     * 浮标姿态方位质控符
     **/
    @JsonProperty("RunningStatus_azimuth_Qc2")
    @TableField("runningstatus_azimuth_qc2")
    private Integer runningstatusAzimuthQc2;
    /**
     * 风速
     **/
    @JsonProperty("BuoyData_WS")
    @TableField("buoydata_ws")
    private String buoydataWs;
    /**
     * 风速质控符
     **/
    @JsonProperty("BuoyData_WS_Qc2")
    @TableField("buoydata_ws_qc2")
    private Integer buoydataWsQc2;
    /**
     * 风向
     **/
    @JsonProperty("BuoyData_WD")
    @TableField("buoydata_wd")
    private String buoydataWd;
    /**
     * 风向质控符
     **/
    @JsonProperty("BuoyData_WD_Qc2")
    @TableField("buoydata_wd_qc2")
    private Integer buoydataWdQc2;
    /**
     * 最大风速
     **/
    @JsonProperty("BuoyData_WSM")
    @TableField("buoydata_wsm")
    private String buoydataWsm;
    /**
     * 最大风速质控符
     **/
    @JsonProperty("BuoyData_WSM_Qc2")
    @TableField("buoydata_wsm_qc2")
    private Integer buoydataWsmQc2;
    /**
     * 最大风速的风向（延时数据）
     **/
    @JsonProperty("BuoyData_WDM")
    @TableField("buoydata_wdm")
    private String buoydataWdm;
    /**
     * 最大风速的风向质控符（延时数据）
     **/
    @JsonProperty("BuoyData_WDM_Qc2")
    @TableField("buoydata_wdm_qc2")
    private Integer buoydataWdmQc2;
    /**
     * 瞬时风速（延时数据）
     **/
    @JsonProperty("BuoyData_WSA")
    @TableField("buoydata_wsa")
    private String buoydataWsa;
    /**
     * 瞬时风速质控符（延时数据）
     **/
    @JsonProperty("BuoyData_WSA_Qc2")
    @TableField("buoydata_wsa_qc2")
    private Integer buoydataWsaQc2;
    /**
     * 瞬时风向（延时数据）
     **/
    @JsonProperty("BuoyData_WDA")
    @TableField("buoydata_wda")
    private String buoydataWda;
    /**
     * 瞬时风向质控符（延时数据）
     **/
    @JsonProperty("BuoyData_WDA_Qc2")
    @TableField("buoydata_wda_qc2")
    private Integer buoydataWdaQc2;
    /**
     * 极大风速（延时数据）
     **/
    @JsonProperty("BuoyData_WSH")
    @TableField("buoydata_wsh")
    private String buoydataWsh;
    /**
     * 极大风速质控符（延时数据）
     **/
    @JsonProperty("BuoyData_WSH_Qc2")
    @TableField("buoydata_wsh_qc2")
    private Integer buoydataWshQc2;
    /**
     * 极大风速的风向（延时数据）
     **/
    @JsonProperty("BuoyData_WDH")
    @TableField("buoydata_wdh")
    private String buoydataWdh;
    /**
     * 极大风速的风向质控符（延时数据）
     **/
    @JsonProperty("BuoyData_WDH_Qc2")
    @TableField("buoydata_wdh_qc2")
    private Integer buoydataWdhQc2;
    /**
     * 气温
     **/
    @JsonProperty("BuoyData_AT")
    @TableField("buoydata_at")
    private String buoydataAt;
    /**
     * 气温质控符
     **/
    @JsonProperty("BuoyData_AT_Qc2")
    @TableField("buoydata_at_qc2")
    private Integer buoydataAtQc2;
    /**
     * 气压
     **/
    @JsonProperty("BuoyData_BP")
    @TableField("buoydata_bp")
    private String buoydataBp;
    /**
     * 气压质控符
     **/
    @JsonProperty("BuoyData_BP_Qc2")
    @TableField("buoydata_bp_qc2")
    private Integer buoydataBpQc2;
    /**
     * 相对湿度
     **/
    @JsonProperty("BuoyData_HU")
    @TableField("buoydata_hu")
    private String buoydataHu;
    /**
     * 相对湿度质控符
     **/
    @JsonProperty("BuoyData_HU_Qc2")
    @TableField("buoydata_hu_qc2")
    private Integer buoydataHuQc2;
    /**
     * 表层水温
     **/
    @JsonProperty("BuoyData_WT")
    @TableField("buoydata_wt")
    private String buoydataWt;
    /**
     * 表层水温质控符
     **/
    @JsonProperty("BuoyData_WT_Qc2")
    @TableField("buoydata_wt_qc2")
    private Integer buoydataWtQc2;
    /**
     * 表层盐度
     **/
    @JsonProperty("BuoyData_SL")
    @TableField("buoydata_sl")
    private String buoydataSl;
    /**
     * 表层盐度质控符
     **/
    @JsonProperty("BuoyData_SL_Qc2")
    @TableField("buoydata_sl_qc2")
    private Integer buoydataSlQc2;
    /**
     * 平均波高
     **/
    @JsonProperty("BuoyData_BG")
    @TableField("buoydata_bg")
    private String buoydataBg;
    /**
     * 平均波高质控符
     **/
    @JsonProperty("BuoyData_BG_Qc2")
    @TableField("buoydata_bg_qc2")
    private Integer buoydataBgQc2;
    /**
     * 平均波向
     **/
    @JsonProperty("BuoyData_BX")
    @TableField("buoydata_bx")
    private String buoydataBx;
    /**
     * 平均波向质控符
     **/
    @JsonProperty("BuoyData_BX_Qc2")
    @TableField("buoydata_bx_qc2")
    private Integer buoydataBxQc2;
    /**
     * 平均波周期
     **/
    @JsonProperty("BuoyData_ZQ")
    @TableField("buoydata_zq")
    private String buoydataZq;
    /**
     * 平均波周期质控符
     **/
    @JsonProperty("BuoyData_ZQ_Qc2")
    @TableField("buoydata_zq_qc2")
    private Integer buoydataZqQc2;
    /**
     * 有效波高
     **/
    @JsonProperty("BuoyData_YBG")
    @TableField("buoydata_ybg")
    private String buoydataYbg;
    /**
     * 有效波高质控符
     **/
    @JsonProperty("BuoyData_YBG_Qc2")
    @TableField("buoydata_ybg_qc2")
    private Integer buoydataYbgQc2;
    /**
     * 有效波周期
     **/
    @JsonProperty("BuoyData_YZQ")
    @TableField("buoydata_yzq")
    private String buoydataYzq;
    /**
     * 有效波周期质控符
     **/
    @JsonProperty("BuoyData_YZQ_Qc2")
    @TableField("buoydata_yzq_qc2")
    private Integer buoydataYzqQc2;
    /**
     * 1/10波高
     **/
    @JsonProperty("BuoyData_TenthBG")
    @TableField("buoydata_tenthbg")
    private String buoydataTenthbg;
    /**
     * 1/10波高质控符
     **/
    @JsonProperty("BuoyData_TenthBG_Qc2")
    @TableField("buoydata_tenthbg_qc2")
    private Integer buoydataTenthbgQc2;
    /**
     * 1/10周期
     **/
    @JsonProperty("BuoyData_TenthZQ")
    @TableField("buoydata_tenthzq")
    private String buoydataTenthzq;
    /**
     * 1/10周期质控符
     **/
    @JsonProperty("BuoyData_TenthZQ_Qc2")
    @TableField("buoydata_tenthzq_qc2")
    private Integer buoydataTenthzqQc2;
    /**
     * 最大波高
     **/
    @JsonProperty("BuoyData_ZBG")
    @TableField("buoydata_zbg")
    private String buoydataZbg;
    /**
     * 最大波高质控符
     **/
    @JsonProperty("BuoyData_ZBG_Qc2")
    @TableField("buoydata_zbg_qc2")
    private Integer buoydataZbgQc2;
    /**
     * 最大波周期
     **/
    @JsonProperty("BuoyData_ZZQ")
    @TableField("buoydata_zzq")
    private String buoydataZzq;
    /**
     * 最大波周期质控符
     **/
    @JsonProperty("BuoyData_ZZQ_Qc2")
    @TableField("buoydata_zzq_qc2")
    private Integer buoydataZzqQc2;
    /**
     * 波数
     **/
    @JsonProperty("BuoyData_BS")
    @TableField("buoydata_bs")
    private String buoydataBs;
    /**
     * 波数质控符
     **/
    @JsonProperty("BuoyData_BS_Qc2")
    @TableField("buoydata_bs_qc2")
    private Integer buoydataBsQc2;
    /**
     * 叶绿素
     **/
    @JsonProperty("BuoyData_YLS")
    @TableField("buoydata_yls")
    private String buoydataYls;
    /**
     * 叶绿素质控符
     **/
    @JsonProperty("BuoyData_YLS_Qc2")
    @TableField("buoydata_yls_qc2")
    private Integer buoydataYlsQc2;
    /**
     * 浊度
     **/
    @JsonProperty("BuoyData_ZD")
    @TableField("buoydata_zd")
    private String buoydataZd;
    /**
     * 浊度质控符
     **/
    @JsonProperty("BuoyData_ZD_Qc2")
    @TableField("buoydata_zd_qc2")
    private Integer buoydataZdQc2;
    /**
     * 能见度
     **/
    @JsonProperty("BuoyData_NJD")
    @TableField("buoydata_njd")
    private String buoydataNjd;
    /**
     * 能见度质控符
     **/
    @JsonProperty("BuoyData_NJD_Qc2")
    @TableField("buoydata_njd_qc2")
    private Integer buoydataNjdQc2;
    /**
     * 表层流速（延时数据）
     **/
    @JsonProperty("BuoyData_CS")
    @TableField("buoydata_cs")
    private String buoydataCs;
    /**
     * 表层流速质控符（延时数据）
     **/
    @JsonProperty("BuoyData_CS_Qc2")
    @TableField("buoydata_cs_qc2")
    private Integer buoydataCsQc2;
    /**
     * 表层流向（延时数据）
     **/
    @JsonProperty("BuoyData_CD")
    @TableField("buoydata_cd")
    private String buoydataCd;
    /**
     * 表层流向质控符（延时数据）
     **/
    @JsonProperty("BuoyData_CD_Qc2")
    @TableField("buoydata_cd_qc2")
    private Integer buoydataCdQc2;
    /**
     * 溶解氧
     **/
    @JsonProperty("BuoyData_DO")
    @TableField("buoydata_do")
    private String buoydataDo;
    /**
     * 溶解氧质控符
     **/
    @JsonProperty("BuoyData_DO_Qc2")
    @TableField("buoydata_do_qc2")
    private Integer buoydataDoQc2;
    /**
     * 剖面层数标记
     **/
    @JsonProperty("Section_NO")
    @TableField("section_no")
    private String sectionNo;
    /**
     * 剖面温度
     **/
    @JsonProperty("TempSalt_WT")
    @TableField("tempsalt_wt")
    private String tempsaltWt;
    /**
     * 剖面温度质控符
     **/
    @JsonProperty("TempSalt_WT_Qc2")
    @TableField("tempsalt_wt_qc2")
    private Integer tempsaltWtQc2;
    /**
     * 剖面盐度
     **/
    @JsonProperty("TempSalt_SL")
    @TableField("tempsalt_sl")
    private String tempsaltSl;
    /**
     * 剖面盐度质控符
     **/
    @JsonProperty("TempSalt_SL_Qc2")
    @TableField("tempsalt_sl_qc2")
    private Integer tempsaltSlQc2;
    /**
     * 温盐剖面深度
     **/
    @JsonProperty("TempSalt_SE")
    @TableField("tempsalt_se")
    private String tempsaltSe;
    /**
     * 温盐剖面深度质控符
     **/
    @JsonProperty("TempSalt_SE_Qc2")
    @TableField("tempsalt_se_qc2")
    private Integer tempsaltSeQc2;
    /**
     * 测流剖面流速
     **/
    @JsonProperty("SeaCurrent_CS")
    @TableField("seacurrent_cs")
    private String seacurrentCs;
    /**
     * 测流剖面流速质控符
     **/
    @JsonProperty("SeaCurrent_CS_Qc2")
    @TableField("seacurrent_cs_qc2")
    private Integer seacurrentCsQc2;
    /**
     * 测流剖面流向
     **/
    @JsonProperty("SeaCurrent_CD")
    @TableField("seacurrent_cd")
    private String seacurrentCd;
    /**
     * 测流剖面流向质控符
     **/
    @JsonProperty("SeaCurrent_CD_Qc2")
    @TableField("seacurrent_cd_qc2")
    private Integer seacurrentCdQc2;
    /**
     * 测流剖面深度
     **/
    @JsonProperty("SeaCurrent_SE")
    @TableField("seacurrent_se")
    private String seacurrentSe;
    /**
     * 测流剖面深度质控符
     **/
    @JsonProperty("SeaCurrent_SE_Qc2")
    @TableField("seacurrent_se_qc2")
    private Integer seacurrentSeQc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 区域
     **/
    @JsonProperty("area")
    @TableField("area")
    private String area;
    /**
     * 类型  1：实时   2: 延时
     **/
    @JsonProperty("type")
    @TableField("type")
    private Integer type;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    @JsonProperty("BuoyData_CHLA")
    @TableField("buoydata_chla")
    private String buoydataChla;
    @JsonProperty("BuoyData_CHLA_Qc2")
    @TableField("buoydata_chla_qc2")
    private Integer buoydataChlaQc2;
    @JsonProperty("BuoyData_NH4_N")
    @TableField("buoydata_nh4_n")
    private String buoydataNh4N;
    @JsonProperty("BuoyData_NH4_N_Qc2")
    @TableField("buoydata_nh4_n_qc2")
    private Integer buoydataNh4NQc2;
    @JsonProperty("BuoyData_NO2_N")
    @TableField("buoydata_no2_n")
    private String buoydataNo2N;
    @JsonProperty("BuoyData_NO2_N_Qc2")
    @TableField("buoydata_no2_n_qc2")
    private Integer buoydataNo2NQc2;
    @JsonProperty("BuoyData_NO3_N")
    @TableField("buoydata_no3_n")
    private String buoydataNo3N;
    @JsonProperty("BuoyData_NO3_N_Qc2")
    @TableField("buoydata_no3_n_qc2")
    private Integer buoydataNo3NQc2;
    @JsonProperty("BuoyData_PO4_P")
    @TableField("buoydata_po4_p")
    private String buoydataPo4P;
    @JsonProperty("BuoyData_PO4_P_Qc2")
    @TableField("buoydata_po4_p_qc2")
    private Integer buoydataPo4PQc2;
    @JsonProperty("BuoyData_SiO3_Si")
    @TableField("buoydata_sio3_si")
    private String buoydataSio3Si;
    @JsonProperty("BuoyData_SiO3_Si_Qc2")
    @TableField("buoydata_sio3_si_qc2")
    private Integer buoydataSio3SiQc2;
    @JsonProperty("BuoyData_pH")
    @TableField("buoydata_ph")
    private String buoydataPh;
    @JsonProperty("BuoyData_pH_Qc2")
    @TableField("buoydata_ph_qc2")
    private Integer buoydataPhQc2;
    /**
     * 名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
    /**
     * 经度
     **/
    @JsonProperty("HY_LONGITUDE")
    @TableField("hy_longitude")
    private String hyLongitude;
    /**
     * 纬度
     **/
    @JsonProperty("HY_LATITUDE")
    @TableField("hy_latitude")
    private String hyLatitude;
}



