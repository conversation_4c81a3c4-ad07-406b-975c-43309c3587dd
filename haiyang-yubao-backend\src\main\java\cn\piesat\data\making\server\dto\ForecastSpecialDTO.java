package cn.piesat.data.making.server.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 公共服务-专项预报DTO
 *
 */
public class ForecastSpecialDTO implements Serializable {
    private static final long serialVersionUID = -1;

    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 发往单位名称
     */
    private String sendUnitName;
    /**
     * 数据源(grid,nmefc)
     */
    private String sourceCode;
    /**
     * 起报时间
     */
    private Date startTime;
    /**
     * 起报时间
     */
    private Date endTime;
    /**
     * 预报开始时间
     */
    private Date forecastTimeS;
    /**
     * 预报结束时间
     */
    private Date forecastTimeE;
    /**
     * 预报区域
     */
    private String forecastArea;
    /**
     * 生成时间（0、未开始，1、正在生成，2、生成完成）
     */
    private Integer status;

    private Integer pageNum;

    private Integer pageSize;
    /**
     * 要素名称
     */
    private String elementName;
    /**
     * 预报nc文件
     * 修改格式为map结构
     * {"要素编码":"xxx.nc,xxx1.nc","要素编码2":"xxx.nc,xxx1.nc"}
     */
    private String forecastNcPath;
    /**
     * 预报区域-自定义
     */
    private String forecastAreaCustomize;

    /**
     * 时间类型
     * 逐小时 H 、逐天 D
     */
    private String timeType;

    /**
     * 图片路径
     */
    private String imagePath;
    /**
     * 海浪注释
     */
    private String waveAnnotation;
    /**
     * 防护措施建议
     */
    private String suggestion;
    /**
     * 牧场名称
     */
    private String ranchName;
    /**
     * 牧场内容
     */
    private String ranchValue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSendUnitName() {
        return sendUnitName;
    }

    public void setSendUnitName(String sendUnitName) {
        this.sendUnitName = sendUnitName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getForecastTimeS() {
        return forecastTimeS;
    }

    public void setForecastTimeS(Date forecastTimeS) {
        this.forecastTimeS = forecastTimeS;
    }

    public Date getForecastTimeE() {
        return forecastTimeE;
    }

    public void setForecastTimeE(Date forecastTimeE) {
        this.forecastTimeE = forecastTimeE;
    }

    public String getForecastArea() {
        return forecastArea;
    }

    public void setForecastArea(String forecastArea) {
        this.forecastArea = forecastArea;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getElementName() {
        return elementName;
    }

    public void setElementName(String elementName) {
        this.elementName = elementName;
    }

    public String getForecastNcPath() {
        return forecastNcPath;
    }

    public void setForecastNcPath(String forecastNcPath) {
        this.forecastNcPath = forecastNcPath;
    }

    public String getForecastAreaCustomize() {
        return forecastAreaCustomize;
    }

    public void setForecastAreaCustomize(String forecastAreaCustomize) {
        this.forecastAreaCustomize = forecastAreaCustomize;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getWaveAnnotation() {
        return waveAnnotation;
    }

    public void setWaveAnnotation(String waveAnnotation) {
        this.waveAnnotation = waveAnnotation;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getRanchName() {
        return ranchName;
    }

    public void setRanchName(String ranchName) {
        this.ranchName = ranchName;
    }

    public String getRanchValue() {
        return ranchValue;
    }

    public void setRanchValue(String ranchValue) {
        this.ranchValue = ranchValue;
    }
}

