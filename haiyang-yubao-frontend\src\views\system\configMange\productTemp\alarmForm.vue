<template>
  <div class="form-container alarm-form">
    <n-form
      ref="form"
      :model="alarmForm"
      label-placement="left"
      label-width="auto"
      require-mark-placement="left"
      :rules="rules"
    >
      <n-form-item label="模板名称" path="templateName">
        <n-input
          v-model:value="alarmForm.templateName"
          :readonly="!props.isEdit"
        />
      </n-form-item>
      <n-form-item label="模板类型" path="templateType">
        <n-select
          v-model:value="alarmForm.templateType"
          placeholder="请选择"
          clearable
          :options="templateTypeList"
          :disabled="!props.isEdit"
          label-field="templateTypeName"
          value-field="templateType"
        />
      </n-form-item>
      <n-form-item label="模板编码" path="templateCode">
        <n-radio-group v-model:value="alarmForm.templateCode" name="radiogroup">
          <n-radio value="CONTENT">内容</n-radio>
          <n-radio value="SMS">短信</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item label="模板状态" path="status">
        <n-switch
          v-model:value="alarmForm.status"
          :checked-value="1"
          :unchecked-value="0"
          :disabled="!props.isEdit"
        />
      </n-form-item>
      <slot name="suffix"></slot>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, PropType, onMounted, onBeforeUnmount } from 'vue'
import type { FormInst } from 'naive-ui'
import eventBus from 'src/utils/eventBus'

const form = ref<FormInst | null>(null)
let templateTypeList = ref<any[]>([])
const props = defineProps({
  typeList: {
    type: Array,
    default: () => []
  },
  isEdit: {
    type: Boolean,
    default: true
  },
  info: {
    type: Object as PropType<AlarmType>,
    default() {
      return {}
    }
  }
})

type AlarmType = {
  [key: string]: any
}

const alarmForm: AlarmType = reactive({
  templateTypeName: '',
  templateType: null,
  templateCode: 'CONTENT',
  templateName: '',
  templateContent: '',
  sort: 1,
  status: 0
})

const rules = {
  templateName: {
    required: true,
    message: '模板名称不能为空',
    trigger: 'blur'
  },
  templateCode: {
    required: true,
    message: '模板编码不能为空',
    trigger: 'blur'
  }
}

watch(
  () => props.info,
  val => {
    if (val) {
      Object.keys(val).forEach((item: any) => {
        if (alarmForm.hasOwnProperty(item)) {
          alarmForm[item] = val[item]
        }
      })
    }
  },
  { immediate: true }
)

watch(
  () => props.typeList,
  val => {
    if (val.length) {
      templateTypeList.value = val
    }
  },
  { immediate: true }
)

onMounted(() => {
  eventBus.on('getTemplateTypeList', (params: any[]) => {
    templateTypeList.value = params
    console.log(templateTypeList.value, 'templateTypeList.value')
  })
})

onBeforeUnmount(() => {
  eventBus.off('getTemplateTypeList')
})

defineExpose({ alarmForm, form })
</script>

<style scoped></style>
