package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.FmGraphicRecordBDao;
import cn.piesat.data.making.server.dao.ForecastProductRecordDao;
import cn.piesat.data.making.server.dto.ForecastProductTemplateDTO;
import cn.piesat.data.making.server.entity.FmGraphicRecordB;
import cn.piesat.data.making.server.entity.ForecastProductRecord;
import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import cn.piesat.data.making.server.entity.ForecastTask;
import cn.piesat.data.making.server.enums.FileType;
import cn.piesat.data.making.server.enums.ForecastTaskStatus;
import cn.piesat.data.making.server.enums.ProductEnum;
import cn.piesat.data.making.server.fegin.DataPushFeign;
import cn.piesat.data.making.server.mapper.ForecastProductRecordMapper;
import cn.piesat.data.making.server.model.DataPushTaskInfo;
import cn.piesat.data.making.server.model.ForecastProductPushResultInfo;
import cn.piesat.data.making.server.model.ProductReleaseResult;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.utils.WordUtil;
import cn.piesat.data.making.server.utils.XmlUtil;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.ForecastProductRecordVO;
import cn.piesat.data.making.server.vo.ForecastProductTemplateVO;
import cn.piesat.data.making.server.vo.ForecastRecordVO;
import cn.piesat.data.push.common.vo.task.ManualTaskLogVO;
import cn.piesat.data.push.common.vo.task.ManualTaskProductResponseVO;
import cn.piesat.data.push.common.vo.task.ManualTaskProductVO;
import cn.piesat.data.push.common.vo.task.ManualTaskVO;
import cn.piesat.data.push.starter.service.PushManualTaskServiceAgent;
import cn.piesat.webconfig.response.R;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.data.Pictures;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预报产品记录表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastProductRecordServiceImpl extends ServiceImpl<ForecastProductRecordDao, ForecastProductRecord> implements ForecastProductRecordService {

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Resource
    private ForecastProductRecordDao forecastProductRecordDao;
    @Resource
    private ForecastRecordService forecastRecordService;
    @Resource
    private ForecastProductTemplateService forecastProductTemplateService;
    @Resource
    private FmGraphicRecordBDao fmGraphicRecordBDao;
    @Resource
    private PushManualTaskServiceAgent pushManualTaskServiceAgent;
    @Resource
    private ForecastTaskService forecastTaskService;
    @Resource
    private DataPushFeign dataPushFeign;

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

    private static final AtomicLong timestampGenerator = new AtomicLong(System.currentTimeMillis());

    @Override
    public List<ForecastProductRecordVO> getList() {
        List<ForecastProductRecord> list = this.list(createQueryWrapper());
        return ForecastProductRecordMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public List<ForecastProductRecordVO> save() {
        long startTime = System.currentTimeMillis();
        //删除旧的产品记录
//        List<ForecastProductRecord> recordList = this.list(createQueryWrapper());
//        if (!CollectionUtils.isEmpty(recordList)) {
//            recordList.stream().filter(record -> StringUtils.isNotBlank(record.getFileUrl())).forEach(record -> FileUtil.deleteFile(record.getFileUrl()));
//            this.removeByIds(recordList.stream().map(ForecastProductRecord::getId).collect(Collectors.toList()));
//        }
        //查询开启并关联预报的产品模板
        ForecastProductTemplateDTO forecastProductTemplateDto = new ForecastProductTemplateDTO();
        forecastProductTemplateDto.setStatus(Boolean.TRUE);
        List<ForecastProductTemplateVO> templateList = forecastProductTemplateService.getList(forecastProductTemplateDto).stream()
                .filter(template -> StringUtils.isNotBlank(template.getRelationTemplateCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(templateList)) {
            return Collections.EMPTY_LIST;
        }
        //关联的预报数据
        Map<String, List<ForecastRecordDetail>> detailMap = this.getForecastRecordDetail(templateList);
        //关联的专题图
        FmGraphicRecordB graphicRecord = this.getGraphicRecord();

        //日期
        String publishTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时"));
        String publishDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        DayOfWeek dayOfWeek = LocalDateTime.now().getDayOfWeek();
        String dayName = dayOfWeek.getDisplayName(TextStyle.FULL, Locale.SIMPLIFIED_CHINESE);

        //模板map
        ConcurrentMap<String, Object> map = new ConcurrentHashMap<>();
        map.put("publishTime", publishTime);
        map.put("publishDate", publishDate);
        map.put("dayOfWeek", dayName);
        String graphicRecordId = null;
        if (graphicRecord != null && StringUtils.isNotBlank(graphicRecord.getFileUrl())) {
            graphicRecordId = graphicRecord.getId().toString();
            map.put("graphicUrl", Pictures.ofLocal(graphicRecord.getFileUrl()).size(200, 200).create());
        }
        String finalGraphicRecordId = graphicRecordId;

        //创建一个线程池
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        //使用 CopyOnWriteArrayList 存储结果，避免并发修改异常
        CopyOnWriteArrayList<ForecastProductRecord> list = new CopyOnWriteArrayList<>();
        //使用 CountDownLatch 来等待所有任务完成
        CountDownLatch latch = new CountDownLatch(templateList.size());
        for (ForecastProductTemplateVO template : templateList) {
            executorService.submit(() -> {
                List<String> codeList = Arrays.asList(template.getRelationTemplateCode().split(","));
                StringBuffer forecastRecordIdStr = new StringBuffer();
                codeList.stream().forEach(code -> {
                    List<ForecastRecordDetail> detailList = detailMap.get(code);
                    if (!CollectionUtils.isEmpty(detailList)) {
                        Long recordId = detailList.get(0).getForecastRecordId();
                        forecastRecordIdStr.append(recordId).append(",");
                    }
                });

                String forecastRecordId = forecastRecordIdStr.length() == 0 ? null : forecastRecordIdStr.substring(0, forecastRecordIdStr.length() - 1);
                ForecastProductRecord record = new ForecastProductRecord();
                record.setName(template.getName());
                record.setProductTemplateId(template.getId());
                record.setForecastRecordId(forecastRecordId);
                record.setGraphicRecordId(finalGraphicRecordId);
                //预报产品文件路径
                String outPath = this.createProductFile(map, detailMap, template);
                record.setFileUrl(outPath);
                list.add(record);
                //任务完成，计数减一
                latch.countDown();
            });
        }
        try {
            //等待所有任务完成
            latch.await();
        } catch (InterruptedException e) {
            log.error("等待任务完成时出现异常：{}", e.getMessage());
            Thread.currentThread().interrupt();
        }
        //关闭线程池
        executorService.shutdown();
        this.saveBatch(list);
        long endTime = System.currentTimeMillis();
        log.debug("程序运行时间：" + (endTime - startTime) + "ms");
        return ForecastProductRecordMapper.INSTANCE.entityListToVoList(list);
    }

    /**
     * 实时数据推送-推送单位管理-任务列表 根据任务查询推送产品列表
     **/
    private List<String> getPushProductIdList() {
        List<String> productIdList = new ArrayList<>();
        try {
            R taskResult = dataPushFeign.getPushTaskList(1, 100, "FTP,DISK,EMAIL,OSS", 1, 0);
            if (!taskResult.isSuccess() || taskResult.getData() == null) {
                return productIdList;
            }
            PageResult taskPageResult = JsonUtil.json2Object(JsonUtil.object2Json(taskResult.getData()), PageResult.class);
            List<DataPushTaskInfo> taskList = JsonUtil.json2ObjectList(JsonUtil.object2Json(taskPageResult.getPageResult()),
                    new TypeReference<List<DataPushTaskInfo>>() {
                    });
            taskList.stream().forEach(task -> {
                try {
                    R productResult = dataPushFeign.getPushProductListByTaskId(1, 100, task.getId());
                    if (!productResult.isSuccess() || productResult.getData() == null) {
                        return;
                    }
                    PageResult productPageResult = JsonUtil.json2Object(JsonUtil.object2Json(productResult.getData()), PageResult.class);
                    List<DataPushTaskInfo.PushProductInfo> productList = JsonUtil.json2ObjectList(JsonUtil.object2Json(productPageResult.getPageResult()),
                            new TypeReference<List<DataPushTaskInfo.PushProductInfo>>() {
                            });
                    productIdList.addAll(productList.stream().map(DataPushTaskInfo.PushProductInfo::getProductId).collect(Collectors.toList()));
                } catch (Exception e) {
                    log.error("获取推送产品列表异常：推送任务id：{}，{}", task.getId(), e.getMessage());
                }
            });
        } catch (Exception e) {
            log.error("获取推送任务列表异常，异常原因：" + e.getMessage());
        }
        return productIdList;
    }

    @Override
    public void release() {
        //查询预报产品列表
        List<ForecastProductRecord> list = this.list(createQueryWrapper());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //查询推送产品列表
        List<String> pushProductIdList = this.getPushProductIdList();
        //预报模板信息
        List<ForecastProductTemplateVO> templateList = forecastProductTemplateService.getList(new ForecastProductTemplateDTO());
        Map<Long, ForecastProductTemplateVO> templateMap = templateList.stream().collect(Collectors.toMap(ForecastProductTemplateVO::getId, Function.identity(),
                (key1, key2) -> key2));

        List<ManualTaskProductVO> productList = new ArrayList<>();
        list.stream().forEach(record -> {
            ForecastProductTemplateVO template = templateMap.get(record.getProductTemplateId());
            if (!pushProductIdList.contains(template.getCode())) {
                return;
            }
            ManualTaskProductVO product = new ManualTaskProductVO();
            product.setProductId(template.getCode());
            //如果预报产品模板是HTML类型 需要word转html进行发布
            if (FileType.HTML.getValue().equals(template.getFileType())) {
                String docxPrefix = record.getFileUrl().split("\\.")[0];
                String htmlName = docxPrefix + ".html";
                WordUtil.convertToHtml(record.getFileUrl(), htmlName);
                record.setFileUrl(htmlName);
            }
            product.setFilePath(record.getFileUrl());
            product.setProductName(record.getName());
            product.setDataTime(new Date().getTime());
            productList.add(product);
        });

        //关联的专题图
        FmGraphicRecordB graphicRecord = this.getGraphicRecord();
        if (graphicRecord != null && pushProductIdList.contains(ProductEnum.FORECAST_IMAGE.getProductId())) {
            ManualTaskProductVO graphicProduct = new ManualTaskProductVO();
            graphicProduct.setProductId(ProductEnum.FORECAST_IMAGE.getProductId());
            graphicProduct.setFilePath(graphicRecord.getFileUrl());
            graphicProduct.setProductName("专题图");
            graphicProduct.setDataTime(new Date().getTime());
            productList.add(graphicProduct);
        }

        //视频
        if (pushProductIdList.contains(ProductEnum.FORECAST_VIDEO.getProductId())) {
            ManualTaskProductVO videoProduct = new ManualTaskProductVO();
            videoProduct.setProductId(ProductEnum.FORECAST_VIDEO.getProductId());
            String filePath = String.format("%s/%s", baseOutputPath, "video.wmv");
            videoProduct.setFilePath(filePath);
            videoProduct.setProductName("视频");
            videoProduct.setDataTime(new Date().getTime());
            productList.add(videoProduct);
        }

        ManualTaskVO manualTaskVO = new ManualTaskVO();
        manualTaskVO.setTaskName("预报产品推送");
        manualTaskVO.setSystem("data-making-server");
        manualTaskVO.setMode(1);
        manualTaskVO.setProducts(productList);
        log.debug("创建推送任务:{}", JsonUtil.object2Json(manualTaskVO));
        Long taskId = pushManualTaskServiceAgent.createManualTask(manualTaskVO);
        log.debug("开始推送任务:{}", taskId);
        pushManualTaskServiceAgent.startManualTask(taskId);
        //更新预报产品记录中的推送任务id
        list.stream().forEach(record -> record.setPushTaskId(taskId));
        this.updateBatchById(list);
        //更新预报任务中的状态
        LambdaQueryWrapper<ForecastTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForecastTask::getStatus, ForecastTaskStatus.MAKED.getValue());
        List<ForecastTask> forecastTaskList = forecastTaskService.list(queryWrapper);
        forecastTaskList.stream().forEach(task -> task.setStatus(ForecastTaskStatus.RELEASED.getValue()));
        forecastTaskService.updateBatchById(forecastTaskList);

        //add by wangxin
        //怀疑这个接口是预报发布接口
        //需要拿template_code和发邮件做对应

        //fmPushResultServiceImpl.addFmPushResList("","SeaWaveAlarm",seaWaveAlarm.getAlarmContent(),seaWaveAlarm.getWordFilePath());

        for(ForecastProductRecord vo : list){
            String filePath = vo.getFileUrl();
            String fileName = filePath.substring(filePath.lastIndexOf("/")+1);
            ForecastProductTemplateVO forecastProductTemplateVO = forecastProductTemplateService.getInfoById(vo.getProductTemplateId());
            fmPushResultServiceImpl.addFmPushResList("",forecastProductTemplateVO.getCode(),vo.getName(),vo.getFileUrl(),fileName,"/forecastProductRecord/downloadDoc/"+vo.getId()+".docx");
        }
    }

    public void downloadDoc(HttpServletResponse response,Long productRecordId){
        ForecastProductRecord forecastProductRecord = this.getById(productRecordId);
        String filePath = forecastProductRecord.getFileUrl();
        String fileName = filePath.substring(filePath.lastIndexOf("/")+1);
        FileUtil.downloadFile(filePath,fileName,response);
    }

    @Override
    public void download(HttpServletResponse response) {
        List<ForecastProductRecord> list = this.list(createQueryWrapper());
        List<String> filePathList = list.stream().filter(record -> StringUtils.isNotBlank(record.getFileUrl()))
                .map(ForecastProductRecord::getFileUrl).collect(Collectors.toList());

        //修改了
        List<String> fileNameList = list.stream().filter(record -> StringUtils.isNotBlank(record.getFileUrl()))
                .map(p -> p.getName()+p.getFileUrl().substring(p.getFileUrl().lastIndexOf("_"))).collect(Collectors.toList());
        //打包下载
        //FileUtil.zipFiles(filePathList, "预报产品.zip", response);
        FileUtil.zipFiles(filePathList,fileNameList, "预报产品_"+System.currentTimeMillis()+".zip", response);
    }

    @Override
    public PageResult<ForecastProductPushResultInfo> getReleaseResultPage(PageParam pageParam) {
        //预报产品推送任务
        List<ForecastProductRecord> taskInfoList = forecastProductRecordDao.getPushTaskInfoList();
        if (CollectionUtils.isEmpty(taskInfoList)) {
            return new PageResult(Collections.EMPTY_LIST, pageParam.getPageNum(), pageParam.getPageSize(), 0L);
        }
        Map<Long, Date> taskInfoMap = taskInfoList.stream().collect(Collectors.toMap(ForecastProductRecord::getPushTaskId,
                ForecastProductRecord::getUpdateTime, (key1, key2) -> key2));
        List<Long> taskIdList = taskInfoMap.keySet().stream().collect(Collectors.toList());
        List<ManualTaskLogVO> taskLogList = pushManualTaskServiceAgent.getManualTaskLogInfos(taskIdList);
        if (CollectionUtils.isEmpty(taskLogList)) {
            return new PageResult(Collections.EMPTY_LIST, pageParam.getPageNum(), pageParam.getPageSize(), 0L);
        }
        ArrayList<ForecastProductPushResultInfo> list = new ArrayList<>();
        taskLogList.stream().forEach(taskLog -> {
            ForecastProductPushResultInfo info = new ForecastProductPushResultInfo();
            info.setId(taskLog.getId());
            info.setTaskName(taskLog.getTaskName());
            info.setTotalCount(taskLog.getTotalCount());
            info.setPushingCount(taskLog.getPushingCount());
            info.setFailedCount(taskLog.getFailedCount());
            info.setSuccessCount(taskLog.getSuccessCount());
            info.setPublishTime(taskInfoMap.get(taskLog.getId()));
            list.add(info);
        });
        List<ForecastProductPushResultInfo> orderList = list.stream().sorted(Comparator.comparing(ForecastProductPushResultInfo::getPublishTime,
                Comparator.reverseOrder())).collect(Collectors.toList());
        return this.convertToPage(orderList, pageParam.getPageNum(), pageParam.getPageSize());
    }

    @Override
    public List<ProductReleaseResult> getReleaseResultByTaskId(Long pushTaskId, String productName, Integer state) {
        List<ProductReleaseResult> list = new ArrayList<>();
        //推送任务
        List<ManualTaskProductResponseVO> taskList = pushManualTaskServiceAgent.getFreeModeManualTaskProductUnits(pushTaskId);
        //预报产品模板
        List<ForecastProductTemplateVO> templateList = forecastProductTemplateService.getList(new ForecastProductTemplateDTO());
        Map<String, String> templateMap = templateList.stream().collect(Collectors.toMap(ForecastProductTemplateVO::getCode,
                ForecastProductTemplateVO::getFileType, (key1, key2) -> key2));

        taskList.stream().forEach(obj -> {
            ProductReleaseResult result = new ProductReleaseResult();
            result.setFileName(obj.getProductName());
            result.setFileType(templateMap.get(obj.getProductId()));
            if ("专题图".equals(obj.getProductName())) {
                result.setFileType(".png");
            }
            if ("视频".equals(obj.getProductName())) {
                result.setFileType(".wmv");
            }
            result.setPushType(obj.getPushType());
            result.setState(obj.getState());
            result.setFilePath(obj.getFilePath());
            if (StringUtils.isNotBlank(productName)) {
                if (productName.equals(obj.getProductName())) {
                    if (state == null || (state != null && state == obj.getState())) {
                        list.add(result);
                    }
                }
            } else {
                if (state == null || (state != null && state == obj.getState())) {
                    list.add(result);
                }
            }
        });
        return list;
    }

    /**
     * list转换page
     **/
    private PageResult convertToPage(List<ForecastProductPushResultInfo> pageResult, Integer pageNum, Integer pageSize) {
        int startIndex = (pageNum - 1) * pageSize;
        if (startIndex > pageResult.size()) {
            return new PageResult<>(Collections.EMPTY_LIST, pageNum, pageSize, 0L);
        }
        int endIndex = Math.min(startIndex + pageSize, pageResult.size());
        List<ForecastProductPushResultInfo> subList = pageResult.subList(startIndex, endIndex);
        return new PageResult<>(subList, pageNum, pageSize, Long.valueOf(subList.size()));
    }

    /**
     * 查询当天的预报记录明细 k:预报类型 v:预报记录明细列表
     **/
    private Map<String, List<ForecastRecordDetail>> getForecastRecordDetail(List<ForecastProductTemplateVO> templateList) {
        Map<String, List<ForecastRecordDetail>> map = new HashMap<>();
        //查询当天全部的预报记录
        List<ForecastRecordVO> recordList = forecastRecordService.getList();
        if (CollectionUtils.isEmpty(recordList)) {
            return map;
        }
        //根据预报类型分组 k:预报类型 v:预报记录
        Map<String, ForecastRecordVO> recordMap = recordList.stream()
                .collect(Collectors.toMap(ForecastRecordVO::getForecastTemplateCode, Function.identity(), (key1, key2) -> key2));
        //k:预报记录id v:预报记录详情列表
        Map<Long, List<ForecastRecordDetail>> detailMap = recordList.stream()
                .collect(Collectors.toMap(ForecastRecordVO::getId, ForecastRecordVO::getDetailList));

        //查询产品模板关联的预报记录明细列表
        templateList.forEach(template -> {
            List<String> codeList = Arrays.asList(template.getRelationTemplateCode().split(","));
            codeList.stream().forEach(code -> {
                ForecastRecordVO forecastRecord = recordMap.get(code);
                if (forecastRecord == null) {
                    return;
                }
                Long recordId = forecastRecord.getId();
                List<ForecastRecordDetail> list = new ArrayList<>();
                List<ForecastRecordDetail> detailList = detailMap.get(recordId);
                //修改逻辑，当forecastRecord的ForecastContent属性不为空时，单独处理
                //modify by wangxin 20250709
                if(forecastRecord.getForecastContent()!=null){
                //if(code.equalsIgnoreCase("area_hqyb")||code.equalsIgnoreCase("areaname_csyb")||code.equalsIgnoreCase("fu ")||code.equalsIgnoreCase("region_sshjyb")){
                    //list.add()
                    ForecastRecordDetail forecastRecordDetail = new ForecastRecordDetail();
                    String codeTmpName = code+"_cont";
                    forecastRecordDetail.setAreaCode("bbwnb");
                    forecastRecordDetail.setColumnCode(codeTmpName);
                    forecastRecordDetail.setValue(forecastRecord.getForecastContent());

                    List<ForecastRecordDetail> detailListTmp = new ArrayList<>();
                    detailListTmp.add(forecastRecordDetail);
                    map.put(codeTmpName,detailListTmp);
                }

                if (CollectionUtils.isEmpty(detailList)) {
                    return;
                }
                list.addAll(detailList);
                map.put(code, list);
            });
        });
        return map;
    }

    /**
     * 查询当天的专题图
     **/
    private FmGraphicRecordB getGraphicRecord() {
        LambdaQueryWrapper<FmGraphicRecordB> graphicQuery = new LambdaQueryWrapper<>();
        graphicQuery.eq(FmGraphicRecordB::getChecked, Boolean.TRUE);
        graphicQuery.ge(FmGraphicRecordB::getCreateTime, LocalDate.now().atTime(LocalTime.MIN));
        graphicQuery.le(FmGraphicRecordB::getCreateTime, LocalDate.now().atTime(LocalTime.MAX));
        return fmGraphicRecordBDao.selectOne(graphicQuery);
    }

    /**
     * 生成产品文件
     **/
    private String createProductFile(ConcurrentMap<String, Object> map, Map<String, List<ForecastRecordDetail>> detailMap, ForecastProductTemplateVO template) {
        String outPath = null;
        try {
            Date date = new Date();
            int year = DateUtil.year(date);
            String day = DateUtil.format(date, "yyyyMMdd");
            String time = DateUtil.format(date, "yyyyMMddHHmmss");
            long timestamp = timestampGenerator.incrementAndGet();
            String tempWordName = String.format(CommonConstant.FORECAST_PRODUCT_TEMP_WORD_NAME, baseOutputPath, year, day, time, timestamp);
            String wordName = String.format(CommonConstant.FORECAST_PRODUCT_WORD_NAME, baseOutputPath, year, day, time, timestamp);
            String txtName = String.format(CommonConstant.FORECAST_PRODUCT_TXT_NAME, baseOutputPath, year, day, time, timestamp);
            String xmlName = String.format(CommonConstant.FORECAST_PRODUCT_XML_NAME, baseOutputPath, year, day, time, timestamp);

            if (FileType.HTML.getValue().equals(template.getFileType())) {
                outPath = wordName;
                WordUtil.createWord(map, detailMap, template.getFileUrl(), wordName);
            }
            if (FileType.DOCX.getValue().equals(template.getFileType())) {
                outPath = wordName;
                //预报单word时，publishTime发布时间，需要固定为17时，需要将publishTime发布时间格式化
                String publishTime = String.valueOf(map.get("publishTime"));
                map.put("publishTime",publishTime.split("日")[0] + "日17时");
                WordUtil.createWord(map, detailMap, template.getFileUrl(), wordName);
            }
            if (FileType.TXT.getValue().equals(template.getFileType())) {
                outPath = txtName;
                //txt内容生成word
                WordUtil.createFile(template.getFileContent(), tempWordName);
                //解析word并赋值
                WordUtil.createWord(map, detailMap, tempWordName, wordName);
                WordUtil.convertToTxt(wordName, txtName);
                FileUtil.deleteFile(tempWordName);
                FileUtil.deleteFile(wordName);
            }
            if (FileType.XML.getValue().equals(template.getFileType())) {
                outPath = xmlName;
                XmlUtil.createXml(template.getFileUrl(), xmlName, detailMap);
            }
        } catch (Exception e) {
            log.error("生成预报产品异常：{},{}", template.getName(), e.getMessage());
        }
        return outPath;
    }

    private LambdaQueryWrapper<ForecastProductRecord> createQueryWrapper() {
        LambdaQueryWrapper<ForecastProductRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(ForecastProductRecord::getCreateTime, LocalDate.now().atTime(LocalTime.MIN));
        queryWrapper.le(ForecastProductRecord::getCreateTime, LocalDate.now().atTime(LocalTime.MAX));
        return queryWrapper.orderByAsc(ForecastProductRecord::getCreateTime);
    }
}





