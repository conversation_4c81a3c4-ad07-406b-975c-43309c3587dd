/*
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-17 17:55:05
 * @LastEditors: xuli<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-19 14:42:11
 * @FilePath: \hainan-jianzai-web\src\utils\plotting\plot\style\MarkerStyleFactory.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */

import MarkerStyleNew from './MarkerStyleNew'
/**
 * @classdesc 样式工厂。根据图元类型生成样式对象
 * <AUTHOR>
 */
class StyleFactory {
  /**
   * @param {PlotTypes} type 类型
   * @static
   */
  static createFTStyle(img, rotation = 0, type = '') {
    if (type === 'mete_wave_direction') {
      const swh = localStorage.getItem('swh') // 有效波高
      const waveCycle = localStorage.getItem('waveCycle') // 波周期
      return new MarkerStyleNew(img, rotation, { swh, waveCycle })
    } else {
      return new MarkerStyleNew(img, rotation)
    }

    // return new PloygonStyle();
  }
}

export default StyleFactory
