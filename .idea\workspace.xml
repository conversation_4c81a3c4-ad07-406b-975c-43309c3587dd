<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e1e97f72-fc2c-4782-8814-29da4f9271df" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/dto/GenerateWordDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/dto/GenerateWordDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/dto/SeaWaveAlarmDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/dto/SeaWaveAlarmDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/dto/StormSurgeAlarmDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/dto/StormSurgeAlarmDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/entity/SeaWaveAlarm.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/entity/SeaWaveAlarm.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/entity/SeaWaveMessage.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/entity/SeaWaveMessage.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/entity/StormSurgeAlarm.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/entity/StormSurgeAlarm.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/processor/StormSurgeGenerateTextProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/processor/StormSurgeGenerateTextProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/processor/WaveGenerateTextProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/processor/WaveGenerateTextProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-frontend/src/views/alarm/components/making-hooks/enums.ts" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-frontend/src/views/alarm/components/making-hooks/enums.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-frontend/src/views/alarm/components/making-hooks/useAlarmContent.ts" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-frontend/src/views/alarm/components/making-hooks/useAlarmContent.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/haiyang-yubao-frontend/src/views/alarm/components/making.vue" beforeDir="false" afterPath="$PROJECT_DIR$/haiyang-yubao-frontend/src/views/alarm/components/making.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/haiyang-yubao-backend" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../../Program Files (x86)/Maven/apache-maven-3.9.2-bin" />
        <option name="localRepository" value="D:\Program Files (x86)\Maven\Repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Program Files (x86)\Maven\apache-maven-3.9.2-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="335kqimO4vb9Hy1UczN7GKOoJku" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.DataMakingServerApplication.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Code/GitRepository/haiyang&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Code Search&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="DataMakingServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.piesat.data.making.server.DataMakingServerApplication" />
      <module name="data-making-server" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.piesat.data.making.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.DataMakingServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e1e97f72-fc2c-4782-8814-29da4f9271df" name="Changes" comment="" />
      <created>1758614512315</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1758614512315</updated>
    </task>
    <servers />
  </component>
</project>