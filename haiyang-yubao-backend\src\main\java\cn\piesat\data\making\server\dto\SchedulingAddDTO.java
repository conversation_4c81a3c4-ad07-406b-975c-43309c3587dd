package cn.piesat.data.making.server.dto;

import cn.piesat.data.making.server.entity.FmSchedulingUser;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

public class SchedulingAddDTO {
    @JsonFormat(pattern = "yyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyy-MM-dd HH:mm:ss")
    private Date schedulingDate;

    private List<FmSchedulingUser> userList;

    public Date getSchedulingDate() {
        return schedulingDate;
    }

    public void setSchedulingDate(Date schedulingDate) {
        this.schedulingDate = schedulingDate;
    }

    public List<FmSchedulingUser> getUserList() {
        return userList;
    }

    public void setUserList(List<FmSchedulingUser> userList) {
        this.userList = userList;
    }
}
