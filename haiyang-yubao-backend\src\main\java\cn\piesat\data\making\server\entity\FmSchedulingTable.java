package cn.piesat.data.making.server.entity;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:04
 */
@Data
@Accessors(chain = true)
@TableName("fm_scheduling_table")
public class FmSchedulingTable implements Serializable {

    private static final long serialVersionUID = 425014695325330216L;


    @TableId("id")
    private Long id;
    @TableField("scheduling_date")
    private Date schedulingDate;
    @TableField("user_name")
    private String userName;
    @TableField("user_id")
    private Long userId;
    @TableField("day")
    private Integer day;
    @TableField("scheduling_main_id")
    private Long schedulingMainId;
    @TableField("sign_date")
    private Date signDate;
    @TableField("sign_user_id")
    private Long signUserId;
    @TableField("sign_user_name")
    private String signUserName;

    public Long getSignUserId() {
        return signUserId;
    }

    public void setSignUserId(Long signUserId) {
        this.signUserId = signUserId;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getSchedulingDate() {
        return schedulingDate;
    }

    public void setSchedulingDate(Date schedulingDate) {
        this.schedulingDate = schedulingDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Long getSchedulingMainId() {
        return schedulingMainId;
    }

    public void setSchedulingMainId(Long schedulingMainId) {
        this.schedulingMainId = schedulingMainId;
    }
}
