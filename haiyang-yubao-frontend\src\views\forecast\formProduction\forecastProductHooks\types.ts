export interface IForecastProductItem {
  id: null
  forecastRecordId: null
  areaId: string
  areaCode: string
  areaName: string
  elementCode: string
  elementName: string
  columnCode: string
  columnName: string
  elementValueHandle: string
  elementDisplay: boolean
  value: string
  createUserId: null
  createUser: null
  createTime: null
  updateUserId: null
  updateUser: null
  updateTime: null
}

export interface IForecastProductBridge {
  tabIndex: number
}

export const bridgeKey = 'forecastProductBridge'
// 海区预报 code
export const seaForecastType = 'area'
// 近岸城市预报 code
export const cityForecastType = 'city'
