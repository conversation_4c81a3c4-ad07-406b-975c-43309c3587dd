/*
 * @Author: alex
 * @Date: 2022-02-16 10:55:13
 * @LastEditTime: 2025-03-21 14:04:42
 * @LastEditors: 樊海玲 <EMAIL>
 * @Description: main.ts
 */
import { createApp, Directive } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
// import { remTool } from '@mo-yu/core'
import './style/app.scss'
import './style/icons.scss'
import './utils/plotting/plot/plottingol.css'
import router from './router'
import { createIoc } from './utils/ioc'
import { naive } from './utils/nativeUl'
// main.js
import microApp from '@micro-zoe/micro-app'
// import 'amfe-flexible'
import 'src/utils/lib-flexible.js'

//注册全局组件
import globalComponent from 'src/components/index'

microApp.start({
  inline: false, // 默认值false 开启inline后，被提取的js会作为script标签插入应用中运行，在开发环境使用
  'disable-memory-router': true, // 关闭虚拟路由系统
  'disable-patch-request': true // 关闭对子应用请求的拦截
})

// 多个微服务需要合并后台接口或从命名的情况配置microServices
import { microServices } from 'src/config/microServices'
//添加全局变量
microApp.setGlobalData({
  apiService: microServices,
  appPath: import.meta.env.BASE_URL
})

// remTool.resetDesignSize(1920, 1080)
// remTool.enable()

const app = createApp(App)
// 循环注册指令
import * as directives from 'src/directives'
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key])
})

const meta = document.createElement('meta')
meta.name = 'naive-ui-style'
document.head.appendChild(meta)

app
  .use(createIoc())
  .use(createPinia())
  .use(naive)
  .use(router)
  .mount('#vite-app')

// 注册全局的组件
for (const componentItme in globalComponent) {
  app.component(componentItme, globalComponent[componentItme])
}

