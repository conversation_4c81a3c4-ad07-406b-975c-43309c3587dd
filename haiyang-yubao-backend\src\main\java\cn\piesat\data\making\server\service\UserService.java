package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.UserDTO;
import cn.piesat.data.making.server.entity.User;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.UserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户表服务接口
 *
 * <AUTHOR>
 */
public interface UserService extends IService<User> {

    /**
     * 查询分页
     */
    PageResult<UserVO> getPage(UserDTO dto, PageParam pageParam);

    /**
     * 查询列表
     */
    List<UserVO> getList(UserDTO dto);

    /**
     * 查询详情
     */
    UserVO getInfoById(Long id);

    /**
     * 保存
     */
    void save(UserDTO dto);

    /**
     * 删除
     */
    void deleteById(Long id);
}




