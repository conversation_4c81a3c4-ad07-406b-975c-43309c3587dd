/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-29 15:50:27
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-20 11:00:38
 * @FilePath: \shandong-ecology-web\src\utils\hexToRgb.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
function hexToRgb(hexValue, opc) {
  var rgx = /^#?([a-f\d])([a-f\d])([a-f\d])$/i
  var hex = hexValue.replace(rgx, function (m, r, g, b) {
    return r + r + g + g + b + b
  })
  var rgb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  if (!rgb) {
    return hexValue
  }
  var r = parseInt(rgb[1], 16)
  var g = parseInt(rgb[2], 16)
  var b = parseInt(rgb[3], 16)
  return [r, g, b, opc]
}

export function hexToRgbs(
  hexValues,
  quantity = [0, 28],
  opacity,
  renderType,
  invalidValue
) {
  let result = []
  let noData = 0
  if (['NaN', ''].includes(invalidValue)) {
    noData = ''
  } else {
    noData = invalidValue
  }
  //拉伸
  if (['ramp'].includes(renderType)) {
    for (let i = 0; i < hexValues.length; i++) {
      result.push(Number(quantity[i]))
      result.push(hexToRgb(hexValues[i], opacity[i]))
    }
    result = [
      'case',
      ['==', ['band', 2], Number(noData)],
      [0, 0, 0, 0],
      ['interpolate', ['linear'], ['band', 1]].concat(result)
    ]
    return result
  }
  //色块
  if (['intervals'].includes(renderType)) {
    for (let i = 0; i < hexValues.length - 1; i++) {
      if (i === 0) {
        result.push(['<', ['band', 1], quantity[0]])
        result.push(hexToRgb(hexValues[0], opacity[0]))
      } else {
        result.push(['between', ['band', 1], quantity[i - 1], quantity[i]])
        result.push(hexToRgb(hexValues[i], opacity[i]))
      }
    }
    result = [
      'case',
      ['==', ['band', 2], Number(noData)],
      [0, 0, 0, 0],
      ...result,
      [
        'color',
        ...hexToRgb(
          hexValues[hexValues.length - 1],
          opacity[hexValues.length - 1]
        )
      ]
    ]
    return result
  }
  //精确值
  if (['values'].includes(renderType)) {
    for (let i = 0; i < hexValues.length; i++) {
      result.push(
        ['==', ['band', 1], quantity[i]],
        [...hexToRgb(hexValues[i], opacity[i])]
      )
    }
    result = [
      'case',
      ['==', ['band', 2], Number(noData)],
      [0, 0, 0, 0],
      ...result,
      ['color', 0, 0, 0, 0]
    ]
    return result
  }
}

export function colorObj(colors) {
  const tickLength = 1 / (colors.length - 1);
  const result = {};
  colors.forEach((element, index) => {
    const key = (tickLength * index).toFixed(4);
    result[key] = element;
  });
  return result;
}