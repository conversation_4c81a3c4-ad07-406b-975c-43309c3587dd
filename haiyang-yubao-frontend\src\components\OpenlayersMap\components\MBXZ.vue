<template>
  <div class="tools-container">
    <div class="query-item">
      <div class="query-title">模板选择：</div>
      <n-select
        v-model:value="modelValue"
        class="query-info"
        :options="options"
        label-field="name"
        value-field="id"
      />
    </div>
  </div>
  <div class="query-bottom">
    <qx-button class="my-btn" @click="changeModelLayer">添加模板</qx-button>
    <qx-button class="my-btn" @click="clearModelLayer">清空</qx-button>
  </div>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { ref, onMounted } from 'vue'
import Api from 'src/requests/forecast'
import { useRoute } from 'vue-router'
const modelValue = ref('')
const options = ref(<any>[])
function getModelList(params: any) {
  Api.getTemplateByParent(params)
    .then((res: any) => {
      console.log(res, '*********')
      options.value = res
      modelValue.value = res.find((item: any) => item.status == true).id
    })
    .catch(() => {})
}
const emits = defineEmits(['changeModelLayer', 'clearModelLayer'])
function changeModelLayer() {
  emits('changeModelLayer', modelValue.value)
}
function clearModelLayer() {
  modelValue.value = ''
  emits('clearModelLayer')
}
onMounted(() => {
  const currentRoute = useRoute()
  let params: any = null
  if (currentRoute.name === 'alarmMaking') {
    const type: any = localStorage.getItem('alarmType')
    params = {
      productType: 2,
      templateType: type == 1 ? 'HLJBZZ' : 'FBCJBZZ'
    }
  } else {
    params = {
      productType: 1,
      templateType: 'YBTZZ'
    }
  }
  getModelList(params)
  console.log(currentRoute, 'currentRoute********')
})
</script>

<style lang="scss" scoped>
.tools-container {
  // width: 346px;
  height: 100px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 12px 0px;
  padding: 0px 12px;
  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 0px;
    .query-title {
      white-space: nowrap;
      width: 70px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
    .query-info {
      width: 346px;
    }
    .small-input {
      width: 63px;
      height: 32px;
      margin-left: 8px;
    }
    .my-slider {
      width: 178px;
    }
  }
}
.my-btn {
  width: 140px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}
.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
