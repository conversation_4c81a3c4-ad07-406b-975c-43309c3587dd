package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class RedisClientTest {

    @Autowired
    private RedissonClient redissonClientImpl;

    @Test
    public void testSave(){
        RMap<String,String> map =  redissonClientImpl.getMap("123");
        map.put("key1", UUID.randomUUID().toString());

        map.get("key");
    }

    @Test
    public void testGet(){
        RMap<String,String> map =  redissonClientImpl.getMap("123");

        String retStr = map.get("key1");
        Assert.assertNotNull(retStr);
        System.out.println(retStr);
    }
}
