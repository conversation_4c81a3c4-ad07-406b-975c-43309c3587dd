package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.TsunamiProductDTO;
import cn.piesat.data.making.server.entity.TsunamiProduct;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.TsunamiProductVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 海啸产品表服务接口
 *
 * <AUTHOR>
 */
public interface TsunamiProductService extends IService<TsunamiProduct> {

    /**
     * 查询分页
     */
    PageResult<TsunamiProductVO> getPage(TsunamiProductDTO dto, PageParam pageParam);

    /**
     * 查询列表
     */
    List<TsunamiProductVO> getList(TsunamiProductDTO dto);

    /**
     * 保存
     */
    void save(TsunamiProductDTO dto);

    void downloadFile(Long id, HttpServletResponse response);

    void batchDownload(TsunamiProductDTO dto, HttpServletResponse response);
}




