package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.ForecastTemplateRow;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 预报模板-行定义服务接口
 *
 * <AUTHOR>
 */
public interface ForecastTemplateRowService extends IService<ForecastTemplateRow> {

    /**
     * 查询列表
     */
    List<ForecastTemplateRow> getList(Long templateId);

    /**
     * 保存
     */
    void saveList(Long templateId, List<ForecastTemplateRow> list);
}




