package cn.piesat.data.making.server.mapper;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import jodd.util.CollectionUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StormSurgeAlarmMapper {
    StormSurgeAlarmMapper INSTANCE = Mappers.getMapper(StormSurgeAlarmMapper.class);

    @Mapping(target = "stationWarning",expression = "java(convertEntityToJson(dto.getStationWarning()))")
    StormSurgeAlarm toEntity(StormSurgeAlarmDTO dto);


    default String convertEntityToJson(List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> levelDTOs) {
        if(CollectionUtils.isEmpty(levelDTOs)) return null;
        return JsonUtil.object2Json(levelDTOs);
    }
}
