package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationMinuteSqO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站分钟数据-每分钟实时资料-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationMinuteSqOService extends IService<OceanStationMinuteSqO> {

    List<OceanStationMinuteSqO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




