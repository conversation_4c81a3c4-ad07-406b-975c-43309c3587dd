<!--
 * @Author: alex
 * @Date: 2022-02-16 10:55:13
 * @LastEditTime: 2025-03-26 10:01:26
 * @LastEditors: 樊海玲 <EMAIL>
 * @Description: App.vue
 * @FilePath: \hainan-jianzai-web\src\App.vue
-->
<template>
  <n-config-provider
    :locale="zhCN"
    :date-locale="dateZhCN"
    :theme-overrides="themeOverrides"
  >
    <n-message-provider>
      <router-view />
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { NConfigProvider, NMessageProvider, zhCN, dateZhCN } from 'naive-ui'
import { onMounted } from 'vue'
import { microServices } from 'src/config/microServices'
import microApp from '@micro-zoe/micro-app'

const themeOverrides = {
  common: {
    primaryColor: '#1C81F8'
  },
  Button: {
    // textColor: '#FFFFFF',
    hoverColor: '#1C81F8',
    borderFocus: '#1C81F8',
    borderHover: '#1C81F8',
    borderPressed: 'transparent',
    textColorHover: '#FFFFFF',
    textColorFocus: '#FFFFFF'
  },
  Select: {
    textColor: '#000000',
    peers: {
      InternalSelection: {
        textColor: '#000000',
        borderFocus: '#1C81F8',
        borderHover: '#1C81F8',
        hoverColor: '#1C81F8',
        heightMedium: '32px',
        height: '32px'
        // borderHoverOutline: '#1C81F8',
        // borderFocusOutline: '#1C81F8'
      }
    },
    hoverColor: '#1C81F8'
  },
  Input: {
    borderHover: '#1C81F8',
    borderFocus: '#1C81F8',
    borderHoverOutline: '#1C81F8',
    borderFocusOutline: '#1C81F8',
    boxShadowFocus: '0 0 0 2px #1C81F8',
    placeholderColor: '#666666',
    heightMedium: '32px'
  },
  Checkbox: {},
  Form: {
    feedbackHeightMedium: '20px'
  }
  // ...
}

function getQueryParams() {
  const params = new URLSearchParams(window.location.search)
  let queryParams: any = {}
  for (let param of params.entries()) {
    queryParams[param[0]] = param[1]
  }
  return queryParams
}

// 续签函数
function renew() {
  ssoClient.init({
    passport_baseURL: config.passportUrl,
    check_callback: (res: any) => {
      if (res.code == 401) {
        location.href = config.loginUrl
      }
    }
  })
}

onMounted(() => {
  // 添加全局变量
  microApp.setGlobalData({
    apiService: microServices,
    appPath: import.meta.env.VITE_Base_Url,
    loginUrl: config?.loginUrl,
    permissionSwitch: config?.permissionSwitch
  })
  renew()
})
</script>
