package cn.piesat.data.making.server.dto;


import java.io.Serializable;

/**
 * 台风信息DTO类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:20
 */
public class FmTyphoonBDTO implements Serializable {

    private static final long serialVersionUID = -56669815952656690L;

    public interface Save {
    }

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 台风编号
     */
    private String tfbh;
    /**
     * 台风唯一编码
     */
    private String ident;
    /**
     * 台风中文名
     */
    private String name;
    /**
     * 台风英文名
     */
    private String ename;
    /**
     * 台风状态 1运行中 0已结束
     */
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public String getIdent() {
        return ident;
    }

    public void setIdent(String ident) {
        this.ident = ident;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
