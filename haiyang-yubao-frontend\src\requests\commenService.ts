import getAxios from 'src/utils/axios'
import moment from 'moment'
import {
  IAstronomicalTideQueryResDTO,
  IAstronomicalTideStationItem,
  IAstronomicalTideStationRow
} from 'src/requests/commenService.type'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)
const CommenService = {
  getStationList(params?: any) {
    return axiosInstance<IAstronomicalTideStationItem[]>({
      url: '/station/list',
      method: 'GET',
      params
    })
  },
  /**
   * 根据时间查询天文大潮数据 (表格)
   * @param params
   */
  getAstronomicalTable(params: { yearMonth: string }) {
    const yearMonth = moment(params.yearMonth).format('YYYY-MM')
    return axiosInstance<IAstronomicalTideStationRow[]>({
      url: 'fmTideDialy/queryListByParam',
      method: 'GET',
      params: {
        ...params,
        yearMonth
      }
    }).then(res => {
      res.forEach(item => {
        if (item.tideTime) {
          item.tideDate = moment(item.tideTime).format('YYYY-MM-DD')
          item.tideOnlyTime = moment(item.tideTime).format('HH:mm')
        }
      })
      return res
    })
  },
  /**
   * 请求上一期天文大潮数据 (表格 + 文字)
   * @param params
   */
  getAstronomicalLastContent(params: { publicType: string | number }) {
    return axiosInstance<IAstronomicalTideQueryResDTO>({
      url: 'fmPublicRecord/getLastRecord',
      method: 'GET',
      params
    }).then(res => {
      if (res.list === null) {
        res.list = []
      }
      res.list?.forEach(item => {
        if (item.tideTime) {
          item.tideDate = moment(item.tideTime).format('YYYY-MM-DD')
          item.tideOnlyTime = moment(item.tideTime).format('HH:mm')
        }
      })
      return res
    })
  },
  /**
   * 保存天文大潮数据
   * @param payload
   */
  saveAstronomicalTideDataList(payload: IAstronomicalTideQueryResDTO) {
    payload.saveTime = moment().format('YYYY-MM-DD HH:mm:ss')
    Reflect.deleteProperty(payload, 'createTime')
    Reflect.deleteProperty(payload, 'updateTime')
    Reflect.deleteProperty(payload, 'reportTime')
    Reflect.deleteProperty(payload, 'submitTime')
    return axiosInstance({
      url: 'fmPublicRecord/saveOrUpdate',
      method: 'POST',
      data: payload
    })
  },
  /**
   * 提交天文大潮数据
   * @param payload
   */
  submitAstronomicalTideDataList(payload: IAstronomicalTideQueryResDTO) {
    return axiosInstance({
      url: 'fmPublicRecord/submit',
      method: 'PUT',
      data: payload
    })
  },
  downloadAstronomicalTideUrl(payload: { id: string }) {
    return `${baseUrl}/fmPublicRecord/download/${payload.id}`
  },

  /**
   * 天文大潮产品列表
   * @param payload
   */
  downloadAstronomicalTide(payload: { id: string }) {
    return axiosInstance({
      url: `fmPublicRecord/download/${payload.id}`,
      method: 'GET',
      responseType: 'blob'
    })
  }
}
export default CommenService
