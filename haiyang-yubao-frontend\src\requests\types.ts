export interface IJSONResponse<T = unknown> {
  code: string
  data: T
  success: boolean
  msg: string
}

/**
 * 查询成功
 */
export interface ISuccessResponse<T = unknown> extends IJSONResponse<T> {
  success: true
  code: '0000'
}

/**
 * 查询失败
 */
export interface IErrorResponse<T = unknown> extends IJSONResponse<T> {
  success: false
}

/**
 * 查询成功, 但无记录
 */
export interface INoRecordResponse<T = unknown> extends IJSONResponse<T> {
  success: true
  code: '0002'
}
