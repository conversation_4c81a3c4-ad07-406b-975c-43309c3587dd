package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmTideDailyDao;
import cn.piesat.data.making.server.entity.FmTideDialy;
import cn.piesat.data.making.server.mapper.FmTideDialyMapper;
import cn.piesat.data.making.server.service.FmTideDialyService;
import cn.piesat.data.making.server.vo.FmTideDialyVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.List;

@Service
@Slf4j
public class FmTideDialyServiceImpl extends ServiceImpl<FmTideDailyDao, FmTideDialy> implements FmTideDialyService {

    @Autowired
    private FmTideDailyDao fmTideDailyDaoImpl;

    @Autowired
    private FmTideDialyMapper fmTideDialyMapperImpl;

    public List<FmTideDialyVO> queryListByParam(FmTideDialyVO fmTideDialyVO) {
        String yearMonth = fmTideDialyVO.getYearMonth();

        Integer month = Integer.parseInt(yearMonth.substring(5))-1;
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH,month);
        cal.set(Calendar.DATE,cal.getActualMaximum(Calendar.DAY_OF_MONTH));

        System.out.println(cal.get(Calendar.DAY_OF_MONTH));

        List<FmTideDialy> listDto = fmTideDailyDaoImpl.queryListByParam(yearMonth+"-01 00:00:00",yearMonth+"-"+cal.get(Calendar.DAY_OF_MONTH)+" 23:59:59");
        return fmTideDialyMapperImpl.entityListToVoList(listDto);
    }
}
