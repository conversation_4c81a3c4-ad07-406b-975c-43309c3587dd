package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmSchedulingMainTableDTO;
import cn.piesat.data.making.server.entity.FmSchedulingMainTable;
import cn.piesat.data.making.server.vo.FmSchedulingMainTableVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:04
 */
@Mapper(componentModel = "spring")
public interface FmSchedulingMainTableMapper {

    FmSchedulingMainTableMapper INSTANCE = Mappers.getMapper(FmSchedulingMainTableMapper.class);

    /**
     * entity-->vo
     */
    FmSchedulingMainTableVO entityToVo(FmSchedulingMainTable entity);

    /**
     * dto-->entity
     */
    FmSchedulingMainTable dtoToEntity(FmSchedulingMainTableDTO dto);

    /**
     * entityList-->voList
     */
    List<FmSchedulingMainTableVO> entityListToVoList(List<FmSchedulingMainTable> list);

    /**
     * dtoList-->entityList
     */
    List<FmSchedulingMainTable> dtoListToEntityList(List<FmSchedulingMainTableDTO> dtoList);
}
