import { computed, ref } from 'vue'

interface IAlarmPeriodItem {
  id: string
  content: string
}

const periods = [
  { id: '1', content: '预计今天中午到明天中午' },
  { id: '2', content: '预计今天夜间到明天白天' },
  { id: '3', content: '预计今天白天到夜间' },
  { id: '4', content: '预计今天白天' },
  { id: '5', content: '预计今天夜间' }
]

export function useAlarmPeriod() {
  const currentAlarmPeriodID = ref<string | null>(periods[0].id)
  const cptCurrentAlarmPeriodObj = computed(() => {
    return periods.find(period => period.id === currentAlarmPeriodID.value)
  })

  return {
    alarmPeriods: periods,
    currentAlarmPeriodID,
    cptCurrentAlarmPeriodObj
  }
}
