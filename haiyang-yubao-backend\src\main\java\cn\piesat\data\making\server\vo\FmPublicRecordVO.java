package cn.piesat.data.making.server.vo;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class FmPublicRecordVO implements Serializable {

    private Long id;

    private Long taskId;

    private Long templateId;

    private String templateCode;

    private String publicType;

    private Date reportTime;

    private List<FmPublicRecordDataVO> list;

    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    private Date updateTime;

    private String contentF;

    private String contentS;

    private String contentT;

    private Integer status;

    private Date saveTime;

    private Date submitTime;

    private Long signUserId;

    private String signUserName;

    private Long signMakerId;

    private String signMakerName;

    private String msgNo;

    private Date msgTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getPublicType() {
        return publicType;
    }

    public void setPublicType(String publicType) {
        this.publicType = publicType;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getContentF() {
        return contentF;
    }

    public void setContentF(String contentF) {
        this.contentF = contentF;
    }

    public String getContentS() {
        return contentS;
    }

    public void setContentS(String contentS) {
        this.contentS = contentS;
    }

    public String getContentT() {
        return contentT;
    }

    public void setContentT(String contentT) {
        this.contentT = contentT;
    }

    public List<FmPublicRecordDataVO> getList() {
        return list;
    }

    public void setList(List<FmPublicRecordDataVO> list) {
        this.list = list;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getSaveTime() {
        return saveTime;
    }

    public void setSaveTime(Date saveTime) {
        this.saveTime = saveTime;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Long getSignUserId() {
        return signUserId;
    }

    public void setSignUserId(Long signUserId) {
        this.signUserId = signUserId;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public Long getSignMakerId() {
        return signMakerId;
    }

    public void setSignMakerId(Long signMakerId) {
        this.signMakerId = signMakerId;
    }

    public String getSignMakerName() {
        return signMakerName;
    }

    public void setSignMakerName(String signMakerName) {
        this.signMakerName = signMakerName;
    }

    public String getMsgNo() {
        return msgNo;
    }

    public void setMsgNo(String msgNo) {
        this.msgNo = msgNo;
    }

    public Date getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(Date msgTime) {
        this.msgTime = msgTime;
    }
}
