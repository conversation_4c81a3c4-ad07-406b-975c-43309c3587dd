import { inject, onMounted, onUnmounted, Ref, watch } from 'vue'
import { Draw } from 'ol/interaction'
import { FlatStyleLike } from 'ol/style/flat'
import Map from 'ol/Map'
import { type IGetMapFunction } from '../../hooks/types'
import { roiStyle } from '../../hooks/useVector'
import { geometryFunction } from 'src/utils/smoothPolygon'
import { DrawEvent, GeometryFunction } from 'ol/interaction/Draw'
import GeoJSON from 'ol/format/GeoJSON'
import Tool<PERSON><PERSON> from 'src/requests/toolRequest'
import Feature from 'ol/Feature'
import Point from 'ol/geom/Point'
import { Fill, Stroke, Text } from 'ol/style'
import Style from 'ol/style/Style'
import VectorSource from 'ol/source/Vector'
import VectorLayer from 'ol/layer/Vector'
import changeStore from 'src/components/OpenlayersMap/components/changeStore'
import { useROIInject } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIInject'
import { IROIOptions } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIOptions'
import { Polygon } from 'ol/geom'

let drawROI: Draw | undefined

/**
 * 绘制 hook
 */
export function useDrawROI() {
  const getMapInject = inject<IGetMapFunction>('getMap')
  const roiOptions = useROIInject('roiOptions').injectROI()
  const vectorSource = useROIInject('vectorSource').injectROI()
  const intersectPointLayer = useROIInject('intersectPointLayer').injectROI()
  if (!getMapInject || !roiOptions || !vectorSource || !intersectPointLayer) {
    throw new Error('注入失败')
  }
  onMounted(() => {
    getMapInject(map => {
      restartDraw({ map, roiOptions, vectorSource, intersectPointLayer })
    })
  })
  onUnmounted(() => {
    drawROI?.setActive(false)
    getMapInject(map => {
      drawROI && map.removeInteraction(drawROI)
    })
  })
  watch(
    [() => roiOptions.value.strokeColor, () => roiOptions.value.fillColor],
    () => {
      getMapInject(map => {
        restartDraw({ map, roiOptions, vectorSource, intersectPointLayer })
      })
    }
  )
}

/**
 * 绘制中的要素样式
 */
export function drawStyle(opt: {
  roiOptions: Ref<IROIOptions>
}): FlatStyleLike {
  return {
    'circle-radius': 5,
    'circle-fill-color': opt.roiOptions.value.strokeColor,
    'stroke-color': 'rgba(255,0,0,0)',
    'stroke-width': 2,
    'fill-color': 'rgba(255,0,0,0.1)'
  }
}

/**
 * 绘制结束回调
 * @param opt
 */
export function onDrawEndFactory(opt: {
  map: Map
  roiOptions: Ref<IROIOptions>
  vectorSource: VectorSource
  intersectPointLayer: VectorLayer<VectorSource<Point>>
}) {
  const { vectorSource, map, roiOptions } = opt
  return (e: DrawEvent) => {
    e.feature.setStyle(roiStyle({ roiOptions }))
    e.feature.set('value', roiOptions.value.value)
    e.feature.set('fillColor', roiOptions.value.fillColor)
    e.feature.set('strokeColor', roiOptions.value.strokeColor)
    e.feature.set('borderWidth', roiOptions.value.strokeWidth)
    e.feature.set('opacity', roiOptions.value.opacity)
    e.feature.set('category', '2')
    e.feature.set('transparency', roiOptions.value.opacity / 100)
    e.feature.set('color', roiOptions.value.fillColor)
    setTimeout(() => {
      getLandPoint({
        vectorSource,
        intersectPointLayer: opt.intersectPointLayer
      })
      changeStore(map)
    }, 0)
  }
}

/**
 * 重新开始绘制工具
 * @param opt
 */
export function restartDraw(opt: {
  map: Map
  roiOptions: Ref<IROIOptions>
  vectorSource: VectorSource
  intersectPointLayer: VectorLayer<VectorSource<Point>>
}) {
  if (drawROI) {
    drawROI.setActive(false)
    opt.map.removeInteraction(drawROI)
  }
  drawROI = new Draw({
    source: opt.vectorSource,
    stopClick: true,
    type: 'Polygon',
    style: drawStyle({ roiOptions: opt.roiOptions }),
    geometryFunction: geometryFunction as unknown as GeometryFunction
  })
  drawROI.on('drawend', onDrawEndFactory({ ...opt }))
  drawROI.setActive(true)
  opt.map.addInteraction(drawROI)
}

/**
 * 追加相交点
 * @param points
 * @param opt
 */
function addLandPoint(
  points: { coordinate: number[]; value: string }[],
  opt: {
    intersectPointLayer: VectorLayer<VectorSource<Point>>
  }
) {
  // opt.intersectPointLayer.getSource()?.refresh()
  points.forEach(item => {
    const feature = new Feature(new Point(item.coordinate))
    const iconStyle = new Style({
      text: new Text({
        font: '16px Calibri,sans-serif',
        text: item.value || 'null',
        fill: new Fill({
          color: '#000'
        }),
        stroke: new Stroke({
          color: '#fff',
          width: 3
        })
      })
    })
    feature.setStyle(iconStyle)
    opt.intersectPointLayer.getSource()?.addFeature(feature)
  })
}

/**
 * 获取相交点
 * @param opt
 */
export function getLandPoint(opt: {
  vectorSource: VectorSource
  intersectPointLayer: VectorLayer<VectorSource<Point>>
}) {
  opt.intersectPointLayer.getSource()?.clear()
  opt.intersectPointLayer.getSource()?.refresh()
  const features: Feature[] = []
  opt.vectorSource.forEachFeature(item => {
    features.push(item)
    item.setProperties({
      properties: { value: item.get('value') }
    })
  })

  features.forEach(feature => {
    const geojsonFormat = new GeoJSON()
    const geometry = feature.getGeometry()
    if (geometry instanceof Polygon) {
      const coordinates = geometry.getCoordinates()
      const [x1, y1] = coordinates[0][0]
      const [x2, y2] = coordinates[0][coordinates[0].length - 1]
      if (x1 !== x2 && y1 !== y2) {
        coordinates[0].push([x1, y1])
      }
      geometry.setCoordinates(coordinates)

      const geojsonObject = geojsonFormat.writeFeatures([feature])
      const graphicJson = JSON.stringify(geojsonObject)
      const formData = new FormData()
      formData.append('geoJsonString', graphicJson)
      ToolApi.getLandPoint(formData)
        .then(_res => {
          const res = _res as unknown as {
            coordinate: number[]
            value: string
          }[]
          if (res.length === 0) {
            res.push({ coordinate: [x1, y1], value: feature.get('value') })
          }
          addLandPoint(res, {
            ...opt
          })
        })
        .catch(err => {
          console.warn(err)
        })
    } else {
      throw new Error('非面要素暂未实现')
    }
  })
}
