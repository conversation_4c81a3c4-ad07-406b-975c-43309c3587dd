package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.entity.FmZfxDict;
import cn.piesat.data.making.server.service.FmZfxDictService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典控制层
 *
 * <AUTHOR>
 * @date 2024-11-08 16:14:14
 */
@RestController
@RequestMapping("fmZfxDict")
public class FmZfxDictController {

    @Resource
    private FmZfxDictService fmZfxDictService;


    /**
     * 查询列表
     *
     * @param type
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "考试系统", moduleName = "管理", operateType = OperateType.SELECT)
    public List<FmZfxDict> getList(@RequestParam(required = false) String type) {
        FmZfxDict dto = new FmZfxDict();
        dto.setType(type);
        return fmZfxDictService.getList(dto);
    }

}
