package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmIsolineDataDTO;
import cn.piesat.data.making.server.entity.FmIsolineData;
import cn.piesat.data.making.server.vo.FmIsolineDataVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:30
 */
public interface FmIsolineDataService extends IService<FmIsolineData> {


    /**
     * 根据参数查询列表
     */
    List<FmIsolineDataVO> getList(FmIsolineDataDTO dto);

}
