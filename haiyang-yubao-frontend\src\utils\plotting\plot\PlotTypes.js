/**
 * @enum
 */
const PlotTypes = {
  MARKER: 'marker',
  TEXT: 'text',
  LINE_MARKER: 'line_marker',
  POLYLINE: 'polyline',
  POLYGON: 'polygon',
  CIRCLE: 'circle',
  ELLIPSE: 'ellipse',
  RECTANGLE: 'rectangle',
  ARC: 'arc',
  ATTACK_ARROW: 'attack_arrow',
  CLOSED_CURVE: 'closed_curve',
  CURVE: 'curve',
  DOUBLE_ARROW: 'double_arrow',
  FINE_ARROW: 'fine_arrow',
  ASSAULT_DIRECTION: 'assault_direction',
  FREEHAND_LINE: 'freehand_line',
  FREEHAND_POLYGON: 'freehand_polygon',
  GATHERING_PLACE: 'gathering_place',
  LUNE: 'lune',
  SECTOR: 'sector',
  SQUAD_COMBAT: 'squad_combat',
  STRAIGHT_ARROW: 'straight_arrow',
  TAILED_ATTACK_ARROW: 'tailed_attack_arrow',
  TAILED_SQUAD_COMBAT: 'tailed_squad_combat',
  // 气象符号
  METE_WARM_FRONT: 'mete_warm_front', // 暖前锋
  METE_COLD_FRONT: 'mete_cold_front', // 冷前锋
  METE_OCCLUDED_FRONT: 'mete_occluded_front', // 锢囚锋
  METE_STATIONARY_FRONT: 'mete_stationary_front', // 静止锋
  METE_TROUGH_LINE: 'mete_trough_line', // 槽线
  METE_RIDGE_LINE: 'mete_ridge_line', // 脊线
  METE_WARM_ADVECTION: 'mete_warm_advection', // 暖气流
  METE_COLD_ADVECTION: 'mete_cold_advection', // 冷气流
  METE_WINDBARB: 'mete_windbarb', // 风羽
  METE_HIGH_PRESSURE: 'mete_high_pressure', // 高气压
  METE_LOW_PRESSURE: 'mete_low_pressure', // 低气压
  METE_WARM_CENTER: 'mete_warm_center', // 暖心
  METE_COLD_CENTER: 'mete_cold_center', // 冷心
  METE_RISING: 'mete_rising', // 升温
  METE_COOLING: 'mete_cooling', // 降温
  METE_TYPHOON: 'mete_typhon', // 台风
  // 新加标记点
  METE_TYPHOON_POINT: 'mete_typhoon_point', // 台风点
  METE_WIND_BARB: 'mete_wind_barb', // 风羽
  METE_POINT: 'mete_point', // 位置点
  METE_HIGH_PRESSURE_POINT: 'mete_high_pressure_point', // 高压位置
  METE_LOW_PRESSURE_POINT: 'mete_low_pressure_point', // 低压位置
  METE_HIGH_PRESSURE1: 'mete_high_pressure1', // 高压
  METE_LOW_PRESSURE1: 'mete_low_pressure1', // 低压
  METE_TYPHOON_PRESSURE: 'mete_typhoon_pressure', // 台风气压
  METE_WAVE_DIRECTION: 'mete_wave_direction' //主波向
}
export default PlotTypes
