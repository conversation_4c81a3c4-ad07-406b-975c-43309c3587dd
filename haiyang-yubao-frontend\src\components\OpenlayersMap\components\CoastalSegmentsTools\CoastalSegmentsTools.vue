<template>
  <div class="xqgj">
    <div class="title">
      <span>目标选择</span>
      <n-popover ref="tipPopover" trigger="click">
        <template #trigger>
          <n-icon size="14">
            <InformationCircle></InformationCircle>
          </n-icon>
        </template>
        <div>目标选择-点选：按住ctrl进行多个近岸基础单元选中操作。</div>
      </n-popover>
    </div>
    <div class="query-item">
      <div class="query-title">填充颜色：</div>
      <div class="color-picker">
        <i
          v-for="item in colors"
          :key="item.level"
          class="level-icon"
          :class="[
            item.className,
            item.level === segmentOptions.colorItem.level ? 'active' : ''
          ]"
          @click="segmentOptions.colorItem = item"
        ></i>
        <!-- <n-color-picker
          v-model:value="fillColor"
          :actions="['confirm']"
          class="color-item"
          :swatches="[
            '#F90102',
            '#FE7B0E',
            '#FFF300',
            '#0083FD',
            'rgba(0, 0, 0, 0)',
            '#FFFFFF'
          ]"
          @update:value="changeColor"
        >
        </n-color-picker> -->
      </div>
    </div>
    <div class="btns">
      <div
        class="my-btn"
        :class="[isToolSelected(Tool.SELECT) ? 'active' : '']"
        @click="changeTool(Tool.SELECT)"
      >
        {{ isToolSelected(Tool.SELECT) ? '确认选区' : '点选' }}
      </div>
      <div class="my-btn" @click="clearSelectStyle">清除</div>
    </div>
  </div>
  <div class="xqgj">
    <div class="title">
      <span>文本标注</span>
    </div>
    <div class="btns">
      <div
        class="my-btn"
        :class="[isToolSelected(Tool.LABEL) ? 'active' : '']"
        @click="segmentOptions.currentTool = Tool.LABEL"
      >
        点选
      </div>
      <div class="my-btn" @click="clearLabel">清除</div>
    </div>
  </div>
  <!--  <div class="xqgj">-->
  <!--    <div class="title">-->
  <!--      <span>提示</span>-->
  <!--    </div>-->
  <!--    <div class="text">-->
  <!--      目标选择-点选：按住ctrl进行多个近岸基础单元选中操作。-->
  <!--    </div>-->
  <!--  </div>-->
  <n-modal
    v-model:show="showDialog"
    :close-on-esc="false"
    :mask-closable="false"
  >
    <n-card
      style="width: 300px"
      title="请输入标注内容"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div class="text-container">
        <n-input v-model:value="formInDialog.text" placeholder="请输入" />
        <span>厘米</span>
      </div>
      <template #footer>
        <qx-button @click="showDialog = false">取消</qx-button>
        <qx-button @click="onConfirm">确定</qx-button>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { NPopover } from 'naive-ui'
import { QxButton } from 'src/components/QxButton/index'
import { InformationCircle } from '@vicons/ionicons5'
import { Style } from 'ol/style'
import { useColor } from './hooks/useColor'
import { useSelectVector } from './hooks/useSelectVector'
import { useSegment } from './hooks/useSegment'
import { Tool } from 'src/components/OpenlayersMap/components/CoastalSegmentsTools/types'
import { useLabel } from 'src/components/OpenlayersMap/components/CoastalSegmentsTools/hooks/useLabel'
import Popup from 'src/utils/olPopup/ol-popup'
import { useLabelV2 } from 'src/components/OpenlayersMap/components/CoastalSegmentsTools/hooks/useLabelV2'

const { colors } = useColor()
const { segmentOptions, changeTool, isToolSelected } = useSegment({
  colors
})
const { vectorSource, vectorLayer, clearSelectStyle } = useSelectVector({
  segmentOptions
})
// const { showDialog, formInDialog, onConfirm, clearLabel } = useLabel({
//   popupFactory: () => new Popup(),
//   segmentOptions,
//   vectorLayer,
//   vectorSource
// })
const { showDialog, formInDialog, onConfirm, clearLabel } = useLabelV2({
  segmentOptions,
  vectorLayer,
  vectorSource
})

</script>

<style lang="scss" scoped>
.xqgj {
  // width: 346px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 5px 0px;

  .title {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    margin: 7px 16px;
    position: relative;
    display: flex;
    align-items: center;

    span {
      &::before {
        content: '';
        position: absolute;
        left: -10px;
        top: 4px;
        width: 4px;
        height: 8px;
        background: #567bff;
      }
    }
  }

  .btns {
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 10px;

    .my-btn {
      width: 150px;
      height: 32px;
      background: #1c81f8;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      cursor: default;

      &.active {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }

      &:hover {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
    }
  }

  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 16px;

    .query-title {
      white-space: nowrap;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }

    .query-info {
      width: 346px;
    }

    .color-picker {
      display: flex;

      .color-item {
        width: 30px;
        height: 30px;
        margin-right: 5px;
      }
    }
  }

  .text {
    margin-left: 16px;
    margin-bottom: 10px;
  }

  .level-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
    display: inline-block;

    &:nth-last-child(1) {
      margin-right: 0;
    }

    &.active {
      border: 2px solid #000000;
    }
  }
}

.text-container {
  display: flex;
  align-items: center;

  span {
    white-space: nowrap;
    margin-left: 10px;
  }
}

.red {
  background: #f90102;
}

.orange {
  background: #fe7b0e;
}

.yellow {
  background: #fff300;
}

.blue {
  background: #0083fd;
}

.grey {
  background: #c4c4c4;
}
</style>
