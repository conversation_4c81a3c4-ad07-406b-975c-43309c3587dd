.ol-popup {
  display: none;
  position: absolute;
  background-color: white;
  padding: 0!important;
  border: 1px solid #cccccc;
  bottom: 12px;
  left: -50px;
}

.ol-popup:after,
.ol-popup:before {
  top: 100%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.ol-popup:after {
  border-top-color: white;
  border-width: 10px;
  left: 48px;
  margin-left: -10px;
}

.ol-popup:before {
  border-top-color: #cccccc;
  border-width: 11px;
  left: 48px;
  margin-left: -11px;
}

.ol-popup-content {
  margin-top: 18px;
  min-height: 40px;
  overflow-x: auto;
  min-width: 200px;
  padding: 0 20px 20px;
}

.ol-popup-closer {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 150%;
  padding: 0 4px;
  color: gray;
  text-decoration: none;
}

.ol-popup-closer:after {
  content: '\2716';
}

.ol-popup div.infoResult {
  min-width: 130px;
}
.title-popup{
  line-height: 22px;
}

.ol-popup div.infoResult p {
  padding: 0.1em;
  margin: 0;
}

.ol-popup-content h3 {
  margin: 0.25em 0;
}

.ol-popup.marker {
  margin-bottom: 30px;
}
