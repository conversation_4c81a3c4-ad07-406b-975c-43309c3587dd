{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "strictNullChecks": true, "noImplicitAny": true, "noUnusedLocals": false, "jsx": "preserve", "jsxImportSource": "vue", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "lib": ["esnext", "dom"], "types": ["node", "naive-ui/volar"], "baseUrl": "./", "paths": {"src/*": ["src/*"]}, "allowJs": true, "outDir": "dist"}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "vite.config.ts", "src/views/*.js"], "exclude": ["node_modules"]}