package cn.piesat.data.making.server.enums;

import cn.piesat.webconfig.exception.BusinessException;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

public enum TimePeriodEnum {
    MORNING("早晨", LocalTime.of(5, 0), LocalTime.of(8, 0)),
    NOON("上午", LocalTime.of(8, 0), LocalTime.of(11, 0)),
    LUNCH("中午", LocalTime.of(11, 0), LocalTime.of(14, 0)),
    AFTERNOON("下午", LocalTime.of(14, 0), LocalTime.of(17, 0)),
    EVENING("傍晚", LocalTime.of(17, 0), LocalTime.of(20, 0)),
    BEFORE_NIGHT("上半夜", LocalTime.of(20, 0), LocalTime.of(23, 0)),
    NIGHT("半夜", LocalTime.of(23, 0), LocalTime.of(2, 0)),
    LATE_NIGHT("下半夜", LocalTime.of(2, 0), LocalTime.of(5, 0));

    private final String name;
    private final LocalTime start;
    private final LocalTime end;

    TimePeriodEnum(String name, LocalTime start, LocalTime end) {
        this.name = name;
        this.start = start;
        this.end = end;
    }

    public String getName() {
        return name;
    }

    public boolean isInPeriod(LocalTime time) {
        // 确保 5:00 属于早晨
        return !time.isBefore(start) && time.isBefore(end);
    }

    public static String getTimePeriod(Date date) {
        // 将 Date 转换为 LocalTime
        LocalTime time = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalTime();

        for (TimePeriodEnum period : TimePeriodEnum.values()) {
            if (period.isInPeriod(time)) {
                return period.getName();
            }
        }
        throw new BusinessException("时间异常");
    }
}
