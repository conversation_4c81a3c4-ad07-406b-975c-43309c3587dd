package cn.piesat.data.making.server.controller;

import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.data.making.server.entity.FmForecastProductData;
import cn.piesat.data.making.server.service.FmForecastProductDataService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 极值数据控制层
 *
 * <AUTHOR>
 * @date 2024-09-27 13:53:59
 */
@RestController
@RequestMapping("fmForecastProductData")
public class FmForecastProductDataController {

    @Resource
    private FmForecastProductDataService fmForecastProductDataService;

    /**
     * 查询分页
     *
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    public void getPage() throws Exception {
        fmForecastProductDataService.saveResult("/Users/<USER>/fsdownload/Hs_L3M_2024090112Nc_area-1727145759074.json");
    }

    /**
     * 查询分页
     *
     * @param forecastStartTime 预报时间
     * @param areaId            区域id
     * @param name              区域名称
     * @param type              区域类型
     * @param elementCode       要素编码
     * @return
     */
    @GetMapping("/list")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    public List<FmForecastProductData> getList(@RequestParam(required = false) Date forecastStartTime,
                                               @RequestParam(required = false) String areaId,
                                               @RequestParam(required = false) String name,
                                               @RequestParam(required = false) String type,
                                               @RequestParam(required = false) String elementCode) throws Exception {
        FmForecastProductData fmForecastProductData = new FmForecastProductData();
//        fmForecastProductData.setForecastStartTime(forecastStartTime);
//        fmForecastProductData.setAreaId(areaId);
        fmForecastProductData.setName(name);
        fmForecastProductData.setType(type);
        fmForecastProductData.setElementCode(elementCode);
        return fmForecastProductDataService.getList(fmForecastProductData);
    }

    /**
     * 获取海浪警报起报时间列表
     *
     * @return
     */
    @GetMapping("/waveStartTimeList")
    public List<Date> waveStartTimeList(@RequestParam(defaultValue = "forecast", required = false) String dataSource,
                                        @RequestParam(defaultValue = "regWave", required = false) String productCode,
                                        @RequestParam(defaultValue = "3", required = false) Integer num) {
        return fmForecastProductDataService.waveStartTimeList(dataSource, productCode, num);
    }

    /**
     * 获取最近一次预报起报时间
     *
     * @param dataSource 数据源：数值预报forecast 智能网格grid
     * @return
     */
    @GetMapping("/lastForecastStartTime")
    public Date getLastForecastStartTime(@RequestParam("dataSource") String dataSource) {
        return fmForecastProductDataService.getLastForecastStartTime(dataSource);
    }
}
