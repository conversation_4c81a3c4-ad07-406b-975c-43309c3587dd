package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmPublicRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicRecordDaoTest {

    @Autowired
    private FmPublicRecordDao fmPublicRecordDaoImpl;

    @Test
    public void testSave(){
        FmPublicRecord fmPublicRecord = new FmPublicRecord();
        fmPublicRecord.setTaskId(123L);
        fmPublicRecordDaoImpl.insert(fmPublicRecord);
    }
}
