import VectorSource from 'ol/source/Vector'
import { inject, provide } from 'vue'
import VectorLayer from 'ol/layer/Vector'
import { SimpleGeometry } from 'ol/geom'
import Map from 'ol/Map'

interface IGetMapFunction {
  (cb: (map: Map) => void): void
}

const injectNames = {
  getMap(): IGetMapFunction | undefined {
    return void 0
  },
  vectorSource(): VectorSource<SimpleGeometry> | undefined {
    return void 0
  },
  vectorLayer(): VectorLayer<VectorSource<SimpleGeometry>> | undefined {
    return void 0
  }
}

/**
 * 根据 name 推导 provider/inject 类型
 */
type SegmentInjectType<T extends keyof typeof injectNames> = ReturnType<
  (typeof injectNames)[T]
>

export function useSegmentInject<T extends keyof typeof injectNames>(name: T) {
  return {
    provide(value: SegmentInjectType<T>) {
      provide<SegmentInjectType<T>>(name, value)
    },
    inject() {
      return inject<SegmentInjectType<T>>(name)
    }
  }
}
