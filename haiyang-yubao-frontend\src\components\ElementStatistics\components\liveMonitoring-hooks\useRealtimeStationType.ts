import { ref } from 'vue'
import {
  EBuoyStationType,
  EOceanStationType
} from './useRealtimeStationType.type'
import Api from 'src/requests/toolRequest'
import { IRealtimeStationItem } from 'src/requests/toolRequest.type'


/**
 * 观测设备/站类型 hook
 */
export function useRealtimeStationType() {
  const oceanSelected = ref<EOceanStationType[]>(defaultOceanStationType())
  const buoySelected = ref<EBuoyStationType[]>(defaultSelectedBuoyStationType())
  const isStationDataLoading = ref(false)
  // 站点列表
  const tableData = ref<IRealtimeStationItem[]>([])

  /**
   * 海洋站类型点击
   * @param val
   */
  async function oceanStationTypeClicked(val: EOceanStationType) {
    if (oceanSelected.value.includes(val)) {
      oceanSelected.value.splice(oceanSelected.value.indexOf(val), 1)
    } else {
      oceanSelected.value.push(val)
    }
    void (await loadOceanStationData())
  }

  async function loadOceanStationData() {
    return loadStationData('oceanStation')
  }

  /**
   * 浮标站类型点击
   * @param val
   */
  async function buoyStationTypeClicked(val: EBuoyStationType) {
    if (buoySelected.value.includes(val)) {
      buoySelected.value.splice(buoySelected.value.indexOf(val), 1)
    } else {
      buoySelected.value.push(val)
    }
    void (await loadBuoyStationData())
  }

  /**
   * 加载站数据
   * @param stationCode
   */
  async function loadStationData(stationCode: 'oceanStation' | 'buoyStation') {
    isStationDataLoading.value = true
    const arr = stationCode === 'oceanStation' ? oceanSelected.value : buoySelected.value
    try {
      const res = await Api.getOceanStationPageV2({
        pageNum: 1,
        pageSize: 1000,
        stationTypeCode: stationCode,
        type: arr
      })
      tableData.value = res
    } catch (e) {
      console.warn(e)
      tableData.value = []
    } finally {
      isStationDataLoading.value = false
    }
  }
  /**
   * 请求浮标站数据
   */
  async function loadBuoyStationData() {
    return loadStationData('buoyStation')
  }

  return {
    buoySelected,
    buoyStationTypeClicked,
    loadBuoyStationData,
    tableData,
    isStationDataLoading,
    oceanSelected,
    oceanStationTypeClicked,
    loadOceanStationData
  }
}

/**
 * 默认选中的浮标站类型
 */
function defaultSelectedBuoyStationType(): EBuoyStationType[] {
  return [
    EBuoyStationType.DISTANCE3M,
    EBuoyStationType.DISTANCE10M,
    EBuoyStationType.WAVE_SPECTRUM
  ]
}

function defaultOceanStationType(): EOceanStationType[] {
  return [
    EOceanStationType.SHENG,
    EOceanStationType.SHI,
    EOceanStationType.CHAO
  ]
}
