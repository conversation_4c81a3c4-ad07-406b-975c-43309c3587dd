<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-25 17:18:47
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-25 17:57:07
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/analystReports/copyTxt.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
-->
<template>
  <div class="copy-txt-container" v-show="visible">
    <div class="copy-txt-header">
      <div class="title">{{ title }}</div>
      <Close @click="onClose" class="icon-close" />
    </div>
    <div class="copy-txt-content">
      <div
        class="copy-txt-item d-flex flex-justify-between flex-align-center"
        v-for="(item, index) in txtList"
        :key="index"
      >
        <div class="copy-txt-item__label">{{ item }}</div>
        <div class="copy-txt-item__btn" @click="onCopy(item)">
          <i class="icon icon-copy"></i>
          复制文本
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, PropType } from 'vue'
import { Close } from '@vicons/ionicons5'
import useClipboard from 'vue-clipboard3'
import {useMessage} from 'naive-ui'

const message = useMessage()
const { toClipboard } = useClipboard()
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'copy', content: string): void
}>()

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  txtList: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  visible: {
    type: Boolean,
    default: false
  }
})
let isShow = ref(false)

watch(
  () => props.visible,
  val => {
    isShow.value = val
  }
)

function onClose() {
  emit('close')
}

const onCopy = async (txt: string) => {
  try {
    await toClipboard(txt)
    emit('copy', txt)
    message.success('复制成功')
    emit('close')
  } catch (e) {
    console.error(e)
    message.error('复制失败')
  }
}
</script>
<style lang="scss" scoped>
.copy-txt-container {
  position: absolute;
  width: 478px;
  z-index: 1;
  right: 30px;
  top: calc(100% + 5px);
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.3);
  background: #fff;
  .copy-txt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 13px 20px 19px 34px;
    background: url(src/assets/images/common/dialog-header-bg1.png) no-repeat;
    background-size: 100% 100%;
    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #104cc0;
      line-height: 19px;
      position: relative;
      &::before {
        content: '';
        display: inline-block;
        width: 15.4px;
        height: 10.4px;
        background: url(src/assets/images/common/dialog-header-bg-icon.png)
          no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: -23px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .icon-close {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
  .icon-copy {
    width: 18px;
    height: 18px;
    background: url(src/assets/images/icons/icon-copy.png) no-repeat;
    background-size: 100% 100%;
  }
  .copy-txt-content {
    box-sizing: border-box;
    padding: 20px;
  }

  .copy-txt-item {
    margin-bottom: 10px;
  }

  .copy-txt-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    width:80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .copy-txt-item__btn {
    background: #1972f8;
    border-radius: 4px 4px 4px 4px;
    height: 24px;
    box-sizing: border-box;
    padding: 0 10px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    display: flex;
    align-items: center;
  }
}
</style>
