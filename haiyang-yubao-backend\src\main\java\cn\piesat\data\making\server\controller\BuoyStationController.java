package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.BuoyStationDataDTO;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.BuoyStationService;
import cn.piesat.data.making.server.vo.BuoyStationDataVO;
import cn.piesat.data.making.server.vo.FillMapVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 浮标站表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/buoyStation")
public class BuoyStationController {

    @Resource
    private BuoyStationService buoyStationService;

    /**
     * 查询中台浮标站列表 数据维护新增站点时使用
     *
     * @param name 浮标站名称
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "浮标站管理", operateType = OperateType.SELECT)
    public List<StationInfo> getList(@RequestParam(required = false) String name) {
        return buoyStationService.getList(name);
    }

    /**
     * 根据时间范围、站点查询观测结果
     *
     * @param dto
     * @return
     */
    @PostMapping("/dataList")
    @SysLog(systemName = "预报制作系统", moduleName = "浮标站管理", operateType = OperateType.SELECT)
    public List<BuoyStationDataVO> getDataList(@Validated(value = {BuoyStationDataDTO.Query.class}) @RequestBody BuoyStationDataDTO dto) {
        return buoyStationService.getDataList(dto);
    }

    /**
     * 根据时间范围、空间范围查询观测结果
     *
     * @param dto
     * @return
     */
    @PostMapping("/geoRangeDataList")
    @SysLog(systemName = "预报制作系统", moduleName = "浮标站管理", operateType = OperateType.SELECT)
    public FillMapVO getGeoRangeDataList(@Validated(value = {BuoyStationDataDTO.RangeQuery.class}) @RequestBody BuoyStationDataDTO dto) {
        return buoyStationService.getGeoRangeDataList(dto);
    }
}

