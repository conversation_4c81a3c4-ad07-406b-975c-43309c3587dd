package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.common.JsonManipulation;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.StormSurgeAlarmDao;
import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import cn.piesat.data.making.server.dto.generate.StormSurgeAlarmWordDTO;
import cn.piesat.data.making.server.entity.AlarmLevel;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import cn.piesat.data.making.server.enums.ProductEnum;
import cn.piesat.data.making.server.enums.WarningMessageStatus;
import cn.piesat.data.making.server.mapper.StormSurgeAlarmMapper;
import cn.piesat.data.making.server.mapper.StormSurgeAlarmWordMapper;
import cn.piesat.data.making.server.model.AlarmInfo;
import cn.piesat.data.making.server.processor.StormSurgeGenerateText;
import cn.piesat.data.making.server.processor.StormSurgeGenerateTextProcessor;
import cn.piesat.data.making.server.service.AlarmLevelService;
import cn.piesat.data.making.server.service.StormSurgeAlarmService;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.utils.GenerateFileUtil;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import cn.piesat.security.ucenter.starter.utils.UserUtils;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 风暴潮警报制作表信息(StormSurgeAlarmB)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:34
 */
@Service("stormSurgeAlarmBService")
public class StormSurgeAlarmServiceImpl extends ServiceImpl<StormSurgeAlarmDao, StormSurgeAlarm> implements StormSurgeAlarmService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Value("${piesat.make.fax}")
    private String fax;
    @Value("${piesat.make.surges-alarm-template}")
    private String surgesAlarmTemplate;
    @Autowired
    private AlarmLevelService alarmLevelService;
    @Autowired
    private StormSurgeGenerateTextProcessor stormSurgeGenerateTextProcessor;
    @Autowired
    private GenerateProductServiceImpl generateProductService;
    @Autowired
    private KafkaTemplate kafkaTemplate;

    @Override
    public PageResult pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime) {
        LambdaQueryWrapper<StormSurgeAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(StormSurgeAlarm::getCreateTime);
        wrapper.eq(StormSurgeAlarm::getDisplay,0);
        if (startTime != null) {
            wrapper.ge(StormSurgeAlarm::getReleaseTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(StormSurgeAlarm::getReleaseTime, endTime);
        }
        Page<StormSurgeAlarm> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }

    @Override
    public Boolean checkNumber(String number) {
        StormSurgeAlarm stormSurgeAlarm = this.selectByNumber(number);
        if(Objects.nonNull(stormSurgeAlarm)) throw new BusinessException("该编号已存在");
        return true;
    }

    @Override
    public StormSurgeAlarm selectByNumber(String number) {
        LambdaQueryWrapper<StormSurgeAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StormSurgeAlarm::getNumber,number);
        return this.getOne(wrapper, false);
    }

    @Override
    public StormSurgeAlarm saveInfo(StormSurgeAlarmDTO stormSurgeAlarmDTO) {
        this.checkNumber(stormSurgeAlarmDTO.getNumber());
        StormSurgeAlarm stormSurgeAlarm = StormSurgeAlarmMapper.INSTANCE.toEntity(stormSurgeAlarmDTO);
        stormSurgeAlarm.setStatus(WarningMessageStatus.NOT_SUBMIT.getValue());
        this.makeFile(stormSurgeAlarm,stormSurgeAlarmDTO.getStationWarning());
        if (!StringUtils.isEmpty(stormSurgeAlarm.getAlarmArea())) {
            stormSurgeAlarm.setAlarmArea(JsonManipulation.addGeoJsonProperties(stormSurgeAlarm.getAlarmArea(), "1", stormSurgeAlarm.getReleaseTime(),
                    stormSurgeAlarm.getAlarmContent()));
        }
        this.save(stormSurgeAlarm);
        if(StringUtils.hasText(stormSurgeAlarm.getLastNumber()))
            this.updateDisplay(stormSurgeAlarm.getLastNumber());
        return stormSurgeAlarm;
    }

    @Override
    public StormSurgeAlarm updateInfo(StormSurgeAlarmDTO stormSurgeAlarmDTO) {
        StormSurgeAlarm stormSurgeAlarm = Optional.ofNullable(this.getById(stormSurgeAlarmDTO.getId())).orElseThrow(() -> new BusinessException("未找到对应警报制作单"));
        if(WarningMessageStatus.SUBMIT.getValue().equals(stormSurgeAlarm.getStatus())) throw new BusinessException("已提交状态警报制作单不可修改");
        if(!stormSurgeAlarmDTO.getNumber().equals(stormSurgeAlarm.getNumber())){this.checkNumber(stormSurgeAlarmDTO.getNumber());}
        stormSurgeAlarm =  StormSurgeAlarmMapper.INSTANCE.toEntity(stormSurgeAlarmDTO);
        this.makeFile(stormSurgeAlarm,stormSurgeAlarmDTO.getStationWarning());
        if (!StringUtils.isEmpty(stormSurgeAlarm.getAlarmArea())) {
            stormSurgeAlarm.setAlarmArea(JsonManipulation.addGeoJsonProperties(stormSurgeAlarm.getAlarmArea(), "1", stormSurgeAlarm.getReleaseTime(),
                    stormSurgeAlarm.getAlarmContent()));
        }
        this.updateById(stormSurgeAlarm);
        return stormSurgeAlarm;
    }

    @Override
    public GenerateTextVO generateText(StormSurgeGenerateText stormSurgeGenerateText) {
        return stormSurgeGenerateTextProcessor.generate(stormSurgeGenerateText,true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void release(Long id) {
        StormSurgeAlarm stormSurgeAlarm = this.getById(id);
        String wordFilePath = stormSurgeAlarm.getWordFilePath();
        String htmlFilePath = stormSurgeAlarm.getHtmlFilePath();
        String smsFilePath = stormSurgeAlarm.getSmsFilePath();
        Date releaseTime = stormSurgeAlarm.getReleaseTime();

        Long pushTaskId = generateProductService.sendPushServer("风暴潮警报产品推送", wordFilePath, htmlFilePath, smsFilePath, ProductEnum.SURGES_WORD, ProductEnum.SURGES_HTML,
                ProductEnum.SURGES_SMS, releaseTime);

        stormSurgeAlarm.setStatus(WarningMessageStatus.SUBMIT.getValue());
        stormSurgeAlarm.setPushTaskId(pushTaskId);
        this.updateById(stormSurgeAlarm);

        AlarmInfo alarmInfo = new AlarmInfo();
        alarmInfo.setId(id.toString());
        alarmInfo.setCategory(1);
        //解除警报
        if (stormSurgeAlarm.getAlarmLevel() == 5) {
            //追溯历史预警记录的id
            List<Long> longList = this.traceHistoryByCode(stormSurgeAlarm.getNumber());
            String ids = longList.stream().map(String::valueOf).collect(Collectors.joining(","));
            alarmInfo.setIds(ids);
            alarmInfo.setReleaseState(0);
        } else {
            alarmInfo.setReleaseState(1);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        alarmInfo.setAlarmReleaseTime(sdf.format(stormSurgeAlarm.getReleaseTime()));
        alarmInfo.setDescriptionInformation(stormSurgeAlarm.getAlarmContent());
        logger.info("发送消息给信息中心，消息内容{}", JsonUtil.object2Json(alarmInfo));
        kafkaTemplate.send("piesat-storm-surge-alarm", JsonUtil.object2Json(alarmInfo));
        //发送xml给国突

    }

    private List<Long> traceHistoryByCode(String code) {
        List<StormSurgeAlarm> list = this.list();
        List<StormSurgeAlarm> history = new ArrayList<>();
        StormSurgeAlarm current = null;
        for (StormSurgeAlarm record : list) {
            if (record.getNumber().equals(code)) {
                current = record;
                break;
            }
        }
        while (current != null && current.getLastNumber() != null) {
            for (StormSurgeAlarm record : list) {
                if (record.getNumber().equals(current.getLastNumber())) {
                    history.add(record);
                    current = record;
                    break;
                }
            }
        }
        return history.stream().map(StormSurgeAlarm::getId).collect(Collectors.toList());
    }

    @Override
    public void updateDisplay(String lastNumber) {
        LambdaUpdateWrapper<StormSurgeAlarm> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StormSurgeAlarm::getNumber,lastNumber);
        wrapper.set(StormSurgeAlarm::getDisplay,1);
        this.update(wrapper);
    }

    @Override
    public Map<Long, Long> statistic(Date startTime, Date endTime) {
        LambdaQueryWrapper<StormSurgeAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(StormSurgeAlarm::getCreateTime);
        wrapper.eq(StormSurgeAlarm::getDisplay,0);
        if (startTime != null) {
            wrapper.ge(StormSurgeAlarm::getReleaseTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(StormSurgeAlarm::getReleaseTime, endTime);
        }
        return this.list(wrapper).stream().collect(Collectors.groupingBy(e -> e.getAlarmLevel().longValue(), Collectors.counting()));
    }

    private void makeFile(StormSurgeAlarm stormSurgeAlarm,List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> stationWarning ){

        //查询警报级别信息
        AlarmLevel alarmLevel = alarmLevelService.getById(stormSurgeAlarm.getAlarmLevel());
        //查询制作人信息
        UserInfoDTO userInfo = UserUtils.getUserInfo(stormSurgeAlarm.getMakeUser());
//        UserInfoDTO userInfo = null;

        StormSurgeAlarmWordDTO stormSurgeAlarmWordDTO = StormSurgeAlarmWordMapper.INSTANCE.toDTO(stormSurgeAlarm, alarmLevel, userInfo, fax, stationWarning);

        //处理上传图片转为数组
        stormSurgeAlarmWordDTO.setImages(GenerateFileUtil.generatePicture(stormSurgeAlarm.getAlarmImages()));

        //拼接产出文件地址
        Date releaseTime = stormSurgeAlarm.getReleaseTime();
        String wordFilePath = String.format(CommonConstant.ALERT_SURGES_WORD_FILE_NAME,baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateWord(wordFilePath,surgesAlarmTemplate, stormSurgeAlarmWordDTO,"stationWarning");
        //word转html
        String htmlFilePath = String.format(CommonConstant.ALERT_SURGES_HTML_FILE_NAME, baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime,
                "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateHtml(wordFilePath, htmlFilePath);
        //短信文件写入
        String smsFilePath = String.format(CommonConstant.ALERT_SURGES_TXT_FILE_NAME, baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateSms(smsFilePath,stormSurgeAlarm.getSmsContent());

        stormSurgeAlarm.setWordFilePath(wordFilePath);
        stormSurgeAlarm.setSmsFilePath(smsFilePath);
    }

    public void downloadDoc(HttpServletResponse response, Long id){
        //stormSurgeAlarmS stormSurgeAlarmServiceImpl = stormSurgeAlarmServiceImpl.getById(id);
        StormSurgeAlarm stormSurgeAlarm = this.getById(id);
        String filePath = stormSurgeAlarm.getWordFilePath();
        String fileName = filePath.substring(filePath.lastIndexOf("/")+1);
        FileUtil.downloadFile(filePath,fileName,response);
    }

    public boolean updateByIdObj(StormSurgeAlarm stormSurgeAlarm) {
        return this.updateById(stormSurgeAlarm);
    }
}

