import Api from 'src/requests/alarm'
import { MaybeRefOrGetter, onMounted, ref, toValue, type Ref } from 'vue'
import type { IAlarmLevelItem } from './useAlarmLevel.type'

export function useAlarmLevel() {
  const levelList = ref<IAlarmLevelItem[]>([])

  onMounted(() => {
    initLevelList(levelList)
  })

  return {
    levelList,
    getTitleByLevel(type: string, levelID: MaybeRefOrGetter<string | number>) {
      const levelIDValue = toValue(levelID)
      const find = levelList.value.find(i => i.id == levelIDValue)
      if (!find) {
        return ''
      }
      return getTitleByLevel(type, find)
    }
  }
}

/**
 * 初始化警报等级列表
 * @param levelList
 */
function initLevelList(levelList: Ref<IAlarmLevelItem[]>) {
  Api.getAlarmLevelList()
    .then((res: any) => {
      levelList.value = res
    })
    .catch(() => {})
}

function getTitleByLevel(type: string, item: IAlarmLevelItem) {
  if (item.id == 5) {
    // 警报解除
    return `${type}警报解除通报`
  }
  return `${type}${item.levelRoman}级警报(${item.levelColor})`
}
