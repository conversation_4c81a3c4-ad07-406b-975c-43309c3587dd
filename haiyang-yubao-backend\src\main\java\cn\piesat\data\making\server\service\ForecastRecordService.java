package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.ForecastRecordDTO;
import cn.piesat.data.making.server.entity.ForecastRecord;
import cn.piesat.data.making.server.vo.ForecastRecordVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * 预报记录表服务接口
 *
 * <AUTHOR>
 */
public interface ForecastRecordService extends IService<ForecastRecord> {

    /**
     * 查询列表
     */
    List<ForecastRecordVO> getList();

    /**
     * 保存
     */
    void save(ForecastRecordDTO dto);

    /**
     * 提交
     */
    void submit(ForecastRecordDTO dto);

    /**
     * 查询详情
     */
    ForecastRecordVO getInfoByTaskId(Long taskId, String dataSource, Date reportTime);

    /**
     * 查询详情
     */
    ForecastRecordVO getInfoByTaskId(Long taskId, Date reportTime);

    /**
     * 加载上一期
     */
    ForecastRecordVO loadPre(Long taskId, String dataSource, Date reportTime);

    /**
     * 重置
     */
    ForecastRecordVO reset(Long taskId, String dataSource, Date reportTime);

    /**
     * 查询预报数据信息，同步到预报数据推送表中
     */
    ForecastRecordVO syncDataToPublish(Long taskId, String dataSource, Date reportTime);

}




