package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.RegionDTO;
import cn.piesat.data.making.server.entity.Region;
import cn.piesat.data.making.server.vo.RegionVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 行政区表服务接口
 *
 * <AUTHOR>
 */
public interface RegionService extends IService<Region> {

    /**
     * 查询列表
     */
    List<RegionVO> getList();

    /**
     * 保存
     */
    void save(RegionDTO dto);

    /**
     * 查询
     */
    String getCodeByName(String code);
}




