package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmPublicTemplate;
import cn.piesat.data.making.server.vo.FmPublicTemplateVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmPublicTemplateMapper {

    FmPublicTemplateMapper INSTANCE = Mappers.getMapper(FmPublicTemplateMapper.class);

    List<FmPublicTemplateVO> entityListToVoList(List<FmPublicTemplate> list);
}
