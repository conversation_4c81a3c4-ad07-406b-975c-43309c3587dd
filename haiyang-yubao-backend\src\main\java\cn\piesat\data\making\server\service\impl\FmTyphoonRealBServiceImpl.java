package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import cn.piesat.data.making.server.dao.FmTyphoonRealBDao;
import cn.piesat.data.making.server.service.FmTyphoonRealBService;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import cn.piesat.data.making.server.utils.page.PageParam;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 台风实时数据信息服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:23
 */
@Service
@Slf4j
public class FmTyphoonRealBServiceImpl extends ServiceImpl<FmTyphoonRealBDao, FmTyphoonRealB> implements FmTyphoonRealBService {

    @Resource
    private FmTyphoonRealBDao fmTyphoonRealBDao;

    @Override
    public PageResult<FmTyphoonRealBVO> getPage(FmTyphoonRealBDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmTyphoonRealBVO> getList(FmTyphoonRealBDTO dto) {
        return null;
    }

    @Override
    public FmTyphoonRealBVO getById(Long id) {
        return null;
    }

    @Override
    public void save(FmTyphoonRealBDTO dto) {

    }

    @Override
    public void saveList(List<FmTyphoonRealBDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }

    @Override
    public List<FmTyphoonRealB> getByTfbh(String typhoonNo) {
        LambdaQueryWrapper<FmTyphoonRealB> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmTyphoonRealB::getTfbh,typhoonNo);
        return this.list(wrapper);
    }

    @Override
    public FmTyphoonRealB getInfo(String typhoonNo, Date typhoonTime) {
        LambdaQueryWrapper<FmTyphoonRealB> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmTyphoonRealB::getTfbh,typhoonNo);
        wrapper.eq(FmTyphoonRealB::getTime,typhoonTime);
        return getOne(wrapper,false);
    }
}
