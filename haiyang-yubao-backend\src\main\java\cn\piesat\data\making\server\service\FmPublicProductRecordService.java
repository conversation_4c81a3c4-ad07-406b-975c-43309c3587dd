package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.vo.FmPublicProductRecordVO;

import javax.servlet.http.HttpServletResponse;

public interface FmPublicProductRecordService {

    void save(FmPublicProductRecordVO vo);

    void update(FmPublicProductRecordVO vo);

    FmPublicProductRecordVO findById(Long id);

    FmPublicProductRecordVO findByRecordId(Long recordId);

    //String makeFile(FmPublicRecordVO vo);

    void download(HttpServletResponse response, Long Id);
}
