import VectorSource from 'ol/source/Vector'
import { SimpleGeometry } from 'ol/geom'
import VectorLayer from 'ol/layer/Vector'
import { Fill, Icon, Stroke, Style, Text } from 'ol/style'
import pointImg from 'src/assets/images/points/youtian.png'
import ZJPT_IMG from 'src/assets/images/points/zuanjingpingtai.png'
import HYMC_IMG from 'src/assets/images/points/haiyangmuchang.png'
import FD_IMG from 'src/assets/images/points/fengdian.png'
import { IGeoJSON } from 'src/utils/geojson/types'
import { randomNumericID } from 'src/utils/util'
import type { IFeatureCategory } from 'src/views/publicServices/specialForecast/components/create-hooks/types'
import { map } from 'lodash'

export function useVector() {
  // 算法需要的要素集
  const { vectorSource, vectorLayer } = vectorFactory()
  vectorLayer.setZIndex(20)
  // 图上标注要素集
  const { vectorSource: labelSource, vectorLayer: labelLayer } = vectorFactory()
  return {
    vectorSource,
    vectorLayer,
    labelSource,
    labelLayer,
    projectPointStyle
  }
}

export function vectorFactory() {
  const vectorSource = new VectorSource<SimpleGeometry>()
  const vectorLayer = new VectorLayer<VectorSource<SimpleGeometry>>({
    source: vectorSource
  })
  return {
    vectorSource,
    vectorLayer
  }
}

function getIcon(category: string) {
  const _category = category as IFeatureCategory
  switch (_category) {
    case 'WX': // 网箱
      return HYMC_IMG
    case 'HSQT': // 海上气田
      return ZJPT_IMG
    case 'FD': // 风电
      return FD_IMG
    default:
      // eslint-disable-next-line no-case-declarations,@typescript-eslint/no-unused-vars
      const _: never = _category
  }
}

/**
 * 海上工程默认样式
 * @param opt
 */
function projectPointStyle(opt: {
  name: string
  category: string
}) {
  return new Style({
    image: new Icon({
      // anchor: [0.5, 1],
      src: getIcon(opt.category),
      scale: [0.6, 0.6]
    }),
    text: new Text({
      text: opt.name,
      font: 'bold 14px Calibri,sans-serif',
      fill: new Fill({
        color: 'black'
      }),
      stroke: new Stroke({
        color: 'white',
        width: 2
      }),
      offsetY: 20
    })
  })
}

export function makeSureFeatureId(geojson: IGeoJSON) {
  geojson.features.forEach(feature => {
    if (!feature.properties) {
      feature.properties = {}
    }
    const id = feature.properties.id
    if (id === undefined) {
      feature.properties.id = randomNumericID()
    }
  })
}
