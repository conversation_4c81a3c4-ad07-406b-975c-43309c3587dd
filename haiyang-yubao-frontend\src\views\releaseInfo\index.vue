<template>
  <!-- 预报信息发布 -->
  <div class="release-info">
    <PublishList @row-click="onRowClick" @change-tab="getTabIndex" />
    <div v-if="tabIndex === 3" class="content">
      <div class="content-header d-flex flex-justify-between">
        <h3>报告预览</h3>

        <qx-button class="primary" @click="onDownload">下载</qx-button>
      </div>
    </div>
    <div v-else class="content">
      <div class="content-header">
        <h3>发布明细</h3>

        <div v-if="tabIndex === 1" class="content-filter d-flex">
          <div class="filter-item d-flex">
            <label for="">文件名称：</label>
            <n-input
              v-model:value="form.productName"
              placeholder="请输入"
              clearable
            />
          </div>
          <div class="filter-item d-flex">
            <label for="">发布状态：</label>
            <n-select
              v-model:value="form.state"
              :options="publishList"
              clearable
              placeholder="请选择"
              style="width: 100%"
            ></n-select>
          </div>
          <qx-button class="primary" @click="getForecastList">查询</qx-button>
        </div>
      </div>

      <div class="table-container">
        <n-data-table
          v-loading="loading"
          :row-key="rowKey"
          class="qx-table"
          :data="tableData"
          :columns="columns"
          :single-line="false"
          :max-height="600"
          :checked-row-keys="checkedKeys"
        ></n-data-table>
      </div>
    </div>
  </div>

  <qx-dialog
    v-model:visible="dialogVisible"
    title="文件预览"
    class="file-preview-dialog"
    width="50%"
    height="80%"
  >
    <template #content>
      <div class="preview-file">
        <iframe
          :src="fileUrl"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { PublishList } from './index'
import { reactive, ref, h } from 'vue'
import { useMessage } from 'naive-ui'
import { QxDialog } from 'src/components/QxDialog'
import ProductApi from 'src/requests/productPublish'
import { forecastInfoColumns, alarmInfoColumns } from './index'
import Alarm from 'src/requests/alarm'
import { QxButton } from 'src/components/QxButton'

const message = useMessage()

let tabIndex = ref<number>(1)
function getTabIndex(index: number) {
  tabIndex.value = index
}

const publishList = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '待推送',
    value: -1
  },
  {
    label: '推送中',
    value: 0
  },
  {
    label: '推送成功',
    value: 1
  },
  {
    label: '推送失败',
    value: 2
  }
]

const form = reactive({
  productName: '',
  state: ''
})

const tableData = ref<any[]>([])
const columns = ref<any[]>([])
const rowKey = (row: any) => row.id
const checkedKeys = ref<string[]>([])
let loading = ref(false)
let rowId = ref('')
// 行点击事件
function onRowClick(index: number, row: any, alarmType: any) {
  initColumns(index)
  tableData.value = []
  if (row) {
    rowId.value = row.id
    if (index === 1) {
      getForecastList()
    } else {
      getAlarmInfoList(row, alarmType)
    }
  }
}

// 初始化表格列
function initColumns(index: number) {
  if (index === 1) {
    let arr = []
    forecastInfoColumns.forEach((item: any) => {
      arr.push(item)
    })
    arr.push({
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 150,
      render(row: any) {
        return [
          h(
            'span',
            {
              size: 'small',
              class: 'operate-btn',
              style: { cursor: 'pointer' },
              onClick: () => onPreview(row)
            },
            { default: () => '预览' }
          ),
          h(
            'span',
            {
              size: 'small',
              class: 'operate-btn',
              style: { cursor: 'pointer' },
              onClick: () => onDownload(row)
            },
            { default: () => '下载' }
          )
        ]
      }
    })

    columns.value = arr
  } else {
    columns.value = alarmInfoColumns
  }
}

// 获取预报发布明细
function getForecastList() {
  ProductApi.getForecastResultInfo(rowId.value, form)
    .then((res: any) => {
      if (res.length) {
        tableData.value = res
      } else {
        tableData.value = []
      }
    })
    .catch(() => {
      tableData.value = []
    })
}

// 获取警报产品发布明细
function getAlarmInfoList(row: any, alarmType: any) {
  const { sourceType, dataType } = alarmType
  loading.value = true
  let funcName = ''
  if (sourceType === 1) {
    if (dataType === 1) {
      funcName = 'getSeaWaveAlarmInfoById' //海浪警报
    } else {
      funcName = 'getStormSurgeAlarmInfoById' // 风暴潮警报
    }
  } else {
    if (dataType === 1) {
      funcName = 'getAlarmInfoById' // 海浪消息
    } else {
      funcName = 'getStormSurgeMessageInfoById' // 风暴潮消息
    }
  }

  alarmCommon(funcName, row)
}

// 警报制作 公共调用
function alarmCommon(name: string, row: any) {
  tableData.value = []
  Alarm[name](row.id)
    .then((res: any) => {
      if (res) {
        tableData.value.push(res)
        loading.value = false
      }
    })
    .catch((e: any) => {
      tableData.value = []
      console.error(e, 'getAlarmList')
      message.error('获取数据失败')
      loading.value = false
    })
}

let dialogVisible = ref(false)
let fileUrl = ref('') //kkFileUrl 地址

//预览
function onPreview(row: any) {
  let { filePath = '' } = row
  if (filePath) {
    let url = config.onlyOfficeServerUrl + filePath
    fileUrl.value = config.kkFileUrl + btoa(url)
    dialogVisible.value = true
  }
}

function onDownload(row: any) {
  const link = document.createElement('a')
  link.href = `${config.fileService}${row.filePath}`
  let arr = row.filePath.split('/')
  let fileName = arr[arr.length - 1].split('.')[0]
  link.download = fileName //下载的文件名称
  link.click()
}
</script>

<style lang="scss">
.release-info {
  display: flex;
  box-sizing: border-box;
  padding: 20px;
  flex: 1;
  .content {
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;

    .content-header {
      box-sizing: border-box;
      padding: 18px 20px 14px;
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 23px;
        position: relative;
        padding-left: 10px;
        margin-bottom: 11px;
        &::before {
          width: 4px;
          height: 18px;
          background: #567bff;
          display: block;
          content: '';
          position: absolute;
          top: 2px;
          left: 0;
        }
      }
    }
  }

  .content-filter {
    box-sizing: border-box;
    label,
    .n-radio__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      margin-right: 10px;
    }

    .filter-item {
      align-items: center;
      margin-right: 32px;
      width: 20%;
    }

    label {
      display: inline-block;
      width: 80px;
      flex-shrink: 0;
      text-align: right;
    }
  }

  .table-container {
    box-sizing: border-box;
    padding: 27px 18px;
  }

  .operate-btn {
    padding: 0 10px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);

    &:nth-last-child(1) {
      border-right: none;
    }
  }
}

.create-publish-dialog {
  .checkbox-group {
    box-sizing: border-box;
    padding: 14px 27px;
  }

  .btns {
    box-sizing: border-box;
    padding: 14px 0 14px 26px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.file-preview-dialog {
  .preview-file {
    height: calc(100% - 60px);
  }
}
</style>
