package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ShipDataDTO;
import cn.piesat.data.making.server.entity.ShipData;
import cn.piesat.data.making.server.vo.ShipDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ShipDataMapper {

    ShipDataMapper INSTANCE = Mappers.getMapper(ShipDataMapper.class);

    /**
     * entity-->vo
     */
    ShipDataVO entityToVo(ShipData entity);

    /**
     * dto-->entity
     */
    ShipData dtoToEntity(ShipDataDTO dto);

    /**
     * entityList-->voList
     */
    List<ShipDataVO> entityListToVoList(List<ShipData> list);

    /**
     * dtoList-->entityList
     */
    List<ShipData> dtoListToEntityList(List<ShipDataDTO> dtoList);
}
