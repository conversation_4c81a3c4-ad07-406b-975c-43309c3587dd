package cn.piesat.data.making.server.entity;

import cn.piesat.data.making.server.config.PgGeometryTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 船舶数据表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_ship_data_b")
public class ShipData implements Serializable {

    private static final long serialVersionUID = 333151627681758290L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     **/
    @TableField("sign_ship")
    private String signShip;
    /**
     * 纬度
     **/
    @TableField("latitude")
    private Double latitude;
    /**
     * 经度
     **/
    @TableField("longitude")
    private Double longitude;
    /**
     * 可见度
     **/
    @TableField("visibility")
    private Double visibility;
    /**
     * 气温
     **/
    @TableField("air_temperature")
    private Double airTemperature;
    /**
     * 露点温度
     **/
    @TableField("dew_point_temp")
    private Double dewPointTemp;
    /**
     * 风向
     **/
    @TableField("wind_direction")
    private Double windDirection;
    /**
     * 风速
     **/
    @TableField("wind_speed")
    private Double windSpeed;
    /**
     * 气压
     **/
    @TableField("air_pressure")
    private Double airPressure;
    /**
     * 云总量
     **/
    @TableField("total_cloud_amount")
    private Double totalCloudAmount;
    /**
     * 云量低
     **/
    @TableField("low_cloud_amount")
    private Double lowCloudAmount;
    /**
     * 海温
     **/
    @TableField("sea_temp")
    private Double seaTemp;
    /**
     * 波期
     **/
    @TableField("wave_period")
    private Double wavePeriod;
    /**
     * 波高
     **/
    @TableField("wave_height")
    private Double waveHeight;
    /**
     * 浪涌方向
     **/
    @TableField("surge_direction")
    private Double surgeDirection;
    /**
     * 浪涌周期
     **/
    @TableField("surge_period")
    private Double surgePeriod;
    /**
     * 浪涌高度
     **/
    @TableField("surge_height")
    private Double surgeHeight;
    /**
     * 位置
     **/
    @TableField(value = "location_geo", typeHandler = PgGeometryTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private String locationGeo;
    /**
     * 位置
     **/
    @TableField("location_json")
    private String locationJson;
    /**
     * 时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("time")
    private Date time;
}



