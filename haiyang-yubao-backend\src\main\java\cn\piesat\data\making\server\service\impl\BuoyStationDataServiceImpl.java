package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.BuoyStationDataDao;
import cn.piesat.data.making.server.dto.BuoyStationDataDTO;
import cn.piesat.data.making.server.entity.BuoyStationData;
import cn.piesat.data.making.server.service.BuoyStationDataService;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.vo.BuoyStationDataVO;
import cn.piesat.data.making.server.vo.FillMapVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 浮标站数据表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BuoyStationDataServiceImpl extends ServiceImpl<BuoyStationDataDao, BuoyStationData>
        implements BuoyStationDataService {

    @Resource
    private BuoyStationDataDao buoyStationDataDao;

    @Override
    @SneakyThrows
    public List<BuoyStationDataVO> getStationDataList(BuoyStationDataDTO dto) {
        String geoRange = null;
        if (StringUtils.isNotBlank(dto.getGeoRange())) {
            geoRange = GeoToolsUtil.geojsonToGeo(dto.getGeoRange());
        }
        return buoyStationDataDao.getDataList(dto.getStartTime(), dto.getEndTime(), geoRange, dto.getBuoyStationCodeList());
    }

    @Override
    public FillMapVO getGeoRangeDataList(BuoyStationDataDTO dto) {
        //浮标站数据
        List<BuoyStationDataVO> dataList = this.getStationDataList(dto);
        //按照数据时间对浮标站数据进行分组
        Map<Date, List<BuoyStationDataVO>> dataMap = dataList.stream().collect(Collectors.groupingBy(BuoyStationDataVO::getTime));
        //数据时间列表
        List<Date> timeList = dataMap.keySet().stream().collect(Collectors.toList()).stream().sorted().collect(Collectors.toList());
        FillMapVO fillMap = new FillMapVO();
        fillMap.setTimeList(timeList);
        fillMap.setTimeAndDataMap(dataMap);
        return fillMap;
    }
}





