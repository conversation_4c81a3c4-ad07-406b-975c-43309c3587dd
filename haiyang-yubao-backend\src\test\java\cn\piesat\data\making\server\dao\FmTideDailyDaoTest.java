package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmTideDialy;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Calendar;
import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmTideDailyDaoTest {

    @Autowired
    private FmTideDailyDao fmTideDailyDaoImpl;

    @Test
    public void testQueryListByParam(){
        List<FmTideDialy> resList = fmTideDailyDaoImpl.queryListByParam("2025-01-01 00:00:00","2025-01-31 23:59:59");

        Assert.assertNotNull(resList);
    }
}
