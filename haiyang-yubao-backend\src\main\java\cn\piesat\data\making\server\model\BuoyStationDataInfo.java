package cn.piesat.data.making.server.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 浮标站数据
 *
 * <AUTHOR>
 */
@Data
public class BuoyStationDataInfo {
    private Long id;
    private String buoyCode;
    private String buoyName;
    private String buoCNname;
    private Double lon;
    private Double lat;
    private List<String> time;
    @JsonProperty("Windspeed")
    private List<Double> windspeed;
    @JsonProperty("Winddir")
    private List<Double> winddir;
    @JsonProperty("WindwaveHeight")
    private List<Double> WindwaveHeight;
    @JsonProperty("Statusbak")
    private Integer Statusbak;
}
