package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报产品记录表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastProductRecordVO implements Serializable {

    private static final long serialVersionUID = -17156281480666503L;

    /**
     * id
     **/
    private Long id;
    /**
     * 名称
     **/
    private String name;
    /**
     * 产品模板id
     **/
    private Long productTemplateId;
    /**
     * 预报记录id
     **/
    private String forecastRecordId;
    /**
     * 图形记录id
     **/
    private String graphicRecordId;
    /**
     * 文件地址
     **/
    private String fileUrl;
    /**
     * 推送任务id
     **/
    private Long pushTaskId;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}



