package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourWlDatODao;
import cn.piesat.data.making.server.entity.OceanStationHourWlDatO;
import cn.piesat.data.making.server.service.OceanStationHourWlDatOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-历时潮汐dat-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourWlDatOServiceImpl extends ServiceImpl<OceanStationHourWlDatODao, OceanStationHourWlDatO>
        implements OceanStationHourWlDatOService {

    @Resource
    private OceanStationHourWlDatODao oceanStationHourWlDatODao;

    @Override
    public List<OceanStationHourWlDatO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourWlDatODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourWlDatODao.getMaxCreateTime();
    }
}





