import { onMounted, onUnmounted, Ref, ref, watch } from 'vue'
import { vectorFactory } from 'src/utils/vector/factory'
import { GeoJSON } from 'ol/format'
import { FeatureCollection } from 'geojson'
import { Feature } from 'ol'
import Map from 'ol/Map'
import { Polygon } from 'ol/geom'
import { isolineStyleFactory } from 'src/utils/isoline'
import { mapTypeList } from 'src/views/analysis/components'
import Analysis from 'src/requests/analysis'
import { useMessage } from 'naive-ui'

export function useIsoline(opt: {
  map: () => Map
  getCurrentTime: () => string
  getTimeRange: () => {
    startTime: string
    endTime: string
  }
  getCurrentType: () => string
}) {
  const message = useMessage()
  const showIsoline = ref(false)
  const { vectorSource, vectorLayer } = vectorFactory<Polygon>()
  vectorLayer.setStyle(isolineStyleFactory(() => 'value'))

  onMounted(() => {
    watch([showIsoline, opt.getCurrentType, opt.getCurrentTime], val => {
      const needRenderIsoline = val[0] && val[1] === 'seaWind'
      if (needRenderIsoline) {
        const productID = getIsolineProductID()
        if (!productID) {
          message.error('无法获取等值线产品 ID')
          return
        }
        getIsoline(productID, opt.getTimeRange, opt.getCurrentTime)
          .then(find => {
            if (!find) {
              throw new Error('无法找到时间点')
            }
            return fetch(`${config.fileService}${find.file_path}`)
          })
          .then(res => {
            return res?.json()
          })
          .then(json => {
            json && loadIsoline({ vectorSource, vectorLayer }, json)
            opt.map().addLayer(vectorLayer)
          })
          .catch(err => {
            console.warn(err)
          })
      } else {
        opt.map().removeLayer(vectorLayer)
      }
    })
  })

  onUnmounted(() => {
    opt.map().removeLayer(vectorLayer)
  })

  return {
    showIsoline
  }
}

function loadIsoline(
  opt: ReturnType<typeof vectorFactory>,
  geojson: FeatureCollection
) {
  const geoJSON = new GeoJSON()
  const features = geoJSON.readFeatures(geojson) as Feature<Polygon>[]
  opt.vectorSource.clear()
  opt.vectorSource.addFeatures(features)
}

/**
 * 获取等值线产品 ID
 */
function getIsolineProductID(): string | undefined {
  const find = mapTypeList.find(i => i.name === '海面风场')
  return find?.isoline?.default
}

async function getIsoline(
  productID: string,
  getTimeRange: () => {
    startTime: string
    endTime: string
  },
  getCurrentTime: () => string
) {
  return Analysis.getProductGeojson(productID, {
    beginTime: getTimeRange().startTime,
    endTime: getTimeRange().endTime,
    _tz: 'GMT',
    orderBy: 'forecast_time',
    sort: 'asc'
  })
    .then(arr => {
      console.log(getCurrentTime())
      return arr.find(item => item.data_time === getCurrentTime())
    })
    .catch(err => {
      console.warn(err)
    })
}
