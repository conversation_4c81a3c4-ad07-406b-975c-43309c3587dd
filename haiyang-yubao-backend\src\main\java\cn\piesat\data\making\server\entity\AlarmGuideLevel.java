package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 指南等级关联(AlarmGuideLevel)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-18 15:06:28
 */
@TableName("fm_alarm_guide_level_r")
public class AlarmGuideLevel implements Serializable {
    private static final long serialVersionUID = -1;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 指南ID
     */
    private Long guideId;
    /**
     * 等级ID
     */
    private Long levelId;
    /**
     * 指南内容
     */
    private String guideContent;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGuideId() {
        return guideId;
    }

    public void setGuideId(Long guideId) {
        this.guideId = guideId;
    }

    public Long getLevelId() {
        return levelId;
    }

    public void setLevelId(Long levelId) {
        this.levelId = levelId;
    }

    public String getGuideContent() {
        return guideContent;
    }

    public void setGuideContent(String guideContent) {
        this.guideContent = guideContent;
    }
}

