<template>
  <div class="common-container error-page">
    <header-bar id="system" name="forecast" class-name="system">
      <template #logo>
        <img src="src/assets/images/common/forecast.png" alt="" />
        <h2>预报警报制作发布系统</h2>
      </template>
    </header-bar>
    <div class="error-content">
      <img src="src/assets/images/404.png" alt="" />
      <div class="duty-info">资源未找到</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HeaderBar } from 'src/components/HeaderBar'
</script>

<style scoped lang="scss">
.error-page {
  width: 100%;
  height: 100%;
  .error-content {
    text-align: center;
    height: calc(100% - 64px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 400px;
      margin-top: -200px;
    }
    .duty-info{
      font-size: 24px;
      font-weight: bold;
      margin-top: 20px;
    }
  }
}
</style>
