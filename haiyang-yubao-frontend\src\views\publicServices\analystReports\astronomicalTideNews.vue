<template>
  <div class="public-container">
    <div class="top">
      <div class="top-left">
<!--        <div class="top-tab active">天文大潮消息</div>-->
        <qx-button class="primary" @click="onDownloadClicked">下载</qx-button>
        <qx-button class="primary" @click="createForm">新建</qx-button>
        <qx-button class="primary" @click="onSave">保存</qx-button>
        <qx-button class="warning" @click="onSubmit">提交</qx-button>
        <span class="date">保存：{{ saveDate }}</span>
        <span class="date">提交：{{ commitDate }}</span>
      </div>
      <div class="top-right">
        <div class="top-right-item">
          <div class="title">编号：</div>
          <n-input
            v-model:value="serialNumber"
            style="width: 100px"
          ></n-input>
        </div>
        <div class="top-right-item">
          <div class="title">拟稿人：</div>
          <n-select
            v-model:value="makeUserId"
            :options="makeUserArr"
            label-field="name"
            value-field="id"
            :consistent-menu-width="false"
          ></n-select>
        </div>
        <div class="top-right-item">
          <div class="title">签发人：</div>
          <n-select
            v-model:value="signUserId"
            :options="signUserArr"
            label-field="name"
            value-field="id"
            :consistent-menu-width="false"
          ></n-select>
        </div>
        <div class="top-right-item">
          <div class="title">发布时间：</div>
          <n-date-picker
            v-model:value="publishDatetime"
            type="datetime"
            format="YYYY-MM-dd HH时"
          ></n-date-picker>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="left">
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">消息综述</div>
              <!--              <span>（上一期：2025-01-22）</span>-->
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">
                表1 海南岛沿海高潮位岸段潮汐预报表
              </div>
            </div>
          </div>
          <div class="card-info">
<!--            <CommonTable :table-head="tableHead" :table-data="tableData" />-->
            <n-data-table
              :columns="tableHead"
              :data="cptTableData"
              max-height="200"
            ></n-data-table>
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">防御意见</div>
            </div>
            <div class="header-right correspondence">
              <qx-button class="primary" @click="isShowCopyTxt = true"
                >参考意见
              </qx-button>
              <CopyTxt
                :visible="isShowCopyTxt"
                :txt-list="defenseOpinions"
                @copy="content => (defenseAdvice = content)"
                title="参考意见"
                @close="isShowCopyTxt = false"
              />
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="defenseAdvice"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
      </div>
      <div class="right">
        <div class="query-item">
          <n-date-picker v-model:value="timeMonth" type="month" clearable />
        </div>
        <div class="query-item">
          <n-input
            v-model:value="stationFilterStr"
            type="text"
            placeholder="请输入"
          />
        </div>

        <div class="station-list">
          <n-space vertical>
            <n-button @click="selectAll" size="tiny">全选</n-button>
            <n-checkbox-group v-model:value="selectedStationIDArr">
              <n-grid :y-gap="8" :cols="1">
                <n-gi v-for="item in cptStationFilter" :key="item.id">
                  <n-checkbox :value="item.id" :label="item.name" />
                </n-gi>
              </n-grid>
            </n-checkbox-group>
          </n-space>

        </div>
        <div class="query-btn">
<!--          <qx-button>重置</qx-button>-->
<!--          <qx-button class="primary">确定</qx-button>-->
        </div>
      </div>
    </div>
    <qx-dialog
      v-model:visible="dialogVisible"
      title="预览"
      width="1372px"
      height="782px"
    >
      <template #content>
        <OfficeEditor
          v-if="cptHasWord"
          v-loading="dialogLoading"
          :url="onlyOfficeObject.wordUrl"
          :callback-url="onlyOfficeObject.callbackUrl"
        ></OfficeEditor>
      </template>
    </qx-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { defenseOpinions } from './constantTxt.js'
import { CommonTable, CopyTxt } from './components'
import moment from 'moment'
import { useStation } from 'src/views/publicServices/analystReports/astronomicalTideNews-hooks/useStation'
import { useContent } from 'src/views/publicServices/analystReports/astronomicalTideNews-hooks/useContent'
import CommenService from 'src/requests/commenService'
import { QxDialog } from 'src/components/QxDialog/index'
import { usePreviewDialog } from 'src/views/publicServices/analystReports/astronomicalTideNews-hooks/usePreviewDialog'
import { OfficeEditor } from 'src/components/OfficeEditor/index'
import { IAstronomicalTideQueryResDTO } from 'src/requests/commenService.type'

// 防止未来需求变化暂不删除
const timeRange = ref<[number, number]>([
  moment().subtract(1, 'months').valueOf(),
  moment().valueOf()
])
const timeMonth = computed<number, number>({
  get() {
    return timeRange.value[1]
  },
  set(val) {
    timeRange.value[1] = val
    timeRange.value[0] = moment(val).startOf('months').valueOf()
  }
})

const {
  seaConditionAnalysis,
  defenseAdvice,
  saveDate,
  commitDate,
  createNewContent,
  onSave,
  onSubmit,
  tableData,
  cptTableData,
  tableHead,
  makeUserArr,
  makeUserInfo,
  makeUserId,
  signUserArr,
  signUserInfo,
  signUserId,
  serialNumber,
  publishDatetime,
  renderLabelFactory,
  contentData
} = useContent(timeRange, () => selectedStationIDArr.value)
const {
  updateStation,
  stations,
  stationFilterStr,
  cptStationFilter,
  selectedStationIDArr,
  cptSelectedStationInfoArr,
  selectAll,
  resetStation
} = useStation()
// 是否显示复制 panel
let isShowCopyTxt = ref(false)

const {
  dialogVisible,
  onDownloadClicked,
  onlyOfficeObject,
  cptHasWord,
  dialogLoading
} = usePreviewDialog({
  idGetter: () => (contentData.value as IAstronomicalTideQueryResDTO).id
})

function createForm() {
  createNewContent()
  resetStation()
}
</script>

<style lang="scss">
@import './style.scss';

.correspondence {
  position: relative;
}
</style>
