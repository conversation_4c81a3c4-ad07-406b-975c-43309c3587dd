# ForecastProductRecordController 接口文档

## 概述

`ForecastProductRecordController` 是预报产品记录管理的控制器，负责处理预报产品的生成、发布、下载等核心业务功能。该控制器提供了完整的预报产品生命周期管理，包括产品记录的查询、保存、发布、下载以及发布结果的查询等功能。

**基础路径**: `/forecastProductRecord`

**系统名称**: 预报警报制作发布系统

**模块名称**: 预报产品记录管理

---

## 接口列表

### 1. 查询预报产品记录列表

**接口路径**: `GET /forecastProductRecord/list`

**功能描述**: 查询所有预报产品记录的列表信息

**请求参数**: 无

**响应数据**: `List<ForecastProductRecordVO>`

**实现流程**:
1. 调用 `forecastProductRecordService.getList()` 方法
2. 从数据库查询所有预报产品记录
3. 转换为 VO 对象返回

**响应字段说明**:
- `id`: 记录ID
- `name`: 产品名称
- `productTemplateId`: 产品模板ID
- `forecastRecordId`: 预报记录ID
- `graphicRecordId`: 图形记录ID
- `fileUrl`: 文件地址
- `pushTaskId`: 推送任务ID
- `createTime`: 创建时间
- `updateTime`: 更新时间

---

### 2. 审核预览生成预报产品记录

**接口路径**: `GET /forecastProductRecord/save`

**功能描述**: 审核预览并生成预报产品记录，用于产品制作完成后的保存操作

**请求参数**: 无

**响应数据**: `List<ForecastProductRecordVO>`

**实现流程**:
1. 调用 `forecastProductRecordService.save()` 方法
2. 根据当前预报任务状态生成产品记录
3. 处理预报模板和数据，生成最终的产品文件
4. 保存产品记录到数据库
5. 返回生成的产品记录列表

**业务逻辑**:
- 检查预报任务状态
- 根据模板生成产品文件
- 创建产品记录
- 更新相关状态

---

### 3. 发布预报产品

**接口路径**: `GET /forecastProductRecord/release`

**功能描述**: 发布预报产品到各个渠道，是产品发布的核心接口

**请求参数**: 无

**响应数据**: `void`

**实现流程**:
1. 调用 `forecastProductRecordService.release()` 方法
2. 查询待发布的产品记录
3. 根据配置的发布渠道进行产品推送
4. 创建推送任务
5. 更新产品状态为已发布
6. 记录发布日志

**业务逻辑**:
- 验证产品状态
- 创建推送任务
- 执行多渠道发布
- 更新发布状态

---

### 4. 下载预报产品

**接口路径**: `GET /forecastProductRecord/download`

**功能描述**: 下载预报产品文件，支持批量下载

**请求参数**: 
- `HttpServletResponse response`: HTTP响应对象

**响应数据**: 文件流

**实现流程**:
1. 调用 `forecastProductRecordService.download(response)` 方法
2. 查询可下载的产品文件
3. 打包文件（如果是多个文件）
4. 设置响应头信息
5. 将文件流写入响应

**文件处理**:
- 支持单文件和多文件下载
- 自动压缩打包
- 设置正确的Content-Type

---

### 5. 根据ID下载Word文档

**接口路径**: `GET /forecastProductRecord/downloadDoc/{id}.docx`

**功能描述**: 根据产品记录ID下载指定的Word文档

**请求参数**:
- `id` (路径参数): 产品记录ID，类型为Long

**响应数据**: Word文档文件流

**实现流程**:
1. 根据产品记录ID查询记录信息
2. 获取对应的Word文档文件路径
3. 验证文件存在性
4. 设置响应头为Word文档类型
5. 将文件流写入响应

**注意事项**:
- 该接口未启用系统日志记录（注释掉了@SysLog）
- 文件名固定为 `{id}.docx` 格式

---

### 6. 查询预报产品发布结果分页

**接口路径**: `GET /forecastProductRecord/releaseResult/page`

**功能描述**: 分页查询预报产品的发布结果信息

**请求参数**:
- `pageNum` (可选): 页码，默认值为1
- `pageSize` (可选): 每页条数，默认值为10

**响应数据**: `PageResult<ForecastProductPushResultInfo>`

**实现流程**:
1. 构建分页参数对象
2. 调用 `forecastProductRecordService.getReleaseResultPage(pageParam)` 方法
3. 查询推送任务信息
4. 统计推送结果数据
5. 组装分页结果返回

**响应字段说明**:
- `taskName`: 任务名称
- `totalCount`: 推送总数
- `pushingCount`: 推送中数量
- `failedCount`: 失败数量
- `successCount`: 成功数量
- `publishTime`: 发布时间

---

### 7. 根据推送任务ID查询产品列表

**接口路径**: `GET /forecastProductRecord/releaseResult/{pushTaskId}`

**功能描述**: 根据推送任务ID查询具体的产品发布结果列表

**请求参数**:
- `pushTaskId` (路径参数): 推送任务ID，类型为Long
- `productName` (可选): 产品名称，用于过滤
- `state` (可选): 发布状态，用于过滤

**响应数据**: `List<ProductReleaseResult>`

**实现流程**:
1. 根据推送任务ID查询推送任务详情
2. 获取产品模板信息
3. 查询产品发布结果
4. 根据产品名称和状态进行过滤
5. 组装结果列表返回

**响应字段说明**:
- `fileName`: 文件名称
- `fileType`: 文件类型
- `pushType`: 发布渠道
- `state`: 发布状态
- `filePath`: 文件地址

---

### 8. 更新产品文件内容

**接口路径**: `POST /forecastProductRecord/updateFile`

**功能描述**: 更新产品文件的内容，用于产品内容的在线编辑

**请求参数**: `FileInfo` (JSON格式)
```json
{
  "path": "文件路径",
  "content": "文件内容"
}
```

**响应数据**: `void`

**实现流程**:
1. 接收FileInfo对象
2. 验证文件路径的有效性
3. 调用 `FileUtil.updateFile(fileInfo)` 方法
4. 将新内容写入指定文件
5. 更新文件修改时间

**业务逻辑**:
- 支持文本文件内容的在线编辑
- 自动备份原文件（如果配置了备份策略）
- 记录文件修改历史

---

## 数据模型说明

### ForecastProductRecordVO
预报产品记录视图对象，包含产品的基本信息和状态。

### ForecastProductPushResultInfo
预报产品推送结果信息，包含推送任务的统计数据。

### ProductReleaseResult
产品发布结果，包含具体产品的发布状态和渠道信息。

### FileInfo
文件信息对象，用于文件内容的更新操作。

---

## 系统日志

所有接口都配置了系统日志记录（除了downloadDoc接口），记录操作类型包括：
- `SELECT`: 查询操作
- `UPDATE`: 更新操作

日志信息包括操作用户、操作时间、操作模块等详细信息。

---

## 异常处理

控制器依赖服务层进行具体的业务逻辑处理和异常处理，常见异常包括：
- 文件不存在异常
- 权限不足异常
- 数据库操作异常
- 网络推送异常

---

## 依赖服务

- `ForecastProductRecordService`: 核心业务服务
- `FileUtil`: 文件操作工具类
- 数据推送服务（通过Feign客户端）
- 产品模板服务

---

## 技术实现细节

### 服务层实现要点

#### 1. 产品记录生成流程
```java
// 核心实现逻辑
public List<ForecastProductRecordVO> save() {
    // 1. 查询预报任务列表
    // 2. 根据任务状态判断处理方式
    // 3. 生成产品文件
    // 4. 保存记录到数据库
    // 5. 返回生成结果
}
```

#### 2. 产品发布流程
```java
public void release() {
    // 1. 查询待发布产品
    // 2. 创建推送任务
    // 3. 调用数据推送服务
    // 4. 更新发布状态
    // 5. 记录推送日志
}
```

#### 3. 文件下载处理
- 支持单文件直接下载
- 多文件自动打包为ZIP格式
- 设置正确的Content-Disposition头
- 处理中文文件名编码问题

### 数据库设计

#### 主要表结构
- `fm_forecast_product_record_b`: 预报产品记录表
- `fm_push_task_log_b`: 推送任务日志表
- `fm_forecast_task_b`: 预报任务表
- `fm_product_template_b`: 产品模板表

#### 关键字段说明
- `push_task_id`: 推送任务ID，用于关联推送结果
- `file_url`: 产品文件存储路径
- `status`: 产品状态（待发布、已发布等）

### 文件存储策略

#### 存储路径规则
- 基础路径：配置文件中的 `piesat.base-output-path`
- 文件命名：`{产品类型}_{时间戳}_{序号}.{扩展名}`
- 目录结构：按日期和产品类型分层存储

#### 支持的文件格式
- Word文档 (.docx)
- PDF文档 (.pdf)
- 文本文件 (.txt)
- XML文件 (.xml)
- 图片文件 (.png, .jpg)

---

## 配置说明

### 必要配置项
```yaml
piesat:
  base-output-path: /data/forecast/products  # 产品文件存储基础路径
  make:
    enable-backup: true                       # 是否启用文件备份
    max-file-size: 100MB                     # 最大文件大小限制
```

### 推送渠道配置
- 官网发布
- 微信公众号
- 短信平台
- 邮件系统
- FTP服务器

---

## 性能优化建议

### 1. 文件操作优化
- 使用NIO进行大文件处理
- 实现文件分片下载
- 添加文件缓存机制

### 2. 数据库查询优化
- 对推送任务ID建立索引
- 使用分页查询避免大结果集
- 实现查询结果缓存

### 3. 并发处理
- 产品发布使用异步处理
- 实现发布队列机制
- 添加重试机制

---

## 监控和运维

### 关键监控指标
- 产品生成成功率
- 文件下载响应时间
- 推送任务成功率
- 存储空间使用情况

### 日志记录
- 操作日志：记录用户操作行为
- 业务日志：记录业务处理过程
- 错误日志：记录异常和错误信息
- 性能日志：记录接口响应时间

### 运维建议
- 定期清理过期产品文件
- 监控磁盘空间使用情况
- 备份重要产品数据
- 定期检查推送服务状态

---

## 安全考虑

### 1. 文件安全
- 验证文件路径，防止路径遍历攻击
- 限制文件大小和类型
- 对敏感文件进行加密存储

### 2. 接口安全
- 实现用户身份验证
- 添加操作权限控制
- 记录详细的操作日志

### 3. 数据安全
- 敏感数据脱敏处理
- 实现数据备份和恢复
- 定期进行安全审计

---

## 常见问题和解决方案

### Q1: 文件下载失败
**原因**: 文件路径不存在或权限不足
**解决**: 检查文件路径和服务器权限设置

### Q2: 产品发布失败
**原因**: 推送服务不可用或网络问题
**解决**: 检查推送服务状态，实现重试机制

### Q3: 大文件下载超时
**原因**: 文件过大导致下载超时
**解决**: 实现分片下载或增加超时时间

### Q4: 并发访问导致的数据不一致
**原因**: 多用户同时操作同一产品
**解决**: 实现乐观锁或悲观锁机制

---

## 版本历史

- v1.0: 基础功能实现
- v1.1: 添加批量下载功能
- v1.2: 优化文件存储策略
- v1.3: 增强安全性和监控功能

---

## 联系方式

如有问题或建议，请联系开发团队：
- 开发负责人: shikaiqian
- 邮箱: <EMAIL>
- 项目地址: 预报警报制作发布系统
