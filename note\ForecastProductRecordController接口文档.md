# ForecastProductRecordController 核心接口代码详解

## 概述

`ForecastProductRecordController` 是预报产品记录管理的控制器，本文档重点分析**审核预览生成预报产品记录**和**发布预报产品**这两个核心接口的具体代码实现。

**基础路径**: `/forecastProductRecord`

**系统名称**: 预报警报制作发布系统

**模块名称**: 预报产品记录管理

---

## 核心接口详解

#### 控制器层代码
```java
@GetMapping("/save")
@SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
public List<ForecastProductRecordVO> save() {
    return forecastProductRecordService.save();
}
```

#### 服务层核心实现代码详解

<augment_code_snippet path="haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/service/impl/ForecastProductRecordServiceImpl.java" mode="EXCERPT">
````java
@Override
public List<ForecastProductRecordVO> save() {
    long startTime = System.currentTimeMillis();

    // 1. 查询开启并关联预报的产品模板
    ForecastProductTemplateDTO forecastProductTemplateDto = new ForecastProductTemplateDTO();
    forecastProductTemplateDto.setStatus(Boolean.TRUE);
    List<ForecastProductTemplateVO> templateList = forecastProductTemplateService.getList(forecastProductTemplateDto).stream()
            .filter(template -> StringUtils.isNotBlank(template.getRelationTemplateCode())).collect(Collectors.toList());

    if (CollectionUtils.isEmpty(templateList)) {
        return Collections.EMPTY_LIST;
    }

    // 2. 获取关联的预报数据
    Map<String, List<ForecastRecordDetail>> detailMap = this.getForecastRecordDetail(templateList);

    // 3. 获取关联的专题图
    FmGraphicRecordB graphicRecord = this.getGraphicRecord();

    // 4. 准备模板变量
    String publishTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时"));
    String publishDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    DayOfWeek dayOfWeek = LocalDateTime.now().getDayOfWeek();
    String dayName = dayOfWeek.getDisplayName(TextStyle.FULL, Locale.SIMPLIFIED_CHINESE);

    ConcurrentMap<String, Object> map = new ConcurrentHashMap<>();
    map.put("publishTime", publishTime);
    map.put("publishDate", publishDate);
    map.put("dayOfWeek", dayName);

    // 5. 处理专题图
    String graphicRecordId = null;
    if (graphicRecord != null && StringUtils.isNotBlank(graphicRecord.getFileUrl())) {
        graphicRecordId = graphicRecord.getId().toString();
        map.put("graphicUrl", Pictures.ofLocal(graphicRecord.getFileUrl()).size(200, 200).create());
    }
    String finalGraphicRecordId = graphicRecordId;

    // 6. 多线程并行处理产品生成
    ExecutorService executorService = Executors.newFixedThreadPool(5);
    CopyOnWriteArrayList<ForecastProductRecord> list = new CopyOnWriteArrayList<>();
    CountDownLatch latch = new CountDownLatch(templateList.size());

    for (ForecastProductTemplateVO template : templateList) {
        executorService.submit(() -> {
            // 处理关联的预报记录ID
            List<String> codeList = Arrays.asList(template.getRelationTemplateCode().split(","));
            StringBuffer forecastRecordIdStr = new StringBuffer();
            codeList.stream().forEach(code -> {
                List<ForecastRecordDetail> detailList = detailMap.get(code);
                if (!CollectionUtils.isEmpty(detailList)) {
                    Long recordId = detailList.get(0).getForecastRecordId();
                    forecastRecordIdStr.append(recordId).append(",");
                }
            });

            String forecastRecordId = forecastRecordIdStr.length() == 0 ? null :
                forecastRecordIdStr.substring(0, forecastRecordIdStr.length() - 1);

            // 创建产品记录
            ForecastProductRecord record = new ForecastProductRecord();
            record.setName(template.getName());
            record.setProductTemplateId(template.getId());
            record.setForecastRecordId(forecastRecordId);
            record.setGraphicRecordId(finalGraphicRecordId);

            // 生成产品文件
            String outPath = this.createProductFile(map, detailMap, template);
            record.setFileUrl(outPath);
            list.add(record);

            latch.countDown();
        });
    }

    try {
        latch.await(); // 等待所有任务完成
    } catch (InterruptedException e) {
        log.error("等待任务完成时出现异常：{}", e.getMessage());
        Thread.currentThread().interrupt();
    }

    executorService.shutdown();

    // 7. 批量保存到数据库
    this.saveBatch(list);

    long endTime = System.currentTimeMillis();
    log.debug("程序运行时间：" + (endTime - startTime) + "ms");

    return ForecastProductRecordMapper.INSTANCE.entityListToVoList(list);
}
````
</augment_code_snippet>

#### 核心业务逻辑分析

**1. 模板查询与过滤**
- 查询状态为启用的产品模板
- 过滤出有关联预报模板代码的模板
- 确保只处理有效的产品模板

**2. 数据准备阶段**
- `getForecastRecordDetail()`: 获取预报记录详情数据
- `getGraphicRecord()`: 获取当天的专题图记录
- 准备模板变量：发布时间、日期、星期等

**3. 多线程并行处理**
- 使用固定大小线程池（5个线程）
- 使用 `CopyOnWriteArrayList` 保证线程安全
- 使用 `CountDownLatch` 等待所有任务完成

**4. 产品文件生成**
- 调用 `createProductFile()` 方法生成具体的产品文件
- 支持多种文件格式：DOCX、TXT、XML、HTML
- 根据模板类型选择不同的生成策略

---

### 2. 发布预报产品 - release() 方法

#### 控制器层代码

```java
@GetMapping("/release")
@SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
public void release() {
    forecastProductRecordService.release();
}
```

#### 服务层核心实现代码详解

<augment_code_snippet path="haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/service/impl/ForecastProductRecordServiceImpl.java" mode="EXCERPT">
````java
@Override
public void release() {
    // 1. 查询预报产品列表（当天的产品记录）
    List<ForecastProductRecord> list = this.list(createQueryWrapper());
    if (CollectionUtils.isEmpty(list)) {
        return;
    }

    // 2. 查询推送产品列表（从数据推送服务获取可推送的产品ID列表）
    List<String> pushProductIdList = this.getPushProductIdList();

    // 3. 获取预报模板信息
    List<ForecastProductTemplateVO> templateList = forecastProductTemplateService.getList(new ForecastProductTemplateDTO());
    Map<Long, ForecastProductTemplateVO> templateMap = templateList.stream()
        .collect(Collectors.toMap(ForecastProductTemplateVO::getId, Function.identity(), (key1, key2) -> key2));

    // 4. 构建推送产品列表
    List<ManualTaskProductVO> productList = new ArrayList<>();
    list.stream().forEach(record -> {
        ForecastProductTemplateVO template = templateMap.get(record.getProductTemplateId());
        if (!pushProductIdList.contains(template.getCode())) {
            return; // 如果该产品不在推送列表中，跳过
        }

        ManualTaskProductVO product = new ManualTaskProductVO();
        product.setProductId(template.getCode());

        // 5. 处理HTML类型文件转换
        if (FileType.HTML.getValue().equals(template.getFileType())) {
            String docxPrefix = record.getFileUrl().split("\\.")[0];
            String htmlName = docxPrefix + ".html";
            WordUtil.convertToHtml(record.getFileUrl(), htmlName); // Word转HTML
            record.setFileUrl(htmlName);
        }

        product.setFilePath(record.getFileUrl());
        product.setProductName(record.getName());
        product.setDataTime(new Date().getTime());
        productList.add(product);
    });

    // 6. 处理专题图推送
    FmGraphicRecordB graphicRecord = this.getGraphicRecord();
    if (graphicRecord != null && pushProductIdList.contains(ProductEnum.FORECAST_IMAGE.getProductId())) {
        ManualTaskProductVO graphicProduct = new ManualTaskProductVO();
        graphicProduct.setProductId(ProductEnum.FORECAST_IMAGE.getProductId());
        graphicProduct.setFilePath(graphicRecord.getFileUrl());
        graphicProduct.setProductName("专题图");
        graphicProduct.setDataTime(new Date().getTime());
        productList.add(graphicProduct);
    }

    // 7. 处理视频推送
    if (pushProductIdList.contains(ProductEnum.FORECAST_VIDEO.getProductId())) {
        ManualTaskProductVO videoProduct = new ManualTaskProductVO();
        videoProduct.setProductId(ProductEnum.FORECAST_VIDEO.getProductId());
        String filePath = String.format("%s/%s", baseOutputPath, "video.wmv");
        videoProduct.setFilePath(filePath);
        videoProduct.setProductName("视频");
        videoProduct.setDataTime(new Date().getTime());
        productList.add(videoProduct);
    }

    // 8. 创建推送任务
    ManualTaskVO manualTaskVO = new ManualTaskVO();
    manualTaskVO.setTaskName("预报产品推送");
    manualTaskVO.setSystem("data-making-server");
    manualTaskVO.setMode(1);
    manualTaskVO.setProducts(productList);

    log.debug("创建推送任务:{}", JsonUtil.object2Json(manualTaskVO));
    Long taskId = pushManualTaskServiceAgent.createManualTask(manualTaskVO);

    // 9. 启动推送任务
    log.debug("开始推送任务:{}", taskId);
    pushManualTaskServiceAgent.startManualTask(taskId);

    // 10. 更新产品记录的推送任务ID
    list.stream().forEach(record -> record.setPushTaskId(taskId));
    this.updateBatchById(list);

    // 11. 更新预报任务状态为已发布
    LambdaQueryWrapper<ForecastTask> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ForecastTask::getStatus, ForecastTaskStatus.MAKED.getValue());
    List<ForecastTask> forecastTaskList = forecastTaskService.list(queryWrapper);
    forecastTaskList.stream().forEach(task -> task.setStatus(ForecastTaskStatus.RELEASED.getValue()));
    forecastTaskService.updateBatchById(forecastTaskList);

    // 12. 记录推送结果（用于邮件发送等后续处理）
    for(ForecastProductRecord vo : list){
        String filePath = vo.getFileUrl();
        String fileName = filePath.substring(filePath.lastIndexOf("/")+1);
        ForecastProductTemplateVO forecastProductTemplateVO = forecastProductTemplateService.getInfoById(vo.getProductTemplateId());
        fmPushResultServiceImpl.addFmPushResList("", forecastProductTemplateVO.getCode(), vo.getName(),
            vo.getFileUrl(), fileName, "/forecastProductRecord/downloadDoc/"+vo.getId()+".docx");
    }
}
````
</augment_code_snippet>

#### 核心业务逻辑分析

**1. 产品筛选与验证**
- 查询当天生成的所有产品记录
- 通过 `getPushProductIdList()` 获取可推送的产品ID列表
- 只处理在推送列表中的产品

**2. 文件格式处理**
- HTML类型：将Word文档转换为HTML格式
- 其他格式：直接使用原文件

**3. 多类型产品支持**
- 预报产品文件：根据模板生成的各类预报文档
- 专题图：当天审核通过的图形文件
- 视频文件：固定路径的视频文件

**4. 推送任务管理**
- 创建手动推送任务
- 设置任务名称、系统标识、模式等
- 启动推送任务并记录任务ID

**5. 状态更新**
- 更新产品记录的推送任务ID
- 将预报任务状态从"已制作"更新为"已发布"
- 记录推送结果用于后续处理

---

## 关键辅助方法详解

### 1. createProductFile() - 产品文件生成方法

<augment_code_snippet path="haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/service/impl/ForecastProductRecordServiceImpl.java" mode="EXCERPT">
````java
private String createProductFile(ConcurrentMap<String, Object> map,
                                Map<String, List<ForecastRecordDetail>> detailMap,
                                ForecastProductTemplateVO template) {
    String outPath = null;
    try {
        Date date = new Date();
        int year = DateUtil.year(date);
        String day = DateUtil.format(date, "yyyyMMdd");
        String time = DateUtil.format(date, "yyyyMMddHHmmss");
        long timestamp = timestampGenerator.incrementAndGet();

        // 生成各种格式的文件路径
        String tempWordName = String.format(CommonConstant.FORECAST_PRODUCT_TEMP_WORD_NAME, baseOutputPath, year, day, time, timestamp);
        String wordName = String.format(CommonConstant.FORECAST_PRODUCT_WORD_NAME, baseOutputPath, year, day, time, timestamp);
        String txtName = String.format(CommonConstant.FORECAST_PRODUCT_TXT_NAME, baseOutputPath, year, day, time, timestamp);
        String xmlName = String.format(CommonConstant.FORECAST_PRODUCT_XML_NAME, baseOutputPath, year, day, time, timestamp);

        // 根据模板文件类型生成不同格式的产品文件
        if (FileType.HTML.getValue().equals(template.getFileType())) {
            outPath = wordName;
            WordUtil.createWord(map, detailMap, template.getFileUrl(), wordName);
        }
        if (FileType.DOCX.getValue().equals(template.getFileType())) {
            outPath = wordName;
            // 预报单word时，publishTime发布时间，需要固定为17时
            String publishTime = String.valueOf(map.get("publishTime"));
            map.put("publishTime", publishTime.split("日")[0] + "日17时");
            WordUtil.createWord(map, detailMap, template.getFileUrl(), wordName);
        }
        if (FileType.TXT.getValue().equals(template.getFileType())) {
            outPath = txtName;
            // txt内容生成word -> 解析word并赋值 -> 转换为txt
            WordUtil.createFile(template.getFileContent(), tempWordName);
            WordUtil.createWord(map, detailMap, tempWordName, wordName);
            WordUtil.convertToTxt(wordName, txtName);
            FileUtil.deleteFile(tempWordName); // 清理临时文件
            FileUtil.deleteFile(wordName);
        }
        if (FileType.XML.getValue().equals(template.getFileType())) {
            outPath = xmlName;
            XmlUtil.createXml(template.getFileUrl(), xmlName, detailMap);
        }
    } catch (Exception e) {
        log.error("生成预报产品异常：{},{}", template.getName(), e.getMessage());
    }
    return outPath;
}
````
</augment_code_snippet>

**文件生成策略说明：**

- **HTML类型**: 直接生成Word文档，后续在发布时转换为HTML
- **DOCX类型**: 生成Word文档，特殊处理发布时间为17时
- **TXT类型**: 先生成临时Word → 填充数据 → 转换为TXT → 清理临时文件
- **XML类型**: 使用XML工具直接生成XML文件

### 2. getForecastRecordDetail() - 获取预报记录详情

<augment_code_snippet path="haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/service/impl/ForecastProductRecordServiceImpl.java" mode="EXCERPT">
````java
private Map<String, List<ForecastRecordDetail>> getForecastRecordDetail(List<ForecastProductTemplateVO> templateList) {
    Map<String, List<ForecastRecordDetail>> map = new HashMap<>();

    // 查询当天全部的预报记录
    List<ForecastRecordVO> recordList = forecastRecordService.getList();
    if (CollectionUtils.isEmpty(recordList)) {
        return map;
    }

    // 根据预报类型分组 k:预报类型 v:预报记录
    Map<String, ForecastRecordVO> recordMap = recordList.stream()
            .collect(Collectors.toMap(ForecastRecordVO::getForecastTemplateCode, Function.identity(), (key1, key2) -> key2));

    // k:预报记录id v:预报记录详情列表
    Map<Long, List<ForecastRecordDetail>> detailMap = recordList.stream()
            .collect(Collectors.toMap(ForecastRecordVO::getId, ForecastRecordVO::getDetailList));

    // 查询产品模板关联的预报记录明细列表
    templateList.forEach(template -> {
        List<String> codeList = Arrays.asList(template.getRelationTemplateCode().split(","));
        codeList.forEach(code -> {
            ForecastRecordVO record = recordMap.get(code);
            if (record != null) {
                List<ForecastRecordDetail> detailList = detailMap.get(record.getId());
                map.put(code, detailList);
            }
        });
    });
    return map;
}
````
</augment_code_snippet>

**数据组织逻辑：**
- 查询当天所有预报记录
- 按预报类型代码分组
- 提取预报记录详情数据
- 根据产品模板的关联代码匹配对应的详情数据

### 3. getGraphicRecord() - 获取专题图记录

<augment_code_snippet path="haiyang-yubao-backend/src/main/java/cn/piesat/data/making/server/service/impl/ForecastProductRecordServiceImpl.java" mode="EXCERPT">
````java
private FmGraphicRecordB getGraphicRecord() {
    LambdaQueryWrapper<FmGraphicRecordB> graphicQuery = new LambdaQueryWrapper<>();
    graphicQuery.eq(FmGraphicRecordB::getChecked, Boolean.TRUE);  // 已审核
    graphicQuery.ge(FmGraphicRecordB::getCreateTime, LocalDate.now().atTime(LocalTime.MIN));  // 当天开始
    graphicQuery.le(FmGraphicRecordB::getCreateTime, LocalDate.now().atTime(LocalTime.MAX));  // 当天结束
    return fmGraphicRecordBDao.selectOne(graphicQuery);
}
````
</augment_code_snippet>

**查询条件：**
- 审核状态为已审核（checked = true）
- 创建时间在当天范围内
- 返回符合条件的专题图记录

### 4. getPushProductIdList() - 获取推送产品列表

这个方法通过Feign客户端调用数据推送服务，获取当前可以推送的产品ID列表，用于过滤哪些产品需要发布。

---

## 技术特点总结

### 1. 并发处理优化
- 使用固定线程池并行生成产品文件
- 使用线程安全的集合类（CopyOnWriteArrayList）
- 使用CountDownLatch确保所有任务完成

### 2. 文件格式支持
- 支持多种输出格式：DOCX、HTML、TXT、XML
- 智能文件转换：Word↔HTML、Word→TXT
- 自动文件路径管理和临时文件清理

### 3. 数据流转设计
- 模板驱动的产品生成
- 预报数据与产品模板的灵活关联
- 专题图与产品的统一管理

### 4. 推送机制
- 多渠道推送支持（FTP、DISK、EMAIL、OSS）
- 推送任务的创建和管理
- 推送状态的实时跟踪

---

## 总结

本文档详细分析了 `ForecastProductRecordController` 中最核心的两个接口：

1. **审核预览生成预报产品记录 (save)**：负责根据预报模板和数据生成各种格式的产品文件
2. **发布预报产品 (release)**：负责将生成的产品文件推送到各个发布渠道

这两个接口构成了预报产品从生成到发布的完整流程，是整个预报警报制作发布系统的核心功能。通过多线程并发处理、多格式文件支持、多渠道推送等技术手段，实现了高效、稳定的预报产品管理。
