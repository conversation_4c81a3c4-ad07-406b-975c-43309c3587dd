<template>
  <div class="query-item">
    <div class="query-title">台风选择：</div>
    <n-select
      v-model:value="typhoon"
      class="query-info"
      :options="typhoonOptions"
      label-field="bhName"
      value-field="tfbh"
      @update:value="getSimilarTyphoonList"
    />
  </div>
  <div class="query-item">
    <div class="query-title"></div>
    <n-checkbox-group v-model:value="types">
      <n-checkbox value="strong" label="强度" />
      <n-checkbox value="path" label="路径" />
    </n-checkbox-group>
  </div>
  <div class="query-item">
    <div class="query-title">相似台风：</div>
    <n-select
      v-model:value="similarTyphoon"
      class="query-info"
      :options="similarTyphoonList"
      label-field="name"
      value-field="tfbh"
      multiple
      :max-tag-count="1"
      clearable
    />
  </div>
  <div class="jigou">
    <!-- <div class="query-title">预报机构：</div>
     <n-select
      v-model:value="institution"
      class="query-info"
      :options="institutionOptions"
      label-field="dataSource"
      value-field="dataSource"
    /> -->
    <div class="jigou-item">
      <div class="line-color zhong"></div>
      <div class="line-name">中国</div>
    </div>
    <div class="jigou-item">
      <div class="line-color xiang"></div>
      <div class="line-name">中国香港</div>
    </div>
    <div class="jigou-item">
      <div class="line-color ri"></div>
      <div class="line-name">日本</div>
    </div>
    <div class="jigou-item">
      <div class="line-color tai"></div>
      <div class="line-name">中国台湾</div>
    </div>
    <div class="jigou-item">
      <div class="line-color mei"></div>
      <div class="line-name">美国</div>
    </div>
  </div>

  <div class="query-bottom">
    <qx-button class="my-btn" @click="clearTyData">清空台风</qx-button>
    <qx-button class="my-btn" @click="getTyData">叠加台风</qx-button>
  </div>
  <div class="legend-container" :class="[isWarning ? 'is-warning' : '']"></div>
  <div id="popup" class="ol-popup1">
    <div id="popup1-content">
      <div v-show="typhoonInfo.name && typhoonInfo.name !== ''" class="info">
        台风名称：{{ typhoonInfo.name }}
      </div>
      <div v-show="typhoonInfo.tm && typhoonInfo.tm !== ''" class="info">
        机构：{{ typhoonInfo.tm }}
      </div>
      <div v-show="typhoonInfo.time && typhoonInfo.time !== ''" class="info">
        过去时间：{{ typhoonInfo.time }}
      </div>
      <div class="info">
        中心位置：{{ typhoonInfo.lng }},{{ typhoonInfo.lat }}
      </div>
      <div v-show="typhoonInfo.speed && typhoonInfo.speed !== ''" class="info">
        最大风速：{{ typhoonInfo.speed }} 米/秒
        <span
          v-show="typhoonInfo.strong && typhoonInfo.strong !== ''"
          class="strong"
          :class="[
            typhoonInfo.strong === '热带低压(TD)' ? 'td' : '',
            typhoonInfo.strong === '热带风暴(TS)' ? 'ts' : '',
            typhoonInfo.strong === '强热带风暴(STS)' ? 'sts' : '',
            typhoonInfo.strong === '台风(TY)' ? 'ty' : '',
            typhoonInfo.strong === '强台风(STY)' ? 'sty' : '',
            typhoonInfo.strong === '超强台风(Super TY)' ? 'superTy' : ''
          ]"
          >{{ typhoonInfo.strong }}</span
        >
      </div>
      <div v-show="typhoonInfo.power && typhoonInfo.power !== ''" class="info">
        中心气压：{{ typhoonInfo.power }} 百帕
      </div>
      <div
        v-show="typhoonInfo.moveDir && typhoonInfo.moveDir !== ''"
        class="info"
      >
        移动方向：{{ typhoonInfo.moveDir }}
      </div>
      <div
        v-show="typhoonInfo.moveSpeed && typhoonInfo.moveSpeed !== ''"
        class="info"
      >
        移动速度：{{ typhoonInfo.moveSpeed }} 公里/小时
      </div>
      <div
        v-if="typhoonInfo.windRadius && typhoonInfo.windRadius.length > 0"
        class="wind-info"
      >
        <div class="wind-level">风圈半径</div>
        <div class="wind-level">东北</div>
        <div class="wind-level">东南</div>
        <div class="wind-level">西南</div>
        <div class="wind-level">西北</div>
        <div
          v-for="(item, index) in typhoonInfo.windRadius"
          :key="index + 'level'"
          class="wind-level"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="TFXZ">
import { ref, onMounted, inject, onUnmounted, reactive } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/toolRequest'
import GeoJSON from 'ol/format/GeoJSON.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { Fill, Stroke, Style, Text, Icon } from 'ol/style.js'
import Circle from 'ol/geom/Circle.js'
import { fromLonLat } from 'ol/proj'
import Feature from 'ol/Feature.js'
import Point from 'ol/geom/Point.js'
import chaoqiangtaifeng from 'src/assets/images/tools/typhoon/chaoqiangtaifeng.png'
import qiangredaifengbao from 'src/assets/images/tools/typhoon/qiangredaifengbao.png'
import qiangtaifeng from 'src/assets/images/tools/typhoon/qiangtaifeng.png'
import redaidiya from 'src/assets/images/tools/typhoon/redaidiya.png'
import redaifengbao from 'src/assets/images/tools/typhoon/redaifengbao.png'
import taifeng from 'src/assets/images/tools/typhoon/taifeng.png'
import Polygon from 'ol/geom/Polygon.js'
import LineString from 'ol/geom/LineString.js'
import Overlay from 'ol/Overlay.js'
import { unByKey } from 'ol/Observable'
import { useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
const getMap = inject<(map: any) => void>('getMap')
const typhoon = ref('')
const typhoonOptions = ref<any>([])
const similarTyphoon = ref([])
const similarTyphoonList = ref<any>([])
const message = useMessage()
// 获取形似台风
function getSimilarTyphoonList(val: any) {
  Api.getSimilarTyphoon({
    code: val,
    distance: 500,
    points: 7
  })
    .then((res: any) => {
      similarTyphoon.value = []
      similarTyphoonList.value = []
      res.forEach((element: any) => {
        similarTyphoonList.value.push(element.fmTyphoon)
      })
    })
    .catch(() => {})
}
const showPopup = ref(true)
const institution = ref('')
const institutionOptions = ref<any>([])
const source = new VectorSource()
const layer = new VectorLayer({
  zIndex: 11,
  source: source
})
let types = ref([])
layer.setProperties({
  layerType: '台风'
})
const windRadiusSource = new VectorSource()
const windRadiusLayer = new VectorLayer({
  zIndex: 10,
  source: windRadiusSource
})
const typhoonInfo = reactive<any>({})
function clearTyData() {
  windRadiusSource.refresh()
  source.refresh()
  typhoon.value = ''
  similarTyphoon.value = []
}
function getTyData() {
  if (!typhoon.value || !similarTyphoon.value) {
    message.warning('请选择查询条件')
    return false
  }

  // addTyphoon()
  windRadiusSource.refresh()
  source.refresh()
  Api.getTyphoonInfo({
    code: typhoon.value
  })
    .then((res: any) => {
      addTyphoon(res)
    })
    .catch(() => {})
  similarTyphoon.value.forEach((item: any) => {
    Api.getTyphoonInfo({
      code: item
    })
      .then((res: any) => {
        addTyphoon(res)
      })
      .catch(() => {})
  })
}
function addTyphoon(data: any) {
  const features: any = []
  const points: any = []
  data.forEach((item: any) => {
    let img = redaidiya
    if (item.strong === '热带低压(TD)') {
      img = redaidiya
    } else if (item.strong === '热带风暴(TS)') {
      img = redaifengbao
    } else if (item.strong === '强热带风暴(STS)') {
      img = qiangredaifengbao
    } else if (item.strong === '台风(TY)') {
      img = taifeng
    } else if (item.strong === '强台风(STY)') {
      img = qiangtaifeng
    } else if (item.strong === '超强台风(Super TY)') {
      img = chaoqiangtaifeng
    }
    const iconStyle = new Style({
      image: new Icon({
        anchor: [0.5, 0.5],
        src: img
      })
    })
    points.push([item.lng, item.lat])
    const iconFeature = new Feature({
      geometry: new Point([item.lng, item.lat])
    })
    iconFeature.setProperties(item)
    iconFeature.setStyle(iconStyle)
    features.push(iconFeature)
  })
  let lineFeature = new Feature({
    geometry: new LineString(points)
  })
  // 添加五个机构的预报台风
  if (data[data.length - 1].forecast) {
    data[data.length - 1].forecast.forEach((ele: any) => {
      let lineColor: any = 'rgb(255, 60, 78)'
      if (ele.tm === '中国') {
        lineColor = 'rgb(255, 60, 78)'
      } else if (ele.tm === '中国香港') {
        lineColor = 'rgb(254, 189, 0)'
      } else if (ele.tm === '日本') {
        lineColor = 'rgb(36, 188, 0)'
      } else if (ele.tm === '中国台湾') {
        lineColor = 'rgb(255, 0, 254)'
      } else if (ele.tm === '美国') {
        lineColor = 'rgb(4, 250, 247) '
      }
      const tmPoints: any = []
      ele.forecastpoints.forEach((item: any) => {
        item.tm = ele.tm
        let img = redaidiya
        if (item.strong === '热带低压') {
          img = redaidiya
        } else if (item.strong === '热带风暴') {
          img = redaifengbao
        } else if (item.strong === '强热带风暴') {
          img = qiangredaifengbao
        } else if (item.strong === '台风') {
          img = taifeng
        } else if (item.strong === '强台风') {
          img = qiangtaifeng
        } else if (item.strong === '超强台风') {
          img = chaoqiangtaifeng
        }
        const iconStyle = new Style({
          image: new Icon({
            anchor: [0.5, 0.5],
            src: img
          })
        })
        tmPoints.push([item.lng, item.lat])
        const iconFeature = new Feature({
          geometry: new Point([item.lng, item.lat])
        })
        iconFeature.setProperties(item)
        iconFeature.setStyle(iconStyle)
        features.push(iconFeature)
      })
      let tmLineFeature = new Feature({
        geometry: new LineString(tmPoints)
      })
      tmLineFeature.setStyle(
        new Style({
          stroke: new Stroke({
            color: lineColor,
            width: 2,
            lineDash: [5, 10] // 设置虚线样式
          })
        })
      )
      features.push(tmLineFeature)
    })
  }
  features.push(lineFeature)
  source.addFeatures(features)
}
function addWindRadius(data: any) {
  windRadiusSource.refresh()
  //  ne:东北 se:东南 sw:西南 nw:西北
  const features: any = []
  if (data.radius7Quad) {
    // 七级风圈
    const info = JSON.parse(data.radius7Quad)
    const center = [data.lng, data.lat]
    const EN7: any = getSectorPoints(center, info.ne, 0, 90, 30)
    EN7.shift()
    EN7.pop()
    const ES7: any = getSectorPoints(center, info.se, 90, 180, 30)
    ES7.shift()
    ES7.pop()
    const WS7: any = getSectorPoints(center, info.sw, 180, 270, 30)
    WS7.shift()
    WS7.pop()
    const WN7: any = getSectorPoints(center, info.nw, 270, 360, 30)
    WN7.shift()
    WN7.pop()
    const arr = [...EN7, ...ES7, ...WS7, ...WN7]
    arr.push(EN7[0])
    const feature = new Feature({
      geometry: new Polygon([arr])
    })
    feature.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#F4D000',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(244, 208, 0,0.3)'
        })
      })
    )
    features.push(feature)
  }
  if (data.radius10Quad) {
    // 十级风圈
    const info = JSON.parse(data.radius10Quad)
    const center = [data.lng, data.lat]
    const EN10: any = getSectorPoints(center, info.ne, 0, 90, 30)
    EN10.shift()
    EN10.pop()
    const ES10: any = getSectorPoints(center, info.se, 90, 180, 30)
    ES10.shift()
    ES10.pop()
    const WS10: any = getSectorPoints(center, info.sw, 180, 270, 30)
    WS10.shift()
    WS10.pop()
    const WN10: any = getSectorPoints(center, info.nw, 270, 360, 30)
    WN10.shift()
    WN10.pop()
    const arr10 = [...EN10, ...ES10, ...WS10, ...WN10]
    arr10.push(EN10[0])
    const feature = new Feature({
      geometry: new Polygon([arr10])
    })
    feature.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#F4D000',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(244, 208, 0,0.3)'
        })
      })
    )
    features.push(feature)
  }
  if (data.radius12Quad) {
    // 十级风圈
    const info = JSON.parse(data.radius12Quad)
    const center = [data.lng, data.lat]
    const EN12: any = getSectorPoints(center, info.ne, 0, 90, 30)
    EN12.shift()
    EN12.pop()
    const ES12: any = getSectorPoints(center, info.se, 90, 180, 30)
    ES12.shift()
    ES12.pop()
    const WS12: any = getSectorPoints(center, info.sw, 180, 270, 30)
    WS12.shift()
    WS12.pop()
    const WN12: any = getSectorPoints(center, info.nw, 270, 360, 30)
    WN12.shift()
    WN12.pop()
    const arr12 = [...EN12, ...ES12, ...WS12, ...WN12]
    arr12.push(EN12[0])
    const feature = new Feature({
      geometry: new Polygon([arr12])
    })
    feature.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#F4D000',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(244, 208, 0,0.3)'
        })
      })
    )
    features.push(feature)
  }
  windRadiusSource.addFeatures(features)
}
/**
 * @description 逆时针计算扇形风圈的点集合
 * @param center - {Array<String|Number>}中心点，例如[117.23,23.123]
 * @param radius - {String|Number} 半径km
 * @param startAngle - {String|Number} 起始角度（单位°）
 * @param endAngle - {String|Number} 结束角度（单位°）
 * @param pointNum - {String|Number} 返回构成的弧点个数，默认30
 * @return {Array}
 */
function getSectorPoints(
  center: any,
  radius: any,
  startAngle: any,
  endAngle: any,
  pointNum: any
) {
  radius = Number(radius) * 1000

  // if (!this.isProjected) {
  var MetersPerUnit = 111319.49079327358 //1度多少米
  radius = radius / MetersPerUnit //转化为度
  // }
  center = [Number(center[0]), Number(center[1])]
  startAngle = Number(startAngle)
  endAngle = Number(endAngle)
  pointNum = Number(pointNum || 30)

  var sin
  var cos
  var x
  var y
  var angle
  var points = []
  var pointsLL = []
  var lonlat = center
  points.push([center[0], center[1]])
  for (var i = 0; i <= pointNum; i++) {
    angle = startAngle + ((endAngle - startAngle) * i) / pointNum
    sin = Math.sin((angle * Math.PI) / 180)
    cos = Math.cos((angle * Math.PI) / 180)
    x = center[0] + radius * sin
    y = center[1] + radius * cos

    points[i + 1] = [x, y]
  }
  points.push([center[0], center[1]])
  for (var j = 0; j < points.length; j++) {
    pointsLL[j] = points[j]
  }

  return pointsLL
}
function clickTyphoon(evt: any, map: any) {
  if (evt.dragging) {
    return
  }
  const pixel = map.getEventPixel(evt.originalEvent)
  const feature = map.forEachFeatureAtPixel(pixel, function (feature: any) {
    return feature
  })
  if (feature) {
    const info: any = feature.getProperties()
    if (info) {
      if (info.id) {
        addWindRadius(info)
        // const coordinate = evt.coordinate
        // overlay.setPosition(coordinate)
      }
    }
  }
}
function displayFeatureInfo(evt: any, map: any) {
  if (evt.dragging) {
    return
  }
  showPopup.value = true
  const pixel = map.getEventPixel(evt.originalEvent)
  const feature = map.forEachFeatureAtPixel(pixel, function (feature: any) {
    return feature
  })
  if (feature) {
    const info: any = feature.getProperties()
    if (info) {
      if (info.lat) {
        const name = typhoonOptions.value.find(
          (item: any) => item.tfbh === info.tfbh
        )?.name
        typhoonInfo.name = name ? name : ''
        typhoonInfo.tm = info.tm
        typhoonInfo.time = info.time
        typhoonInfo.speed = info.speed
        typhoonInfo.pressure = info.pressure
        typhoonInfo.lng = info.lng
        typhoonInfo.lat = info.lat
        typhoonInfo.moveDir = info.moveDir
        typhoonInfo.moveSpeed = info.moveSpeed
        typhoonInfo.strong = info.strong
        typhoonInfo.windRadius = []
        if (info.radius7Quad) {
          typhoonInfo.windRadius.push('7级')
          const radius: any = JSON.parse(info.radius7Quad)
          typhoonInfo.windRadius.push(
            radius.ne,
            radius.se,
            radius.sw,
            radius.nw + '(KM)'
          )
          //  ne:东北 se:东南 sw:西南 nw:西北
        }
        if (info.radius10Quad) {
          typhoonInfo.windRadius.push('10级')
          const radius: any = JSON.parse(info.radius10Quad)
          typhoonInfo.windRadius.push(
            radius.ne,
            radius.se,
            radius.sw,
            radius.nw + '(KM)'
          )
        }
        if (info.radius12Quad) {
          typhoonInfo.windRadius.push('12级')
          const radius: any = JSON.parse(info.radius12Quad)
          typhoonInfo.windRadius.push(
            radius.ne,
            radius.se,
            radius.sw,
            radius.nw + '(KM)'
          )
        }
        const coordinate = evt.coordinate
        overlay.setPosition(coordinate)
      } else {
        overlay.setPosition(undefined)
      }
    } else {
      overlay.setPosition(undefined)
    }
  } else {
    overlay.setPosition(undefined)
  }
}
let overlay: any = null
let moveHandel: any = null
let clickHandel: any = null
function ctrlPopup(flag: any) {
  showPopup.value = flag
}
defineExpose({
  ctrlPopup
})
const isWarning = ref(false)
onMounted(() => {
  const currentRoute = useRoute()
  isWarning.value = currentRoute.name === 'alarmMaking'
  if (getMap) {
    getMap((map: any) => {
      map.addLayer(layer)
      map.addLayer(windRadiusLayer)
      const container: any = document.getElementById('popup')
      overlay = new Overlay({
        element: container
        // autoPan: {
        //   animation: {
        //     duration: 250
        //   }
        // }
      })
      map.addOverlay(overlay)
      // overlay.setPosition([116,39])
      moveHandel = map.on('pointermove', function (evt: any) {
        displayFeatureInfo(evt, map)
      })
      clickHandel = map.on('click', function (evt: any) {
        clickTyphoon(evt, map)
      })
    })
  }
  Api.getTyList()
    .then((res: any) => {
      typhoonOptions.value = res.map((item: any) => {
        item.bhName = `${item.tfbh}-${item.name}`
        return item
      })
    })
    .catch(() => {})
})
onUnmounted(() => {
  if (getMap) {
    getMap((map: any) => {
      map.removeLayer(layer)
      map.removeLayer(windRadiusLayer)
      unByKey(moveHandel)
      unByKey(clickHandel)
    })
  }
})
</script>

<style lang="scss" scoped>
.ol-popup1 {
  position: fixed;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #cccccc;
  bottom: 12px;
  left: -50px;
  min-width: 280px;
  min-width: 335px;
  font-size: 14px;
  line-height: 22px;
  #popup1-content {
    .info {
      height: 22px;
      /**
      typhoonInfo.strong === '热带低压(TD)' ? 'td' : '',
            typhoonInfo.strong === '热带风暴(TS)' ? 'ts' : '',
            typhoonInfo.strong === '强热带风暴(STS)' ? 'sts' : '',
            typhoonInfo.strong === '台风(TY)' ? 'ty' : '',
            typhoonInfo.strong === '强台风(STY)' ? 'sty' : '',
            typhoonInfo.strong === '超强台风(SuperTY)' ? 'superTy' : ''
      */
      .strong {
        padding: 3px;
        color: #fff;
        &.td {
          background-color: #0083fd;
        }
        &.ts {
          background-color: #6bb654;
        }
        &.sts {
          background-color: #fff300;
        }
        &.ty {
          background-color: #fe7b0e;
        }
        &.sty {
          background-color: #f90102;
        }
        &.superTy {
          background-color: #9a5bbc;
        }
      }
    }
    .wind-info {
      display: flex;
      flex-wrap: wrap;
      margin-top: 10px;
      .wind-level {
        width: 56px;
        text-align: center;
        height: 22px;
        line-height: 22px;
      }
    }
  }
}
.ol-popup1:after,
.ol-popup1:before {
  top: 100%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.ol-popup1:after {
  border-top-color: white;
  border-width: 10px;
  left: 48px;
  margin-left: -10px;
}
.ol-popup1:before {
  border-top-color: #cccccc;
  border-width: 11px;
  left: 48px;
  margin-left: -11px;
}
.ol-popup1-closer {
  text-decoration: none;
  position: absolute;
  top: 2px;
  right: 8px;
}
.ol-popup1-closer:after {
  content: '✖';
}

.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;
  .query-title {
    white-space: nowrap;
    width: 80px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }
  .query-info {
    width: 225px;
  }
}
.jigou {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-right: 30px;
  .jigou-item {
    width: 130px;
    height: 32px;
    display: flex;
    align-items: center;
    .line-color {
      width: 70px;
      height: 1px;
      border: 1px dashed;
      &.zhong {
        border-color: rgb(255, 60, 78);
      }
      &.xiang {
        border-color: rgb(254, 189, 0);
      }
      &.ri {
        border-color: rgb(36, 188, 0);
      }
      &.tai {
        border-color: rgb(255, 0, 254);
      }
      &.mei {
        border-color: rgb(4, 250, 247);
      }
    }
    .line-name {
      margin-left: 10px;
      white-space: nowrap;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
  }
}
.my-btn {
  width: 140px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}
.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}
.legend-container {
  width: 127px;
  height: 190px;
  position: fixed;
  bottom: 50px;
  left: 460px;
  background: url('src/assets/images/tools/typhoon/legend.png') no-repeat;
  background-size: 100% 100%;
}
.is-warning {
  left: 16px;
}
</style>
