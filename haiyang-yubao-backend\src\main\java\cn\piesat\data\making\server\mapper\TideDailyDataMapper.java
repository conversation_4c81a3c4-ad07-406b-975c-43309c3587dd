package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.TideDailyData;
import cn.piesat.data.making.server.vo.TideDailyWarnLevelVO;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface TideDailyDataMapper {
    TideDailyDataMapper INSTANCE = Mappers.getMapper(TideDailyDataMapper.class);

    TideDailyWarnLevelVO entityToVO(TideDailyData tideDailyData);

    List<TideDailyWarnLevelVO> entitiesToVOs(List<TideDailyData> tideDailyDataList);

    @Mapping(target = "id",ignore = true)
    @Mapping(target = "datum", constant = "datum")
    @Mapping(target = "height",expression = "java(source.getHeight() + difference)")
    TideDailyData mapSingle(TideDailyData source, @Context Integer difference);

    // 列表映射方法
    default List<TideDailyData> handle85(List<TideDailyData> list, Integer difference) {
        return list.stream()
                .map(source -> mapSingle(source, difference))
                .collect(Collectors.toList());
    }
}
