import { computed, ref } from 'vue'
import type { RowData } from '../seaAreaForecastHooks/useContentTable'

export function useParagraph(opt: {
  areaGetter: () => { name: string; index: number; city: string }[]
  contentArr: () => RowData[]
}) {
  const paragraph = ref('')

  const getAreaName = (index: number) =>
    opt.areaGetter().find(i => i.index === index)?.name
  const getCityName = (index: number) =>
    opt.areaGetter().find(i => i.index === index)?.city

  const getWords = (
    areaNameList: string[],
    cityNameList: string[],
    swhInterval: string,
    langjiInterval: string
  ) => {
    return `${areaNameList.join('、')}近岸海域将出现${swhInterval}米的${langjiInterval}。`
  }

  const cptText = computed(() => {
    const textArr: string[] = []
    opt.contentArr().forEach(item => {
      const areaNameList: string[] = []
      const cityNameList: string[] = []
      const indexArr = item.index.split(',').map(i => Number(i))
      indexArr.forEach(i => {
        areaNameList.push(getAreaName(i) || '-')
        cityNameList.push(getCityName(i) || '-')
      })
      const words = getWords(areaNameList, cityNameList, item.swhInterval, item.langjiInterval)
      textArr.push(words)
    })
    return textArr.join('')
  })

  return {
    paragraph,
    // cptText,
    updateParagraph() {
      paragraph.value = cptText.value
    },
    setParagraph(text: string) {
      paragraph.value = text
    }
  }
}
