import { inject, provide, ref } from 'vue'
import { GetterMap, IBridge } from './types'

function typedKeys<T extends object>(obj: T): Array<keyof T> {
  return Object.keys(obj) as Array<keyof T>
}

/**
 * 组件数据依赖收集
 */
export function useBridge<T extends Record<string, any>>(
  bridgeKey: symbol | string = 'bridge'
) {
  const fields: GetterMap<T> = {}

  const parent = inject<IBridge<any> | undefined>(bridgeKey, void 0)

  const bridge: IBridge<T> = {
    register(key, getter) {
      fields[key] = getter
    },
    collect() {
      const result: Partial<T> = {}
      for (const key in fields) {
        const getter = fields[key]
        if (getter) result[key] = getter()
      }
      return result
    },
    parent
  }

  // 扁平合并子桥数据
  if (parent) {
    // 将当前 bridge 的所有 register 结果转发给 parent
    const childCollect = bridge.collect

    queueMicrotask(() => {
      const collected = childCollect()
      for (const key in collected) {
        parent.register(key, () => collected[key])
      }
    })
  }

  provide(bridgeKey, bridge)

  return bridge
}
