package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.BuoyStationDataDTO;
import cn.piesat.data.making.server.entity.BuoyStationData;
import cn.piesat.data.making.server.vo.BuoyStationDataVO;
import cn.piesat.data.making.server.vo.FillMapVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 浮标站数据表服务接口
 *
 * <AUTHOR>
 */
public interface BuoyStationDataService extends IService<BuoyStationData> {

    /**
     * 查询列表
     */
    List<BuoyStationDataVO> getStationDataList(BuoyStationDataDTO dto);

    /**
     * 查询列表
     */
    FillMapVO getGeoRangeDataList(BuoyStationDataDTO dto);
}




