package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.ForecastTemplateColumnDao;
import cn.piesat.data.making.server.entity.ForecastTemplateColumn;
import cn.piesat.data.making.server.service.ForecastTemplateColumnService;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 预报模板-列定义服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastTemplateColumnServiceImpl extends ServiceImpl<ForecastTemplateColumnDao, ForecastTemplateColumn> implements ForecastTemplateColumnService {

    @Override
    public List<ForecastTemplateColumn> getList(Long templateId) {
        return this.list(createQueryWrapper(templateId));
    }

    @Override
    public void saveList(Long templateId, List<ForecastTemplateColumn> list) {
        LambdaQueryWrapper<ForecastTemplateColumn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForecastTemplateColumn::getForecastTemplateId, templateId);
        this.remove(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(column -> {
                String valueHandle = column.getValueHandle();
                String handle = valueHandle.substring(0, 1).toUpperCase() + valueHandle.substring(1);
                if ("浪级".equals(column.getColumnName())) {
                    column.setColumnCode("langji" + handle);
                } else {
                    column.setColumnCode(column.getElementCode() + handle);
                }
            });
            //检查是否存在重复的要素编码和数值处理方式（columnCode）
            List<ForecastTemplateColumn> cbList = list.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(tc -> tc.getColumnCode()))), ArrayList::new));
            boolean hasDuplicates = list.size() != cbList.size();
            if (hasDuplicates) {
                throw new BusinessException("存在重复的列！");
            }
            this.saveBatch(list);
        }
    }

    private LambdaQueryWrapper<ForecastTemplateColumn> createQueryWrapper(Long templateId) {
        LambdaQueryWrapper<ForecastTemplateColumn> queryWrapper = new LambdaQueryWrapper<>();
        if (templateId != null) {
            queryWrapper.eq(ForecastTemplateColumn::getForecastTemplateId, templateId);
        }
        return queryWrapper.orderByAsc(ForecastTemplateColumn::getCreateTime);
    }
}





