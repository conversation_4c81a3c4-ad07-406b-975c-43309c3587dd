/*
 * @Author: <PERSON>
 * @Date: 2024-01-18 22:44:22
 * @LastEditTime: 2024-01-18 22:47:17
 * @LastEditors: Alex
 */
import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_AUTH_BASE_URL
const axiosInstance = getAxios(baseUrl)

type loginType = {
  loginName: string
  loginPass: string
}

const login = (data: loginType) => {
  return axiosInstance.post('/api/login', data)
}

const logout = () => {
  return axiosInstance.post('/api/logout')
}

export default {
  login,
  logout
}
