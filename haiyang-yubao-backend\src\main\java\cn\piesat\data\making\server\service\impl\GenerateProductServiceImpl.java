package cn.piesat.data.making.server.service.impl;


import cn.hutool.core.io.FileUtil;
import cn.piesat.data.making.server.enums.ProductEnum;
import cn.piesat.data.making.server.service.GenerateProductService;
import cn.piesat.data.making.server.utils.GenerateFileUtil;
import cn.piesat.data.making.server.utils.WordUtil;
import cn.piesat.data.push.common.vo.task.ManualTaskProductVO;
import cn.piesat.data.push.common.vo.task.ManualTaskVO;
import cn.piesat.data.push.starter.service.PushManualTaskServiceAgent;
import cn.piesat.webconfig.exception.BusinessException;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

@Service
public class GenerateProductServiceImpl implements GenerateProductService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());
    @Value("${piesat.base-output-path}")
    public String baseOutputPath;
    @Autowired
    private PushManualTaskServiceAgent pushManualTaskService;

    @Override
    public Long sendPushServer(String taskName, String wordFilePath, String htmlFilePath, String smsFilePath, ProductEnum word, ProductEnum html, ProductEnum sms, Date time) {
        //发送推送服务
        ManualTaskProductVO wordProduct = new ManualTaskProductVO();
        wordProduct.setProductId(word.getProductId());
        wordProduct.setFilePath(wordFilePath);
        wordProduct.setProductName(word.getProductName());
        wordProduct.setDataTime(time.getTime());
        ManualTaskProductVO htmlProduct = new ManualTaskProductVO();
        htmlProduct.setProductId(html.getProductId());
        htmlProduct.setFilePath(htmlFilePath);
        htmlProduct.setProductName(html.getProductName());
        htmlProduct.setDataTime(time.getTime());
        ManualTaskProductVO smsProduct = new ManualTaskProductVO();
        wordProduct.setProductId(sms.getProductId());
        wordProduct.setFilePath(smsFilePath);
        wordProduct.setProductName(sms.getProductName());
        wordProduct.setDataTime(time.getTime());
        ManualTaskVO manualTaskVO =  new ManualTaskVO();
        manualTaskVO.setTaskName(taskName);
        manualTaskVO.setSystem("data-making-server");
        manualTaskVO.setMode(1);
        manualTaskVO.setProducts(Arrays.asList(wordProduct,htmlProduct,smsProduct));
        Long manualTask = pushManualTaskService.createManualTask(manualTaskVO);
        pushManualTaskService.startManualTask(manualTask);
        return manualTask;
    }

    @Override
    public void generateSms(String smsFilePath, String param) {
        FileUtil.writeUtf8String(param,smsFilePath);
    }

    @Override
    public void generateWord(String filePath, String template, Object param) {
        //拼接产出文件地址
        try {
            GenerateFileUtil.createFile(filePath);
            Configure config = Configure.builder().useSpringEL().build();
            XWPFTemplate.compile(template,config).render(param).writeAndClose(new FileOutputStream(filePath));
        } catch (Exception e) {
            logger.error("创建word失败",e);
            throw new BusinessException("创建word文件失败");
        }
    }

    @Override
    public void generateWord(String filePath, String template, Object param, String... tableNames) {
        //拼接产出文件地址
        try {
            GenerateFileUtil.createFile(filePath);
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
            ConfigureBuilder builder = Configure.builder().useSpringEL();
            for (String tableName : tableNames) {
                builder.bind(tableName, policy);
            }
            Configure config = builder.build();
            XWPFTemplate.compile(template,config).render(param).writeAndClose(new FileOutputStream(filePath));
        } catch (Exception e) {
            logger.error("创建word失败",e);
            throw new BusinessException("创建word文件失败");
        }
    }

    @Override
    public void generateHtml(String wordFilePath, String htmlFilePath) {
        //拼接产出文件地址
        try {
            WordUtil.convertToHtml(wordFilePath, htmlFilePath);
        } catch (Exception e) {
            logger.error("创建html失败", e);
            throw new BusinessException("创建html文件失败");
        }
    }

}
