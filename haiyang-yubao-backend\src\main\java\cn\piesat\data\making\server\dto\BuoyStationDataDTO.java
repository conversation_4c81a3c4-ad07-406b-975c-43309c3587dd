package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 浮标站数据表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BuoyStationDataDTO implements Serializable {

    private static final long serialVersionUID = -72255254946458345L;

    public interface Query {
    }

    public interface RangeQuery {
    }

    /**
     * 开始时间
     **/
    @NotNull(message = "开始时间不能为空", groups = {Query.class, RangeQuery.class})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     **/
    @NotNull(message = "结束时间不能为空", groups = {Query.class, RangeQuery.class})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 浮标类型 3米浮标站3m 10米浮标站10m 波浪谱浮标站waveSpectrum
     **/
//    @NotEmpty(message = "浮标站不能为空", groups = {Query.class, RangeQuery.class})
    private String type;
    /**
     * 空间范围
     **/
    @NotBlank(message = "空间范围不能为空", groups = {RangeQuery.class})
    private String geoRange;
    /**
     * 浮标站
     **/
    @NotEmpty(message = "浮标站不能为空", groups = {Query.class})
    private List<String> buoyStationCodeList;
}



