package cn.piesat.data.making.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.piesat.data.making.server.dao.AlarmGuideLevelDao;
import cn.piesat.data.making.server.entity.AlarmGuideLevel;
import cn.piesat.data.making.server.service.AlarmGuideLevelService;
import org.springframework.stereotype.Service;

/**
 * (AlarmGuideLevel)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-18 15:06:28
 */
@Service("AlarmGuideLevelService")
public class AlarmGuideLevelServiceImpl extends ServiceImpl<AlarmGuideLevelDao, AlarmGuideLevel> implements AlarmGuideLevelService {

}

