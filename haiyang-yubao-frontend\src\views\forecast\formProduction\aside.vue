<template>
  <div class="forecast-aside common-aside">
    <!-- 预报列表/审核预览-->
    <div class="aside-header">
      <div class="title-wrap">
        <h3>预报列表</h3>
        <n-button
          type="primary"
          :bordered="false"
          :focusable="false"
          :disabled="tabIndex !== 2"
          @click="onAuditPreview"
          >审核预览
        </n-button>
      </div>
      <Tab :active="tabIndex" :tab-list="tabList" @change="changeTab" />
    </div>
    <!-- 预报列表状态tab -->
    <div v-loading="loading" class="data-list">
      <div v-if="taskInfo.length" class="tips">
        <template v-for="item in taskInfo" :key="item.name"
          >{{ item.name }}:{{ item.value ? item.value : 0 }}个
        </template>
      </div>
      <qx-no-data v-if="dataList.length === 0" />
      <template v-else>
        <div
          v-for="item in dataList"
          :key="item.id"
          class="data-item"
          :class="item.id === currentId ? 'active' : ''"
          @click="onChangeProduct(item)"
        >
          <div class="data-item-title">{{ item.name }}</div>
          <div class="data-item-time">
            <span>每天</span>
            <div class="time">{{ `${item.startTime}-${item.endTime}` }}</div>
          </div>
          <i class="icon dage" :class="getIconClass(item.status)"></i>
        </div>
      </template>
    </div>
  </div>
  <!-- 审核预览弹框 -->
  <qx-dialog
    v-model:visible="dialogVisible"
    title="审核预览"
    class="audit-preview"
    width="1372px"
    height="782px"
    @update:visible="dialogVisible = false"
  >
    <template #content>
      <div class="btns-group">
        <qx-button @click="onRelease">直接发布</qx-button>
        <qx-button class="primary" @click="onDownLoad">下载</qx-button>
        <!-- <qx-button>发布确认</qx-button> -->
      </div>
      <div v-loading="productLoading" class="dialog-content">
        <div class="menu-aside">
          <h3>产品目录</h3>
          <div class="menu-list">
            <div
              v-for="(item, index) in productMenuList"
              :key="item.id"
              class="menu-item"
              :class="index === currentProductIndex ? 'active' : ''"
              @click="getCurrentProduct(index, item)"
            >
              <!-- <i class="icon" :class="getClassForType(item)"></i> -->
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="product-info">
          <div class="title d-flex flex-justify-between flex-align-center">
            <h3>产品预览</h3>
            <div>
              <qx-button class="primary" @click="onSave">保存</qx-button>
            </div>
          </div>

          <template
            v-if="
              currentFileType.includes('txt') || currentFileType.includes('xml')
            "
          >
            <n-input
              v-model:value="fileContent"
              placeholder="请输入"
              type="textarea"
              size="small"
              :autosize="{
                minRows: 6,
                maxRows: 8
              }"
            />
          </template>
          <template v-else>
            <!-- <iframe :src="fileUrl" width="100%" height="100%" frameborder="0"/> -->
            <OfficeEditor
              v-if="wordUrl"
              v-loading="loading"
              :url="wordUrl"
              :callback-url="callbackUrl"
            ></OfficeEditor>
          </template>
        </div>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { Tab } from 'src/components/Tab'
import { ref, onMounted, reactive, inject, Ref } from 'vue'
import { QxDialog } from 'src/components/QxDialog'
import { QxButton } from 'src/components/QxButton'
import { OfficeEditor } from 'src/components/OfficeEditor'
import { useMessage } from 'naive-ui'
import Api, { pushReleaseForecastToOther } from 'src/requests/forecast'
import eventBus from 'src/utils/eventBus'
import { createDiscreteApi } from 'naive-ui'
import { IForecastItem } from 'src/views/forecast/formProduction/asideHooks/types'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import {
  bridgeKey,
  IForecastProductBridge
} from 'src/views/forecast/formProduction/forecastProductHooks/types'

const { dialog } = createDiscreteApi(['dialog'])
const reportTime = inject<Ref<string>>('reportTime')!
const dataSource = inject<Ref<'forecast' | 'grid'>>('dataSource')!

const message = useMessage()
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  }
})

/*
 * tab start
 */
const tabIndex = ref(1)
const tabList = reactive<any[]>([
  {
    label: '待制作',
    id: 1,
    iconColor: '#F90102'
  },
  {
    label: '已制作',
    id: 2,
    iconColor: '#3FC8CB'
  },
  {
    label: '已发布',
    id: 3,
    iconColor: '#82B437'
  }
])

function changeTab(val: number) {
  tabIndex.value = val
  dataList.value = []
  currentId.value = ''
  queryForecastTaskList()
  emits('changeTab', val)
}

function getIconClass(status: string | number) {
  if (status == 1) {
    return 'icon-dage'
  } else if (status == 2) {
    return 'icon-dage-maked'
  } else if (status == 3) {
    return 'icon-dage-published'
  }
}

/**
 * tab end
 */
const loading = ref(true)
const emits = defineEmits(['changeProduct', 'changeTab'])
const currentId = ref('') // 预报列表当前选中的id
const dataList = ref<any[]>([])
let taskInfo = ref<any[]>([])

// 左侧列表点击切换事件
function onChangeProduct(item: any) {
  console.log(props.isEdit, 'isEdit')
  if (item.id !== currentId.value && props.isEdit) {
    dialog.warning({
      title: '提示',
      content: `是否保存当前数据？${
        props.isEdit ? '保存后将退出编辑状态' : ''
      }`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        emits('changeProduct', item, true)
        currentId.value = item.id
      },
      onNegativeClick: () => {
        emits('changeProduct', item)
        currentId.value = item.id
      }
    })
  } else {
    emits('changeProduct', item)
    currentId.value = item.id
  }
}

// 查询预报任务列表
function queryForecastTaskList() {
  Api.queryForecastTaskList(tabIndex.value)
    .then(res => {
      dataList.value = res as any
      loading.value = false
      if (dataList.value.length != 0) {
        currentId.value = dataList.value[0]['id']
        emits('changeProduct', dataList.value[0], false)
      } else {
        emits('changeProduct', '')
      }
    })
    .catch(() => {
      emits('changeProduct', '')
      loading.value = false
      message.error('获取数据失败')
    })
}

// 查询预报列表统计情况 -- 左侧列表上的统计数据 待制作 已制作 已发布
async function queryForecastTaskInfo() {
  try {
    let res = (await Api.queryForecastTaskInfo()) as any
    if (res) {
      let result: any = []
      Object.keys(res).forEach((item: any) => {
        let obj = {
          name: item,
          value: res[item]
        }
        result.push(obj)
      })
      taskInfo.value = result
    }
  } catch (error) {
    console.error(error, 'error-----forecast-product-aside')
  }
}

/**
 * word 编辑器 start
 */
let productLoading = ref(true)
let wordUrl = ref('')
let callbackUrl = ref('')

/**
 * word 编辑器 end
 */

/**
 * 审核预览弹窗 start
 */
const productMenuList = ref<any[]>([])
const currentProductIndex = ref(0)
let currentFileType = ref('')
let fileContent = ref('')
let filePath = ref('')
const dialogVisible = ref<boolean>(false)

// 审核预览按钮
function onAuditPreview() {
  Api.getForecastProductRecord()
    .then((res: any) => {
      if (res.length) {
        productMenuList.value = res
        callbackUrl.value = config.onlyOfficeCallBack + 'record/' + res[0]?.id
        wordUrl.value = config.onlyOfficeServerUrl + res[0].fileUrl
        // fileUrl.value = config.kkFileUrl +decodeURIComponent(wordUrl.value)
      }
      productLoading.value = false
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
      productLoading.value = false
    })
  dialogVisible.value = true
}

// 获取当前选中产品
function getCurrentProduct(index: number, item: any) {
  currentProductIndex.value = index
  filePath.value = item?.fileUrl
  let type = item.fileUrl.substring(item.fileUrl.lastIndexOf('.') + 1)
  currentFileType.value = type
  if (type.includes('txt') || type.includes('xml')) {
    readFile(config.onlyOfficeServerUrl + item?.fileUrl)
  } else {
    callbackUrl.value = config.onlyOfficeCallBack + 'record/' + item?.id
    wordUrl.value = config.onlyOfficeServerUrl + item.fileUrl
    // fileUrl.value = config.kkFileUrl +decodeURIComponent(wordUrl.value)
  }
}

// 读取txt/xml文件
function readFile(url: string) {
  console.log(url, 'url=------fetch')
  fetch(url)
    .then(response => response.text())
    .then(data => {
      fileContent.value = data
    })
    .catch(() => {
      message.warning('暂无数据')
    })
}

// 直接发布
function onRelease() {
  productLoading.value = true

  const pushDataToOther = () => {
    const dataArr = dataList.value as IForecastItem[]
    const pArr = dataArr.map(item => {
      return Api.getLastForecastTimeV2()
        .then(lastObj => {
          return pushReleaseForecastToOther({
            // TODO: 饮鸩止渴
            dataSource: 'grid',
            reportTime: reportTime.value,
            taskId: item.id
          }) as unknown as Promise<
            | { success: true; data: IForecastItem }
            | { success: false; data: null }
          >
        }).then(res => res)
        .catch(err => {
          console.warn(err)
          return { success: false, data: null }
        })
    })
    return Promise.all(pArr)
  }
  pushDataToOther()
    .then(_ => {
      // alert('push done')
      return Api.releaseForecastProductRecord()
    })
    .then(() => {
      message.success('操作成功')
      dialogVisible.value = false

      queryForecastTaskList()
      queryForecastTaskInfo()
    })
    .finally(() => {
      productLoading.value = false
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '操作失败')
    })

  // Api.releaseForecastProductRecord()
  //   .then(() => {
  //     message.success('操作成功')
  //     dialogVisible.value = false
  //
  //     queryForecastTaskList()
  //     queryForecastTaskInfo()
  //   })
  //   .finally(() => {
  //     productLoading.value = false
  //   })
  //   .catch(e => {
  //     let { msg = null } = e?.response?.data || {}
  //     message.error(msg || '操作失败')
  //   })
}

// 下载
function onDownLoad() {
  Api.downloadForecast()
    .then((res: any) => {
      const link = document.createElement('a')
      const _fileName = decodeURIComponent(res.fileName)
      const blob = new Blob([res.data], { type: 'application/zip' })
      const url = window.URL.createObjectURL(blob)
      link.href = url
      link.download = _fileName // 下载的文件名称
      link.click()
      window.URL.revokeObjectURL(url)
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '下载失败')
    })
}

function onSave() {
  const params = {
    path: filePath.value,
    content: fileContent.value
  }

  Api.saveFileContent(params)
    .then(() => {
      message.success('保存成功')
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '保存成功')
    })
}

/**
 * 审核预览弹窗 end
 */

onMounted(() => {
  queryForecastTaskList()
  queryForecastTaskInfo()

  // 提交后下发updateForecast事件,更新任务列表,获取任务统计信息
  eventBus.on('updateForecast', () => {
    queryForecastTaskList()
    queryForecastTaskInfo()
  })
})

const bridge = inject<IBridge<IForecastProductBridge>>(bridgeKey)
bridge?.register('tabIndex', () => tabIndex.value)
</script>

<style lang="scss" scoped>
.forecast-aside {
  height: calc(100vh - 104px) !important;

  .ant-modal-footer {
    text-align: center !important;
  }

  .tips {
    box-sizing: border-box;
    padding: 3px 22px 2px 23px;
    background: #efefef;
    text-align: center;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
    font-style: normal;
    text-transform: none;
    margin-bottom: 16px;
  }

  .data-list {
    box-sizing: border-box;
    padding: 15px;
    background: #fafafa;
    overflow-y: auto;
    flex: 1;

    .data-item {
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      margin-bottom: 7px;
      position: relative;
      cursor: pointer;

      .icon.dage {
        position: absolute;
        top: 0;
        right: 37px;
      }

      &.active {
        border: 1px solid #3c83ed;
      }

      &-title {
        background: linear-gradient(180deg, #ffffff 0%, #e9eff3 100%);
        border-radius: 4px;
        box-sizing: border-box;
        padding: 9.5px 0 9.5px 13px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 600;
        font-size: 14px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      &-time {
        display: flex;
        align-items: center;
        height: 38px;

        span {
          padding: 0 7px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          line-height: 16px;
          font-style: normal;
          text-transform: none;
          flex-shrink: 0;
        }

        .time {
          // padding: 10px 6px;
          padding-left: 6px;
          border-left: 1px solid rgba($color: #000000, $alpha: 0.1);
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 13px;
          color: #222222;
          line-height: 16px;
          font-style: normal;
          text-transform: none;
          flex: 1;
        }
      }
    }
  }
}

.audit-preview {
  :deep(.qx-dialog) {
    display: flex;
    flex-direction: column;

    .qx-dialog-header {
      background: url(src/assets/images/forecast/dialog-bg.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: none;
    }

    .dialog-content {
      flex: 1;
    }
  }

  .btns-group {
    box-sizing: border-box;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 14px 0 14px 26px;
    padding-right: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .dialog-content {
    box-sizing: border-box;
    padding: 13px;
    display: flex;
  }

  .menu-aside {
    box-sizing: border-box;
    padding: 14px 0 14px 19px;
    width: 245px;
    background: #fafcfe;
    border-radius: 4px;
    border: 1px solid #e7f0fb;
    margin-right: 11px;

    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
      margin-bottom: 15px;
    }

    .menu-item {
      display: flex;
      align-items: center;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 16px;
      margin-bottom: 14px;
      cursor: pointer;

      i.icon {
        margin-right: 4px;
      }

      &.active {
        color: #3a78f7;
      }
    }
  }

  .product-info {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e7f0fb;
    flex: 1;
    display: flex;
    flex-direction: column;

    .title {
      box-sizing: border-box;
      padding: 13px 19px;
    }

    h3 {
      background: #fafcfe;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
    }

    .ql-toolbar.ql-snow,
    .ql-container.ql-snow {
      border: none;
    }

    .ql-toolbar.ql-snow {
      border-top: 1px solid rgba(0, 0, 0, 0.1);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      .ql-formats {
        margin-right: 0;
      }
    }
  }
}
</style>
