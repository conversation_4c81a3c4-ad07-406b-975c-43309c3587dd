/*
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-30 15:05:54
 * @LastEditors: xuli<PERSON>e <EMAIL>
 * @LastEditTime: 2024-10-28 16:53:33
 * @FilePath: \hainan-jianzai-web\src\components\Bubble\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @descripion:
 * @param {Viewer} viewer
 * @param {Cartesian2} position
 * @param {String} title
 * @return {*}
 */

// import Vue from 'vue'
import { createVNode, render } from "vue"
import Label from './index.vue'
import { Overlay } from 'ol'
import DragPan from 'ol/interaction/DragPan'
// const WindowVm = Vue.extend(Label)
export default class Bubble {
  constructor(val) {
    this.map = val.map
    //  this.height = val.height;
    this.position = val.position
    // const title = val.monitoItems.data.name;
    // const state = val.monitoItems.data.state;
    this.lineFeature = val.lineFeature
    this.startPoint = val.startPoint
    this.source = val.source
    this.feature = val.feature
    this.showChartFlag = val.showChartFlag
    this.vmInstance = createVNode(Label,{
      propsData: {
        ...val.monitoItems // 父组件传值
      }
    })//.$mount() // 根据模板创建一个面板

    this.vmInstance.closeEvent = e => {
      this.windowClose()
    }

    let node = document.getElementById('openlayers-map')

    render(this.vmInstance, node)
    val.map.getTargetElement().appendChild(this.vmInstance.el) // 将字符串模板生成的内容添加到DOM上

    this.addPostRender()
  }

  // 渲染
  addPostRender() {
    const element = this.vmInstance.el
    // const title = this.vmInstance.$refs.dragTitle
    var popup = new Overlay({
      element: element,
      positioning: 'top-left',
      stopEvent: false,
      dragging: false,
      offset: [0, 0]
    })
    popup.setPosition(this.position)
    this.map.addOverlay(popup)

    // display popup on click
    // var dragPan
    // this.map.getInteractions().forEach(function (interaction) {
    //   if (interaction instanceof DragPan) {
    //     dragPan = interaction
    //   }
    // })

    // title.addEventListener('mousedown', function (evt) {
    //   dragPan.setActive(false)
    //   popup.set('dragging', true)
    // })
    // this.map.on('pointermove', evt => {
    //   var startPoint = this.startPoint
    //   if (popup.get('dragging')) {
    //     var dd2 = this.map.getPixelFromCoordinate(evt.coordinate)
    //     var newX = dd2[0] - 0 // 减去偏移量
    //     var newY = dd2[1] - 0
    //     var newPoint = this.map.getCoordinateFromPixel([newX, newY])
    //     popup.setPosition(newPoint)
    //     this.lineFeature
    //       .getGeometry()
    //       .setCoordinates([startPoint, evt.coordinate])
    //   }
    // })
    // this.map.on('pointerup', function (evt) {
    //   if (popup.get('dragging') === true) {
    //     dragPan.setActive(true)
    //     popup.set('dragging', false)
    //   }
    // })
  }
  // 关闭
  windowClose() {
    if (this.vmInstance) {
      // this.source.removeFeature(this.feature)
      // this.source.removeFeature(this.lineFeature)
      this.vmInstance.el.style.display = 'none'
      this.vmInstance.el.remove()
      this.vmInstance.destroy()
    }
    // this.vmInstance.$el.style.display = "none"; //删除dom
    // this.viewer.scene.postRender.removeEventListener(this.postRender, this); // 移除事件监听
  }
}
