package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmSchedulingMainTableDTO;
import cn.piesat.data.making.server.entity.FmSchedulingMainTable;
import cn.piesat.data.making.server.vo.FmSchedulingMainTableVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:01
 */
public interface FmSchedulingMainTableService extends IService<FmSchedulingMainTable>{

        /**
         * 根据参数查询分页
         */
        PageResult<FmSchedulingMainTableVO> getPage(FmSchedulingMainTableDTO dto,PageParam pageParam);

        /**
         * 根据参数查询列表
         */
        List<FmSchedulingMainTableVO> getList(FmSchedulingMainTableDTO dto);

        /**
         * 根据id查询数据
         */
        FmSchedulingMainTableVO getById();

        /**
         * 保存数据
         */
        void save(FmSchedulingMainTableDTO dto);

        /**
         * 批量保存数据
         */
        void saveList(List<FmSchedulingMainTableDTO> dtoList);

        /**
         * 根据id删除数据
         */
        void deleteById(Long id);

        /**
         * 根据idList批量删除数据
         */
        void deleteByIdList(List<Long> idList);
        }
