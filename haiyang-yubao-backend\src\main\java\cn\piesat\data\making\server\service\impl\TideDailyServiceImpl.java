package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.dao.StationDao;
import cn.piesat.data.making.server.dao.TideDailyDataDao;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.entity.TideDailyData;
import cn.piesat.data.making.server.entity.TideDailyHourData;
import cn.piesat.data.making.server.enums.DatumEnum;
import cn.piesat.data.making.server.enums.TideWarnLevel;
import cn.piesat.data.making.server.model.TideDayInfo;
import cn.piesat.data.making.server.model.TideHourInfo;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.service.TideDailyHourService;
import cn.piesat.data.making.server.vo.TideDailyWarnLevelVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TideDailyServiceImpl extends ServiceImpl<TideDailyDataDao, TideDailyData> {

    @Resource
    private TideDailyHourService tideDailyHourService;
    @Resource
    private TideDailyDataDao tideDailyDataDao;
    @Resource
    private StationService stationService;
    @Resource
    private StationDao stationDao;

    /**
     * 获取潮位站日高低潮数据信息
     *
     * @param stationIds 潮位站ID
     * @param startTime  起始时间
     * @param endTime    结束时间
     * @param datum
     * @return
     */
    public List<TideDailyWarnLevelVO> getTideDailyListByStation(List<Long> stationIds, Date startTime, Date endTime, String datum) {
        if (stationIds == null || stationIds.isEmpty()) {
            throw new BusinessException("台站参数不能位空");
        }
        /*LambdaQueryWrapper<TideDailyData> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TideDailyData::getStationId,stationIds).between(TideDailyData::getTideTime,startTime,endTime);
        queryWrapper.orderByDesc(TideDailyData::getStationId,TideDailyData::getTideTime);
        List<TideDailyData> tideDailyDataList = this.getBaseMapper().selectList(queryWrapper);
        if(tideDailyDataList == null || tideDailyDataList.isEmpty()) return null;*/
        Map<Integer, String> levelMap = TideWarnLevel.toMap();
        List<TideDailyWarnLevelVO> tideDailyDataList = this.baseMapper.selectWarnLevel(stationIds, startTime, endTime, datum);
        tideDailyDataList.forEach(i -> {
            i.setLevelDesc(Objects.isNull(i.getLevel()) ? null : levelMap.get(i.getLevel()));
            i.setDate(DateUtil.format(i.getTideTime(), "dd日"));
            i.setTide(DateUtil.format(i.getTideTime(), "HH:mm"));
            //增加查询警戒潮位信息，目前没有加增水信息，后续完善
            i = getwarnHeight(i);
        });
        return tideDailyDataList;

    }

    private TideDailyWarnLevelVO getwarnHeight(TideDailyWarnLevelVO vo){
        Long id = vo.getId();
        Station station = stationDao.selectById(vo.getStationId());
        Integer datumBlueWarn = station.getDatumBlueWarn();
        Integer datumYellowWarn = station.getDatumYellowWarn();
        Integer datumOrangeWarn = station.getDatumOrangeWarn();
        Integer datumRedWarn = station.getDatumRedWarn();
        int height = vo.getHeight();
        //判断潮位属于四色警戒潮位的哪一个级别
        if (height < datumBlueWarn) {
            vo.setWarnHeight(datumBlueWarn);
            vo.setLevel(0);
            vo.setLevelDesc("无");
        } else if (height >= datumBlueWarn && height < datumYellowWarn) {
            vo.setWarnHeight(datumBlueWarn);
            vo.setLevel(1);
            vo.setLevelDesc("蓝色");
        } else if (height >= datumYellowWarn && height < datumOrangeWarn) {
            vo.setWarnHeight(datumYellowWarn);
            vo.setLevel(2);
            vo.setLevelDesc("黄色");
        } else if (height >= datumOrangeWarn && height < datumRedWarn) {
            vo.setWarnHeight(datumOrangeWarn);
            vo.setLevel(3);
            vo.setLevelDesc("橙色");
        }else if (height >= datumRedWarn) {
            vo.setWarnHeight(datumRedWarn);
            vo.setLevel(4);
            vo.setLevelDesc("红色");
        }
        return vo;
    }

    public List<TideDailyHourData> getList(Long stationId, Date startTime, Date endTime, String datum) {
        return tideDailyHourService.list(createQueryWrapper(stationId, startTime, endTime, datum));
    }

    public void exportList(HttpServletResponse response, String stationIds, Date startTime, Date endTime, String datum) {
        //站点信息
        List<String> stringList = Arrays.asList(stationIds.split(","));
        List<Long> longList = stringList.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Station> stationList = stationService.listByIds(longList);
        if (CollectionUtils.isEmpty(stationList)) {
            return;
        }
        ArrayList<TideDayInfo> maxTideList = new ArrayList<>();
        ArrayList<TideDayInfo> minTideList = new ArrayList<>();
        ArrayList<TideHourInfo> hourTideList = new ArrayList<>();

        stationList.stream().forEach(station -> {
            Long stationId = station.getId();
            Integer blueWarnValue = null;
            Integer yellowWarnValue = null;
            Integer orangeWarnValue = null;
            Integer redWarnValue = null;
            if (DatumEnum.TIDE_DATUM.getValue().equals(datum)) {
                blueWarnValue = station.getTideDatumBlueWarn();
                yellowWarnValue = station.getTideDatumYellowWarn();
                orangeWarnValue = station.getTideDatumOrangeWarn();
                redWarnValue = station.getTideDatumRedWarn();
            }
            if (DatumEnum.DATUM.getValue().equals(datum)) {
                blueWarnValue = station.getDatumBlueWarn();
                yellowWarnValue = station.getDatumBlueWarn();
                orangeWarnValue = station.getDatumBlueWarn();
                redWarnValue = station.getDatumBlueWarn();
            }
            if (DatumEnum.GAUGE_ZERO.getValue().equals(datum)) {
                blueWarnValue = station.getGaugeZeroBlueWarn();
                yellowWarnValue = station.getGaugeZeroBlueWarn();
                orangeWarnValue = station.getGaugeZeroBlueWarn();
                redWarnValue = station.getGaugeZeroBlueWarn();
            }
            Integer finalBlueWarnValue = blueWarnValue;
            Integer finalYellowWarnValue = yellowWarnValue;
            Integer finalOrangeWarnValue = orangeWarnValue;
            Integer finalRedWarnValue = redWarnValue;

            //低潮
            List<TideDailyData> minTideInfoList = tideDailyDataDao.getMinTideInfoList(stationId, startTime, endTime);
            //填充低潮数据
            minTideInfoList.forEach(day -> {
                TideDayInfo tideInfo = new TideDayInfo();
                tideInfo.setName(day.getStationName());
                tideInfo.setTime(day.getTideTime());
                tideInfo.setTide(day.getHeight());
                tideInfo.setBlueWarnValue(finalBlueWarnValue);
                tideInfo.setBlueDiffValue(finalBlueWarnValue == null ? null : finalBlueWarnValue - day.getHeight());
                tideInfo.setYellowWarnValue(finalYellowWarnValue);
                tideInfo.setYellowDiffValue(finalYellowWarnValue == null ? null : finalYellowWarnValue - day.getHeight());
                tideInfo.setOrangeWarnValue(finalOrangeWarnValue);
                tideInfo.setOrangeDiffValue(finalOrangeWarnValue == null ? null : finalOrangeWarnValue - day.getHeight());
                tideInfo.setRedWarnValue(finalRedWarnValue);
                tideInfo.setRedDiffValue(finalRedWarnValue == null ? null : finalRedWarnValue - day.getHeight());
                minTideList.add(tideInfo);
            });

            //高潮
            List<TideDailyData> maxTideInfoList = tideDailyDataDao.getMaxTideInfoList(stationId, startTime, endTime);
            //填充高潮数据
            maxTideInfoList.forEach(day -> {
                TideDayInfo tideInfo = new TideDayInfo();
                tideInfo.setName(day.getStationName());
                tideInfo.setTime(day.getTideTime());
                tideInfo.setTide(day.getHeight());
                tideInfo.setBlueWarnValue(finalBlueWarnValue);
                tideInfo.setBlueDiffValue(finalBlueWarnValue == null ? null : finalBlueWarnValue - day.getHeight());
                tideInfo.setYellowWarnValue(finalYellowWarnValue);
                tideInfo.setYellowDiffValue(finalYellowWarnValue == null ? null : finalYellowWarnValue - day.getHeight());
                tideInfo.setOrangeWarnValue(finalOrangeWarnValue);
                tideInfo.setOrangeDiffValue(finalOrangeWarnValue == null ? null : finalOrangeWarnValue - day.getHeight());
                tideInfo.setRedWarnValue(finalRedWarnValue);
                tideInfo.setRedDiffValue(finalRedWarnValue == null ? null : finalRedWarnValue - day.getHeight());
                maxTideList.add(tideInfo);
            });

            //逐时潮汐表
            List<TideDailyHourData> hourTideInfoList = tideDailyHourService.list(createQueryWrapper(stationId, startTime, endTime, datum));
            //填充逐时数据
            hourTideInfoList.forEach(hour -> {
                TideHourInfo tideInfo = new TideHourInfo();
                tideInfo.setName(hour.getStationName());
                tideInfo.setTime(hour.getTideTime());
                tideInfo.setTide(hour.getHeight());
                tideInfo.setBlueWarnValue(finalBlueWarnValue);
                tideInfo.setDiffValue(finalBlueWarnValue == null ? null : finalBlueWarnValue - hour.getHeight());
                hourTideList.add(tideInfo);
            });
        });
        this.exportExcel(response, minTideList, maxTideList, hourTideList);
    }

    private LambdaQueryWrapper<TideDailyHourData> createQueryWrapper(Long stationId, Date startTime, Date endTime, String datum) {
        LambdaQueryWrapper<TideDailyHourData> queryWrapper = new LambdaQueryWrapper<>();
        if (stationId != null) {
            queryWrapper.eq(TideDailyHourData::getStationId, stationId);
        }
        if (startTime != null) {
            queryWrapper.ge(TideDailyHourData::getTideTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(TideDailyHourData::getTideTime, endTime);
        }
        if (StringUtils.isNotBlank(datum)) {
            queryWrapper.eq(TideDailyHourData::getDatum, datum);
        }
        return queryWrapper.orderByAsc(TideDailyHourData::getTideTime);
    }

    private void exportExcel(HttpServletResponse response, List<TideDayInfo> minTideList, List<TideDayInfo> maxTideList, List<TideHourInfo> hourTideList) {
        ExcelWriter writer;
        try {
            //设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("天文潮数据", "UTF-8"));
            writer = EasyExcel.write(response.getOutputStream(), TideDayInfo.class).build();
            //写入第一个Sheet
            WriteSheet sheet0 = EasyExcel.writerSheet(0, "低潮").build();
            writer.write(minTideList, sheet0);
            //写入第二个Sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(1, "高潮").head(TideDayInfo.class).build();
            writer.write(maxTideList, sheet1);
            //写入第三个Sheet
            WriteSheet sheet2 = EasyExcel.writerSheet(2, "逐时").head(TideHourInfo.class).build();
            writer.write(hourTideList, sheet2);
            //关闭流
            writer.finish();
        } catch (IOException e) {
            throw new BusinessException("导出失败！失败原因：" + e.getMessage());
        }
    }
}
