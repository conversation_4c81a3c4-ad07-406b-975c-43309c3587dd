package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.ForecastTaskDao;
import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.entity.AreaType;
import cn.piesat.data.making.server.entity.ForecastTask;
import cn.piesat.data.making.server.enums.ForecastTaskStatus;
import cn.piesat.data.making.server.mapper.ForecastTaskMapper;
import cn.piesat.data.making.server.service.AreaService;
import cn.piesat.data.making.server.service.FmForecastProductDataService;
import cn.piesat.data.making.server.service.ForecastTaskService;
import cn.piesat.data.making.server.service.ForecastTemplateService;
import cn.piesat.data.making.server.vo.AreaVO;
import cn.piesat.data.making.server.vo.ForecastTaskVO;
import cn.piesat.data.making.server.vo.ForecastTemplateVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预报任务表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastTaskServiceImpl extends ServiceImpl<ForecastTaskDao, ForecastTask> implements ForecastTaskService {

    @Resource
    private AreaService areaService;
    @Resource
    private FmForecastProductDataService fmForecastProductDataService;
    @Resource
    private ForecastTemplateService forecastTemplateService;

    @Override
    public List<ForecastTaskVO> getList(Integer status) {
        List<ForecastTask> list = this.list(createQueryWrapper(status));
        return ForecastTaskMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public List<ForecastTask> getList() {
        return this.list(createQueryWrapper(null));
    }

    @Override
    public Map<String, Long> getInfo() {
        List<ForecastTask> list = this.list(createQueryWrapper(null));
        Map<Integer, String> statusMap = ForecastTaskStatus.toMap();
        Map<Integer, Long> countMap = list.stream().collect(Collectors.groupingBy(ForecastTask::getStatus, Collectors.counting()));
        Map<String, Long> map = new HashMap<>();
        statusMap.entrySet().stream().forEach(status -> map.put(status.getValue(), countMap.get(status.getKey())));
        map.put("共", Long.valueOf(list.size()));
        return map;
    }

    @Override
    public void createForecastTask() {
        List<ForecastTask> list = new ArrayList<>();
        LocalTime start = LocalTime.of(00, 00, 00);
        LocalTime end = LocalTime.of(23, 59, 59);
        LocalDate localDate = LocalDate.now();
        Date startTime = Date.from(LocalDateTime.of(localDate, start).atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(LocalDateTime.of(localDate, end).atZone(ZoneId.systemDefault()).toInstant());
        //查询预报模板
        List<ForecastTemplateVO> forecastList = forecastTemplateService.getList(Boolean.TRUE);
        if (!CollectionUtils.isEmpty(forecastList)) {
            LinkedHashMap<String, AreaType> forecastTypeMap = forecastTemplateService.getForecastType();
            forecastList.forEach(template -> {
                ForecastTask task = new ForecastTask();
                task.setName(forecastTypeMap.get(template.getForecastType()).getName());
                task.setTemplateId(template.getId());
                task.setForecastType(template.getForecastType());
                task.setStartTime(startTime);
                task.setEndTime(endTime);
                task.setStatus(ForecastTaskStatus.WAIT_MAKE.getValue());
                list.add(task);
                //保存潮位数据到fm_forecast_product_data
                AreaDTO area = new AreaDTO();
                area.setAreaTypeCode(task.getForecastType());
                List<AreaVO> areaList = areaService.getList(area);
                log.debug("开始同步潮位极值======");
                if (!CollectionUtils.isEmpty(areaList)) {
                    fmForecastProductDataService.saveTideData(areaList, startTime, endTime);
                }
                log.debug("同步潮位极值结束======");
            });
        }
        this.saveBatch(list);
    }

    private LambdaQueryWrapper<ForecastTask> createQueryWrapper(Integer status) {
        LambdaQueryWrapper<ForecastTask> queryWrapper = new LambdaQueryWrapper<>();
        if (status != null) {
            queryWrapper.eq(ForecastTask::getStatus, status);
        }
        queryWrapper.ne(ForecastTask::getForecastType, "graphic");
        queryWrapper.ge(ForecastTask::getStartTime, LocalDate.now().atTime(LocalTime.MIN));
        queryWrapper.le(ForecastTask::getEndTime, LocalDate.now().atTime(LocalTime.MAX));
        return queryWrapper.orderByAsc(ForecastTask::getCreateTime);
    }
}





