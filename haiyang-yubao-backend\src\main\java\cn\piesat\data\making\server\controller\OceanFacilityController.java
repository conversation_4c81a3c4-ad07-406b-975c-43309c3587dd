package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.entity.OceanFacility;
import cn.piesat.data.making.server.service.OceanFacilityService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 海洋设施表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oceanFacility")
public class OceanFacilityController {

    @Resource
    private OceanFacilityService oceanFacilityService;

    /**
     * 查询海洋设施类型列表
     *
     * @return
     */
    @GetMapping("/typeList")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋设施表管理", operateType = OperateType.SELECT)
    public List<String> getTypeList() {
        List<OceanFacility> list = oceanFacilityService.list(null);
        List<String> resultList = list.stream().map(OceanFacility::getType).distinct().collect(Collectors.toList());
        Collections.sort(resultList);
        return resultList;
    }

    /**
     * 根据海洋设施类型查询海洋设施列表
     *
     * @param type 海洋设施类型
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋设施表管理", operateType = OperateType.SELECT)
    public List<OceanFacility> getList(@RequestParam("type") String type) {
        LambdaQueryWrapper<OceanFacility> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OceanFacility::getType, type);
        queryWrapper.orderByAsc(OceanFacility::getCode);
        return oceanFacilityService.list(queryWrapper);
    }
}