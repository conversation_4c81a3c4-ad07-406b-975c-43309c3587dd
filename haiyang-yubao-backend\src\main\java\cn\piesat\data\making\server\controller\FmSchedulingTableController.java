package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmSchedulingTableDTO;
import cn.piesat.data.making.server.dto.SchedulingAddDTO;
import cn.piesat.data.making.server.entity.FmSchedulingTable;
import cn.piesat.data.making.server.vo.FmSchedulingTableVO;
import cn.piesat.data.making.server.service.FmSchedulingTableService;
import cn.piesat.webconfig.response.R;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 排班控制层
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:06
 */
@RestController
@RequestMapping("fmSchedulingTable")
public class FmSchedulingTableController {

    @Resource
    private FmSchedulingTableService fmSchedulingTableService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.SELECT)
    public PageResult<FmSchedulingTableVO> getPage(@RequestParam(required = false) Long id,
                                                   @RequestParam(defaultValue = "1") Integer pageNum,
                                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        FmSchedulingTableDTO dto = new FmSchedulingTableDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmSchedulingTableService.getPage(dto, pageParam);
    }

    /**
     * 根据月份查询排班信息
     *
     * @param date
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.SELECT)
    public List<FmSchedulingTable> getList(@RequestParam(required = false)Date date) {
        FmSchedulingTableDTO dto = new FmSchedulingTableDTO();
        dto.setSchedulingDate(date);
        return fmSchedulingTableService.getList(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.SELECT)
    public FmSchedulingTableVO getById(@PathVariable Long id) {
        return null;
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.INSERT)
    public R<List<FmSchedulingTable>> save(@RequestBody SchedulingAddDTO dto) {
        return R.builder().buildOk(fmSchedulingTableService.save(dto));
    }
    /**
     * 保存全部数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/saveAll")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.INSERT)
    public void saveAll(@RequestBody List<FmSchedulingTable> dto) {
        fmSchedulingTableService.saveAll(dto);
    }

    /**
     * 排班管理：修改排班表
     *
     * @param list
     * @return
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.INSERT)
    public R<List<FmSchedulingTable>> update(@RequestBody List<FmSchedulingTable> list) {
        return R.builder().buildOk(fmSchedulingTableService.update(list));
    }

    /**
     * 排班管理：签到
     *
     * @param schedulingTable
     * @return
     */
    @PostMapping("/sign")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.INSERT)
    public R<Integer> update(@RequestBody FmSchedulingTable schedulingTable) {
        return R.builder().buildOk(fmSchedulingTableService.sign(schedulingTable));
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmSchedulingTableService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmSchedulingTableService.deleteByIdList(idList);
    }

    /**
     * 上传
     *
     * @param multipartFile
     * @return
     */
    @PostMapping("/upload")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.DELETE)
    public R<List<FmSchedulingTable>> upload(@RequestParam MultipartFile multipartFile,@RequestParam Date date) {
        return R.builder().buildOk(fmSchedulingTableService.upload(multipartFile,date));
    }


    /**
     * 导出
     *
     * @param date
     * @return
     */
    @GetMapping("/download")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.DELETE)
    public void download(@RequestParam Date date) {
        fmSchedulingTableService.download(date);
    }
}
