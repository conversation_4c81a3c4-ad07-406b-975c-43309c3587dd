package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmPublicRecordData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicRecordDataDaoTest {

    @Autowired
    private FmPublicRecordDataDao fmPublicRecordDataDaoImpl;

    @Test
    public void testSave(){
        FmPublicRecordData data = new FmPublicRecordData();
        data.setRecordId(12345678L);

        fmPublicRecordDataDaoImpl.insert(data);
    }
}
