package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmGraphicRecordBDTO;
import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.entity.FmGraphicRecordB;
import cn.piesat.data.making.server.entity.FmTyphoonForecast;
import cn.piesat.data.making.server.vo.FmGraphicRecordBVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import cn.piesat.data.making.server.vo.FmTyphoonForecastVO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 图形记录表服务接口
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:38
 */
public interface FmGraphicRecordBService extends IService<FmGraphicRecordB>{
    /**
     * 根据参数查询分页
     */
    PageResult<FmGraphicRecordBVO> getPage(FmGraphicRecordBDTO dto,PageParam pageParam);

    /**
     * 根据参数查询列表
     */
    List<FmGraphicRecordBVO> getList(FmGraphicRecordBDTO dto);

    /**
     * 根据参数查询列表
     */
    List<FmGraphicRecordBVO> getInfoList(FmGraphicRecordBDTO dto);

    /**
     * 根据id查询数据
     */
    FmGraphicRecordBVO getInfoById(Long id);

    /**
     * 保存数据
     */
    void save(FmGraphicRecordBDTO dto);

    /**
     * 批量保存数据
     */
    void saveList(List<FmGraphicRecordBDTO> dtoList);

    /**
     * 根据id删除数据
     */
    void deleteById(Long id);

    /**
     * 根据idList批量删除数据
     */
    void deleteByIdList(List<Long> idList);

    /**
     * 提交图形记录
     */
    void submit(FmGraphicRecordBDTO dto);

    /**
     * 查询台风列表
     * @param year
     */
    List<FmTyphoonBVO> getTyphoonList(String year);

    /**
     * 根据编号查询台风数据
     */
    List<FmTyphoonRealBVO> getTyphoonByCode(FmTyphoonRealBDTO fmTyphoonRealBDTO);


    List<FmTyphoonForecastVO> findTyphoonForecastByCode(FmTyphoonRealBDTO fmTyphoonRealBDTO);
}
