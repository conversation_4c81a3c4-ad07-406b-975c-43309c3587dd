<template>
  <div v-show="parent.activeVal === 'bjgj'" class="bjgj">
    <div class="btns">
      <qx-button
        class="edit-btn"
        :class="[activeBtnBJGJ === 'draw' ? 'active' : '']"
        @click="triggerBJGJ('draw')"
        >绘制</qx-button
      >
      <!-- @click="triggerBJGJ('draw')" -->
      <!-- <qx-button
        class="edit-btn"
        :class="[activeBtnBJGJ === 'modify' ? 'active' : '']"
        @click="triggerBJGJ('modify')"
        >修改</qx-button
      >
      <qx-button
        class="edit-btn"
        :class="[activeBtnBJGJ === 'move' ? 'active' : '']"
        @click="triggerBJGJ('move')"
        >移动</qx-button
      > -->
      <qx-button
        class="edit-btn"
        :class="[activeBtnBJGJ === 'delete' ? 'active' : '']"
        @click="triggerBJGJ('delete')"
        >删除</qx-button
      >
      <qx-button
        class="edit-btn"
        :class="[activeBtnBJGJ === 'clear' ? 'active' : '']"
        @click="triggerBJGJ('clear')"
        >清空</qx-button
      >
    </div>
    <div class="tools-container">
      <div
        v-for="item in tools"
        :key="item.id"
        class="tool-item"
        :class="activeToolBJGJ === item.id ? 'toolAct' : ''"
        @click="toolClickHandler(item)"
      >
        <img
          :src="
            activeToolBJGJ === item.id
              ? imagesMap[item.activeUrl]
              : imagesMap[item.url]
          "
        />
        <span>{{ item.name }}</span>
      </div>
    </div>
    <div v-show="activeToolBJGJ === 'mete_wind_barb'" class="tools-info">
      <div class="wind-info wind-direction">
        <div class="top">
          <div
            class="text-btn"
            :class="[direction === 0 ? 'active' : '']"
            @click="direction = 0"
          >
            北
          </div>
        </div>
        <div class="center">
          <div class="left-text">
            <div
              class="text-btn"
              :class="[direction === 315 ? 'active' : '']"
              @click="direction = 315"
            >
              西北
            </div>
            <div
              class="text-btn"
              :class="[direction === 275 ? 'active' : '']"
              @click="direction = 275"
            >
              西
            </div>
            <div
              class="text-btn"
              :class="[direction === 230 ? 'active' : '']"
              @click="direction = 230"
            >
              西南
            </div>
          </div>
          <div class="wind-icon">
            <img
              :src="imagesMap[`feng${level}`]"
              alt=""
              :style="{ transform: `rotate(${direction}deg` }"
            />
          </div>
          <div class="right-text">
            <div
              class="text-btn"
              :class="[direction === 45 ? 'active' : '']"
              @click="direction = 45"
            >
              东北
            </div>
            <div
              class="text-btn"
              :class="[direction === 90 ? 'active' : '']"
              @click="direction = 90"
            >
              东
            </div>
            <div
              class="text-btn"
              :class="[direction === 135 ? 'active' : '']"
              @click="direction = 135"
            >
              东南
            </div>
          </div>
        </div>
        <div class="bottom">
          <div
            class="text-btn"
            :class="[direction === 180 ? 'active' : '']"
            @click="direction = 180"
          >
            南
          </div>
        </div>
      </div>
      <div class="wind-info wind-levels">
        <div
          v-for="item in 15"
          :key="item + 'level'"
          class="wind-level"
          :class="[level === item + 2 ? 'active' : '']"
          @click="level = item + 2"
        >
          {{ item + 2 }}级
        </div>
      </div>
    </div>
    <div v-show="activeToolBJGJ === 'mete_wave_direction'" class="tools-info">
      <div class="wind-info wind-direction">
        <div class="top">
          <div
            class="text-btn"
            :class="[direction === 0 ? 'active' : '']"
            @click="direction = 0"
          >
            北
          </div>
        </div>
        <div class="center">
          <div class="left-text">
            <div
              class="text-btn"
              :class="[direction === 315 ? 'active' : '']"
              @click="direction = 315"
            >
              西北
            </div>
            <div
              class="text-btn"
              :class="[direction === 275 ? 'active' : '']"
              @click="direction = 275"
            >
              西
            </div>
            <div
              class="text-btn"
              :class="[direction === 230 ? 'active' : '']"
              @click="direction = 230"
            >
              西南
            </div>
          </div>
          <div class="wind-icon">
            <img
              :src="imagesMap[`zhuboxiang`]"
              alt=""
              :style="{ transform: `rotate(${direction}deg` }"
            />
          </div>
          <div class="right-text">
            <div
              class="text-btn"
              :class="[direction === 45 ? 'active' : '']"
              @click="direction = 45"
            >
              东北
            </div>
            <div
              class="text-btn"
              :class="[direction === 90 ? 'active' : '']"
              @click="direction = 90"
            >
              东
            </div>
            <div
              class="text-btn"
              :class="[direction === 135 ? 'active' : '']"
              @click="direction = 135"
            >
              东南
            </div>
          </div>
        </div>
        <div class="bottom">
          <div
            class="text-btn"
            :class="[direction === 180 ? 'active' : '']"
            @click="direction = 180"
          >
            南
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-show="parent.activeVal === 'fgj'" class="fgj">
    <div>
      <div class="btns">
        <qx-button
          class="edit-btn"
          :class="[activeBtn === 'draw' ? 'active' : '']"
          @click="trigger('draw')"
          >绘制</qx-button
        >
        <qx-button
          class="edit-btn"
          :class="[activeBtn === 'modify' ? 'active' : '']"
          @click="trigger('modify')"
          >修改</qx-button
        >
        <qx-button
          class="edit-btn"
          :class="[activeBtn === 'delete' ? 'active' : '']"
          @click="trigger('delete')"
          >删除</qx-button
        >
        <qx-button
          class="edit-btn"
          :class="[activeBtn === 'clear' ? 'active' : '']"
          @click="trigger('clear')"
          >清空</qx-button
        >
      </div>
      <div class="tools-container">
        <div
          v-for="item in toolsFGJ"
          :key="item.id"
          class="tool-item"
          :class="activeTool === item.id ? 'toolAct' : ''"
          @click="activeTool = item.id"
        >
          <img
            :src="
              activeTool === item.id
                ? imagesMap[item.activeUrl]
                : imagesMap[item.url]
            "
          />
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>

  <qx-dialog v-model:visible="dialogVisible" title="经纬度" width="300px">
    <template #content>
      <div class="form-container">
        <n-form :model="dialogForm" :label-width="50" label-placement="left">
          <n-form-item label="经度:">
            <n-input v-model:value="dialogForm.lon" placeholder="请输入经度" />
          </n-form-item>
          <n-form-item label="纬度:">
            <n-input v-model:value="dialogForm.lat" placeholder="请输入纬度" />
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="dialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onDialogSave">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup name="BJGJ">
import {
  ref,
  watch,
  inject,
  onMounted,
  getCurrentInstance,
  onUnmounted,
  reactive
} from 'vue'
import { QxButton } from 'src/components/QxButton'
import PlottingLayer from 'src/utils/plotting/plot/PlottingLayer.js'
import FeatureOperatorEvent from 'src/utils/plotting/plot/events/FeatureOperatorEvent.js'
import Feature from 'ol/Feature.js'
import LineString from 'ol/geom/LineString.js'
import { Stroke, Style } from 'ol/style.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { QxDialog } from 'src/components/QxDialog'

const { proxy } = getCurrentInstance()
const parent = proxy.$parent.$parent
const getMap = inject('getMap')
let g_op_layer = null
let styletext = null
let g_op_feature = null
const activeBtnBJGJ = ref('')

const req1 = import.meta.glob('src/assets/images/tools/*.*', { eager: true })
const req = { ...req1 }
let imagesMap = {}
// 循环所有图片，将图片名设置成键，值为导入该图片的地址
for (const key in req) {
  // let name = key.replace(/(\.\/images\/|\..*)/g, '')
  let name = key.split('/').slice(-1)[0].split('.')[0]

  // 抛出图片大对象后，文件页面直接引入后将图片的具体名称作为属性就能导入该图片
  imagesMap[name] = req[key].default
}

const tools = [
  {
    name: '风向风速',
    id: 'mete_wind_barb',
    url: 'feng',
    activeUrl: 'feng-active'
  },
  {
    name: '位置',
    id: 'mete_point',
    url: 'weizhi',
    activeUrl: 'weizhi-active'
  },
  {
    name: '高压位置',
    id: 'mete_high_pressure_point',
    url: 'gaoya',
    activeUrl: 'gaoya-active'
  },
  {
    name: '低压位置',
    id: 'mete_low_pressure_point',
    url: 'diya',
    activeUrl: 'diya-active'
  },
  {
    name: '高压',
    id: 'mete_high_pressure1',
    url: 'gao',
    activeUrl: 'gao-active'
  },
  {
    name: '台风气压',
    id: 'mete_typhoon_pressure',
    url: 'taifeng',
    activeUrl: 'taifeng-active'
  },
  {
    name: '主波向',
    id: 'mete_wave_direction',
    url: 'zhuboxiang',
    activeUrl: 'zhuboxiang-active'
  },
  {
    name: '低压',
    id: 'mete_low_pressure1',
    url: 'di',
    activeUrl: 'di-active'
  },
  {
    name: '台风',
    id: 'mete_typhoon_point',
    url: 'tai',
    activeUrl: 'tai-active'
  }
]
const activeToolBJGJ = ref('')
let dialogVisible = ref(false)
let dialogForm = reactive({
  lon: '',
  lat: ''
})
function toolClickHandler(item) {
  activeToolBJGJ.value = item.id

  if (
    ['mete_high_pressure_point', 'mete_low_pressure_point'].includes(item.id)
  ) {
    dialogVisible.value = true
  }
}

function onDialogSave(){
  triggerBJGJ('draw')
}

function triggerBJGJ(type) {
  activeBtnBJGJ.value = type
  if (type === 'draw') {
    activate(activeToolBJGJ.value)
  }
  if (type === 'delete') {
    g_op_layer.removeFeature(g_op_feature)
    g_op_feature = null
  }
  if (type === 'clear') {
    if (g_op_layer) {
      g_op_layer.clearFeatures()
      g_op_feature = null
    }
    activeBtnBJGJ.value = ''
  }
}

const direction = ref(0)
const level = ref(3)
function activate(type) {
  if (parent.activeVal === 'bjgj') {
    let rotation = 0
    let url = ''
    if (type === 'mete_wind_barb') {
      rotation = direction.value * (Math.PI / 180)
      url = `feng${level.value}`
    } else if (type === 'mete_wave_direction') {
      rotation = direction.value * (Math.PI / 180)
      url = tools.find(item => item.id === type).url
    } else {
      url = tools.find(item => item.id === type).url
    }

    localStorage.setItem('markUrl', imagesMap[url])
    localStorage.setItem('rotation', rotation)
  }

  g_op_layer.addFeature(type)
}
function drawend(feature) {
  console.log(g_op_layer,'g_op_layer',feature)
  setTimeout(() => {
    activeBtnBJGJ.value = ''
    if (activeTool.value === 'mete_typhon') {
      drawTyphonPath()
    }
    activeBtn.value = ''
  }, 1)
}

//风工具

const activeBtn = ref('')
const toolsFGJ = [
  {
    name: '台风',
    id: 'mete_typhon',
    // id: 'mete_point',
    url: 'tai',
    activeUrl: 'tai-active'
  },
  {
    name: '冷锋',
    id: 'mete_cold_front',
    url: 'lengfeng',
    activeUrl: 'lengfeng-active'
  },
  {
    name: '暖锋',
    id: 'mete_warm_front',
    url: 'nuanfeng',
    activeUrl: 'nuanfeng-active'
  },
  {
    name: '静止锋',
    id: 'mete_stationary_front',
    url: 'jingzhifeng',
    activeUrl: 'jingzhifeng-active'
  },
  {
    name: '折线',
    id: 'polyline',
    url: 'zhexian',
    activeUrl: 'zhexian-active'
  }
]
const activeTool = ref('')
function trigger(type) {
  activeBtn.value = type
  if (type === 'draw') {
    activate(activeTool.value)
  }
  if (type === 'clear') {
    if (g_op_layer) {
      g_op_layer.clearFeatures()
      g_op_feature = null
      vectorSource.clear()
    }
    activeBtn.value = ''
  }
  if (type === 'delete') {
    g_op_layer.removeFeature(g_op_feature)
    g_op_feature = null
    drawTyphonPath()
  }
}
let vectorSource = null
let vectorLayer = null

function creatVectorLayer(map) {
  vectorSource = new VectorSource()
  vectorLayer = new VectorLayer({
    source: vectorSource,
    zIndex: 12,
    transparent: 'true'
  })
  map.addLayer(vectorLayer)
}

// 绘制台风路径
function drawTyphonPath() {
  vectorSource.refresh()
  const features = g_op_layer.showLayer.values_.source.featuresRtree_.items_
  const points = []
  for (let key in features) {
    const fea = features[key].value.values_.geometry
    if (fea.type && fea.type === 'mete_typhon') {
      points.push(fea.points[0])
    }
  }
  if (points.length > 1) {
    const line = new Feature({
      geometry: new LineString(points)
    })
    line.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#333333',
          width: 2,
          lineDash: [5, 10] // 设置虚线样式
        })
      })
    )
    vectorSource.addFeatures([line])
  }
}

onMounted(() => {
  getMap(map => {
    g_op_layer = new PlottingLayer(map, undefined, feature => {
      drawend(feature)
    })
    console.log(g_op_layer,'g_op_layer')
    g_op_layer.on(FeatureOperatorEvent.ACTIVATE, function (e) {
      g_op_feature = e.feature_operator
      styletext = JSON.stringify(g_op_feature.getStyle())
      /* window.g_op_feature.iteratorAttribute(function (key) {
                that.tableData.push({key:key,value:this.getAttribute(key)})
            }, window.g_op_feature) */
    })
    g_op_layer.on(FeatureOperatorEvent.DEACTIVATE, function (e) {
      g_op_feature = null
      styletext = ''
      // that.tableData=[];
    })
    creatVectorLayer(map)
    // map.getView().on('change:resolution', function () {
    //   console.log(g_op_layer,'g_op_layer')

    //   const style = g_op_layer.getSource().getStyle()
    //   console.log(style.image,'style=====')
    //   let zoom = map.getView().getZoom()
    //   // style.image.icon.scale = zoom / 10
    // })
  })
})
onUnmounted(() => {
  getMap(map => {
    map.removeLayer(vectorLayer)
    g_op_layer.clearFeatures(g_op_feature)
  })
})
</script>

<style lang="scss" scoped>
.bjgj {
  .btns {
    display: flex;
    justify-content: space-between;
    .edit-btn {
      width: 60px;
      height: 32px;
      background: #1c81f8;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      /* margin-right: 0; */
      padding: 0px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      &.active {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
      &:hover {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
    }
  }

  .tools-container {
    // width: 346px;
    height: 119px;
    background: #fafcfe;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e7f0fb;
    margin: 12px 0px;
    padding: 0px 12px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .tool-item {
      width: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #222222;
      cursor: pointer;
      &.toolAct {
        color: #0026ff;
      }
      img {
        width: 28px;
        height: 28px;
      }
      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
  .tools-info {
    display: flex;
    justify-content: space-between;
    .wind-info {
      width: 167px;
      height: 168px;
      background: #fafcfe;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #e7f0fb;
      box-sizing: border-box;
      position: relative;
      &.wind-direction {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .center {
          display: flex;
          .left-text {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;
          }
          .right-text {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-end;
          }
          .wind-icon {
            height: 80px;
            width: 80px;
            border-radius: 40px;
            border: 1px solid #66a3ff;
            margin: 10px 5px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .text-btn {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #000000;
          line-height: 16px;
          cursor: pointer;
          &.active {
            color: #0026ff;
          }
        }
      }
      &.wind-levels {
        display: flex;
        flex-wrap: wrap;
        .wind-level {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 52px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #000000;
          cursor: pointer;
          &.active {
            color: #0026ff;
          }
        }
      }
    }
  }
}
.fgj {
  .btns {
    display: flex;
    justify-content: space-between;
    .edit-btn {
      width: 80px;
      height: 32px;
      background: #1c81f8;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      /* margin-right: 0; */
      padding: 0px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      &.active {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
      &:hover {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
    }
  }

  .tools-container {
    // width: 346px;
    height: 119px;
    background: #fafcfe;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e7f0fb;
    margin: 12px 0px;
    padding: 0px 12px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .tool-item {
      width: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #222222;
      cursor: pointer;
      &.toolAct {
        color: #0026ff;
      }
      img {
        width: 28px;
        height: 28px;
      }
      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
}
</style>
