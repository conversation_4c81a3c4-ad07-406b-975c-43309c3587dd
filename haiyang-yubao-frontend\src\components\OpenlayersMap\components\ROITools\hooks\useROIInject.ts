import { ActionType, type IROIOptions } from './useROIOptions'
import { inject, InjectionKey, provide, ref, type Ref } from 'vue'
import VectorLayer from 'ol/layer/Vector'
import VectorSource from 'ol/source/Vector'
import { Point, Polygon, SimpleGeometry } from 'ol/geom'
import { Select } from 'ol/interaction.js'
import type { IGetMapFunction } from './types'

const injectNames = {
  getMap(): IGetMapFunction | undefined {
    return void 0
  },
  // 落区面板配置
  roiOptions(): Ref<IROIOptions> | undefined {
    return void 0
  },
  // 当前激活的落区工具
  currentAction(): Ref<ActionType> | undefined {
    return void 0
  },
  // 绘制的矢量图层
  vectorLayer(): VectorLayer<VectorSource<SimpleGeometry>> | undefined {
    return void 0
  },
  // 绘制的矢量数据源
  vectorSource(): VectorSource<SimpleGeometry> | undefined {
    return void 0
  },
  // 选择要素的交互
  selectInteraction(): Select | undefined {
    return void 0
  },
  // 与陆地相交点的矢量图层
  intersectPointLayer(): VectorLayer<VectorSource<Point>> | undefined {
    return void 0
  }
}

/**
 * 根据 name 推导 provider/inject 类型
 */
export type ROIInjectType<T extends keyof typeof injectNames> = ReturnType<
  (typeof injectNames)[T]
>

export function useROIInject<T extends keyof typeof injectNames>(name: T) {
  return {
    provideROI(value: ROIInjectType<T>) {
      provide<ROIInjectType<T>>(name, value)
    },
    injectROI() {
      return inject<ROIInjectType<T>>(name)
    }
  }
}
