package cn.piesat.data.making.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.piesat.data.making.server.dao.AlarmLevelDao;
import cn.piesat.data.making.server.entity.AlarmLevel;
import cn.piesat.data.making.server.service.AlarmLevelService;
import org.springframework.stereotype.Service;

/**
 * 警报等级信息表(AlarmLevel)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:16
 */
@Service("AlarmLevelService")
public class AlarmLevelServiceImpl extends ServiceImpl<AlarmLevelDao, AlarmLevel> implements AlarmLevelService {

    @Override
    public PageResult pageList(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<AlarmLevel> wrapper = new LambdaQueryWrapper<>();
        Page<AlarmLevel> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }
}

