package cn.piesat.data.making.server.fegin;

import cn.piesat.data.making.server.dto.dataSearch.RecordBaseResultTypeForm;
import cn.piesat.data.making.server.dto.generate.ManualExecutionDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "data-manager-search",url = "${data.manage.search-url:}")
public interface DataManageSearchFegin {

    @GetMapping("/api/product/record/range/{namespace}/{productId}")
    Object queryByProductIdAndTimeRange(@PathVariable String namespace,
                                        @PathVariable String productId,
                                        RecordBaseResultTypeForm recordBaseResultTypeForm);
}
