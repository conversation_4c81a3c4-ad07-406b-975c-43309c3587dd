<template>
  <div v-if="visible" class="add-data-dialog">
    <div class="header">
      <h3>{{ isEditOut ? '编辑' : '新建' }}数据</h3>
      <Close class="icon-close" @click="onClose" />
    </div>
    <div class="dialog-content">
      <n-form
        ref="formRef"
        :model="form"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item label="区域分类">
          <n-select
            v-if="curTab === 1"
            v-model:value="form.areaTypeCode"
            placeholder="请选择"
            :options="regionalCategoryList"
            label-field="name"
            value-field="code"
          />
          <n-tree-select
            v-if="curTab === 2"
            ref="stationTreeSelectRef"
            :options="selectTreeData"
            :value="form.parentCode"
            label-field="name"
            key-field="code"
            children-field="childList"
            @update:value="handleUpdateValue"
          />
        </n-form-item>

        <n-form-item v-if="curTab === 2" label="站点分类">
          <n-select
            v-model:value="form.stationTypeCode"
            :options="stationTypeOptions"
            @update:value="changeStationType"
          />
        </n-form-item>
        <n-form-item v-if="curTab === 2" label="关联站点">
          <n-select
            v-model:value="form.relationStation"
            label-field="name"
            value-field="code"
            filterable
            :options="relationStationOptions"
          />
        </n-form-item>
        <template v-if="curTab === 2 && form.stationTypeCode === 'ship'">
          <n-form-item label="观测频率">
            <n-radio-group v-model:value="form.frequency" name="radiogroup">
              <n-space>
                <n-radio value="D"> 逐天 </n-radio>
                <n-radio value="H"> 逐小时 </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="是否启用">
            <n-switch v-model:value="form.enable" />
          </n-form-item>
        </template>

        <n-form-item
          v-if="form.stationTypeCode === 'buoyStation' && curTab === 2"
          label="浮标种类"
        >
          <n-select
            v-model:value="form.type"
            :options="buoyStationTypeOptions"
          />
        </n-form-item>
        <template v-if="form.stationTypeCode !== 'ship'">
          <n-form-item label="数据范围">
            <n-input
              v-model:value="form.locationJson"
              placeholder="请输入"
              clearabled
              @update:value="updateFeature"
            />

            <Scan v-if="!isEditOut" class="icon-scan" @click="drawHandler" />
            <n-upload
              action=""
              :default-upload="false"
              :max="1"
              :multiple="false"
              :show-file-list="false"
              class="upload-btn"
              @update:file-list="handleFileChange"
              @before-upload="beforeUpload"
            >
              <CloudUploadOutline></CloudUploadOutline>
              <!-- <i class="icon icon-upload"></i> -->
            </n-upload>
          </n-form-item>
          <n-form-item v-if="curTab === 2" label="站点位置">
            <n-input v-model:value="location" disabled></n-input>
          </n-form-item>
          <n-form-item label="数据名称">
            <n-input
              v-model:value="form.name"
              placeholder="请输入"
              clearabled
              @update:value="updatePointName"
            />
          </n-form-item>

          <n-form-item
            v-if="curTab === 2 && form.stationTypeCode === 'oceanStation'"
            label="建站时间"
          >
            <n-date-picker v-model:value="form.createTime" type="date" />
          </n-form-item>
          <n-form-item
            v-if="curTab === 2 && form.stationTypeCode === 'buoyStation'"
            label="布放时间"
          >
            <n-date-picker v-model:value="form.createTime" type="date" />
          </n-form-item>
          <n-form-item v-if="curTab === 2" label="是否启用">
            <n-switch v-model:value="form.enable" />
          </n-form-item>
        </template>
      </n-form>
      <div class="btns text-center">
        <qx-button class="primary" @click="onSave">保存</qx-button>
        <qx-button @click="onClose">取消</qx-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Close, Scan, CloudUploadOutline } from '@vicons/ionicons5'
import {
  ref,
  reactive,
  computed,
  onMounted,
  watch,
  getCurrentInstance,
  onUnmounted
} from 'vue'
import { QxButton } from 'src/components/QxButton'
import { Draw } from 'ol/interaction'
import { Vector as VectorSource } from 'ol/source'
import { Vector as VectorLayer } from 'ol/layer'
import { Circle, Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import { useMessage } from 'naive-ui'
import { GeoJSON } from 'ol/format'
import Api from 'src/requests/forecast'
import ToolApi from 'src/requests/toolRequest'
import StationApi from 'src/requests/dataMaintenance'
import eventBus from 'src/utils/eventBus'
import { Modify } from 'ol/interaction.js'
import stationImg from 'src/assets/images/station.png'
import Collection from 'ol/Collection.js'
import linkApi from 'src/requests/analysis'
const message = useMessage()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  categoryList: {
    type: Array<any>,
    default() {
      return []
    }
  },
  map: {
    type: Object,
    default() {
      return {}
    }
  },
  isEdit: {
    type: Boolean,
    default() {
      return false
    }
  },
  type: {
    type: Number,
    default: 1
  }
})
let isEditOut = computed(() => props.isEdit)
const dataRange = ref<string>('')
// 区域分类
let regionalCategoryList = computed({
  get: () => props.categoryList,
  set: () => {}
})
let curTab = computed({
  get: () => props.type,
  set: () => {}
})
let selectTreeData = ref<any>([])
// 数据类型
const typeList = ref<any[]>([
  {
    label: '区域',
    type: 1
  },
  {
    label: '站点',
    type: 2
  }
])
const stationTypeOptions = [
  {
    label: '海洋站',
    value: 'oceanStation'
  },
  {
    label: '船舶',
    value: 'ship'
  },
  {
    label: '浮标站',
    value: 'buoyStation'
  }
]
const buoyStationTypeOptions = [
  // {
  //   label: '潮位',
  //   value: 'tide'
  // },
  // {
  //   label: '实况观测',
  //   value: 'liveObservation'
  // },
  {
    label: '3米浮标站',
    value: '3m'
  },
  {
    label: '10米浮标站',
    value: '10m'
  },
  {
    label: '波浪谱浮标站',
    value: 'waveSpectrum'
  }
]
type FeatureType = {
  type: string
  features: any[]
}

let feature: FeatureType = {
  type: 'FeatureCollection',
  features: []
}

interface FormType {
  [key: string]: any // 添加字符串索引签名
}
const form: FormType = reactive({
  areaTypeCode: '', //区域类型编码
  code: '',
  stationTypeCode: '',
  parentCode: '',
  name: '', //数据名称
  locationJson: '', //位置
  type: '',
  enable: '',
  relationStation: '',
  frequency: 'D',
  createTime: null
})

const location = computed<string>({
  // 读取值
  get() {
    let value = ''
    if (form.locationJson !== '' && curTab.value === 2) {
      const feature = new GeoJSON().readFeatures(form.locationJson)
      console.log(feature, 'feature')
      if (feature.length > 0) {
        // @ts-ignore
        const coo = feature[0].getGeometry()?.getCoordinates()
        value = coo?.join(',') || ''
      }
    }

    return value
  },
  // 写入值
  set(newVal) {
    console.log(newVal) // 李-四
  }
})
const emit = defineEmits([
  'update:visible',
  'update:data',
  'changeSelectFeature',
  'removeFeature'
])

const visible = computed({
  get: () => props.visible,
  set: () => emit('update:visible')
})

function onClose() {
  form.areaTypeCode = '' //区域类型编码
  form.code = ''
  form.stationTypeCode = ''
  form.parentCode = ''
  form.name = '' //数据名称
  form.locationJson = '' //位置
  form.type = ''
  form.enable = ''
  form.relationStation = ''
  if (modify) {
    map.removeInteraction(modify)
  }
  emit('update:visible', false)
}

let draw: any = null
let source: any = new VectorSource({ wrapX: false })
const vector = new VectorLayer({
  source,
  style: new Style({
    stroke: new Stroke({
      color: 'red',
      width: 2
    }),
    fill: new Fill({
      color: 'rgba(255,0,0,0.2)'
    })
  })
})
function drawHandler() {
  // return
  if (draw) {
    props.map.removeInteraction(draw)
  }
  console.log(curTab.value)
  draw = new Draw({
    type: curTab.value === 1 ? 'Polygon' : 'Point', //绘制的几何图形的几何类型
    source
  })
  props.map.addInteraction(draw)

  draw.on('drawstart', drawstart)
  draw.on('drawend', drawend)
}

function drawstart(event: Event) {
  source.refresh()
  if (curTab.value === 1) {
    message.warning('开始绘制,双击击结束')
  }
}
function drawend(event: any) {
  const json = new GeoJSON().writeFeature(event.feature)
  console.log(json, 'json')

  let featureGeoJson = JSON.parse(new GeoJSON().writeFeature(event.feature))
  if (curTab.value === 2) {
    event.feature.setStyle(
      new Style({
        image: new Icon({
          anchor: [0.5, 1],
          src: stationImg
        })
      })
    )
  }
  // feature.features.push(featureGeoJson)
  form.locationJson = json
  // const geo = event.feature.getGeometry()
  // dataRange.value = geo.getExtent().join(',')
  props.map.removeInteraction(draw)
}
const stationTreeSelectRef = ref()
function onSave() {
  if (isEditOut.value) {
    form.id = curId
  }
  form.locationJson = JSON.stringify(JSON.parse(`${form.locationJson}`))
  form.locationJson = decodeURIComponent(form.locationJson)
  if (curTab.value === 1) {
    Api.saveArea(form)
      .then((res: any) => {
        message.success('保存成功')
        emit('update:data', res, form.locationJson)
        if (!isEditOut.value) {
          // 是新增
          source.refresh()
        }
        onClose()
      })
      .catch(e => {
        let { msg = '' } = e?.response?.data || {}
        message.error(msg || '保存失败')
      })
  } else {
    const data = {
      name: form.name,
      // stationTypeCode: selectReginData.value.parentCode,
      // regionCode: selectReginData.value.code,
      stationTypeCode: form.stationTypeCode, //form.parentCode,
      regionCode: form.parentCode,
      locationJson: form.locationJson,
      id: form.id,
      type: form.type,
      enable: form.enable,
      relationStation: form.relationStation
    }
    if (!isEditOut.value) {
      // 是新增
      source.refresh()
    }
    StationApi.saveStation(data)
      .then((res: any) => {
        message.success('保存成功')
        emit('update:data', res, form)
        onClose()
        updateFeature(form.locationJson)
      })
      .catch(e => {
        let { msg = '' } = e?.response?.data
        message.error(msg || '保存失败')
      })
  }
}

const overlayStyle = (function () {
  const styles: any = {}
  styles['Polygon'] = [
    new Style({
      fill: new Fill({
        color: [255, 255, 255, 0.5]
      })
    }),
    new Style({
      stroke: new Stroke({
        color: [255, 255, 255, 1],
        width: 5
      })
    }),
    new Style({
      stroke: new Stroke({
        color: [0, 153, 255, 1],
        width: 3
      })
    })
  ]

  styles['Point'] = [
    new Style({
      image: new Circle({
        radius: 7,
        fill: new Fill({
          color: [0, 153, 255, 1]
        }),
        stroke: new Stroke({
          color: [255, 255, 255, 0.75],
          width: 1.5
        })
      }),
      zIndex: 100000
    })
  ]

  return function (feature: any) {
    return styles[feature.getGeometry().getType()]
  }
})()
let map: any = null
watch(
  () => props.map,
  val => {
    map = val
  },
  {
    immediate: true
  }
)
let curLayer: any = null
function changeSelectLayer(option: any) {
  console.log(option, '编辑里')
}
// eventBus.on('selectedTree', params => {
//   console.log(params, '***********')
//   Object.keys(params).forEach((key: any) => {
//     if (form.hasOwnProperty(key)) {
//       form[key] = params[key]
//     }
//   })
// })
const relationStationOptions = ref(<any>[])
function getLinkStation() {
  linkApi
    .getOceanStationList()
    .then((res: any) => {
      relationStationOptions.value = res
    })
    .catch(() => {
      relationStationOptions.value = []
    })
}

// 获取浮标站关联站点
async function getBuoyStationList() {
  relationStationOptions.value = []
  try {
    let result: any = await ToolApi.getBuoyStationList()
    if (result.length) {
      result.forEach((item: any) => {
        item.name = item.code + item.name
        relationStationOptions.value.push(item)
      })
    }
  } catch (e) {
    console.error(e, 'getBuoyStationList')
  }
}

// 获取船舶关联站点
function getShipStation() {
  relationStationOptions.value = []
  ToolApi.getShipDataList()
    .then((res: any) => {
      let result: Record<string, string>[] = []
      res.forEach((item: any) => {
        let obj = {
          name: item,
          code: item
        }
        result.push(obj)
      })
      console.log(result, 'result')
      relationStationOptions.value = result
      console.log(relationStationOptions.value, 'relationStationOptions.value')
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg || '获取失败')
    })
}

function changeStationType(val: string) {
  relationStationOptions.value = []
  if (val === 'buoyStation') {
    getBuoyStationList()
  } else if (val === 'oceanStation') {
    getLinkStation()
  } else {
    getShipStation()
  }
}

let modify: any = null
let curId: any = null
function addModify(selectFeature: any, params: any) {
  if (modify) {
    map.removeInteraction(modify)
  }
  Object.keys(params).forEach((key: any) => {
    if (Object.prototype.hasOwnProperty.call(form, key)) {
      form[key] = params[key]
    }
  })
  console.log(form, 'form====')
  if (form.stationTypeCode === 'buoyStation') {
    getBuoyStationList()
  } else if (form.stationTypeCode === 'ship') {
    getShipStation()
  } else {
    getLinkStation()
  }
  if (selectFeature) {
    curLayer = selectFeature
    curId = params.id

    let coll = new Collection([selectFeature])
    modify = new Modify({
      features: coll,
      style: overlayStyle,
      insertVertexCondition: function () {
        // prevent new vertices to be added to the polygons
        return (
          selectFeature && /Polygon/.test(selectFeature.getGeometry().getType())
        )
      }
    })
    modify.on('modifyend', function (event: any) {
      console.log(event)
      // form.locationJson = JSON.stringify(event.features.getArray()[0])
      form.locationJson = new GeoJSON().writeFeature(
        event.features.getArray()[0]
      )
    })
    map.addInteraction(modify)
  }
}
// 更新要素
function updateFeature(value: any) {
  const feature = new GeoJSON().readFeatures(value)
  console.log(feature, 'feature')
  if (curTab.value === 1) {
    feature[0].setStyle(
      new Style({
        fill: new Fill({
          color: 'rgba(0, 0, 255, 0.1)'
        }),
        stroke: new Stroke({
          color: '#ff0000'
        })
      })
    )
  } else {
    const iconStyle = new Style({
      image: new Icon({
        anchor: [0.5, 1],
        src: stationImg,
        scale: [0.6, 0.6]
      }),
      text: new Text({
        text: form.name,
        font: 'bold 14px Calibri,sans-serif',
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        }),
        offsetY: 10
      })
    })
    feature[0].setProperties({
      name: form.name,
      id: form.id
    })
    feature[0].setStyle(iconStyle)
  }

  if (isEditOut.value) {
    emit('removeFeature', { id: curId }, feature[0])
  } else {
    source.addFeature(feature[0])
  }

  if (modify) {
    map.removeInteraction(modify)
  }
  let coll = new Collection(feature)
  modify = new Modify({
    features: coll,
    style: overlayStyle
  })
  modify.on('modifyend', function (event: any) {
    // form.locationJson = JSON.stringify(event.features.getArray()[0])
    form.locationJson = new GeoJSON().writeFeature(event.features.getArray()[0])
  })
  map.addInteraction(modify)
}
function updatePointName(value: any) {
  if (curTab.value === 2) {
  }
}
function handleFileChange(fileList: any) {
  console.log(fileList, 'fileList')
  let reader = new FileReader() //新建一个FileReader
  reader.readAsText(fileList[0].file, 'UTF-8') //读取文件
  reader.onload = function (evt: any) {
    //读取完文件之后会回来这里
    let fileString = evt.target.result // 读取文件内容
    form.locationJson = fileString
    updateFeature(form.locationJson)
  }
}
function beforeUpload(data: any) {
  console.log(data, 'data')
  if (!data.file.file?.type.includes('json')) {
    message.error('只能上传json格式的文件，请重新上传')
    return false
  }
  return true
}
function handleFinish(options: any) {
  console.log(options, 'file')
  // let reader = new FileReader() //新建一个FileReader
  // reader.readAsText(file.raw, 'UTF-8') //读取文件
  // reader.onload = function (evt: any) {
  //   //读取完文件之后会回来这里
  //   let fileString = evt.target.result // 读取文件内容
  //   console.log(fileString, '*********')
  // }
}
let selectReginData = ref<any>(null)
function handleUpdateValue(val: any, option: any) {
  console.log(val, 'val')
  form.parentCode = val
  console.log(option)
  selectReginData.value = option
}

onMounted(() => {
  const { proxy } = getCurrentInstance() as any
  const parent = proxy.$parent
  map.addLayer(vector)
  if (curTab.value === 2) {
    StationApi.getStationType({
      flag: 'region'
    })
      .then(res => {
        console.log(res, 'res')
        selectTreeData.value = res
      })
      .catch(() => {})
  }
  // eventBus.on('selectedTree', params => {
  //   console.log(params,"***********")
  //   Object.keys(params).forEach(key => {
  //     if (form.hasOwnProperty(key)) {
  //       form[key] = params[key]
  //     }
  //   })
  // })
  // const select = new Select({
  //   style: overlayStyle
  // })
  // select.on('select', function (e) {
  //   e.target
  //     .getFeatures()
  //     .getArray()
  //     .forEach((item: any) => {
  //       console.log(item.values_)
  //       emit('changeSelectFeature', item.values_.id)
  //     })
  // })
  // const modify = new Modify({
  //   features: select.getFeatures(),
  //   style: overlayStyle,
  //   insertVertexCondition: function () {
  //     // prevent new vertices to be added to the polygons
  //     return !select
  //       .getFeatures()
  //       .getArray()
  //       .every(function (feature: any) {
  //         // return /Polygon/.test(feature.getGeometry().getType());
  //         return feature && /Polygon/.test(feature.getGeometry().getType())
  //       })
  //   }
  // })
  // map.addInteraction(select)
  // map.addInteraction(modify)
})
defineExpose({
  changeSelectLayer,
  addModify
})
onUnmounted(() => {
  eventBus.off('selectedTree')
  map.removeLayer(vector)
  map.removeInteraction(modify)
  map.removeInteraction(draw)
})
</script>

<style lang="scss">
.add-data-dialog {
  position: absolute;
  z-index: 99;
  top: 100px;
  right: 20px;
  width: 369px;
  border-radius: 8px;
  overflow: hidden;
  .header {
    background: linear-gradient(180deg, #f1f9ff 0%, #ffffff 100%);

    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 20px;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #222222;
      line-height: 19px;
    }
  }
  .dialog-content {
    background: #fff;
    box-sizing: border-box;
    padding: 11px;
  }
  .icon-scan {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.upload-btn {
  width: 20px;
  height: 20px;
  margin-left: 10px;
  .n-upload-trigger {
    width: 20px;
    height: 20px;
  }
}
</style>
