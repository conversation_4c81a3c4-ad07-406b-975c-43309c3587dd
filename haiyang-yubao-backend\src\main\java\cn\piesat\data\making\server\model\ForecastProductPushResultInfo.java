package cn.piesat.data.making.server.model;

import lombok.Data;

import java.util.Date;

/**
 * 预报产品推送结果
 *
 * <AUTHOR>
 */
@Data
public class ForecastProductPushResultInfo {
    private String taskName;

    private Long id;

    private int mode;

    private String system;

    /**
     * 本次提交事务所有任务推送数据总量
     */
    private int totalCount;
    /**
     * 推送中数据量
     */
    private int pushingCount;

    /**
     * 推送失败数
     */
    private int failedCount;

    /**
     * 推送成功数
     */
    private int successCount;

    /**
     * 发布时间
     */
    private Date publishTime;
}
