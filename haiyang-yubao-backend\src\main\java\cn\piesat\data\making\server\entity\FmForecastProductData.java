package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-09-27 10:27:52
 */

@TableName("fm_forecast_product_data")
public class FmForecastProductData implements Serializable {

    private static final long serialVersionUID = -63628468599501195L;


    @TableId
    private Long id;
    /**
     * 最大值
     */
    @TableField("max_value")
    private String maxValue;
    /**
     * 最小值
     */
    @TableField("min_value")
    private String minValue;
    /**
     * 类型
     */
    @TableField("type")
    private String type;
    /**
     * 名称
     */
    @TableField("name")
    private String name;
    /**
     * 平均值
     */
    @TableField("ave_value")
    private String aveValue;
    /**
     * 区域id
     */
    @TableField("area_id")
    private Long areaId;
    /**
     * 要素编码
     */
    @TableField("element_code")
    private String elementCode;

    /**
     * 数据源
     */
    @TableField("data_source")
    private String dataSource;
    /**
     * 修订后的最大值
     */
    @TableField("modify_max_value")
    private String modifyMaxValue;
    /**
     * 修订后的最小值
     */
    @TableField("modify_min_value")
    private String modifyMinValue;
    /**
     * 修订后的平均值
     */
    @TableField("modify_ave_value")
    private String modifyAveValue;

    @TableField("product_code")
    private String productCode;
    /**
     * 起报时间
     */
    @TableField("start_time")
    private Date startTime;
    /**
     * 预报时长
     */
    @TableField("forecast_time")
    private Date forecastTime;
    /**
     * 区域编码
     */
    @TableField(exist = false)
    private String areaCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(String maxValue) {
        this.maxValue = maxValue;
    }

    public String getMinValue() {
        return minValue;
    }

    public void setMinValue(String minValue) {
        this.minValue = minValue;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAveValue() {
        return aveValue;
    }

    public void setAveValue(String aveValue) {
        this.aveValue = aveValue;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getElementCode() {
        return elementCode;
    }

    public void setElementCode(String elementCode) {
        this.elementCode = elementCode;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getModifyMaxValue() {
        return modifyMaxValue;
    }

    public void setModifyMaxValue(String modifyMaxValue) {
        this.modifyMaxValue = modifyMaxValue;
    }

    public String getModifyMinValue() {
        return modifyMinValue;
    }

    public void setModifyMinValue(String modifyMinValue) {
        this.modifyMinValue = modifyMinValue;
    }

    public String getModifyAveValue() {
        return modifyAveValue;
    }

    public void setModifyAveValue(String modifyAveValue) {
        this.modifyAveValue = modifyAveValue;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getForecastTime() {
        return forecastTime;
    }

    public void setForecastTime(Date forecastTime) {
        this.forecastTime = forecastTime;
    }


    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
}
