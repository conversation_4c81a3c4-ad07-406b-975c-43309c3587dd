package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicTemplateBDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import cn.piesat.data.making.server.vo.FmGraphicTemplateBVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 图形模板表Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:40
 */
@Mapper(componentModel = "spring")
public interface FmGraphicTemplateBMapper {

    FmGraphicTemplateBMapper INSTANCE = Mappers.getMapper(FmGraphicTemplateBMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicTemplateBVO entityToVo(FmGraphicTemplateB entity);

    /**
     * dto-->entity
     */
    FmGraphicTemplateB dtoToEntity(FmGraphicTemplateBDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicTemplateBVO> entityListToVoList(List<FmGraphicTemplateB> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicTemplateB> dtoListToEntityList(List<FmGraphicTemplateBDTO> dtoList);
}
