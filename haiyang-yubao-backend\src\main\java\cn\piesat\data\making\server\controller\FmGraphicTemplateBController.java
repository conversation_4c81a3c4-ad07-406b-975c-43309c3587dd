package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmGraphicTemplateBDTO;
import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.dto.FmGraphicTmplateSignDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import cn.piesat.data.making.server.mapper.FmGraphicTemplateBMapper;
import cn.piesat.data.making.server.service.FmGraphicSignService;
import cn.piesat.data.making.server.service.FmGraphicToolsService;
import cn.piesat.data.making.server.vo.FmGraphicTemplateBVO;
import cn.piesat.data.making.server.service.FmGraphicTemplateBService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 图形模板表控制层
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:40
 */
@RestController
@RequestMapping("fmGraphicTemplateB")
public class FmGraphicTemplateBController {

    @Resource
    private FmGraphicTemplateBService fmGraphicTemplateBService;
    @Resource
    private FmGraphicSignService fmGraphicSignService;
    @Resource
    private FmGraphicToolsService fmGraphicToolsService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.SELECT)
    public PageResult<FmGraphicTemplateBVO> getPage(@RequestParam(required = false) Long id,
                                                    @RequestParam(defaultValue = "1") Integer pageNum,
                                                    @RequestParam(defaultValue = "10") Integer pageSize) {
        FmGraphicTemplateBDTO dto = new FmGraphicTemplateBDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmGraphicTemplateBService.getPage(dto, pageParam);
    }

    /**
     * 查询列表
     *
     * @param productType 产品类型 1 预报 2 警报
     * @param status 模版状态 true 启用 false 禁用
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.SELECT)
    public List<FmGraphicTemplateB> getList(@RequestParam(required = false) String productType,
                                            @RequestParam(required = false) Boolean status,
                                            @RequestParam(required = false) String templateType ) {
        FmGraphicTemplateBDTO dto = new FmGraphicTemplateBDTO();
        dto.setProductType(productType);
        dto.setStatus(status);
        dto.setTemplateType(templateType);
        return fmGraphicTemplateBService.getListAll(dto);
    }

    /**
     * 查询列表
     *
     * @param productType 产品类型 1 预报 2 警报
     * @return
     */
    @GetMapping("/getListParent")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.SELECT)
    public List<FmGraphicTemplateBVO> getListParent(@RequestParam(required = false) String productType) {
        FmGraphicTemplateBDTO dto = new FmGraphicTemplateBDTO();
        dto.setProductType(productType);
        return fmGraphicTemplateBService.getListParent(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.SELECT)
    public FmGraphicTemplateBVO getById(@PathVariable Long id) {
        return fmGraphicTemplateBService.getById(id);
    }

    /**
     * 根据parentId查询数据
     *
     * @param parentId
     * @return
     */
    @GetMapping("/getByParentId/{parentId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.SELECT)
    public List<FmGraphicTemplateBVO> getByParentId(@PathVariable Long parentId) {
        return fmGraphicTemplateBService.getByParentId(parentId);
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {FmGraphicTemplateBDTO.Save.class}) @RequestBody FmGraphicTemplateBDTO dto) {
        fmGraphicTemplateBService.save(dto);
    }

    /**
     * 更新数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.INSERT)
    public void update(@Validated(value = {FmGraphicTemplateBDTO.Save.class}) @RequestBody FmGraphicTemplateBDTO dto) {
        List<FmGraphicTemplateToolsDTO> tools = dto.getTools();
        List<FmGraphicTmplateSignDTO> sign = dto.getSign();
        fmGraphicSignService.saveByTemplateId(sign,dto.getId());
        fmGraphicToolsService.saveByTemplateId(tools,dto.getId());
        fmGraphicTemplateBService.updateById(FmGraphicTemplateBMapper.INSTANCE.dtoToEntity(dto));
    }

    /**
     * 更新图形模版状态
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateStatus")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.INSERT)
    public void updateStatus(@Validated(value = {FmGraphicTemplateBDTO.Save.class}) @RequestBody FmGraphicTemplateBDTO dto) {

        fmGraphicTemplateBService.updateStatus(dto);
    }

    /**
     * 更新图形模版名称
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateName")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.UPDATE)
    public void updateName(@RequestBody FmGraphicTemplateBDTO dto) {
        fmGraphicTemplateBService.updateName(dto);
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmGraphicTemplateBService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmGraphicTemplateBService.deleteByIdList(idList);
    }

    /**
     * base64上传
     *
     * @param base64ImageData
     * @return
     */
    @PostMapping("/uploadImageBase")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.DELETE)
    public String uploadImageBase(@RequestParam String base64ImageData) {
        return fmGraphicTemplateBService.uploadImageBase(base64ImageData);
    }

    /**
     * 图片上传
     *
     * @param file
     * @return
     */
    @PostMapping("/uploadImage")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形模板管理", operateType = OperateType.DELETE)
    public String uploadImage(@RequestParam("file") MultipartFile file) {
        return fmGraphicTemplateBService.uploadImageFile(file);
    }
}
