package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.LiveObserveElementDao;
import cn.piesat.data.making.server.entity.LiveObserveElement;
import cn.piesat.data.making.server.service.LiveObserveElementService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实况观测要素表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LiveObserveElementServiceImpl extends ServiceImpl<LiveObserveElementDao, LiveObserveElement>
        implements LiveObserveElementService {

    @Override
    public List<LiveObserveElement> getList(LiveObserveElement entity) {
        return this.list(createQueryWrapper(entity));
    }

    private LambdaQueryWrapper<LiveObserveElement> createQueryWrapper(LiveObserveElement entity) {
        LambdaQueryWrapper<LiveObserveElement> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(entity.getType())) {
            queryWrapper.eq(LiveObserveElement::getType, entity.getType());
        }
        return queryWrapper.orderByAsc(LiveObserveElement::getId);
    }
}





