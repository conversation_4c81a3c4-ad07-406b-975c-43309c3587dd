<template>
  <!-- 图形模板 -->
  <div class="image-temp d-flex">
    <Aside @selected="selectedModel" @change-tab="clearMapData" />
    <div class="content">
      <div v-show="isEdit" class="content-header">
        <h3>模板编辑</h3>
      </div>
      <div class="main-container d-flex">
        <div id="openlayers-map" class="image-editor" />
        <div v-show="isEdit" class="config">
          <div class="config-title">模板配置</div>
          <n-form
            ref="formRef"
            class="config-info"
            :model="form"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="left"
          >
            <div class="title">截图区域</div>

            <n-grid :cols="24" :x-gap="12" class="mb10">
              <n-form-item-gi :span="12" label="左上经度" path="leftLon">
                <n-input
                  v-model:value="form.leftLon"
                  placeholder="请输入"
                  @change="handleChange"
                />
              </n-form-item-gi>
              <n-form-item-gi :span="12" label="左上纬度" path="leftLat">
                <n-input
                  v-model:value="form.leftLat"
                  placeholder="请输入"
                  @change="handleChange"
                />
              </n-form-item-gi>
            </n-grid>

            <n-grid :cols="24" :x-gap="12">
              <n-form-item-gi :span="12" label="右下经度" path="rightLon">
                <n-input
                  v-model:value="form.rightLon"
                  placeholder="请输入"
                  @change="handleChange"
                />
              </n-form-item-gi>
              <n-form-item-gi :span="12" label="右下纬度" path="rightLat">
                <n-input
                  v-model:value="form.rightLat"
                  placeholder="请输入"
                  @change="handleChange"
                />
              </n-form-item-gi>
            </n-grid>

            <n-divider />

            <div class="title">标签选择</div>
            <n-checkbox-group
              v-model:value="labels"
              class="qx-checkbox-group oneLine"
              @update:value="changeLabels"
            >
              <n-checkbox
                v-for="item in labelList"
                :key="item.id"
                class="qx-checkbox"
                :value="item.id"
                :label="item.context"
              />
            </n-checkbox-group>

            <n-divider />

            <div v-show="curTab === 2" class="title">图例上传</div>
            <n-upload
              v-show="curTab === 2"
              action=""
              :file-list="fileListImg"
              list-type="image-card"
              :max="1"
              @change="changeLegend"
              @before-upload="beforeUpload"
            >
              + 上传
            </n-upload>

            <div class="config-title mb10">模板配置</div>
            <div class="title">工具选择</div>
            <n-checkbox-group
              v-model:value="tool"
              class="qx-checkbox-group d-flex flex-wrap"
            >
              <n-checkbox
                v-for="item in toolList"
                :key="item.type"
                class="qx-checkbox"
                :value="item.type"
                :label="item.name"
              />
            </n-checkbox-group>
            <n-divider />
            <div class="btns text-right">
              <!-- <qx-button @click="reset">重置</qx-button> -->
              <qx-button class="primary" @click="saveTemplate">保存</qx-button>
            </div>
          </n-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { Aside } from './index'
import type { UploadFileInfo } from 'naive-ui'
import { useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import olFunc from 'src/components/OpenlayersMap/openlayersInit'
import gisUtils from 'src/utils/gis'
import Api from 'src/requests/forecast'
import { disable } from 'ol/rotationconstraint'
const message = useMessage()
let map: any = null
let isEdit = ref(false)

const form = reactive({
  leftLon: '107.35',
  leftLat: '21.95',
  rightLon: '112.63',
  rightLat: '17.76'
})
const rules = reactive({})
const tool = ref<any[]>([])
const labels = ref<any[]>([])
const curTab = ref(1)
const labelList = ref<any[]>([
  {
    context: '发布时间：yyyy-MM-dd hh时发布',
    id: 1
  },
  {
    context: '制作单位：海南省海洋预报台',
    id: 2
  },
  {
    context: '预报时效：24小时',
    id: 3
  },
  {
    context: '预报时效：48小时',
    id: 4
  },
  {
    context: '审图号：琼S（2024）041号',
    id: 5
  }
])

const toolList = ref<any[]>([])

const fileListImg = ref<UploadFileInfo[]>([])
// 校验上传的图片格式
function beforeUpload(file: any) {
  const { type = '' } = file.file
  if (type.indexOf('image') === -1) {
    message.error('文件格式错误')
    return false
  }
  return true
}
let legendLayer: any = null
let legendInfo: any = null
// 图例修改
function changeLegend({
  file,
  fileList,
  event
}: {
  file: any
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  if (fileList.length > 0) {
    // if(file.status === 'finished') {}
    if (event && (event.type === 'load' || event.type === 'change')) {
      let formdata = new FormData()
      formdata.append('file', file.file)
      Api.uploadLegendImg(formdata)
        .then((res: any) => {
          console.log(res)
          if (res) {
            legendInfo = {
              src: res,
              coordinate: [Number(form.leftLon), Number(form.rightLat)] // [107.35, 17.76]
            }
            legendLayer = gisUtils.addLegend(map, legendInfo, legendLayer)
            fileListImg.value = []
            fileListImg.value.push({
              id: 'c',
              name: 'aaa.png',
              status: 'finished',
              url: res
            })
          }
        })
        .catch(() => {})
    }
  } else {
    if (legendLayer) {
      gisUtils.removeLayer(map, legendLayer)
      legendLayer = null
    }
    fileListImg.value = []
  }
  console.log(file, fileList, event)
}
function getBase64(file: any) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    let fileResult: any = ''
    reader.readAsDataURL(file) //开始转
    reader.onload = function () {
      fileResult = reader.result
    }
    //转失败
    reader.onerror = function (error) {
      reject(error)
    }
    //转结束 resolve 出去
    reader.onloadend = function () {
      resolve(fileResult)
    }
  })
}
let labelLayer: any = null
function changeLabels(
  value: (string | number)[]
  // meta: { actionType: 'check' | 'uncheck'; value: string | number }
) {
  console.log('调用了', value)

  // if (meta.actionType === 'check') {
  //   if (meta.value === "3") {
  //     if (value.includes("4")) {
  //       // 移除48小时
  //       let index = labels.value.indexOf("4")
  //       labels.value.splice(index, 1)
  //     }
  //   }
  //   if (meta.value === "4") {
  //     if (value.includes("3")) {
  //       // 移除24小时
  //       let index = labels.value.indexOf("3")
  //       labels.value.splice(index, 1)
  //     }
  //   }
  // }
  const labelInfo = []
  let str = ''
  value.sort((a: any, b: any) => a - b)
  value.forEach(item => {
    let la = labelList.value.find(ele => ele.id === item)

    if (la && la.position === 'leftDown') {
      console.log(la, '**************')
      /**
       * leftLon: '107.35',
        leftLat: '21.95',
        rightLon: '112.63',
        rightLat: '17.76'
       */
      labelInfo.push({
        label: la.context,
        name: la.position,
        coordinate: [Number(form.leftLon), Number(form.rightLat)] //[107.35, 17.76]
      })
    } else {
      str = str + labelList.value.find(ele => ele.id === item).context + '\n'
    }
  })
  labelInfo.push({
    label: str,
    name: 'topLabel',
    coordinate: [Number(form.leftLon), Number(form.leftLat)]
  })
  console.log('labelInfo*******', labelInfo)
  if (
    form.leftLon !== '' &&
    form.leftLat !== '' &&
    form.rightLon !== '' &&
    form.rightLat !== ''
  ) {
    labelLayer = gisUtils.addLabel(map, labelInfo, labelLayer)
  }
}

let rangeRectLayer: any = null
// 修改范围框
function handleChange() {
  rangeRectLayer = gisUtils.addRect(map, form, rangeRectLayer)
}
// 重置图上模板
function reset() {
  //
}
function initLabel(info: any) {
  const labelInfo = []
  let str = ''
  info.forEach((item: any) => {
    if (item.position === 'leftDown') {
      labelInfo.push({
        label: item.context,
        name: item.position,
        coordinate: [Number(form.leftLon), Number(form.rightLat)] //[107.35, 17.76]
      })
    } else {
      str = str + item.context + '\n'
    }
  })
  labelInfo.push({
    label: str,
    name: 'topLabel',
    coordinate: [Number(form.leftLon), Number(form.leftLat)]
  })
  if (
    form.leftLon !== '' &&
    form.leftLat !== '' &&
    form.rightLon !== '' &&
    form.rightLat !== ''
  ) {
    labelLayer = gisUtils.addLabel(map, labelInfo, labelLayer)
  }
}
// 选中模板
let curTemplateInfo: any = null
function selectedModel(info: any) {
  if (info.imageUrl && info.imageUrl !== '' && curTab.value === 2) {
    fileListImg.value = []
    fileListImg.value.push({
      id: 'c',
      name: 'aaa.png',
      status: 'finished',
      url: info.imageUrl
    })
  } else {
    fileListImg.value = []
  }

  getLabels(info)
  getTools(info)
  curTemplateInfo = info
  isEdit.value = true
  form.leftLon = info.leftLongitude
  form.leftLat = info.leftLatitude
  form.rightLon = info.rightLongitude
  form.rightLat = info.rightLatitude
  if (
    form.leftLon !== '' &&
    form.leftLat !== '' &&
    form.rightLon !== '' &&
    form.rightLat !== ''
  ) {
    rangeRectLayer = gisUtils.addRect(map, form, rangeRectLayer)
    if (info.imageUrl && info.imageUrl !== '' && curTab.value === 2) {
      legendInfo = {
        src: info.imageUrl,
        coordinate: [Number(form.leftLon), Number(form.rightLat)] // [107.35, 17.76]
      }
      legendLayer = gisUtils.addLegend(map, legendInfo, legendLayer)
    }
  }
  labels.value = []
  info.sign.forEach((item: any) => {
    labels.value.push(item.id)
  })
  tool.value = []
  info.tools.forEach((item: any) => {
    tool.value.push(item.type)
  })
  initLabel(info.sign)
}
function getLabels(info: any) {
  Api.getTemplateLabel({ type: info.templateType })
    .then((res: any) => {
      labelList.value = res
    })
    .catch(() => {
      labelList.value = []
    })
}
function getTools(info: any) {
  Api.getTemplateTool({ type: info.templateType })
    .then((res: any) => {
      toolList.value = res
    })
    .catch(() => {
      toolList.value = []
    })
}
// 清空图层（切换tab时需要清空图层）
function clearMapData(value: any) {
  isEdit.value = false
  curTab.value = value
  fileListImg.value = []
  if (rangeRectLayer) {
    gisUtils.removeLayer(map, rangeRectLayer)
    rangeRectLayer = null
  }
  if (legendLayer) {
    gisUtils.removeLayer(map, legendLayer)
    legendLayer = null
  }
  if (labelLayer) {
    gisUtils.removeLayer(map, labelLayer)
    labelLayer = null
  }
}
function saveTemplate() {
  const data = {
    ...curTemplateInfo
  }
  data.leftLongitude = form.leftLon
  data.leftLatitude = form.leftLat
  data.rightLongitude = form.rightLon
  data.rightLatitude = form.rightLat
  data.sign = []

  labels.value.forEach((item: any) => {
    let la = labelList.value.find(ele => ele.id === item)
    data.sign.push(la)
  })
  data.tools = []
  tool.value.forEach((item: any) => {
    let too = toolList.value.find(ele => ele.type === item)
    data.tools.push(too)
  })
  data.imageUrl = legendInfo?.src
  Api.updateProductTemp(data)
    .then((res: any) => {
      message.success('保存成功')
    })
    .catch(() => {})
}
onMounted(function () {
  map = olFunc.init({ elId: 'openlayers-map' })
  //
  // gisUtils.getThematic({})
  // gisUtils.addText(map, { position: [107.35, 21.95], fontSize: "30px", text: 'ol' })
})
</script>

<style lang="scss">
.image-temp {
  height: 100%;
  .content {
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    .content-header {
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      box-sizing: border-box;
      padding: 9px 20px;
      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 21px;
      }
    }
    .main-container {
      flex: 1;
      height: 0;
      .image-editor {
        flex: 1;
        background: transparent;
      }
    }
  }
  .config {
    height: 100%;
    width: 320px;
    background: #fafafa;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-top: none;
    flex-shrink: 0;
    overflow: auto;
    .config-title {
      box-sizing: border-box;
      padding: 11px 0 10px 14px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      background: #f7f9fa;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    .config-info {
      box-sizing: border-box;
      padding: 12px;
      .title {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 600;
        font-size: 12px;
        color: #222222;
        line-height: 14px;
        margin-bottom: 13px;
      }
      .n-form-item-label {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 32px;
        min-height: 32px;
      }
      .n-input .n-input__input-el,
      .n-form-item-blank {
        height: 32px;
        line-height: 32px;
        min-height: 32px;
      }
    }
  }
  .n-divider.n-divider--no-title {
    margin: 20px 0 9px;
  }
  .n-upload-file-list.n-upload-file-list--grid {
    grid-template-columns: repeat(auto-fill, 88px);
  }
  .n-upload-trigger.n-upload-trigger--image-card {
    width: 88px;
    height: 88px;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    .n-upload-dragger {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
    }
  }
  .n-upload-file-list .n-upload-file.n-upload-file--image-card-type {
    width: 88px;
    height: 88px;
    border-radius: 8px;
  }

  .n-upload-file-list
    .n-upload-file
    .n-upload-file-info
    .n-upload-file-info__action.n-upload-file-info__action--image-card-type {
    background: rgba(0, 0, 0, 0.5);
  }

  .btns.text-right {
    margin: 10px 0;
  }
}

.mb10 {
  margin-bottom: 10px;
}

.qx-checkbox-group {
  &.oneLine {
    display: flex;
    flex-direction: column;
  }
  &.d-flex {
    .qx-checkbox {
      width: calc(100% / 3);
    }
  }
  &.custom-group {
    display: flex;
    .qx-checkbox:nth-child(1) {
      width: 30%;
    }
    .qx-checkbox:not(:nth-child(1)) {
      flex: 1;
    }
  }
  .qx-checkbox {
    margin-bottom: 13px;
  }
}
.qx-checkbox {
  .n-checkbox__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #222222;
    line-height: 14px;
  }
}
</style>
