package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.OceanFacility;
import cn.piesat.data.making.server.model.OceanFacilityImport;
import cn.piesat.data.making.server.service.OceanFacilityService;
import cn.piesat.data.making.server.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class OceanFacilityControllerTest {

    @Resource
    private OceanFacilityService oceanFacilityService;

    @Test
    public void readAndSaveShipData() throws FileNotFoundException {
        File file = new File("C:\\Users\\<USER>\\Desktop\\海洋设施.xlsx");
        FileInputStream fis = new FileInputStream(file);
        List<OceanFacility> list = new ArrayList<>();
        List<OceanFacilityImport> importList = ExcelUtil.readData(fis, 0, OceanFacilityImport.class);
        importList.stream().forEach(facilityImport -> {
            OceanFacility facility = new OceanFacility();
            facility.setType(facilityImport.getType());
            facility.setCode(facilityImport.getCode());
            facility.setLongitude(facilityImport.getLongitude());
            facility.setLatitude(facilityImport.getLatitude());
            facility.setHeight(facilityImport.getHeight());
            list.add(facility);
        });
        oceanFacilityService.saveBatch(list);
    }
}