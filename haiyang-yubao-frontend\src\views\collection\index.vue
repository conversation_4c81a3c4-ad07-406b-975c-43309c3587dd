<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-22 09:36:19
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-07 11:19:50
 * @FilePath: /hainan-jianzai-web/src/views/collection/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <micro-common
    :url="url"
    app-name="assemble_sub_web"
    replace-path="/pooledProcessing/collection"
  />
</template>

<script setup lang="ts">
import { MicroCommon } from 'src/components/microApp'

const url = `${config.assemble_sub_web}`
</script>

<style scoped></style>
