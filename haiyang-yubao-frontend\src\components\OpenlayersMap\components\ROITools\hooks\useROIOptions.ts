import { type Component, onMounted, provide, Ref, ref } from 'vue'
import DrawROI from '../com-draw/DrawROI.vue'
import { defaultColors } from './useColors'
import { useROIInject } from './useROIInject'
import ModifyROI from 'src/components/OpenlayersMap/components/ROITools/com-modify/ModifyROI.vue'
import MoveROI from 'src/components/OpenlayersMap/components/ROITools/com-move/MoveROI.vue'
import DeleteROI from 'src/components/OpenlayersMap/components/ROITools/com-delete/DeleteROI.vue'
import ClearROI from 'src/components/OpenlayersMap/components/ROITools/com-clear/ClearROI.vue'


/**
 * ROI 选项类型
 */
export interface IROIOptions {
  opacity: number
  strokeWidth: number
  strokeColor: string
  fillColor: string
  value: string
}

// 激活的动作类型
export enum ActionType {
  NONE,
  DRAW,
  MODIFY,
  MOVE,
  DELETE,
  CLEAR
}

/**
 * 落区选项
 */
export function useROIOptions() {
  const roiOptions = ref(resetROIOptions())
  // 当前选择的子工具
  const currentAction = ref(ActionType.NONE)
  useROIInject('roiOptions').provideROI(roiOptions)
  useROIInject('currentAction').provideROI(currentAction)

  onMounted(() => {
    roiOptions.value = resetROIOptions()
  })

  return {
    roiOptions,
    defaultStrokeWidths,
    formatTooltip,
    currentAction,
    toggleAction: (targetActionType: ActionType) =>
      toggleAction(currentAction, targetActionType),
    actionTypeToComponentMap
  }
}

/**
 * 默认线宽列表
 */
export function defaultStrokeWidths() {
  return [
    {
      label: '1px',
      value: 1
    },
    {
      label: '2px',
      value: 2
    },
    {
      label: '3px',
      value: 3
    }
  ]
}

// 格式化 formatter
export const formatTooltip = (value: number) => `${value}%`

/**
 * 枚举 -> 组件
 */
export const actionTypeToComponentMap: {
  [key in ActionType]: Component | undefined
} = {
  [ActionType.NONE]: void 0,
  [ActionType.DRAW]: DrawROI,
  [ActionType.MODIFY]: ModifyROI,
  [ActionType.MOVE]: MoveROI,
  [ActionType.DELETE]: DeleteROI,
  [ActionType.CLEAR]: ClearROI
}

/**
 * 切换激活的工具
 * @param currentAction
 * @param targetActionType
 */
export function toggleAction(
  currentAction: Ref<ActionType>,
  targetActionType: ActionType
) {
  if (
    currentAction.value === ActionType.NONE ||
    currentAction.value !== targetActionType
  ) {
    currentAction.value = targetActionType
  } else {
    currentAction.value = ActionType.NONE
  }
}

/**
 * 重置选项
 */
export function resetROIOptions(): IROIOptions {
  return {
    fillColor: defaultColors()[0].value,
    opacity: 0,
    strokeColor: defaultColors()[0].value,
    strokeWidth: 1,
    value: '3'
  }
}
