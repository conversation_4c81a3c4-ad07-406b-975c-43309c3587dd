<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-22 09:36:19
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2024-11-25 17:15:18
 * @FilePath: /hainan-jianzai-web/src/views/system/accessStatistics/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 用户访问统计 -->
  <div class="access-statistics">
    <div class="common-header d-flex flex-align-center flex-justify-between">
      <h3>{{ searchTime }}流量报告</h3>
      <n-date-picker
        v-model:formatted-value="searchTime"
        type="date"
        clearable
        value-format="yyyy-MM-dd"
        @update:formatted-value="onChangeTime"
      />
    </div>
    <div class="d-flex statistics-info-wrapper">
      <div class="statistics-info d-flex">
        <div class="statistics-info-item">
          <div class="title">IP数量</div>
          <span>34</span>
        </div>
        <div class="statistics-info-item">
          <div class="title">浏览量（PV）</div>
          <span>47</span>
        </div>

        <div class="statistics-info-item">
          <div class="title">访客数（UV）</div>
          <span>34</span>
        </div>

        <div class="statistics-info-item">
          <div class="title">接口访问次数</div>

          <span>211</span>
        </div>
      </div>
      <div class="statistics-info-chart">
        <qx-echarts :option="echartOption" />
      </div>
    </div>

    <div class="chart-list d-flex flex-wrap flex1">
      <div class="chart-item">
        <div class="column-header">
          <h3>流量分析</h3>
        </div>
        <common-chart :data="tableData" :columns="columns" />
      </div>
      <div class="chart-item">
        <div class="column-header">
          <h3>访客分析</h3>
        </div>
        <common-chart :data="visitorTableData" :columns="visitorColumn" />
      </div>
      <div class="chart-item content-analysis">
        <div class="column-header">
          <h3>内容分析</h3>
        </div>
        <div class="table-container">
          <n-data-table
            :bordered="false"
            :single-line="true"
            :columns="contentColumns"
            :data="contentTableData"
          />
        </div>
      </div>
      <div class="chart-item">
        <div class="column-header">
          <h3>终端分析</h3>
        </div>
        <common-chart :data="terminalTableData" :columns="terminalColumn" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import { ref, onMounted } from 'vue'
import { QxEcharts } from 'src/components/QxEcharts'
import { CommonChart } from './index'
import type { DataTableColumn } from 'naive-ui'

let searchTime = ref(moment().format('YYYY-MM-DD'))
let echartOption = ref({})

function renderChart() {
  let data = []
  for (let i = 0; i < 24; i++) {
    data.push({
      time: `2024-11-22 ${i < 10 ? '0' + i : i}:00:00`,
      value: Math.round(Math.random() * 100)
    })
  }
  let result = JSON.parse(JSON.stringify(data))
  result.sort((a: any, b: any) => {
    return a.value + b.value
  })
  let maxTime = moment(result[0].time, 'YYYY-MM-DD HH:mm:ss').hours()
  let minTime = moment(
    result[result.length - 1].time,
    'YYYY-MM-DD HH:mm:ss'
  ).hours()

  echartOption.value = {
    title: {
      subtext: [
        '接口访问次数最高峰是{b|' +
          maxTime +
          '}时,最低谷是{b|' +
          minTime +
          '}时'
      ],
      left: '50px',
      top: '-10px',
      subtextStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        rich: {
          b: { color: '#1C81F8', fontSize: 14, fontWeight: 'bold' }
        }
      }
    },
    xAxis: {
      type: 'time'
    },
    yAxis: {
      type: 'value'
    },
    dataset: {
      dimensions: ['time', 'value'],
      source: data
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    series: [
      {
        encode: {
          x: 'time',
          y: 'value'
        },
        type: 'line',
        smooth: true
      }
    ]
  }
}

let tableData = ref<any[]>([
  {
    source: '网站',
    percentage: 10,
    chain: 10,
    time: 10
  },
  {
    source: 'APP',
    percentage: 10,
    chain: 10,
    time: 10
  },
  {
    source: '微博',
    percentage: 10,
    chain: 10,
    time: 10
  },
  {
    source: '小程序',
    percentage: 10,
    chain: 10,
    time: 10
  },
  {
    source: '短视频',
    percentage: 10,
    chain: 10,
    time: 10
  }
])
const columns = ref<DataTableColumn[]>([
  {
    title: '来源(%)',
    key: 'source',
    align: 'center'
  },
  {
    title: '占比(%)',
    key: 'value',
    align: 'center'
  },
  {
    title: '环比(%)',
    key: 'chain',
    align: 'center'
  },
  {
    title: '平均访问时长(%)',
    key: 'time',
    align: 'center'
  }
])

const visitorColumn = ref<DataTableColumn[]>([
  {
    title: '访客',
    key: 'source'
  },
  {
    title: '占比',
    key: 'value'
  },
  {
    title: '环比',
    key: 'chain'
  },
  {
    title: '平均访问时长',
    key: 'time'
  }
])
let visitorTableData = ref<any[]>([
  {
    source: '老访客',
    percentage: 65,
    chain: 54,
    time: 10
  },
  {
    source: '新访客',
    percentage: 33,
    chain: 16,
    time: 26
  }
])

let terminalTableData = ref<any[]>([
  {
    source: 'PC端',
    percentage: 10,
    chain: 10,
    time: 10
  },
  {
    source: 'Android',
    percentage: 40,
    chain: 40,
    time: 40
  },
  {
    source: 'IOS',
    percentage: 56,
    chain: 35,
    time: 77
  }
])
const terminalColumn = ref<DataTableColumn[]>([
  {
    title: '终端',
    key: 'source'
  },
  {
    title: '占比',
    key: 'value'
  },
  {
    title: '环比',
    key: 'chain'
  },
  {
    title: '平均访问时长',
    key: 'time'
  }
])

let contentColumns = ref<any[]>([
  {
    title: '序号',
    render(row: any, index: number) {
      return index + 1
    },
    align: 'center',
    width: 60
  },
  {
    title: '最受欢迎功能TOP10',
    key: 'name'
  },
  {
    title: '来源',
    key: 'source'
  },
  {
    title: '贡献IP',
    key: 'ip'
  },
  {
    title: '环比(%)',
    key: 'chain'
  },
  {
    title: '贡献PV',
    key: 'pv'
  },
  {
    title: '环比(%)',
    key: 'chain1'
  }
])
let contentTableData = ref<any[]>([
  {
    name: '功能1',
    source: '网站',
    ip: 10,
    chain: 10,
    pv: 10,
    chain1: 10
  },
  {
    name: '功能2',
    source: 'APP',
    ip: 10,
    chain: 10,
    pv: 10,
    chain1: 10
  },
  {
    name: '功能3',
    source: '微博',
    ip: 10,
    chain: 10,
    pv: 10,
    chain1: 10
  },
  {
    name: '功能4',
    source: '小程序',
    ip: 10,
    chain: 10,
    pv: 10,
    chain1: 10
  }
])

// 根据属性求和
function getSum(data: any, key: string) {
  return data.reduce((item: number, obj: any) => (item += obj[key]), 0)
}

//时间修改
function onChangeTime() {}

function formatData(data:any){
  let percentage = getSum(data, 'percentage')
  let chain = getSum(data, 'chain')
  let time = getSum(data, 'time')

  data.forEach((item: any) => {
    item.value = `${((item.percentage / percentage) * 100).toFixed(2)}`
    item.chain = `${((item.chain / chain) * 100).toFixed(2)}`
    item.time = `${((item.time / time) * 100).toFixed(2)}`
    item.name = item.source
  })
}


onMounted(() => {
  formatData(visitorTableData.value)
  formatData(terminalTableData.value)
  formatData(tableData.value)

  let pvSum = getSum(contentTableData.value, 'pv')
  let ipSum = getSum(contentTableData.value, 'ip')
  contentTableData.value.forEach((item: any) => {
    item.chain = `${((item.chain / ipSum) * 100).toFixed(2)}`
    item.chain1 = `${((item.chain1 / pvSum) * 100).toFixed(2)}`
  })
  renderChart()
})
</script>

<style scoped lang="scss">
.access-statistics {
  display: flex;
  flex-direction: column;
  .content-header {
    box-sizing: border-box;
    padding: 18px 23px 8px 26px;
    width: 100%;
    background-color: #fff;
    background-image: url(/src/assets/images/common/content-header.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 23px;
      margin-right: 12px;
    }
  }
  .statistics-info-wrapper {
    background: #fff;
    margin-bottom: 12px;
    .statistics-info {
      width: 40%;
      &-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
        box-sizing: border-box;
        padding: 10px 0;
        .title{
          margin-bottom: 30px;
        }
        span {
          font-size: 40px;
          color: #222222;
        }
      }
    }
    .title {
      font-size: 13px;
      color: #7f7f7f;
      margin-bottom: 20px;
    }
  }
  .statistics-info-chart {
    flex: 1;
    height: 133px;
  }
  .column-header {
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      padding-left: 10px;
      position: relative;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 3px;
        height: 19px;
        background: rgba(64, 124, 242, 1);
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
  .chart-item {
    width: calc(50% - 10px);
    height: calc(50% - 10px);
    background: #fff;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 12px;
    &:nth-child(1),
    &:nth-child(3) {
      margin-right: 12px;
    }
    &:nth-child(1),
    &:nth-child(2) {
      margin-bottom: 12px;
    }
  }
  .content-analysis {
    .column-header {
      margin-bottom: 12px;
    }
  }
}
</style>