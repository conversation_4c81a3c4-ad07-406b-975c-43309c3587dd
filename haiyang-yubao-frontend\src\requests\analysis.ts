/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-24 15:03:59
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-31 10:24:18
 * @FilePath: \hainan-jianzai-web\src\requests\analysis.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import getAxios from 'src/utils/axios'

const productUrl = import.meta.env.VITE_PRODUCT_BASE_URL
const axiosProductInstance = getAxios(productUrl)
const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)
const meteoEttpUrl = import.meta.env.VITE_ETTP_SERVICE_BASE_API
const axiosMeteoEttpInstance = getAxios(meteoEttpUrl)
const ettpUrl = import.meta.env.VITE_ETTP_BASE_API
const axiosEttpInstance = getAxios(ettpUrl)

interface ProductGeojsonResponse {
  big_type: string
  data_time: string
  event_id: string
  ext_code: string
  file_format: string
  file_name: string
  file_path: string
  file_size: string
  forecast_time: string
  id: string
  insert_time: string
  product_id: string
  product_level: string
  son_type: string
  time_split: string
}

const Analysis = {
  // 获取产品geoJson
  getProductGeojson(productId: string, params: any): Promise<ProductGeojsonResponse[]> {
    return axiosProductInstance({
      url: `/api/product/record/range/forecast/${productId}`,
      method: 'GET',
      params
    })
  },
  // 获取风暴潮站点列表
  getStationListofStormSurge(params?: any) {
    return axiosInstance({
      url: '/station/list',
      method: 'GET',
      params
    })
  },
  //根据站点id、起报时间查询潮汐数据列表
  getTideList(params: any) {
    return axiosInstance({
      url: '/tide/list',
      method: 'GET',
      params
    })
  },
  //根据产品标识获取tiff数据列表
  getTiff(params: any) {
    return axiosEttpInstance({
      url: '/ettp/queryCondition',
      method: 'GET',
      params
    })
  },

  //根据产品标识获取数据源标识与图层标识
  getIdentifybyProductId(productId: string) {
    return axiosEttpInstance({
      url: `/ettp/queryIdentify/${productId}`,
      method: 'GET'
    })
  },

  // 获取图层样式
  getLayerStyle(item: string, source: string, layer: string, hign?: any) {
    return axiosMeteoEttpInstance({
      url: `/meteo_ettp_service/style/findStyleByLayerIdentify/${item}/${source}/${layer}/${hign}`,
      method: 'GET'
    })
  },

  /**
   * 获取实况观测站点潮位
   * @param params
   * @returns
   */
  getOceanStationTideList(params: any) {
    return axiosInstance({
      url: `/oceanStation/tideList`,
      method: 'GET',
      params
    })
  },
  /**
   * 风暴潮导出数据
   * @param params
   * @returns
   */
  tideExport(params: any) {
    return axiosInstance({
      url: '/tide/export',
      method: 'GET',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取风暴潮数据站点潮位
   * @param params
   * @returns
   */
  getStormSurgeTideList(params: any) {
    return axiosInstance({
      url: '/stormSurge/tideList',
      method: 'GET',
      params
    })
  },
  //查询中台海洋站列表 数据维护新增站点时使用
  getOceanStationList() {
    return axiosInstance({
      url: '/oceanStation/list',
      method: 'GET'
    })
  }
}

export default Analysis
