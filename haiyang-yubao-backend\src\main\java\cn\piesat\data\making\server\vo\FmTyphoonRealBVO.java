package cn.piesat.data.making.server.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * 台风实时数据信息VO类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
public class FmTyphoonRealBVO implements Serializable {

    private static final long serialVersionUID = -80111183754305202L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 台风编号
     */
    private String tfbh;
    /**
     * 数据时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    /**
     * 经度
     */
    private Double lng;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 强度
     */
    private String strong;
    /**
     * 等级
     */
    private Double power;
    /**
     * 中心速度
     */
    private Double speed;
    /**
     * 移动方向
     */
    @JsonAlias(value = {"movedirection"})
    private String moveDir;
    /**
     * 移动速度
     */
    @JsonAlias(value = {"movespeed"})
    private Double moveSpeed;
    /**
     * 中心气压
     */
    private Double pressure;
    /**
     * 七级大风半径
     */
    private Double radius7;
    /**
     * 十级大风半径
     */
    private Double radius10;
    /**
     * 十二级大风半径
     */
    private Double radius12;
    /**
     * 七级大风四边形
     */
    private String radius7Quad;
    /**
     * 十级大风四边形
     */
    private String radius10Quad;
    /**
     * 十二级大风四边形
     */
    private String radius12Quad;

    private String ybsj;

    private String tm;

    @JsonProperty(required = false)
    private List<FmTyphoonForecastVO> forecast;

    private String remark;

    public String getRadius7Quad() {
        return radius7Quad;
    }

    public void setRadius7Quad(String radius7Quad) {
        this.radius7Quad = radius7Quad;
    }

    public String getRadius10Quad() {
        return radius10Quad;
    }

    public void setRadius10Quad(String radius10Quad) {
        this.radius10Quad = radius10Quad;
    }

    public String getRadius12Quad() {
        return radius12Quad;
    }

    public void setRadius12Quad(String radius12Quad) {
        this.radius12Quad = radius12Quad;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public Double getPower() {
        return power;
    }

    public FmTyphoonRealBVO setPower(Double power) {
        this.power = power;
        return this;
    }

    public Double getSpeed() {
        return speed;
    }

    public FmTyphoonRealBVO setSpeed(Double speed) {
        this.speed = speed;
        return this;
    }

    public String getMoveDir() {
        return moveDir;
    }

    public FmTyphoonRealBVO setMoveDir(String moveDir) {
        this.moveDir = moveDir;
        return this;
    }

    public Double getMoveSpeed() {
        return moveSpeed;
    }

    public FmTyphoonRealBVO setMoveSpeed(Double moveSpeed) {
        this.moveSpeed = moveSpeed;
        return this;
    }

    public Double getPressure() {
        return pressure;
    }

    public FmTyphoonRealBVO setPressure(Double pressure) {
        this.pressure = pressure;
        return this;
    }

    public Double getRadius7() {
        return radius7;
    }

    public FmTyphoonRealBVO setRadius7(Double radius7) {
        this.radius7 = radius7;
        return this;
    }

    public Double getRadius10() {
        return radius10;
    }

    public FmTyphoonRealBVO setRadius10(Double radius10) {
        this.radius10 = radius10;
        return this;
    }

    public Double getRadius12() {
        return radius12;
    }

    public FmTyphoonRealBVO setRadius12(Double radius12) {
        this.radius12 = radius12;
        return this;
    }

    public void setForecast(List<FmTyphoonForecastVO> forecast) {
        this.forecast = forecast;
    }

    public List<FmTyphoonForecastVO> getForecast() {
        return forecast;
    }

    public String getYbsj() {
        return ybsj;
    }

    public void setYbsj(String ybsj) {
        this.ybsj = ybsj;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getRemark() {
        return remark;
    }

    public FmTyphoonRealBVO setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
