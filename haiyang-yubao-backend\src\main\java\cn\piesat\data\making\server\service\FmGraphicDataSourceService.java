package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmGraphicDataSourceDTO;
import cn.piesat.data.making.server.entity.FmGraphicDataSource;
import cn.piesat.data.making.server.vo.FmGraphicDataSourceVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:26
 */
public interface FmGraphicDataSourceService extends IService<FmGraphicDataSource> {

    /**
     * 根据参数查询列表
     */
    List<FmGraphicDataSourceVO> getList();

}
