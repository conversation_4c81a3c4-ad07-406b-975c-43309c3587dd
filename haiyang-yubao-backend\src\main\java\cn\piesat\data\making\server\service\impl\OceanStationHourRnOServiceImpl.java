package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourRnODao;
import cn.piesat.data.making.server.entity.OceanStationHourRnO;
import cn.piesat.data.making.server.service.OceanStationHourRnOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-降水-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourRnOServiceImpl extends ServiceImpl<OceanStationHourRnODao, OceanStationHourRnO>
        implements OceanStationHourRnOService {

    @Resource
    private OceanStationHourRnODao oceanStationHourRnODao;

    @Override
    public List<OceanStationHourRnO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourRnODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourRnODao.getMaxCreateTime();
    }
}





