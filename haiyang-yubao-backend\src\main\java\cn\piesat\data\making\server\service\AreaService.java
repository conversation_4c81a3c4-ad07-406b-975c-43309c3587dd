package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.entity.Area;
import cn.piesat.data.making.server.vo.AreaVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 区域表服务接口
 *
 * <AUTHOR>
 */
public interface AreaService extends IService<Area> {

    /**
     * 查询列表
     */
    List<AreaVO> getList(AreaDTO dto);

    /**
     * 保存
     */
    Long save(AreaDTO dto);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 生成geojson
     * @param areaIds
     */
    void generateAreaJson(List<Long> areaIds);
}




