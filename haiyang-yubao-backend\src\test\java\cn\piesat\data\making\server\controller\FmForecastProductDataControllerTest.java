package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.DataMakingServerApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@Slf4j
@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)

public class FmForecastProductDataControllerTest {

    @Autowired
    private FmForecastProductDataController fmForecastProductDataControllerImpl;

    @Test
    public void testGetLastForecastStartTime(){
        Date date =fmForecastProductDataControllerImpl.getLastForecastStartTime("grid");

        Assert.assertNotNull(date);
    }
}
