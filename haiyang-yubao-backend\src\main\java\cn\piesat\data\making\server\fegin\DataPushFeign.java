package cn.piesat.data.making.server.fegin;

import cn.piesat.webconfig.response.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "data-push-server", url = "${piesat.datapush.search-url:}")
public interface DataPushFeign {

    @GetMapping("/api/push/task/queryConditionPage")
    R getPushTaskList(@RequestParam Integer pageNum, @RequestParam Integer pageSize, @RequestParam String unitType, @RequestParam Integer taskType
            , @RequestParam Integer groupId);

    @GetMapping("/api/push/product/task")
    R getPushProductListByTaskId(@RequestParam Integer pageNum, @RequestParam Integer pageSize, @RequestParam String taskId);
}
