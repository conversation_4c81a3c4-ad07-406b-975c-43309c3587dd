<template>
  <div class="image-display-download">
    <div class="header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="content">
      <Icon size="22" color="#AEAEAE" @click="download">
        <DownloadOutlined />
      </Icon>
      <div class="title">
        <slot name="title">{{ imgName }}</slot>
      </div>
      <img :src="imgUrl" :alt="title" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { Icon } from '@vicons/utils/lib'
import { DownloadOutlined } from '@vicons/antd'

export default defineComponent({
  components: {
    Icon,
    DownloadOutlined
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    imgUrl: {
      type: String,
      default: ''
    },
    imgName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const methods = reactive({
      async download() {
        //图片下载
        try {
          const response = await fetch(props.imgUrl)
          if (!response.ok) {
            throw new Error('Network response was not ok')
          }
          const blob = await response.blob()
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)
          link.download = props.imgName // 设置你想要的文件名
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(link.href) // 清理
        } catch (error) {
          console.error('There was a problem with the fetch operation:', error)
        }
      }
    })
    return {
      ...toRefs(methods)
    }
  }
})
</script>

<style scoped lang="scss">
.image-display-download {
  flex: 1;
  background: #fff;

  .header {
    background: url(src/assets/images/common/content-header.png) 100% 100%
      no-repeat;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      position: relative;
      padding-left: 32px;

      &::before {
        width: 4px;
        height: 18px;
        background: #567bff;
        display: block;
        content: '';
        position: absolute;
        top: 2px;
        left: 8px;
      }
    }
  }

  .content {
    position: relative;
    padding: 20px;
    text-align: center;
    .xicon {
      position: absolute;
      right: 20px;
      top: 20px;
      &:hover {
        color: #567bff !important;
      }
    }
    .title {
      padding: 15px 0;
      text-align: center;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #222222;
      line-height: 19px;
      font-style: normal;
      text-transform: none;
    }
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
}
</style>
