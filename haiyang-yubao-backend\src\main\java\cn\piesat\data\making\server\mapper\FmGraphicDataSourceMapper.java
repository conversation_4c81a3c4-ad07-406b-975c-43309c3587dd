package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicDataSourceDTO;
import cn.piesat.data.making.server.entity.FmGraphicDataSource;
import cn.piesat.data.making.server.vo.FmGraphicDataSourceVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:29
 */
@Mapper(componentModel = "spring")
public interface FmGraphicDataSourceMapper {

    FmGraphicDataSourceMapper INSTANCE = Mappers.getMapper(FmGraphicDataSourceMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicDataSourceVO entityToVo(FmGraphicDataSource entity);

    /**
     * dto-->entity
     */
    FmGraphicDataSource dtoToEntity(FmGraphicDataSourceDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicDataSourceVO> entityListToVoList(List<FmGraphicDataSource> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicDataSource> dtoListToEntityList(List<FmGraphicDataSourceDTO> dtoList);
}
