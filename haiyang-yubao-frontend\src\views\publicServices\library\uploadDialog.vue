<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-21 16:06:50
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-21 17:35:20
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/library/uploadDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%
-->
<template>
  <qx-dialog
    title="上传产品"
    :visible="dialogVisible"
    width="346px"
    class="library-upload-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <div class="dialog-content">
        <n-form
          ref="formRef"
          :model="form"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
          :show-feedback="false"
        >
          <n-form-item label="产品类别:">
            <n-select
              v-model:value="form.type"
              :options="props.productOptions"
              clearable
            />
          </n-form-item>
          <n-form-item label="产品名称:">
            <n-input v-model:value="form.name" placeholder="请输入" clearable />
          </n-form-item>
          <n-form-item label="上传文件" label-placement="top">
            <n-upload
              multiple
              directory-dnd
              action="https://www.mocky.io/v2/5e4bafc63100007100d8b70f"
              :max="5"
            >
              <n-upload-dragger>
                <div class="upload-icon"></div>
                <div class="upload-text">
                  点击或将文件拖拽到这里上传 支持扩展名：{{ typeList }}
                </div>
              </n-upload-dragger>
            </n-upload>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="onClose">取消</qx-button>
        <qx-button class="primary" @click="onUpload">上传</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>
<script lang="ts" setup>
import { QxDialog } from 'src/components/QxDialog'
import { ref, reactive, watch } from 'vue'
import type { SelectOption } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'

const emit = defineEmits(['close'])
const props = defineProps({
  productOptions: {
    type: Array as () => SelectOption[],
    default: () => []
  },
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default:
      'DOC、DOCX、PDF、TXT、RTF、HTML、ODT、XLS、XLSX、PPT、PPTX、PST、ODT、ODS、ODP、XML、JSON、TOML、ZIP、RAR、7Z、TAR.GZ'
  }
})
let dialogVisible = ref(false)
let typeList = ref('')
watch(
  () => props.visible,
  (val: boolean) => {
    dialogVisible.value = val
  }
)
watch(
  () => props.type,
  (val: string) => {
    let mapper: any = {
      file: 'DOC、DOCX、PDF、TXT、RTF、HTML、ODT、XLS、XLSX、PPT、PPTX、PST、ODT、ODS、ODP、XML、JSON、TOML、ZIP、RAR、7Z、TAR.GZ',
      img: 'JPEG、JPG、PNG、BMP、TIFF、WebP、RAW',
      video: 'MP4、WMV'
    }
    typeList.value = mapper[val]
  },{
    immediate: true
  }
)
let form = reactive({
  type: null,
  name: '',
  fileList: []
})

function onUpload() {}
function onClose() {
  emit('close', false)
}
</script>
<style lang="scss">
.library-upload-dialog {
  .qx-dialog-header {
    background: url(src/assets/images/common/dialog-header-bg1.png) no-repeat;
    background-size: 100% 100%;
    h3 {
      position: relative;
      &::before {
        content: '';
        display: inline-block;
        width: 15.4px;
        height: 10.4px;
        background: url(src/assets/images/common/dialog-header-bg-icon.png)
          no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: -23px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .n-form {
    box-sizing: border-box;
    padding: 0 24px;
    margin-top: 10px;
    .n-form-item {
      margin-bottom: 10px;
    }
  }
  .upload-icon {
    width: 40px;
    height: 40px;
    background: url(src/assets/images/common/upload-icon.png) no-repeat;
    background-size: 100% 100%;
    margin-bottom: 4px;
  }
  .upload-text {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: rgba(34, 34, 34, 0.42);
    line-height: 14px;
    text-align: center;
  }
  .n-upload-dragger {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
}
</style>
