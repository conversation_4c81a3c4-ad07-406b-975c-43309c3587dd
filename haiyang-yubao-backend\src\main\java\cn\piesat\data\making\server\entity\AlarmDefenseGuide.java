package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 警报防御指南(AlarmDefenseGuide)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:28:07
 */
@TableName("fm_alarm_defense_guide_b")
public class AlarmDefenseGuide implements Serializable {
    private static final long serialVersionUID = -1;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 警报类型 1 海浪 2风暴潮
     */
    private Integer alarmType;
    /**
     * 指南名称
     */
    private String guideName;
    /**
     * 状态
     */
    private Integer status;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGuideName() {
        return guideName;
    }

    public void setGuideName(String guideName) {
        this.guideName = guideName;
    }

    public Integer getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(Integer alarmType) {
        this.alarmType = alarmType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

