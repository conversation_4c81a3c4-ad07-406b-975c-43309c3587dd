<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-17 15:39:24
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-28 10:53:18
 * @FilePath: /hainan-jianzai-web/src/views/analysis/components/astronomicalTidesDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <transition name="fade">
    <div v-if="isSwitch" class="astronomical-tides-dialog">
      <div class="dialog-title d-flex flex-justify-between">
        <div class="dialog-title__label d-flex flex-align-center">
          <h3>【{{ props.info?.name }}】站点风暴潮预报曲线图</h3>
          <div class="start-time">
            起报时间: <span>{{ currentYearFirstDay }}</span>
          </div>
        </div>
        <div class="dialog-legend d-flex flex-align-center">
          <div class="legend-item d-flex flex-align-center">
            <div class="legend-item__color" style="background: #1e9fff"></div>
            <div class="legend-item__label">天文潮</div>
          </div>
          <div class="legend-item d-flex flex-align-center">
            <div class="legend-item__color" style="background: #ea9813"></div>
            <div class="legend-item__label">实况观测潮位</div>
          </div>
          <div class="legend-item d-flex flex-align-center">
            <div class="legend-item__color" style="background: #009688"></div>
            <div class="legend-item__label">台风风暴潮预报</div>
          </div>
        </div>
        <n-form
          ref="formRef"
          :model="dialog"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
          :show-feedback="false"
          class="qx-form d-flex flex-align-center"
        >
          <n-grid :cols="24" :x-gap="12">
            <n-form-item-gi :span="13" label="时间选择:">
              <n-date-picker
                v-model:formatted-value="dialog.timeRange"
                type="datetimerange"
                clearable
                :default-time="['00:00:00', '23:59:59']"
                @update:formatted-value="getChartData"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="8" label="基面选择:">
              <n-select
                v-model:value="dialog.datum"
                :options="baseSelectList"
                @update:value="onChangeDatum"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="3" label="">
              <qx-button class="primary" @click="dialogVisible = true"
                >导出</qx-button
              >
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </div>
      <div class="chart-container">
        <qx-echarts :option="chartsOption" />
      </div>

      <div class="dialog-switch" @click="onHidePanel">
        <i class="icon" :class="isSwitch ? 'icon-down' : 'icon-up'"></i>
      </div>
    </div>
  </transition>
  <ExportDialog
    :visible="dialogVisible"
    @update:visible="dialogVisible = false"
    @export="onExport"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, PropType } from 'vue'
import { QxButton } from 'src/components/QxButton'
import moment from 'moment'
import { QxEcharts } from 'src/components/QxEcharts'
import { ExportDialog } from '../index'
import Analysis from 'src/requests/analysis'
import { useMessage } from 'naive-ui'

const message = useMessage()
const currentYearFirstDay = moment().format('YYYY-01-01 00:00:00')
const emit = defineEmits(['update:flag'])
const props = defineProps({
  flag: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object as PropType<any>,
    default: () => {}
  }
})
let baseSelectList = ref([
  {
    label: '85基面',
    value: 'datum'
  },
  {
    label: '水尺零点',
    value: 'gauge_zero'
  },
  {
    label: '潮汐表基面',
    value: 'tide_datum'
  }
]) // 基面选择列表

let dialogVisible = ref(false)
interface DialogType {
  timeRange: any
  datum: string
  stationId: string
  reportTime: string
}
const dialog = reactive<DialogType>({
  timeRange: [
    moment().format('YYYY-MM-DD 00:00:00'),
    moment().format('YYYY-MM-DD 23:59:59')
  ],
  // timeRange: ['2025-02-26 00:00:00', '2025-02-26 23:59:59'],
  datum: 'datum',
  stationId: '',
  reportTime: moment().format('YYYY-01-01 00:00:00')
})
/**
 * 图表部分
 */

const chartsOption = reactive({
  color: ['#1E9FFF', '#EA9813', '#009688'],
  dataset: [
    {
      dimensions: ['tideTime', 'height', 'tideHeight', 'stormHeight'],
      source: []
    }
  ],
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    nameLocation: 'middle',
    axisLabel: {
      formatter(val: string) {
        return val ? moment(val).format('HH') + '时' : ''
      },
      color: '#6F717A',
      fontSize: 16
    }
  },
  grid: {
    left: '3%',
    right: '3%',
    bottom: '12%'
  },
  yAxis: {
    name: '单位:cm',
    max: 100,
    nameTextStyle: {
      color: '#6F717A',
      fontSize: 16
    },
    axisLabel: {
      color: '#6F717A',
      fontSize: 16
    }
  },
  series: [
    {
      name: '天文潮',
      type: 'line',
      smooth: true,
      showSymbol: false,
      encode: {
        x: 'tideTime',
        y: 'height'
      },
      markLine: {
        data: []
      }
    },
    {
      name: '实况观测潮位',
      type: 'line',
      smooth: true,
      showSymbol: false,
      encode: {
        x: 'tideTime',
        y: 'tideHeight'
      }
    },
    {
      name: '台风风暴潮预报',
      type: 'line',
      smooth: true,
      showSymbol: false,
      encode: {
        x: 'tideTime',
        y: 'stormHeight'
      }
    }
  ]
})

/**
 * 面板开关部分
 */

let isSwitch = ref(false) // 面板开关
watch(
  () => props.flag,
  (val: boolean) => {
    isSwitch.value = val
  }
)

watch(
  () => props.info,
  (val: any) => {
    if (val) {
      dialog.stationId = val.id
      let data: Array<any> = []
      Object.keys(val).forEach((item: any) => {
        if (item.includes(dialog.datum)) {
          data.push({
            yAxis: val[item],
            lineStyle: {
              color: getColor(item)
            }
          })
        }
      })
      // @ts-ignore
      chartsOption.series[0].markLine.data = data
      chartsOption.yAxis.max =
        Math.max(...data.map((item: any) => item.yAxis)) + 10
      getChartData()
    }
  },
  {
    immediate: true,
    deep: true
  }
)

function getColor(str: string) {
  // 定义一个正则表达式，用于匹配可能的颜色单词
  const colorRegex = /(Red|Blue|Yellow|Orange)/i
  // 使用正则表达式的 exec 方法在字符串中查找匹配的颜色
  const match = colorRegex.exec(str)
  // 如果找到匹配项，返回匹配到的颜色，否则返回 null
  return match ? match[0] : null
}

function getChartData() {
  const params = JSON.parse(JSON.stringify(dialog))
  if (params.timeRange?.length) {
    params.startTime = params.timeRange[0]
    params.endTime = params.timeRange[1]
    delete params.timeRange
  }
  const tideParams = {
    stationNum: props.info.relationStation,
    startTime: params.startTime,
    endTime: params.endTime
  }

  Promise.all([
    Analysis.getTideList(params),
    Analysis.getOceanStationTideList(tideParams),
    Analysis.getStormSurgeTideList({
      stationNum: props.info.code,
      startTime: params.startTime,
      endTime: params.endTime
    })
  ])
    .then((res: any) => {
      let tidelist = res[1].map((item: any) => {
        return {
          name: item.stationName,
          stationId: item.stationId,
          tideHeight: item.height,
          tideTime: item.tideTime
        }
      })
      let stormList = res[2].map((item: any) => {
        return {
          name: item.stationName,
          stationId: item.stationId,
          stormHeight: item.height,
          tideTime: item.tideTime
        }
      })
      // 合并数组
      const combinedArray = [...res[0], ...tidelist, ...stormList]

      // 按时间排序
      combinedArray.sort(
        (a, b) =>
          new Date(a.tideTime).getTime() - new Date(b.tideTime).getTime()
      )

      // 填充缺失的值
      const result: any = []
      const timeMap: any = {}

      combinedArray.forEach(item => {
        const { tideTime, height, tideHeight, stormHeight } = item
        if (!timeMap[tideTime]) {
          timeMap[tideTime] = {
            tideTime,
            hegiht: '',
            tideHeight: '',
            stormHeight: ''
          }
          result.push(timeMap[tideTime])
        }
        if (height !== undefined) timeMap[tideTime].height = height
        if (tideHeight !== undefined) timeMap[tideTime].tideHeight = tideHeight
        if (stormHeight !== undefined)
          timeMap[tideTime].stormHeight = stormHeight
      })
      chartsOption.dataset[0].source = result
    })
    .catch(e => {
      console.error(e, 'eeee')
    })
}

function onChangeDatum(val: string) {
  let data: Array<any> = []
  Object.keys(props.info).forEach((item: any) => {
    let str = val.replace(/_(\w)/g, (_, c) => c.toUpperCase())
    if (item.includes(str)) {
      data.push({
        yAxis: props.info[item],
        lineStyle: {
          color: getColor(item)
        }
      })
    }
  })
  // @ts-ignore
  chartsOption.series[0].markLine.data = data
  chartsOption.yAxis.max = Math.max(...data.map((item: any) => item.yAxis)) + 10
  getChartData()
}

function onHidePanel() {
  isSwitch.value = !isSwitch.value
  emit('update:flag', false)
}

function onExport(val: any) {
  const params = {
    stationIds: val.stationIds.toString(),
    datum: val.datum,
    startTime: '',
    endTime: ''
  }

  if (val.dataTime.length) {
    params.startTime = val.dataTime[0]
    params.endTime = val.dataTime[1]
  }

  Analysis.tideExport(params)
    .then((res: any) => {
      const link = document.createElement('a')
      const _fileName = decodeURIComponent(res.fileName)
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
      })
      const url = window.URL.createObjectURL(blob)
      link.href = url
      link.download = _fileName // 下载的文件名称
      link.click()
      window.URL.revokeObjectURL(url)
      dialogVisible.value = false
    })
    .catch(e => {
      let { msg = null } = e?.response?.data
      message.error(msg || '导出失败')
      console.error(e.message, 'eeeeee')
    })
}
</script>

<style scoped lang="scss">
.astronomical-tides-dialog {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 420px;
  z-index: 100;
  background: #fff;
  display: flex;
  flex-direction: column;
  .dialog-title__label {
    flex-shrink: 0;
  }
  .dialog-title {
    height: 60px;
    background: #f5f5f5;
    box-sizing: border-box;
    padding: 0 20px;
    flex-shrink: 0;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 18px;
      color: #222222;
      position: relative;
      &::before {
        width: 4px;
        height: 18px;
        content: '';
        display: inline-block;
        background: #567bff;
        position: absolute;
        left: 0px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .start-time {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      margin-left: 40px;
      span {
        color: #567bff;
      }
    }
  }
  .dialog-legend {
    flex-shrink: 0;
    .legend-item {
      margin-right: 46px;
      &::nth-last-child(1) {
        margin-right: 0;
      }
    }
    .legend-item__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      margin-left: 12px;
    }
    .legend-item__color {
      width: 14px;
      height: 14px;
    }
  }
  .qx-form {
    width: 700px;
  }
  .chart-container {
    width: 100%;
    flex: 1;
  }
  .dialog-switch {
    width: 80px;
    height: 20px;
    background: #0c67d0;
    border-radius: 4px 4px 0px 0px;
    position: absolute;
    top: -20px;
    right: -40px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    i.icon-down {
      width: 12px;
      height: 12px;
      background-image: url(src/assets/images/icons/icon-down.png);
    }
    i.icon-up {
      width: 12px;
      height: 12px;
      background-image: url(src/assets/images/icons/icon-up.png);
    }
  }
}

@keyframes fade-in {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
.fade-enter-active {
  // transform-origin: left center;
  animation: fade-in 1s;
}
.fade-leave-active {
  // transform-origin: left center;
  animation: fade-in 1s reverse;
}
</style>
