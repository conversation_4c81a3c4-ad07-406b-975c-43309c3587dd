package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmTideDialy;
import cn.piesat.data.making.server.vo.FmTideDialyVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmTideDialyMapper {

    FmTideDialyMapper INSTANCE = Mappers.getMapper(FmTideDialyMapper.class);

    List<FmTideDialyVO> entityListToVoList(List<FmTideDialy> list);
}
