import type { levelType } from '../types'

export function useColor() {
  return {
    colors: defaultColors()
  }
}

export interface ISegmentColorItem {
  level: levelType
  color: string
  className: string
  name: string
}

function defaultColors(): ISegmentColorItem[] {
  return [
    {
      level: 'I',
      color: '#F90102',
      className: 'red',
      name: '红色警报'
    },
    {
      level: 'II',
      color: '#FE7B0E',
      className: 'orange',
      name: '橙色警报'
    },
    {
      level: 'III',
      color: '#FFF300',
      className: 'yellow',
      name: '黄色警报'
    },
    {
      level: 'IV',
      color: '#0083FD',
      className: 'blue',
      name: '蓝色警报'
    },
    {
      level: 'V',
      color: '#c4c4c4',
      className: 'grey',
      name: '警报解除'
    }
  ]
}
