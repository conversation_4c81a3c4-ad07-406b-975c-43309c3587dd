import type { RenderFunction } from 'vue'
import redaidiya from 'src/assets/images/tools/typhoon/redaidiya.png'
import redaifengbao from 'src/assets/images/tools/typhoon/redaifengbao.png'
import qiangredaifengbao from 'src/assets/images/tools/typhoon/qiangredaifengbao.png'
import taifeng from 'src/assets/images/tools/typhoon/taifeng.png'
import qiangtaifeng from 'src/assets/images/tools/typhoon/qiangtaifeng.png'
import chaoqiangtaifeng from 'src/assets/images/tools/typhoon/chaoqiangtaifeng.png'

interface ITyphoonLegendItem {
  label: string
  value: string
  render: string | RenderFunction
}

export function useTyphoonLegend() {
  return {
    typhoonLegend: getTyphoonLegendOptions()
  }
}

function getTyphoonLegendOptions(): ITyphoonLegendItem[] {
  return [
    {
      render: () => <img src={redaidiya} alt="" />,
      label: '热带低压',
      value: '热带低压'
    },
    {
      render: () => <img src={redaifengbao} alt="" />,
      label: '热带风暴',
      value: '热带风暴'
    },
    {
      render: () => <img src={qiangredaifengbao} alt="" />,
      label: '强热带风暴',
      value: '强热带风暴'
    },
    {
      render: () => <img src={taifeng} alt="" />,
      label: '台风',
      value: '台风'
    },
    {
      render: () => <img src={qiangtaifeng} alt="" />,
      label: '强台风',
      value: '强台风'
    },
    {
      render: () => <img src={chaoqiangtaifeng} alt="" />,
      label: '超强台风',
      value: '超强台风'
    }
  ]
}
