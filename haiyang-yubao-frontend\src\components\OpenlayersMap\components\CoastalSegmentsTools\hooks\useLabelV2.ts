import { Feature, MapBrowserEvent } from 'ol'
import Polygon from 'ol/geom/Polygon'
import LineString from 'ol/geom/LineString'
import { Coordinate } from 'ol/coordinate'
import Map from 'ol/Map'
import { onMounted, onUnmounted, ref, Ref, toRefs, watch } from 'vue'
import {
  ISegmentOptions,
  Tool
} from 'src/components/OpenlayersMap/components/CoastalSegmentsTools/types'
import VectorSource from 'ol/source/Vector'
import { SimpleGeometry } from 'ol/geom'
import VectorLayer from 'ol/layer/Vector'
import { useSegmentInject } from './useSegmentInject'
import { EventsKey } from 'ol/events'
import Point from 'ol/geom/Point'
import { Fill, Stroke, Style, Text } from 'ol/style'
import { unByKey } from 'ol/Observable'
import Translate from 'ol/interaction/Translate'
import { StyleLike } from 'ol/style/Style'
import { Select } from 'ol/interaction'
import { pointerMove } from 'ol/events/condition'
import { FeatureLike } from 'ol/Feature'

export function useLabelV2(opt: {
  segmentOptions: Ref<ISegmentOptions>
  vectorSource: VectorSource<SimpleGeometry>
  vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
}) {
  let labelManager: LabelManagerV2 | null = null
  const showDialog = ref(false)
  const formInDialog = ref({
    text: ''
  })
  const { text } = toRefs(formInDialog.value)

  const getMap = useSegmentInject('getMap').inject()
  if (!getMap) {
    throw new Error('注入失败')
  }

  onMounted(() => {
    getMap(map => {
      labelManager = new LabelManagerV2({
        map,
        ...opt,
        showDialog,
        formInDialog: text
      })
    })
  })

  onUnmounted(() => {
    labelManager?.close()
  })

  watch(
    () => opt.segmentOptions.value.currentTool,
    (value, oldValue) => {
      if (oldValue === Tool.NONE && value === Tool.LABEL) {
        labelManager?.start()
      } else if (oldValue === Tool.LABEL && value === Tool.NONE) {
        labelManager?.close()
      }
    }
  )

  return {
    showDialog,
    formInDialog,
    onConfirm: () => {
      labelManager?.onConfirm()
    },
    clearLabel() {
      labelManager?.clear()
    }
  }
}

class LabelManagerV2 {
  get coordinate(): number[] {
    return this._coordinate
  }

  private _coordinate: number[] = []
  private _eventKey: EventsKey | null = null
  private _pointVecSource: VectorSource<SimpleGeometry>
  private _pointVecLayer: VectorLayer<VectorSource<SimpleGeometry>>
  private _translate: Translate | undefined
  private _translateSelect: Select

  constructor(
    protected opt: {
      segmentOptions: Ref<ISegmentOptions>
      vectorSource: VectorSource<SimpleGeometry>
      vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
      map: Map
      showDialog: Ref<boolean>
      formInDialog: Ref<string>
    }
  ) {
    this._pointVecSource = new VectorSource<Point>({
      useSpatialIndex: false
    })
    this._pointVecLayer = new VectorLayer({
      zIndex: 100,
      source: this._pointVecSource
    })
    // 向后兼容
    this._pointVecSource.set('layerType', '选取工具文字')
    this.opt.map.addLayer(this._pointVecLayer)

    this._translateSelect = new Select({
      layers: [this._pointVecLayer],
      condition: mapBrowserEvent => {
        return pointerMove(mapBrowserEvent)
      },
      style: (fea: FeatureLike) => {
        if (fea instanceof Feature) {
          const $style = fea.get('$style') as Style
          const style = $style.clone()
          style.getStroke().setColor('red')
          return style
        }
        return void 0
      }
    })
    this.opt.map.addInteraction(this._translateSelect)
  }

  start() {
    // this.opt.showDialog.value = true
    this._eventKey = this.opt.map.on('click', this.onMapClick.bind(this))
    this.initTranslate()
  }

  initTranslate() {
    const collection = this._translateSelect.getFeatures()
    if (!collection) {
      throw new Error('无法获取近岸基础单元要素集')
    }
    this._translate = new Translate({
      features: collection
    })
    this.opt.map.addInteraction(this._translate)
  }

  onMapClick(e: MapBrowserEvent<UIEvent>) {
    this._coordinate = e.coordinate
    const eventPixel = this.opt.map.getEventPixel(e.originalEvent)
    const feature = this.opt.map.forEachFeatureAtPixel<Feature<SimpleGeometry>>(
      eventPixel,
      fea => fea as Feature<SimpleGeometry>
    )
    if (!(feature && this.opt.vectorSource.hasFeature(feature))) {
      return
    }
    this.showDialog()
  }

  createPopup() {
    this.createLinkedBubbleFeature({ bubblePixelSize: [100, 30] })
  }

  showDialog() {
    this.opt.showDialog.value = true
  }

  hideDialog() {
    this.opt.showDialog.value = false
  }

  onConfirm(): void {
    this.hideDialog()
    this.createPopup()
  }

  clear() {
    this._pointVecSource.clear()
  }

  close() {
    this._eventKey && unByKey(this._eventKey)
    this.opt.map.removeLayer(this._pointVecLayer)
    this._translate && this.opt.map.removeInteraction(this._translate)
    this._translateSelect && this.opt.map.removeInteraction(this._translateSelect)
  }

  getBubbleGeoCenter() {
    const pixel = this.opt.map.getPixelFromCoordinate(this.coordinate)
    pixel[0] += 100
    pixel[1] -= 100
    return this.opt.map.getCoordinateFromPixel(pixel)
  }

  createLinkedBubbleFeature(options: {
    bubblePixelSize: [number, number] // CSS 像素尺寸
  }) {
    const { bubblePixelSize } = options
    const bubbleCenter = this.getBubbleGeoCenter()

    const res = this.opt.map.getView().getResolution()
    if (!res) throw new Error('无法获取地图分辨率')

    const [pixelW, pixelH] = bubblePixelSize
    const width = pixelW * res
    const height = pixelH * res
    const halfW = width / 2
    const halfH = height / 2

    // 构建矩形 Polygon
    const rectCoords: Coordinate[] = [
      [bubbleCenter[0] - halfW, bubbleCenter[1] - halfH],
      [bubbleCenter[0] + halfW, bubbleCenter[1] - halfH],
      [bubbleCenter[0] + halfW, bubbleCenter[1] + halfH],
      [bubbleCenter[0] - halfW, bubbleCenter[1] + halfH],
      [bubbleCenter[0] - halfW, bubbleCenter[1] - halfH]
    ]
    const bubble = new Feature(new Polygon([rectCoords]))
    bubble.setStyle(this.bubbleStyle(this.opt.formInDialog.value))
    bubble.set('$style', this.bubbleStyle(this.opt.formInDialog.value))

    // 初始最近角点
    const initialClosest = this.getClosestCorner(
      rectCoords.slice(0, 4),
      this.coordinate
    )
    const initialTarget = [...this.coordinate]
    const line = new Feature(new LineString([this.coordinate, initialClosest]))
    line.setStyle(this.lineStyle())

    this._pointVecSource.addFeature(bubble)
    this._pointVecSource.addFeature(line)

    // 自动更新连线函数
    const updateLineFromBubble = () => {
      const geom = bubble.getGeometry()
      if (!(geom instanceof Polygon)) return

      const corners = geom.getCoordinates()[0].slice(0, 4) // 取前四个角
      const closest = this.getClosestCorner(corners, initialTarget)
      line.getGeometry()?.setCoordinates([initialTarget, closest])
    }

    // 内聚监听器：当气泡位置变化时自动更新连线
    bubble.on('change', updateLineFromBubble)

    return {
      bubble,
      line,
      // 可选暴露更新函数（如果拖拽非几何，而是 style/属性驱动）
      updateLine: updateLineFromBubble
    }
  }

  getClosestCorner(rectCoords: Coordinate[], target: Coordinate): Coordinate {
    let minDist = Infinity
    let closest = rectCoords[0]
    for (const pt of rectCoords) {
      const dx = pt[0] - target[0]
      const dy = pt[1] - target[1]
      const dist = dx * dx + dy * dy
      if (dist < minDist) {
        minDist = dist
        closest = pt
      }
    }
    return closest
  }

  bubbleStyle(content: string): StyleLike {
    return new Style({
      fill: new Fill({
        color: '#fff'
      }),
      stroke: new Stroke({
        color: '#b8b8b8',
        width: 1
      }),
      text: new Text({
        placement: 'point',
        font: 'bold 14px Arial',
        text: `${content}厘米`
      })
    })
  }

  lineStyle(): StyleLike {
    return new Style({
      stroke: new Stroke({
        color: '#fff',
        width: 2
      })
    })
  }
}
