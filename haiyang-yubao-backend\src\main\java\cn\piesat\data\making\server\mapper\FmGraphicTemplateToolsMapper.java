package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateTools;
import cn.piesat.data.making.server.vo.FmGraphicTemplateToolsVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-10 09:45:47
 */
@Mapper(componentModel = "spring")
public interface FmGraphicTemplateToolsMapper {

    FmGraphicTemplateToolsMapper INSTANCE = Mappers.getMapper(FmGraphicTemplateToolsMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicTemplateToolsVO entityToVo(FmGraphicTemplateTools entity);

    /**
     * dto-->entity
     */
    FmGraphicTemplateTools dtoToEntity(FmGraphicTemplateToolsDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicTemplateToolsVO> entityListToVoList(List<FmGraphicTemplateTools> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicTemplateTools> dtoListToEntityList(List<FmGraphicTemplateToolsDTO> dtoList);
}
