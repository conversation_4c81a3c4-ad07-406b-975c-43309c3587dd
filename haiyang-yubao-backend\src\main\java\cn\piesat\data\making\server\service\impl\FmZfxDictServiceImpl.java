package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.FmZfxDict;

import cn.piesat.data.making.server.dao.FmZfxDictDao;
import cn.piesat.data.making.server.service.FmZfxDictService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-11-08 16:14:14
 */
@Service
@Slf4j
public class FmZfxDictServiceImpl extends ServiceImpl<FmZfxDictDao, FmZfxDict> implements FmZfxDictService {

    @Resource
    private FmZfxDictDao fmZfxDictDao;


    @Override
    public List<FmZfxDict> getList(FmZfxDict dto) {
        LambdaQueryWrapper<FmZfxDict> queryWrapper = createQueryWrapper(dto.getType());

        return this.list(queryWrapper);
    }
    private LambdaQueryWrapper<FmZfxDict> createQueryWrapper(String type) {
        LambdaQueryWrapper<FmZfxDict> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq(FmZfxDict::getType, type);
        }
        return queryWrapper;
    }
}
