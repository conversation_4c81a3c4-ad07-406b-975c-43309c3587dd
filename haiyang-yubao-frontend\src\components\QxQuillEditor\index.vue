<!--
 * @Author: 樊海玲 <EMAIL> 
 * @Date: 2024-01-04 10:06:50
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2024-02-01 14:51:36
 * @FilePath: /yx-web/src/components/HtQuillEditor.vue v-model="editorVal"
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <quill-editor
    ref="quillEditor"
    :options="editorOption"
  ></quill-editor>
</template>

<script lang="ts" setup>
import { QuillEditor, Quill } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { ref, computed } from 'vue'

const fontSizeStyle = Quill.import('attributors/style/size') // 引入这个后会把样式写在style上
fontSizeStyle.whitelist = [
  '12px',
  '14px',
  '16px',
  '18px',
  '20px',
  '22px',
  '24px',
  '26px',
  '28px',
  '30px'
]
// 设置字体
const fontStyle = Quill.import('attributors/style/font') // 引入这个后会把样式写在style上
fontStyle.whitelist = [
  'SimSun', // 宋体
  'SimHei', // 黑体
  'STFANGSO', // 华文仿宋
  'Microsoft-YaHei', // 微软雅黑
  'KaiTi', // 楷体
  'FangSong', // 仿宋
  'STKAITI', // 华文楷体
  'Arial',
  'Times-New-Roman',
  'sans-serif'
]
Quill.register(fontStyle, true)
Quill.register(fontSizeStyle, true)

const editorOption = {
  modules: {
    toolbar: [
      // 字体种类-----[{ font: [] }]
      [{font: fontStyle.whitelist}],
       // 字体大小-----[{ size: ['small', false, 'large', 'huge'] }]
      [{ size: fontSizeStyle.whitelist }],
      // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
      ['bold', 'italic', 'underline', 'strike'],
      // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
      [{ color: [] }, { background: [] }],
      // 引用  代码块-----['blockquote', 'code-block']
      // ['blockquote', 'code-block'],
      // 1、2 级标题-----[{ header: 1 }, { header: 2 }]
      // [{ header: 1 }, { header: 2 }],
      // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
      // [{ list: 'ordered' }, { list: 'bullet' }],
      // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
      // [{ script: 'sub' }, { script: 'super' }],
      // 缩进-----[{ indent: '-1' }, { indent: '+1' }]
      [{ indent: '-1' }, { indent: '+1' }],
      // 文本方向-----[{'direction': 'rtl'}]
      // [{ direction: "rtl" }],
      // 对齐方式-----[{ align: [] }]
      [{ align: [] }],
      // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
      // [{ header: [1, 2, 3, 4, 5, 6, false] }],
      // 清除文本格式-----['clean']
      // ['clean'],
      // 链接、图片、视频-----['link', 'image', 'video']
      ['link','video','image']
    ]
  },
  theme: 'snow',
  placeholder: '请输入',
}

const props = defineProps({
  value: {
    type: String,
    default: ''
  }
})



const emit = defineEmits(['update:value'])

const editorVal = computed({
  get: () => props.value,
  set: () => emit('update:value')
})
</script>
<style lang="scss">
.ql-container {
  height: 200px;
}
.ql-snow .ql-picker {
  line-height: 24px;
}
</style>

<style>
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimHei]::before {
  content: "黑体";
  font-family: "SimHei";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Microsoft-YaHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Microsoft-YaHei]::before {
  content: "微软雅黑";
  font-family: "Microsoft YaHei";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=KaiTi]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=KaiTi]::before {
  content: "楷体";
  font-family: "KaiTi";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=FangSong]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=FangSong]::before {
  content: "仿宋";
  font-family: "FangSong";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Arial]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Arial]::before {
  content: "Arial";
  font-family: "Arial";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Times-New-Roman]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Times-New-Roman]::before {
  content: "Times New Roman";
  font-family: "Times New Roman";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=sans-serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=sans-serif]::before {
  content: "sans-serif";
  font-family: "sans-serif";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=STFANGSO]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=STFANGSO]::before {
  content: "华文仿宋";
  font-family: "STFANGSO";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=STKAITI]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=STKAITI]::before {
  content: "华文楷体";
  font-family: "STKAITI";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimSun]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimSun]::before {
  content: "宋体";
  font-family: "SimSun";
}


.ql-font-SimSun {
  font-family: "SimSun";
}

.ql-font-SimHei {
  font-family: "SimHei";
}

.ql-font-Microsoft-YaHei {
  font-family: "Microsoft YaHei";
}

.ql-font-KaiTi {
  font-family: "KaiTi";
}

.ql-font-FangSong {
  font-family: "FangSong";
}

.ql-font-Arial {
  font-family: "Arial";
}

.ql-font-Times-New-Roman {
  font-family: "Times New Roman";
}

.ql-font-sans-serif {
  font-family: "sans-serif";
}

/* 字号设置 */
/* 默认字号 */
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  content: "12px";
  font-size: 12px;
}

.ql-size-12px {
  font-size: 12px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  content: "14px";
  font-size: 14px;
}

.ql-size-14px {
  font-size: 14px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
  content: "16px";
  font-size: 16px;
}

.ql-size-16px {
  font-size: 16px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
  content: "18px";
  font-size: 18px;
}

.ql-size-18px {
  font-size: 18px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
  content: "20px";
  font-size: 20px;
}

.ql-size-20px {
  font-size: 20px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="22px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="22px"]::before {
  content: "22px";
  font-size: 22px;
}

.ql-size-22px {
  font-size: 22px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  content: "24px";
  font-size: 24px;
}

.ql-size-24px {
  font-size: 24px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="26px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="26px"]::before {
  content: "26px";
  font-size: 26px;
}

.ql-size-26px {
  font-size: 26px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="28px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="28px"]::before {
  content: "28px";
  font-size: 28px;
}

.ql-size-28px {
  font-size: 28px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="30px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="30px"]::before {
  content: "30px";
  font-size: 30px;
}

.ql-size-30px {
  font-size: 30px;
}

</style>