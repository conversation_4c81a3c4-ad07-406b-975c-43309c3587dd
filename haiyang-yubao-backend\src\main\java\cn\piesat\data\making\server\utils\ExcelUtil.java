package cn.piesat.data.making.server.utils;

import cn.piesat.webconfig.exception.BusinessException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExcelUtil {

    /**
     * 获取excel中的数据
     */
    public static <T> List<T> readData(InputStream inputStream, Integer sheetNo, Class<T> tClass) {
        try {
            ExcelListener<T> readListener = new ExcelListener<>();
            ExcelReaderBuilder readerBuilder = EasyExcel.read(inputStream, tClass, readListener);
            readerBuilder.autoCloseStream(true);
            readerBuilder.autoTrim(false);
            readerBuilder.excelType(ExcelTypeEnum.XLSX);
            ExcelReaderSheetBuilder sheetBuilder = readerBuilder.sheet(sheetNo);
            sheetBuilder.doRead();
            return readListener.getList();
        } catch (Exception e) {
            throw new BusinessException("解析文档失败！失败原因：" + e.getMessage());
        }
    }

}
