package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.FmSchedulingMainTable;
import cn.piesat.data.making.server.dto.FmSchedulingMainTableDTO;
import cn.piesat.data.making.server.vo.FmSchedulingMainTableVO;
import cn.piesat.data.making.server.dao.FmSchedulingMainTableDao;
import cn.piesat.data.making.server.service.FmSchedulingMainTableService;
import cn.piesat.data.making.server.mapper.FmSchedulingMainTableMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:01
 */
@Service("fmSchedulingMainTableService")
public class FmSchedulingMainTableServiceImpl extends ServiceImpl<FmSchedulingMainTableDao, FmSchedulingMainTable> implements FmSchedulingMainTableService {

    @Resource
    private FmSchedulingMainTableDao fmSchedulingMainTableDao;

    @Override
    public PageResult<FmSchedulingMainTableVO> getPage(FmSchedulingMainTableDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmSchedulingMainTableVO> getList(FmSchedulingMainTableDTO dto) {
        return null;
    }

    @Override
    public FmSchedulingMainTableVO getById() {
        return null;
    }

    @Override
    public void save(FmSchedulingMainTableDTO dto) {

    }

    @Override
    public void saveList(List<FmSchedulingMainTableDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }
}
