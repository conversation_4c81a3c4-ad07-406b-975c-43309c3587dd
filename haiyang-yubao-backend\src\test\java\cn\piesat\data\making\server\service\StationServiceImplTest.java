package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.vo.StationVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class StationServiceImplTest {

    @Autowired
    private StationService stationServiceImpl;

    @Test
    public void testGetListByStationTypeCode(){
        StationDTO dto = new StationDTO();
        dto.setStationTypeCode("oceanStation");
        List<StationVO> list = stationServiceImpl.getListByStationTypeCode(dto);
        Assert.assertNotNull(list);
    }
}
