package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.model.TideDataParseResult;

import java.io.IOException;
import java.text.ParseException;

/**
 * 天文潮汐服务类
 * <AUTHOR>
 * @
 */
public interface TideService {

    /**
     * 解析潮汐表数据
     * @param filePath
     * @return
     */
    TideDataParseResult parseTideData(String filePath) throws IOException, ParseException;

    void parseAndSaveTideData(String filePath) throws Exception;

    void updateWarnHeight();

    void count(Long stationId,String datum,Integer difference);
}
