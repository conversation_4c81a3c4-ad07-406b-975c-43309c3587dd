import { Vector as VectorSource } from 'ol/source'
import { Vector as VectorLayer } from 'ol/layer'
import { Polygon, LineString, Point } from 'ol/geom'
import { Feature, type Map } from 'ol'
import {
  Stroke,
  Style,
  Text,
  Fill,
  Circle as CircleStyle,
  Icon
} from 'ol/style'
import { GeoJSON } from 'ol/format'
import html2canvas from 'html2canvas'
import { hexToRgbs } from './hexToRgb.js'
const defaultRectLonLats = {
  leftLon: '107.35',
  leftLat: '21.95',
  rightLon: '112.63',
  rightLat: '17.76'
}
function addLabel(map: any, form: any, layer: any) {
  if (layer) {
    map.removeLayer(layer)
  }
  const iconFeatures: any = []
  form.forEach((item: any) => {
    const iconStyle = new Style({
      text: new Text({
        text: item.label,
        font: '16px Calibri,sans-serif',
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        }),
        textAlign: 'left',
        textBaseline: item.name === 'topLabel' ? 'top' : 'bottom',
        offsetX: 10,
        offsetY: item.name === 'topLabel' ? 10 : -10
      })
    })
    const iconFeature = new Feature({
      geometry: new Point(item.coordinate),
      name: item.name
    })
    iconFeature.setStyle([iconStyle])
    iconFeatures.push(iconFeature)
  })

  const vectorSource = new VectorSource({
    features: iconFeatures
  })
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    zIndex: 10
  })
  map.addLayer(vectorLayer)
  return vectorLayer
}
function addLegend(map: any, form: any, layer: any, anchor: [number, number]=[0, 1]) {
  if (layer) {
    map.removeLayer(layer)
  }
  const iconFeatures: any = []
  const iconStyle = new Style({
    image: new Icon({
      anchor,
      src: form.src,
      displacement: [10, 35],
      crossOrigin: 'anonymous'
    })
  })
  const iconFeature = new Feature({
    geometry: new Point(form.coordinate),
    name: form.id
  })
  iconFeature.setStyle([iconStyle])
  iconFeatures.push(iconFeature)

  const vectorSource = new VectorSource({
    features: iconFeatures
  })
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    zIndex: 10
  })
  map.addLayer(vectorLayer)
  return vectorLayer
}
// 获取矩形框的默认四至范围
function getDefaultRectLonLats() {
  return structuredClone(defaultRectLonLats)
}

// 画地图的矩形框
function addRect(map: any, form: any, layer: any) {
  if (layer) {
    map.removeLayer(layer)
  }
  const source = new VectorSource()
  const vectorLayer = new VectorLayer({
    source,
    style: new Style({
      stroke: new Stroke({ color: '#ff0000', width: 2 })
    }),
    zIndex: 10
  })
  const coordinates: number[][] = [
    [Number(form.leftLon), Number(form.leftLat)],
    [Number(form.rightLon), Number(form.leftLat)],
    [Number(form.rightLon), Number(form.rightLat)],
    [Number(form.leftLon), Number(form.rightLat)],
    [Number(form.leftLon), Number(form.leftLat)]
  ]
  const polygon = new Polygon([coordinates as any])
  const feature = new Feature(polygon)
  source.addFeature(feature)
  map.addLayer(vectorLayer)
  map.getView().fit(polygon)
  return vectorLayer
}
// function modifyVectorSource(map: any, source: any, form: any) {
//   const overlayStyle = (function () {
//     const styles: any = {
//       Polygon: [
//         new Style({
//           fill: new Fill({
//             color: [255, 255, 255, 0.5]
//           })
//         }),
//         new Style({
//           stroke: new Stroke({
//             color: [255, 255, 255, 1],
//             width: 5
//           })
//         }),
//         new Style({
//           stroke: new Stroke({
//             color: [0, 153, 255, 1],
//             width: 3
//           })
//         })
//       ],
//       MultiPolygon: [
//         new Style({
//           fill: new Fill({
//             color: [255, 255, 255, 0.5]
//           })
//         }),
//         new Style({
//           stroke: new Stroke({
//             color: [255, 255, 255, 1],
//             width: 5
//           })
//         }),
//         new Style({
//           stroke: new Stroke({
//             color: [0, 153, 255, 1],
//             width: 3
//           })
//         })
//       ],
//       LineString: [
//         new Style({
//           stroke: new Stroke({
//             color: [255, 255, 255, 1],
//             width: 5
//           })
//         }),
//         new Style({
//           stroke: new Stroke({
//             color: [0, 153, 255, 1],
//             width: 3
//           })
//         })
//       ],
//       MultiLineString: [
//         new Style({
//           stroke: new Stroke({
//             color: [255, 255, 255, 1],
//             width: 5
//           })
//         }),
//         new Style({
//           stroke: new Stroke({
//             color: [0, 153, 255, 1],
//             width: 3
//           })
//         })
//       ],
//       Point: [
//         new Style({
//           image: new CircleStyle({
//             radius: 7,
//             fill: new Fill({
//               color: [0, 153, 255, 1]
//             }),
//             stroke: new Stroke({
//               color: [255, 255, 255, 0.75],
//               width: 1.5
//             })
//           }),
//           zIndex: 100000
//         })
//       ],
//       MultiPoint: [
//         new Style({
//           image: new CircleStyle({
//             radius: 7,
//             fill: new Fill({
//               color: [0, 153, 255, 1]
//             }),
//             stroke: new Stroke({
//               color: [255, 255, 255, 0.75],
//               width: 1.5
//             })
//           }),
//           zIndex: 100000
//         })
//       ]
//     }
//     styles['GeometryCollection'] = styles['Polygon'].concat(styles['Point'])
//     return function (feature: Feature) {
//       return styles[feature.getGeometry().getType()]
//     }
//   })()
// }

let gridLayer: any = null
// 画经纬网格
function addGrid(map: any, layer: any, boxExtent: any) {
  if (layer) {
    map.removeLayer(layer)
  }
  function createGrid(spacing = 5) {
    const features: any = []
    const extent = [-180, -90, 180, 90]
    // [
    //   res.leftLongitude,
    //   res.rightLatitude,
    //   res.rightLongitude,
    //   res.leftLatitude
    // ]
    // 创建经度线
    for (let lon = extent[0]; lon <= extent[2]; lon += spacing) {
      const line = new LineString([
        [lon, extent[1]],
        [lon, extent[3]]
      ])
      features.push(new Feature(line))
      if (boxExtent.leftLongitude <= lon && lon <= boxExtent.rightLongitude) {
        // 标注经度
        const textFeature = new Feature({
          geometry: new Point([lon, boxExtent.rightLatitude]), // 20 表示纬度 20
          name: lon + '°' + (lon >= 0 ? 'E' : 'W')
        }) // 下边
        textFeature.set('type', 'bottom')
        const textFeature1 = new Feature({
          geometry: new Point([lon, boxExtent.leftLatitude]), // 20 表示纬度 20
          name: lon + '°' + (lon >= 0 ? 'E' : 'W')
        }) // 上边
        textFeature1.set('type', 'top')
        features.push(textFeature, textFeature1)
      }
    }

    // 创建纬度线
    for (let lat = extent[1]; lat <= extent[3]; lat += spacing) {
      const line = new LineString([
        [extent[0], lat],
        [extent[2], lat]
      ])
      features.push(new Feature(line))
      if (lat < boxExtent.leftLatitude && lat > boxExtent.rightLatitude) {
        // 标注纬度
        const textFeature = new Feature({
          geometry: new Point([boxExtent.leftLongitude, lat]), // 110 表示经度 110
          name: lat + '°' + (lat >= 0 ? 'N' : 'S')
        }) //左边
        textFeature.set('type', 'left')
        const textFeature1 = new Feature({
          geometry: new Point([boxExtent.rightLongitude, lat]), // 110 表示经度 110
          name: lat + '°' + (lat >= 0 ? 'N' : 'S')
        }) // 右边
        textFeature1.set('type', 'right')
        features.push(textFeature, textFeature1)
      }
    }

    return features
  }

  const gridSource = new VectorSource({
    features: createGrid(5) // 设置网格间距为5度
  })

  const gridLayer = new VectorLayer({
    source: gridSource,
    style: function (feature: any) {
      const geometryType = feature.getGeometry().getType()
      if (geometryType === 'Point') {
        let offsetX = 0
        let offsetY = 0
        if (feature.get('type') === 'left') {
          offsetX = 20
        }
        if (feature.get('type') === 'right') {
          offsetX = -20
        }
        if (feature.get('type') === 'top') {
          offsetY = 10
          offsetX = -20
        }
        if (feature.get('type') === 'bottom') {
          offsetY = -10
          offsetX = -20
        }
        return new Style({
          text: new Text({
            font: '12px Calibri,sans-serif',
            fill: new Fill({
              color: '#000'
            }),
            stroke: new Stroke({
              color: '#fff',
              width: 3
            }),
            text: feature.get('name'),
            offsetX,
            offsetY
          })
        })
      } else {
        return new Style({
          stroke: new Stroke({
            color: 'rgb(37, 129, 208)',
            width: 1,
            lineDash: [5, 5] // 设置虚线[实线长度, 空白长度]
          })
        })
      }
    },
    zIndex: 8
  })

  map.addLayer(gridLayer)
  return gridLayer
}

// 移除经纬网格
function removeGrid(map: any) {
  if (gridLayer !== null) {
    map.removeLayer(gridLayer)
    gridLayer = null
  }
}

// 根据四至范围获取gis截图数据
// 待实根据extent现裁剪功能
function getThematic(iptOpts: any) {
  const defaultOpts = {
    el: '#openlayers-map',
    backgroundColor: '#ffffff', // 背景颜色
    callback: function () {}, // 截图的回调方法
    extent: [], // 截图像素范围 [x, y, width, height]
    gray: false
  }
  const opts = Object.assign({}, defaultOpts, iptOpts) // 合并数据
  // if (opts.extent.length === 4) {
  //   opts.x = opts.extent[0]
  //   opts.y = opts.extent[1]
  //   opts.width = opts.extent[2]
  //   opts.height = opts.extent[3]
  // }
  const wrapDom: any = document.querySelector(opts.el)
  if (!wrapDom) {
    // 若不存在 dom，则退出执行
    return
  }
  const html2canvasOpts = { backgroundColor: opts.backgroundColor || null }
  console.log(opts, '**************')
  html2canvas(wrapDom, opts)
    .then((canvas: any) => {
      if (!opts.gray) {
        const link = document.createElement('a')
        link.href = canvas.toDataURL('image/png')
        link.download = 'screenshot111.png'
        link.click()
        opts.callback({ data: canvas.toDataURL('image/png') })
        return
      }
      const ctx: any = canvas.getContext('2d')
      const imgData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imgData.data

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i]
        const g = data[i + 1]
        const b = data[i + 2]

        // 计算灰度值
        const gray = 0.3 * r + 0.59 * g + 0.11 * b

        // 设置为灰度值
        data[i] = data[i + 1] = data[i + 2] = gray
      }

      ctx.putImageData(imgData, 0, 0)

      const href = canvas.toDataURL('image/png') // base64格式数据
      // opts.callback({ data: href })
      // test: 以下为测试代码，方便查看。测试完需要删除
      // 导出为图片
      const link = document.createElement('a')
      link.href = href
      link.download = 'screenshot.png'
      link.click()
    })
    .catch(() => {
      console.log('实现失败')
    })
}

// 画文字
function addText(map: Map, iptOpts: Object) {
  const defaultOpts = {
    position: [], // 位置
    fontSize: '100px',
    fontFamily: 'sans-serif',
    textAlign: 'left', // 相对位置点的水平对齐方式
    justify: 'center', // 文字本身的水平对齐方式
    text: '',
    fillColor: [0, 0, 0, 1], // 文字颜色
    padding: [2, 2, 2, 2]
  }
  const opts = Object.assign({}, defaultOpts, iptOpts)

  const feature = new Feature({
    geometry: new Point(opts.position)
  })
  console.log(`bold ${opts.fontSize} ${opts.fontFamily}`)
  feature.setStyle(
    new Style({
      text: new Text({
        // font: "bold 30px Arial",
        font: `bold ${opts.fontSize} ${opts.fontFamily}`,
        text: opts.text,
        // textAlign: opts.textAlign,
        // justify: opts.justify,
        fill: new Fill({ color: opts.fillColor }),
        padding: opts.padding
      })
    })
  )
  console.log(feature)
  const source = new VectorSource({
    features: [feature],
    format: new GeoJSON()
  })
  const layer = new VectorLayer({
    source
  })
  map.addLayer(layer)
}
function removeLayer(map: Map, layer: any) {
  map.removeLayer(layer)
}

// 将数组平均分成几份
function divideRangeIntoParts(
  min: number,
  max: number,
  parts: number
): number[] {
  if (max <= min) {
    throw new Error('max must be greater than min')
  }

  const range = max - min
  const step = range / (parts - 1) // parts - 1 是为了确保包含 max 值
  const result = []

  for (let i = 0; i < parts; i++) {
    const value = min + i * step
    result.push(value)
  }

  return result
}

// 获取图层样式
function getTifStyle(colorData: any) {
  let obj: any = {}
  if (colorData) {
    const { colorAttr, invalidValue, renderType } = colorData
    const color: string[] = []
    const opacity: number[] = []
    const quantity: number[] = []
    colorAttr.forEach((item: any) => {
      color.push(item.color)
      opacity.push(item.opacity - 0)
      quantity.push(item.quantity - 0)
    })
    obj = hexToRgbs(color, quantity, opacity, renderType, invalidValue)
    return obj
  } else {
    return obj
  }
}

/**
 * @description 逆时针计算扇形风圈的点集合
 * @param center - {Array<String|Number>}中心点，例如[117.23,23.123]
 * @param radius - {String|Number} 半径km
 * @param startAngle - {String|Number} 起始角度（单位°）
 * @param endAngle - {String|Number} 结束角度（单位°）
 * @param pointNum - {String|Number} 返回构成的弧点个数，默认30
 * @return {Array}
 */
function getSectorPoints(
  center: any,
  radius: any,
  startAngle: any,
  endAngle: any,
  pointNum: any
) {
  radius = Number(radius) * 1000

  // if (!this.isProjected) {
  const MetersPerUnit = 111319.49079327358 //1度多少米
  radius = radius / MetersPerUnit //转化为度
  // }
  center = [Number(center[0]), Number(center[1])]
  startAngle = Number(startAngle)
  endAngle = Number(endAngle)
  pointNum = Number(pointNum || 30)

  let sin
  let cos
  let x
  let y
  let angle
  const points = []
  const pointsLL = []
  const lonlat = center
  points.push([center[0], center[1]])
  for (let i = 0; i <= pointNum; i++) {
    angle = startAngle + ((endAngle - startAngle) * i) / pointNum
    sin = Math.sin((angle * Math.PI) / 180)
    cos = Math.cos((angle * Math.PI) / 180)
    x = center[0] + radius * sin
    y = center[1] + radius * cos

    points[i + 1] = [x, y]
  }
  points.push([center[0], center[1]])
  for (let j = 0; j < points.length; j++) {
    pointsLL[j] = points[j]
  }

  return pointsLL
}

function exportImage(map: Map, extent: number[]): string {
  const leftTopPosition = map.getPixelFromCoordinate([extent[0], extent[1]])
  // 地理坐标转换屏幕坐标
  const bottomRightPosition = map.getPixelFromCoordinate([extent[2], extent[3]])
  // 计算框选矩形的宽度以及高度像素
  const width = Math.abs(bottomRightPosition[0] - leftTopPosition[0])
  const height = Math.abs(bottomRightPosition[1] - leftTopPosition[1])
  // 计算框选矩形的左上角屏幕坐标,放置用户反过来绘制
  const minx =
    leftTopPosition[0] <= bottomRightPosition[0]
      ? leftTopPosition[0]
      : bottomRightPosition[0]
  const miny =
    leftTopPosition[1] <= bottomRightPosition[1]
      ? leftTopPosition[1]
      : bottomRightPosition[1]

  const mapCanvas = document.createElement('canvas')
  mapCanvas.width = width
  mapCanvas.height = height
  const mapContext: any = mapCanvas.getContext('2d')
  Array.prototype.forEach.call(
    map.getViewport().querySelectorAll('.ol-layer canvas, canvas.ol-layer'),
    function (canvas) {
      if (canvas.width > 0) {
        const opacity = canvas.parentNode.style.opacity || canvas.style.opacity
        mapContext.globalAlpha = opacity === '' ? 1 : Number(opacity)
        let matrix
        const transform = canvas.style.transform
        if (transform) {
          // Get the transform parameters from the style's transform matrix
          matrix = transform
            .match(/^matrix\(([^\(]*)\)$/)[1]
            .split(',')
            .map(Number)
        } else {
          matrix = [
            parseFloat(canvas.style.width) / canvas.width,
            0,
            0,
            parseFloat(canvas.style.height) / canvas.height,
            0,
            0
          ]
        }
        // Apply the transform to the export map context
        CanvasRenderingContext2D.prototype.setTransform.apply(
          mapContext,
          matrix
        )
        const backgroundColor = canvas.parentNode.style.backgroundColor
        if (backgroundColor) {
          mapContext.fillStyle = backgroundColor
          mapContext.fillRect(minx, miny, width, height)
        }
        mapContext.drawImage(canvas, -minx, -miny)
      }
    }
  )
  mapContext.globalAlpha = 1
  mapContext.setTransform(1, 0, 0, 1, 0, 0)
  const fileUrlColorful = mapCanvas.toDataURL('image/png')
  return fileUrlColorful
}

function exportImageV2(map: Map, extent: number[]): string {
  const dpr = window.devicePixelRatio
  const leftBottomPixel = map.getPixelFromCoordinate([extent[0], extent[1]])
  const rightTopPixel = map.getPixelFromCoordinate([extent[2], extent[3]])
  const viewport = map.getViewport()
  const canvases = viewport.getElementsByTagName('canvas')

  const ctx = canvases[0].getContext('2d')
  const origin = [leftBottomPixel[0] * dpr, rightTopPixel[1] * dpr],
    originWidth = (rightTopPixel[0] - leftBottomPixel[0]) * dpr,
    originHeight = (leftBottomPixel[1] - rightTopPixel[1]) * dpr
  const imgData: ImageData = ctx!.getImageData(
    origin[0],
    origin[1],
    originWidth,
    originHeight
  )
  const resultCanvas = document.createElement('canvas')
  resultCanvas!.width = originWidth
  resultCanvas!.height = originHeight
  const resultCtx = resultCanvas!.getContext('2d')
  resultCtx!.putImageData(imgData, 0, 0)
  const fileUrlColorful = resultCanvas.toDataURL('image/png')
  return fileUrlColorful
}

const pointMap: Record<number, number> = {
  0: 2, // 第一个角点的对角是第三个角点
  1: 3, // 第二个角点的对角是第四个角点
  2: 0, // 第三个角点的对角是第一个角点
  3: 1 // 第四个角点的对角是第二个角点
}

// 定义坐标点类型
type Coordinate = [number, number]

// 定义特征对象的接口
interface FeatureType<T> {
  getGeometry?: () => Geometry
  geometry?: Geometry
}

// 定义几何对象的接口
interface Geometry {
  getCoordinates: () => Coordinate[][]
}
/**
 * 编辑矩形截图的坐标，保持矩形形状
 * @param feature - 修改后的特征对象
 * @param modifyGeometry - 修改前的特征对象
 * @returns 更新后的矩形坐标数组
 */
function screenshotEditRect<T extends FeatureType<T>>(
  feature: T,
  modifyGeometry: T
): Coordinate[] {
  // 获取修改后的坐标
  const coords = feature?.getGeometry?.()?.getCoordinates()[0] || []

  // 获取修改前的坐标
  const modifyCoords = modifyGeometry?.geometry?.getCoordinates()[0] || []

  // 确保有足够的坐标点
  if (coords.length < 4 || modifyCoords.length < 4) {
    throw new Error('Invalid coordinates for rectangle')
  }

  // 找到第一个不同的点索引
  const index = findFirstDifferentIndex(coords, modifyCoords)
  const oppositeIndex = pointMap[index]

  // 保存对角点位置
  const oppositePoint: Coordinate = [...modifyCoords[oppositeIndex]]
  const draggedPoint: Coordinate = coords[index]

  // 计算其他两个点的位置
  let point1: Coordinate, point2: Coordinate

  // 根据拖动的点确定其他两个点的位置
  switch (index) {
    case 0: // 左上角
      point1 = [oppositePoint[0], draggedPoint[1]] // 右上角
      point2 = [draggedPoint[0], oppositePoint[1]] // 左下角
      break
    case 1: // 右上角
      point1 = [draggedPoint[0], oppositePoint[1]] // 右下角
      point2 = [oppositePoint[0], draggedPoint[1]] // 左上角
      break
    case 2: // 右下角
      point1 = [oppositePoint[0], draggedPoint[1]] // 左下角
      point2 = [draggedPoint[0], oppositePoint[1]] // 右上角
      break
    case 3: // 左下角
      point1 = [draggedPoint[0], oppositePoint[1]] // 左上角
      point2 = [oppositePoint[0], draggedPoint[1]] // 右下角
      break
    default:
      throw new Error('Invalid point index for rectangle')
  }

  // 更新坐标，保持矩形形状
  const newCoordinates: Coordinate[] = [
    draggedPoint,
    point1,
    oppositePoint,
    point2,
    [...draggedPoint] // 闭合多边形
  ]

  return newCoordinates
}

function findFirstDifferentIndex(arr1: number[][], arr2: number[][]) {
  // 确保两个数组的长度相同
  if (arr1.length !== arr2.length) {
    throw new Error('两个数组的长度不相同')
  }

  // 遍历数组
  for (let i = 0; i < arr1.length; i++) {
    // 检查每个子数组的长度是否相同
    if (arr1[i].length !== arr2[i].length) {
      throw new Error(`第 ${i} 行的数组长度不相同`)
    }

    // 比较子数组的每个元素
    for (let j = 0; j < arr1[i].length; j++) {
      if (arr1[i][j] !== arr2[i][j]) {
        return i // 返回第一个不一样的子数组的索引
      }
    }
  }

  return -1 // 如果没有不一样的子数组，返回-1
}

export default {
  getDefaultRectLonLats,
  addRect,
  addGrid,
  removeGrid,
  getThematic,
  addText,
  addLabel,
  addLegend,
  removeLayer,
  divideRangeIntoParts,
  getTifStyle,
  getSectorPoints,
  exportImage,
  screenshotEditRect,
  exportImageV2
}
