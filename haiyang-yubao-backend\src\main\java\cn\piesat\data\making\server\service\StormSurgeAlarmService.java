package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import cn.piesat.data.making.server.processor.StormSurgeGenerateText;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;

/**
 * 风暴潮警报制作表信息(StormSurgeAlarmB)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:34
 */
public interface StormSurgeAlarmService extends IService<StormSurgeAlarm> {
    PageResult<StormSurgeAlarm> pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime);

    Boolean checkNumber(String number);

    StormSurgeAlarm selectByNumber(String number);

    StormSurgeAlarm saveInfo(StormSurgeAlarmDTO stormSurgeAlarmDTO);

    StormSurgeAlarm updateInfo(StormSurgeAlarmDTO stormSurgeAlarmDTO);

    GenerateTextVO generateText(StormSurgeGenerateText stormSurgeGenerateText);

    void release(Long id);

    void updateDisplay(String lastNumber);

    Map<Long, Long> statistic(Date startTime, Date endTime);

    void downloadDoc(HttpServletResponse response, Long id);

    boolean updateByIdObj(StormSurgeAlarm stormSurgeAlarm);
}

