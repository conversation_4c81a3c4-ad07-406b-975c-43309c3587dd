<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-04 17:39:58
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-05-06 11:08:01
 * @FilePath: \hainan-jianzai-web\src\components\ElementStatistics\components\LiveMonitoring.vue
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="query-item">
    <div class="query-title">工具：</div>
    <div class="query-info">
      <n-button size="tiny" @click="startOrFinish">
        {{ selectStart ? '结束框选' : '框选' }}
      </n-button>
    </div>
  </div>
  <div class="query-item">
    <div class="query-title">观测设备：</div>
    <div class="query-info">
      <div
        v-for="item in equipmentList"
        :key="item.value"
        class="radio-btn"
        :class="observationEquipment === item.value ? 'active' : ''"
        @click="changeEquipement(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
  <div v-if="observationEquipment === 'oceanStation'" class="query-item">
    <div class="query-title">站类型:</div>
    <div class="query-info">
      <div
        v-for="item in oceanTypeList"
        :key="item.value"
        class="radio-btn"
        :class="oceanSelected.includes(item.value) ? 'active' : ''"
        @click="oceanStationTypeClicked(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
  <div v-if="observationEquipment === 'buoyStation'" class="query-item">
    <div class="query-title">浮标类型:</div>
    <div class="query-info">
      <div
        v-for="item in buoyTypeList"
        :key="item.value"
        class="radio-btn"
        :class="buoySelected.includes(item.value) ? 'active' : ''"
        @click="buoyStationTypeClicked(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
  <div v-loading="isStationDataLoading" class="table-container">
    <n-data-table
      :bordered="true"
      :single-line="true"
      :columns="columns"
      :data="tableData"
      :row-props="rowProps"
      size="small"
      max-height="300"
    />
  </div>

  <n-pagination
    v-model:page="pagination.page"
    class="qx-pagination flex-justify-end"
    :page-count="pagination.pageCount"
    size="small"
    :page-slot="7"
    @update:page="pagination.onChange"
    v-if="observationEquipment === 'ship'"
  />

  <LiveObserveDialog
    v-if="dialogVisible"
    :title="dialogTitle"
    :visible="dialogVisible"
    :station-type="observationEquipment"
    :station-info="stationInfo"
    @close="dialogVisible = false"
  />
</template>

<script setup>
import { ref, reactive, inject, onMounted, onUnmounted, nextTick, watch } from 'vue'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import Point from 'ol/geom/Point.js'
import { LiveObserveDialog } from './index'
import { useMessage } from 'naive-ui'
import haiyang from 'src/assets/images/points/tide.png'
import fubiao3m from 'src/assets/images/points/3m.png'
import fubiao10m from 'src/assets/images/points/10m.png'
import fubiaobl from 'src/assets/images/points/bl.png'
import chuanbo from 'src/assets/images/chuanbo.png'
import { Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import Feature from 'ol/Feature.js'
import { unByKey } from 'ol/Observable'
import ToolApi from 'src/requests/toolRequest'
import shipData from './PEND.json'
import Popup from 'src/utils/olPopup/ol-popup.js'
import 'src/utils/olPopup/ol-popup.css'
import { default as OlMap } from 'ol/Map'
import { useSelectManager } from 'src/components/ElementStatistics/components/liveMonitoring-hooks/useSelectManager'
import eventBus from 'src/utils/eventBus'
import { showStationDialog } from 'src/components/ElementStatistics/components/types'
import { useRealtimeStationType } from 'src/components/ElementStatistics/components/liveMonitoring-hooks/useRealtimeStationType'

const message = useMessage()
/** @type {IGetMapFunction} */
const getMap = inject('getMap')
const getMapV2 = inject('getMapV2')
const observationEquipment = ref('oceanStation')
const equipmentList = [
  {
    label: '海洋站',
    value: 'oceanStation'
  },
  {
    label: '浮标站',
    value: 'buoyStation'
  },
  {
    label: '志愿船',
    value: 'ship'
  }
]
const oceanType = ref('liveObservation')
const oceanTypeList = [
  // {
  //   label: '全部',
  //   value: 'liveObservation'
  // },
  {
    label: '省级',
    value: 'sheng'
  },
  {
    label: '市县',
    value: 'shi'
  },
  {
    label: '潮位简易站',
    value: 'chao'
  }
]
let buoyType = ref('')
const buoyTypeList = [
  {
    label: '3米',
    value: '3m'
  },
  {
    label: '10米',
    value: '10m'
  },
  {
    label: '波浪谱',
    value: 'waveSpectrum'
  }
]

const {
  buoySelected,
  isStationDataLoading,
  buoyStationTypeClicked,
  tableData,
  loadBuoyStationData,
  oceanSelected,
  loadOceanStationData,
  oceanStationTypeClicked
} = useRealtimeStationType({})

watch(tableData, val => {
  addStationPoint()
})

function changeEquipement(val) {
  stationSource.clear()
  observationEquipment.value = val

  getStationList()
}

/** 表格部分 */
const columns = ref(createColumns())
// 站点列表
function getStationList() {
  stationSource.refresh()
  if (observationEquipment.value === 'ship') {
    columns.value = [
      {
        title: '序号',
        width: 50,
        render(row, rowIndex) {
          return rowIndex + 1
        }
      },
      {
        title: '设备名称',
        key: 'name'
      }
    ]
  } else {
    columns.value = createColumns()
  }

  tableData.value = []
  // 适配多选逻辑
  if (observationEquipment.value === 'buoyStation') {
    loadBuoyStationData()
    return
  } else if (observationEquipment.value === 'oceanStation') {
    loadOceanStationData()
    return
  }
  let type =
    observationEquipment.value == 'oceanStation'
      ? oceanType.value
      : buoyType.value
  const params = {
    pageNum: pagination.page,
    pageSize: 1000, //pagination.pageSize,
    stationTypeCode: observationEquipment.value,
    type: observationEquipment.value == 'ship' ? '' : type
  }
  ToolApi.getOceanStatioPage(params)
    .then(res => {
      if (res) {
        tableData.value = res.pageResult
        pagination.pageCount = Number(res.pages)
        pagination.itemCount = Number(res.pages)
        addStationPoint()
      }
    })
    .catch(e => {
      message.error('获取数据失败')
      console.error(e, 'live-monitor-list')
    })
}
// 获取船舶数据
function getShipPage() {
  const params = {
    pageNum: pagination.page,
    pageSize: pagination.pageSize
  }
  ToolApi.getShipPage(params)
    .then(res => {
      let { pageResult = [], pages } = res || {}
      let result = []
      pageResult.forEach(item => {
        let obj = {
          signShip: item
        }
        result.push(obj)
      })
      tableData.value = result
      pagination.pageCount = Number(pages)
      columns.value = [
        {
          title: '序号',
          width: 50,
          render(row, rowIndex) {
            return rowIndex + 1
          }
        },
        {
          title: '设备名称',
          key: 'signShip'
        }
      ]
    })
    .catch(e => {
      console.log(e, 'eeeee')
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg || '获取船舶数据失败')
    })
}

function changeOceanType(val) {
  oceanType.value = val
  pagination.page = 1
  getStationList()
}

function changeBuoyType(val) {
  buoyType.value = val
  pagination.page = 1
  getStationList()
}

// 创建表头
function createColumns() {
  return [
    {
      title: '序号',
      width: 50,
      render(row, rowIndex) {
        return rowIndex + 1
      }
    },
    {
      title: '站位名称',
      key: 'name',
      ellipsis: {
        tooltip: {
          maxWidth: 300
        },
        lineClamp: 1
      }
    },
    {
      title: '站位编号',
      key: 'relationStation',
      ellipsis: {
        tooltip: {
          maxWidth: 300
        },
        lineClamp: 1
      }
    }
  ]
}
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageSlot: 5,
  pageCount: 0,
  itemCount: 0,
  size: 'small',
  onChange: page => {
    pagination.page = page
    changeEquipement(observationEquipment.value)
  }
})
const currentRowIndex = ref(-1)
function rowProps(row, rowIndex) {
  return {
    style: { cursor: 'pointer' },
    class: currentRowIndex.value === rowIndex ? 'row-active' : '',
    onClick() {
      onRowClick(row, rowIndex)
    }
  }
}
function onRowClick(row, rowIndex) {
  currentRowIndex.value = rowIndex
  getMap(map => {
    // feature.getStyle()[0].getText().getFill().setColor('#ff0000')
    stationSource.forEachFeature(feature => {
      const info = feature.getProperties()
      if (info.id === row.id) {
        feature.getStyle()[0].getText().getFill().setColor('#ff0000')
      } else {
        feature.getStyle()[0].getText().getFill().setColor('black')
      }
    })

    if (observationEquipment.value === 'ship') {
      getShipInfo(row)
      // map.getView().setCenter([row.longitude, row.latitude])
    } else {
      let point = JSON.parse(row.locationJson)
      const geo = point.geometry
      map.getView().setCenter(geo.coordinates)
    }
  })
}

// 获取船舶航行轨迹
function getShipInfo(row) {
  const params = {
    signShip: row.name
  }
  ToolApi.getShipInfo(params)
    .then(res => {
      addStationPoint(res)
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg || '获取船舶数据失败')
    })
}
/** 地图部分 */
const stationSource = new VectorSource()
const stationLayer = new VectorLayer({
  source: stationSource
})

function getBuoyIcon() {
  let mapper = {
    '3m': fubiao3m,
    '10m': fubiao10m,
    waveSpectrum: fubiaobl
  }
  return mapper[buoyType.value]
}
function addStationPoint(data = []) {
  stationSource.refresh()
  let mapper = {
    oceanStation: haiyang,
    buoyStation: getBuoyIcon() || fubiao3m,
    ship: chuanbo
  }
  const img = mapper[observationEquipment.value]
  data = data.length ? data : tableData.value

  data.forEach(item => {
    let station = {}
    if (observationEquipment.value === 'ship') {
      station = new Feature({
        geometry: new Point([item.longitude, item.latitude])
      })
    } else {
      let point = JSON.parse(item.locationJson)
      const geo = point.geometry
      station = new Feature({
        geometry: new Point(geo.coordinates)
      })
    }
    const iconStyle = new Style({
      image: new Icon({
        // anchor: [0.5, 1],
        src: img,
        scale: [0.6, 0.6]
      }),
      text: new Text({
        text: item.name,
        font: 'bold 14px Calibri,sans-serif',
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        }),
        offsetY: 20
      })
    })
    station.setStyle([iconStyle])
    station.setProperties(item)
    stationSource.addFeature(station)
  })
  getMap(map => {
    const extent = stationSource.getExtent()
    if (extent.includes(Infinity) || extent.includes(-Infinity)) {
      return
    }
    map.getView().fit(extent)
  })
}

let dialogTitle = ref('')
let dialogVisible = ref(false)
let stationInfo = ref({})
function displayFeatureInfo(evt, map) {
  if (evt.dragging) {
    return
  }
  // showPopup.value = true
  const pixel = map.getEventPixel(evt.originalEvent)

  stationLayer
    .getFeatures(pixel)
    .then(features => {
      if (features.length > 0) {
        const feature = features[0]
        const info = feature.getProperties()

        stationInfo.value = info

        if (observationEquipment.value === 'ship') {
          popup.hide()
          let html = `<div class="ship-popup">
                <p class="title-popup">名称:${info.signShip}</p>
                <p class="title-popup">时间:${info.time}</p>
                <p class="title-popup">经度:${info.longitude}</p>
                <p class="title-popup">维度:${info.latitude}</p>
                <p class="title-popup">可见度:${info.visibility}</p>
                <p class="title-popup">气温:${filterInvalidValues(
                  info.airTemperature
                )} ℃</p>
                <p class="title-popup">露点温度:${filterInvalidValues(
                  info.dewPointTemp
                )} ℃</p>
                <p class="title-popup">风向:${info.windDirection} (°)</p>
                <p class="title-popup">风速:${info.windSpeed} m/s</p>
                <p class="title-popup">气压:${filterInvalidValues(
                  info.airPressure
                )}hpa</p>
                <p class="title-popup">云总量:${info.totalCloudAmount}</p>
                <p class="title-popup">云量低:${filterInvalidValues(
                  info.lowCloudAmount
                )}</p>
                <p class="title-popup">海温:${filterInvalidValues(
                  info.seaTemp
                )} ℃</p>
                <p class="title-popup">波期:${filterInvalidValues(
                  info.wavePeriod
                )} s</p>
                <p class="title-popup">波高:${filterInvalidValues(
                  info.waveHeight
                )} m</p>
                <p class="title-popup">浪涌方向:${info.surgeDirection} (°)</p>
                <p class="title-popup">浪涌周期:${info.surgePeriod} s</p>
                <p class="title-popup">浪涌高度:${info.surgeHeight} m</p>
                </div>`
          popup.show(evt.coordinate, html)
        } else {
          onRowClick(info, info.key)
          dialogTitle.value = info.name
          dialogVisible.value = true
        }
      }
    })
    .catch(e => {
      console.log(e)
    })
}

function filterInvalidValues(val) {
  if (val === null || val === undefined || val.toString().includes('99.9')) {
    return '--'
  }
  return val
}

let moveHandel = null
const popup = new Popup()
onMounted(() => {
  getStationList()
  getMap(map => {
    map.addLayer(stationLayer)
    map.addOverlay(popup)
    moveHandel = map.on('click', function (evt) {
      displayFeatureInfo(evt, map)
    })
  })
})
onUnmounted(() => {
  getMap(map => {
    unByKey(moveHandel)
    map.removeLayer(stationLayer)
  })
})

const { startOrFinish, selectStart } = useSelectManager({
  vectorLayer: stationLayer,
  vectorSource: stationSource,
  map: getMapV2,
  onFinish(collection) {
    const propsArr = collection
      .getArray()
      .map(f => f.getProperties())
      .filter(i => {
        return ['oceanStation', 'buoyStation'].includes(i.stationTypeCode)
      })
    if (propsArr.length === 0) {
      message.warning('未识别到海洋站或浮标站')
      return
    }
    dialogVisible.value = true
    stationInfo.value = propsArr[0]

    nextTick(() => {
      if (propsArr.length > 1) {
      }
      eventBus.emit(showStationDialog, propsArr)
    })
  }
})
</script>

<style lang="scss" scoped>
.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;
  .query-title {
    white-space: nowrap;
    width: 70px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    flex-shrink: 0;
  }
  .query-info {
    width: 346px;
    display: flex;
    overflow-x: auto;
    .radio-btn {
      text-wrap: nowrap;
      background-color: rgb(231, 241, 253);
      padding: 5px 8px;
      margin: 0px 5px;
      border-radius: 4px;
      color: #222222;
      cursor: pointer;
      &:hover {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
      &.active {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
    }
  }
}
::v-deep .row-active {
  background: #e8f0fa;
  .n-data-table-td {
    background: transparent;
  }
}
.qx-pagination {
  margin-top: 12px;
}
.table-container {
  height: 400px;
}
</style>
<style>
.ship-popup {
  max-height: 300px;
}
</style>
