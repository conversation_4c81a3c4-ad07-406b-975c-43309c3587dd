<template>
  <!-- 预报检验 -->
  <div class="forecast-test">
    <mo-left-menu
      :title="leftMenu.title"
      :value="leftMenu.value"
      :list="leftMenuList"
      @click="handleClickChange"
    />
    <div class="mo-right-content">
      <div class="mo-right-header">
        <div
          v-for="item in heraderData.list"
          :key="item.value"
          class="mo-right-header-item"
          :class="item.value === heraderData.value ? 'active' : ''"
          @click="handleClickHeader(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="mo-right-content-overflow">
        <template v-if="heraderData.value === '1'">
          <div class="mo-right-content-body">
            <qx-no-data v-if="!imgDataList.length" />
            <template v-else>
              <div
                v-for="(item, index) in imgDataList"
                :key="index"
                class="d-flex"
              >
                <img-dis-dl
                  :title="item.title"
                  :img-url="item.url"
                  :img-name="`${radioData.value}${leftMenu.title}${item.title}`"
                />
              </div>
            </template>
          </div>
        </template>
        <template v-else>
          <div v-if="!reportData.url" class="no-data">
            请在右侧选择检验报告的开始时间和结束时间！
          </div>
          <div v-else class="report-show">
            <Icon size="26" color="#AEAEAE" @click="downloadReport">
              <DownloadOutlined />
            </Icon>
            <div class="image">
              <iframe
                :src="reportData.url"
                width="100%"
                height="100%"
                frameborder="0"
              ></iframe>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="inquire-option">
      <template v-if="heraderData.value === '1'">
        <div class="inquire-option-box">
          <n-date-picker
            v-model:formatted-value="formData.startTime"
            class="m-b-20"
            type="date"
            clearable
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
          <n-date-picker
            v-model:formatted-value="formData.endTime"
            class="m-b-20"
            type="date"
            clearable
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
          <qx-button
            style="width: 100%"
            class="primary m-b-20"
            @click="getSearchTime"
            >搜索
          </qx-button>
          <template v-if="radioData.list.length">
            <n-radio-group
              v-model:value="radioData.value"
              name="radiogroup"
              @update:value="onChangeTime"
            >
              <n-space>
                <n-radio
                  v-for="(item, index) in radioData.list"
                  :key="index"
                  class="m-b-10"
                  :value="item.dataTime"
                >
                  {{ item.dataTime }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </template>
        </div>
      </template>
      <template v-else>
        <div class="inquire-option-box">
          <n-date-picker
            v-model:formatted-value="reportData.startTime"
            class="m-b-20"
            type="date"
            clearable
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
          <n-date-picker
            v-model:formatted-value="reportData.endTime"
            class="m-b-20"
            type="date"
            clearable
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
          <qx-button
            style="width: 100%"
            class="primary m-b-20"
            @click="getSearchTimeReport"
            >生成报告
          </qx-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, defineComponent, toRefs, ref, onMounted } from 'vue'
import { MoLeftMenu, ImgDisDl } from './components/index'
import moment from 'moment'
import { QxButton } from 'src/components/QxButton/index'
import { Icon } from '@vicons/utils/lib'
import { DownloadOutlined } from '@vicons/antd'
import Api from 'src/requests/forecastTest'
import { groupBy } from 'lodash'
import png4 from '../../assets/images/forecastTest/u44.png'
import { useMessage } from 'naive-ui'

export default defineComponent({
  components: {
    QxButton,
    MoLeftMenu,
    ImgDisDl,
    Icon,
    DownloadOutlined
  },
  setup() {
    const message = useMessage()
    const state = reactive({
      leftMenu: {
        title: '海面风预报检验',
        value: '1'
      },
      leftMenuList: [],
      heraderData: {
        value: '1',
        list: [
          { label: '检验结果', value: '1' },
          { label: '报告生成', value: '2' }
        ]
      },
      imgDataList: [] as any[],
      radioData: {
        value: null,
        list: [] as any[]
      },
      formData: {
        startTime: '2024-05-01', //moment().subtract(7, 'days').format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      },
      reportData: {
        title: '',
        url: '',
        startTime: '2024-05-01', //moment().subtract(7, 'days').format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD'),
        productId: '',
        fileName: '',
        filePath: ''
      }
    })
    const methods = reactive({
      async download() {
        //图片下载
        try {
          const response = await fetch(state.reportData.url)
          if (!response.ok) {
            throw new Error('Network response was not ok')
          }
          const blob = await response.blob()
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)
          link.download = state.reportData.title // 设置你想要的文件名
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(link.href) // 清理
        } catch (error) {
          console.error('There was a problem with the fetch operation:', error)
        }
      },
      handleClickChange(val?: any) {
        state.leftMenu.value = val.productId
        state.leftMenu.title = val.name
        state.reportData.productId = val.docxProductId
        state.reportData.url = ''
        methods.getSearchTime()
      },
      handleClickHeader(val?: any) {
        state.heraderData.value = val.value
      },
      // 获取产品数据
      async getData() {
        state.radioData.list = []
        //请求数据
        const params = {
          beginTime: state.formData.startTime,
          endTime: state.formData.endTime
          // beginTime: '2024-10-25 00:00:00'
          // endTime: '2024-10-28 00:00:00'
        }
        await Api.getTestResuls(state.leftMenu.value, params).then(
          async (res: any) => {
            if (res?.length) {
              res.forEach((item: any) => {
                let arr = item.file_name.split('_')
                let result = arr[arr.length - 1].split('.')[0]
                let typeName = ''
                if (result == 'table') {
                  typeName = '精度评价表'
                } else if (result == 'scatter') {
                  typeName = '散点图'
                } else if (result == 'hist') {
                  typeName = '直方图'
                }
                item.type = result
                item.title = typeName
                item.url = config.onlyOfficeServerUrl + item.file_path
              })
              let arr = groupBy(res, 'data_time')
              let result: any[] = []
              Object.keys(arr).forEach(item => {
                result.push({
                  dataTime: item,
                  data: methods.sortHander(arr[item])
                })
              })

              if (result) {
                result.sort((a: any, b: any) => {
                  return (
                    new Date(a.dataTime).getTime() -
                    new Date(b.dataTime).getTime()
                  )
                })

                let imageList = result[0].data
                // for (let i = 0; i < imageList.length; i++) {
                //   let item = imageList[i]
                //   let image = await Api.previewImage(btoa(item.file_path))
                //   item.url = window.URL.createObjectURL(new Blob([image.data], {type: "application/json"}))
                // }

                state.imgDataList = imageList

                console.log(state.imgDataList, 'state.imgDataList')
                state.radioData.list = result
                state.radioData.value = result[0].dataTime
              }
            } else {
              message.warning('暂无数据')
            }
          }
        )
      },
      // 按照散点图，直方图，精度评价表排序
      sortHander(data: any) {
        let arr: any[] = []
        let mapper = ['scatter', 'hist', 'table']
        mapper.forEach((item: any) => {
          let result = data.find((ite: any) => ite.type == item)
          arr.push(result)
        })
        return arr
      },
      // 获取左侧菜单数据
      getMenuList() {
        Api.getMenuList({ type: 'ZJ' })
          .then((res: any) => {
            state.leftMenuList = res
            state.leftMenu.value = res[0].productId
            state.reportData.productId = res[0].docxProductId
            methods.getData()
          })
          .catch(e => {
            let { msg = null } = e?.response?.data || {}
            message.error(msg || '获取菜单失败')
          })
      },
      // 切换事件重新获取图片列表
      async onChangeTime(val: any) {
        let result = state.radioData.list.find(
          (item: any) => item.dataTime == val
        )
        let imageList = result.data
        // for (let i = 0; i < imageList.length; i++) {
        //   let item = imageList[i]
        //   let image = await Api.previewImage(btoa(item.file_path))
        //   item.url = URL.createObjectURL(image.data)
        // }

        state.imgDataList = imageList
      },
      getSearchTime() {
        //请求检验结果数据
        methods.getData()
      },
      // 获取检验报告
      getSearchTimeReport() {
        const params = {
          beginTime: state.reportData.startTime,
          endTime: state.reportData.endTime
        }
        Api.getTestResuls(state.reportData.productId, params)
          .then((res: any) => {
            if (res?.length) {
              state.reportData.fileName = res[0].file_name
              state.reportData.filePath =
                config.onlyOfficeServerUrl + res[0].file_path
              state.reportData.url =
                config.kkFileUrl +
                btoa(config.onlyOfficeServerUrl + res[0].file_path)
            }
          })
          .catch(e => {
            let { msg = null } = e?.response?.data || {}
            message.error(msg || '获取报告失败')
          })
      },
      downloadReport() {
        let a = document.createElement('a')
        a.download = state.reportData.fileName
        a.style.display = 'none'
        let url = state.reportData.filePath
        a.href = url
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(url) // 销毁
        document.body.removeChild(a)
      }
    })
    onMounted(() => {
      methods.getMenuList()
    })
    return {
      ...toRefs(state),
      ...toRefs(methods)
    }
  }
})
</script>

<style scoped lang="scss">
.forecast-test {
  width: 100%;
  //height: 100%;
  overflow: auto;
  padding: 20px;
  display: flex;

  .mo-right-content {
    width: 100%;

    .mo-right-header {
      height: 60px;
      background: #ffffff;
      border-radius: 8px 8px 0px 0px;
      display: flex;
      padding: 0 8px;
      margin-bottom: 20px;

      .mo-right-header-item {
        padding: 21px 25px 0;
        font-size: 16px;
        color: #666666;
        cursor: pointer;
        line-height: 19px;

        &.active {
          color: #222222;
          border-bottom: 3px solid #3c83ed;
        }
      }
    }
  }

  .mo-right-content-overflow {
    overflow: hidden;
    height: calc(100% - 80px);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mo-right-content-body {
    overflow: auto;
    height: 100%;
    padding-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto auto;
    //gap: 20px; /* 边距20px */
    grid-gap: 20px;

    .d-flex:nth-child(3) {
      grid-column: 1 / -1;
    }
  }

  .inquire-option {
    width: 270px;
    height: 100%;
    margin-left: 20px;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;

    .inquire-option-box {
      padding: 20px;

      .m-b-20 {
        margin-bottom: 20px;
      }
    }

    .m-b-10 {
      margin-bottom: 10px;
    }
  }

  .no-data {
    width: 468px;
    height: 60px;
    background: #fff3f3;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ffa7a7;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 18px;
    color: #d60000;
    line-height: 60px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .report-show {
    text-align: center;
    position: relative;
    width: 100%;
    height: 100%;
    .image {
      height: 100%;
      overflow: auto;
      padding-top: 35px;
    }
    img {
      max-width: 80%;
    }
    .xicon {
      position: absolute;
      right: 48px;
      top: 0;
      &:hover {
        color: #567bff !important;
      }
    }
  }
}
</style>
