package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;
import cn.piesat.data.making.server.entity.StormSurge;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
public class AlertStormSurgeXmlServiceTest {

    @Autowired
    private StormSurgeAlarmService stormSurgeAlarmServiceImpl;

    @Autowired
    private AlertStormSurgeXmlService alertStormSurgeXmlServiceImpl;

    @Test
    public void testSender(){
        StormSurgeAlarm stormSurgeAlarm = stormSurgeAlarmServiceImpl.getById(1952645416654737410L);

        alertStormSurgeXmlServiceImpl.sender(Thread.currentThread().getContextClassLoader().getResource("").getPath()+"alertxml/stormsurgetemplate.xml",stormSurgeAlarm);
    }
}
