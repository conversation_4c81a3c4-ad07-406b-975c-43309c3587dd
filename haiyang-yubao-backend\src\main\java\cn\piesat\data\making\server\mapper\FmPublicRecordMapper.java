package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmPublicRecord;
import cn.piesat.data.making.server.vo.FmPublicRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmPublicRecordMapper {

    FmPublicRecordMapper INSTANCE = Mappers.getMapper(FmPublicRecordMapper.class);

    List<FmPublicRecordVO> entityListToVoList(List<FmPublicRecord> list);

    FmPublicRecord voToEntity(FmPublicRecordVO vo);

    FmPublicRecordVO entityToVo(FmPublicRecord fmPublicRecord);
}
