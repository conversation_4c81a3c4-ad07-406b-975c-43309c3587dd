import { computed } from 'vue'

const cityMap = new Map([
  ['海南岛北部', '海口、澄迈、临高、文昌'],
  ['海南岛东部', '琼海、万宁、陵水'],
  ['海南岛南部', '三亚、乐东'],
  ['海南岛西部', '儋州、昌江、东方']
])

export function useCityName(tableData: () => { name: string }[]) {
  const cptTableData = computed(() => {
    return tableData().map(i => ({
      name: i.name,
      city: getCityName(i.name)
    }))
  })

  return {
    cptTableData
  }
}

/**
 * 区域名称转城市名称
 * @param areaName
 */
export function getCityName(areaName: string) {
  return cityMap.get(areaName)
}
