<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-14 17:31:31
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-27 17:50:05
 * @FilePath: /hainan-jianzai-web/src/views/analysis/components/dataSource.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="map-data-source d-flex">
    <n-radio-group v-model:value="curretType" name="radiogroup" @update:value="onChange">
      <n-radio label="智能网格" value="grid"></n-radio>
      <n-radio label="NMEFC" value="nmefc"></n-radio>
    </n-radio-group>
    <!-- <div class="data-source-item">
      <n-switch v-model:value="dataSourceGrid" size="small" @update:value="(val)=>onChange('grid',val)"/>智能网格
    </div>
    <div class="data-source-item">
      <n-switch v-model:value="dataSourceNmefc" size="small" @update:value="(val)=>onChange('NMEFC',val)"/>NMEFC
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref,watch } from 'vue'
const emit = defineEmits(['change'])
let curretType = ref('nmefc')

const props = defineProps({
  value:{
    type: String,
    default: ''
  }
})

watch(()=>props.value,(newVal)=>{
  curretType.value = newVal
})



function onChange(type: string) {
  emit('change', type)
}
</script>

<style lang="scss">
.map-data-source {
  position: absolute;
  right: 20px;
  z-index: 8;
  bottom: 150px;
  .n-radio__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 14px;
    &:nth-child(1) {
      margin-right: 18px;
    }
  }
  .n-switch {
    margin-right: 5px;
  }
}
</style>