package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dto.SeaWaveMessageDTO;
import cn.piesat.data.making.server.dto.generate.WaveMessageWordDTO;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.enums.ProductEnum;
import cn.piesat.data.making.server.enums.WarningMessageStatus;
import cn.piesat.data.making.server.mapper.SeaWaveMessageMapper;
import cn.piesat.data.making.server.mapper.WaveMessageWordMapper;
import cn.piesat.data.making.server.processor.WaveGenerateText;
import cn.piesat.data.making.server.processor.WaveGenerateTextProcessor;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.data.making.server.vo.UserVO;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import cn.piesat.security.ucenter.starter.utils.UserUtils;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.piesat.data.making.server.dao.SeaWaveMessageDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * (SeaWaveMessageB)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-11 16:59:03
 */
@Service("seaWaveMessageService")
public class SeaWaveMessageServiceImpl extends ServiceImpl<SeaWaveMessageDao, SeaWaveMessage> implements SeaWaveMessageService {

    @Value("${piesat.make.fax}")
    private String fax;
    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Value("${piesat.make.wave-message-template}")
    private String waveMessageTemplate;
    @Autowired
    private WaveGenerateTextProcessor waveGenerateTextProcessor;
    @Autowired
    private GenerateProductService generateProductService;

    @Autowired
    private UserService userServiceImpl;

    @Override
    public PageResult<SeaWaveMessage> pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime) {
        LambdaQueryWrapper<SeaWaveMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(SeaWaveMessage::getCreateTime);
        if (startTime != null) {
            wrapper.ge(SeaWaveMessage::getReleaseTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(SeaWaveMessage::getReleaseTime, endTime);
        }
        Page<SeaWaveMessage> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }

    @Override
    public void checkNumber(String number) {
        SeaWaveMessage seaWaveMessage = this.selectByNumber(number);
        if(Objects.nonNull(seaWaveMessage)) throw new BusinessException("该编号已存在");
    }

    @Override
    public SeaWaveMessage selectByNumber(String number) {
        LambdaQueryWrapper<SeaWaveMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SeaWaveMessage::getNumber,number);
        return this.getOne(wrapper, false);
    }

    @Override
    public SeaWaveMessage saveInfo(SeaWaveMessageDTO seaWaveMessageDTO) {
        this.checkNumber(seaWaveMessageDTO.getNumber());
        SeaWaveMessage  seaWaveMessage = SeaWaveMessageMapper.INSTANCE.toEntity(seaWaveMessageDTO);
        seaWaveMessage.setStatus(WarningMessageStatus.NOT_SUBMIT.getValue());
        this.save(seaWaveMessage);
        return seaWaveMessage;
    }

    @Override
    public SeaWaveMessage updateInfo(SeaWaveMessageDTO seaWaveMessageDTO) {
        SeaWaveMessage seaWaveMessage = Optional.ofNullable(this.getById(seaWaveMessageDTO.getId())).orElseThrow(() -> new BusinessException("未找到对应警报消息"));
        if(WarningMessageStatus.SUBMIT.getValue().equals(seaWaveMessage.getStatus())) throw new BusinessException("已提交状态警报消息不可修改");
        if(!seaWaveMessageDTO.getNumber().equals(seaWaveMessage.getNumber())){this.checkNumber(seaWaveMessageDTO.getNumber());}
        seaWaveMessage =  SeaWaveMessageMapper.INSTANCE.toEntity(seaWaveMessageDTO);
        this.updateById(seaWaveMessage);
        return seaWaveMessage;
    }

    @Override
    public GenerateTextVO generateText(WaveGenerateText waveAlarmGenerateText) {
        return waveGenerateTextProcessor.generate(waveAlarmGenerateText,false);
    }

    @Override
    public void release(SeaWaveMessageDTO seaWaveMessageDTO) {
        SeaWaveMessage seaWaveMessage;
        if(Objects.nonNull(seaWaveMessageDTO.getId())){
            seaWaveMessage = this.updateInfo(seaWaveMessageDTO);
        }else {
            seaWaveMessage = this.saveInfo(seaWaveMessageDTO);
        }
        //查询制作人信息
        //UserInfoDTO userInfo = UserUtils.getUserInfo(seaWaveMessage.getMakeUser());

        UserVO userVO = userServiceImpl.getInfoById(seaWaveMessage.getMakeUser());
        WaveMessageWordDTO waveMessageWordDTO = WaveMessageWordMapper.INSTANCE.toDTO(seaWaveMessage,userVO,fax);

        //拼接产出文件地址
        Date releaseTime = seaWaveMessage.getReleaseTime();
        String wordFilePath = String.format(CommonConstant.MESSAGE_WAVE_WORD_FILE_NAME,baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateWord(wordFilePath,waveMessageTemplate, waveMessageWordDTO);
        //word转html
        String htmlFilePath = String.format(CommonConstant.MESSAGE_WAVE_HTML_FILE_NAME,baseOutputPath,DateUtil.year(releaseTime), DateUtil.format(releaseTime,
                "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateHtml(wordFilePath,htmlFilePath);
        //短信文件写入
        String smsFilePath = String.format(CommonConstant.MESSAGE_WAVE_TXT_FILE_NAME, baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateSms(smsFilePath,seaWaveMessageDTO.getSmsContent());
        Long pushTaskId = generateProductService.sendPushServer("海浪消息产品推送", wordFilePath, htmlFilePath, smsFilePath, ProductEnum.WAVE_MESSAGE_WORD,
                ProductEnum.WAVE_MESSAGE_HTML, ProductEnum.WAVE_MESSAGE_SMS, releaseTime);
        seaWaveMessage.setPushTaskId(pushTaskId);
        seaWaveMessage.setStatus(WarningMessageStatus.SUBMIT.getValue());
        this.updateById(seaWaveMessage);
    }
}

