<template>
  <!-- 数据维护 -->
  <div class="data-maintenance">
    <data-list
      ref="dataListRef"
      :tree-data="treeData"
      @search="onSearch"
      @edit="onEditTree"
      @delete="onDeleteTree"
    />
    <div class="data-maintenance-map">
      <div class="maintenance-header">
        <h3>数据详情</h3>
        <div class="btns">
          <!-- <qx-button @click="onEdit">编辑</qx-button> -->
          <qx-button class="primary" @click="onCreate">新建</qx-button>
        </div>
      </div>
      <div class="map-container">
        <base-map :show-tool="false" @ready="mapReady"> </base-map>
      </div>
    </div>

    <add-dialog
      v-if="dialogVisible"
      ref="addDialogRef"
      v-model:visible="dialogVisible"
      :category-list="treeData"
      :map="map"
      :type="curTab"
      :is-edit="isEdit"
      @update:data="updateTree"
      @change-select-feature="changeSelectFeature"
      @remove-feature="removeFeature"
    />
  </div>
</template>

<script setup lang="ts">
import { BaseMap } from 'src/components/OpenlayersMap'
import { DataList } from './index'
import { QxButton } from 'src/components/QxButton'
import { AddDialog } from './index'
import { useMessage } from 'naive-ui'
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import Api from 'src/requests/forecast'
import DataApi from 'src/requests/dataMaintenance'
import { GeoJSON } from 'ol/format'
import { Vector as VectorSource } from 'ol/source'
import { Tile as TileLayer, Vector as VectorLayer } from 'ol/layer'
import eventBus from 'src/utils/eventBus'
import { Circle, Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import stationImg from 'src/assets/images/station.png'
import { createDiscreteApi } from 'naive-ui'
const { dialog } = createDiscreteApi(['dialog'])
const dataListRef = ref()
const message = useMessage()
const dialogVisible = ref<boolean>(false)
const treeData = ref<any[]>([])
const searchVal = ref<string>('')
const isEdit = ref<boolean>(false)
const addDialogRef = ref()
let map: any = null

function onSearch(val: string) {
  searchVal.value = val
  getAreaList()
}
function updateTree(data: any, form: any) {
  treeData.value = []
  const params: any = {
    name: ''
  }
  if (curTab.value === 1) {
    Api.getAreaList(params)
      .then((res: any) => {
        treeData.value = res
        if (data) {
          dataListRef.value.setSelectedKey(data)
          drawPolygon({ id: data, locationJson: form.locationJson, form: form })
        }
      })
      .catch(e => {
        console.log(e)
        let { msg } = e?.response?.data || {}
        message.error(msg || '获取数据失败')
      })
  } else {
    params.flag = 'station'
    params.stationTypeCode = 'oceanStation'
    DataApi.getStationList(params)
      .then((res: any) => {
        treeData.value = res
        if (data) {
          dataListRef.value.setSelectedKey(data)
          drawPolygon({
            id: data,
            locationJson: form.locationJson,
            name: form.name
          })
        }
      })
      .catch(e => {
        let { msg } = e?.response?.data
        message.error(msg || '获取数据失败')
      })
  }
}
// 获取区域列表
function getAreaList() {
  const params: any = {
    name: searchVal.value
  }
  if (curTab.value === 1) {
    Api.getAreaList(params)
      .then((res: any) => {
        treeData.value = res
      })
      .catch(e => {
        let { msg } = e?.response?.data || {}
        message.error(msg || '获取数据失败')
      })
  } else {
    params.flag = 'station'
    params.stationTypeCode = 'oceanStation'

    DataApi.getStationList(params)
      .then((res: any) => {
        treeData.value = res
      })
      .catch(e => {
        let { msg } = e?.response?.data || {}
        message.error(msg || '获取数据失败')
      })
  }
}
let curTab = ref(2)
function onCreate() {
  isEdit.value = false
  dialogVisible.value = true
}
let vectorSource = new VectorSource()
let vectorLayer = new VectorLayer({
  source: vectorSource,
  zIndex: 8
})

function mapReady(val: any) {
  map = val
  map.addLayer(vectorLayer)

  if (val) {
    val.getAllLayers().forEach((layer: any) => {
      const property = layer.getProperties()
      if (property && property.name === '海南陆地') {
        layer.setOpacity(0)
      }
    })
    val.getView().setZoom(6)
  }
}
let mapFeatures = ref<any[]>([])
const styles = {
  Point: new Style({
    image: new Icon({
      anchor: [0.5, 1],
      src: stationImg
    })
  }),

  Polygon: new Style({
    stroke: new Stroke({
      color: 'blue',
      lineDash: [4],
      width: 3
    }),
    fill: new Fill({
      color: 'rgba(0, 0, 255, 0.1)'
    })
  })
}

function drawPolygon(params: any) {
  if (mapFeatures.value.includes(params.id)) {
    if (vectorSource) {
      let feature = vectorSource?.getFeatureById(params.id)
      if (feature) {
        // vectorSource.removeFeature(feature)
        map.getView().fit(feature.getGeometry())
        map.getView().setZoom(8)
      }
    }
    mapFeatures.value = mapFeatures.value.filter(item => item !== params.id)
    return
  } else {
    if (!params.locationJson) {
      return false
    }
    let feature = new GeoJSON().readFeature(params.locationJson)
    const iconStyle = new Style({
      image: new Icon({
        anchor: [0.5, 1],
        src: stationImg,
        scale: [0.6, 0.6]
      }),
      text: new Text({
        text: params.name,
        font: 'bold 14px Calibri,sans-serif',
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        }),
        offsetY: 10
      })
    })

    const pointStyle = new Style({
      image: new Circle({
        radius: 7,
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        })
      })
    })
    if (curTab.value === 2) {
      feature.setStyle([iconStyle])
    }
    feature.setId(params.id)
    mapFeatures.value.push(params.id)
    feature.setProperties({
      name: params.name,
      id: params.id
    })
    vectorSource.addFeature(feature)
  }
}

function onEdit() {
  dialogVisible.value = true
  isEdit.value = true
}
// 点击左侧列表
function selectedTree(option: any) {
  addDialogRef.value.changeSelectLayer(option)
}
function changeSelectFeature(key: any) {
  dataListRef.value.setSelectedKey(key)
}
function removeFeatureById(id: any) {
  if (vectorSource) {
    let feature = vectorSource?.getFeatureById(id)
    if (feature) {
      vectorSource.removeFeature(feature)
    }
  }
  mapFeatures.value = mapFeatures.value.filter(item => item !== id)
}
function onDeleteTree(option: any) {
  dialog.warning({
    title: '提示',
    content: `是否删除${option.name}`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      if (curTab.value === 1) {
        Api.deleteArea(option)
          .then((res: any) => {
            message.success('删除成功')
            removeFeatureById(option.id)
          })
          .finally(() => {
            getAreaList()
          })
          .catch(() => {
            message.error('删除失败')
          })
      } else {
        Api.deleteStation(option)
          .then((res: any) => {
            message.success('删除成功')
            removeFeatureById(option.id)
          })
          .finally(() => {
            getAreaList()
          })
          .catch(() => {
            message.error('删除失败')
          })
      }
    },
    onNegativeClick: () => {}
  })
}
function onEditTree(option: any) {
  isEdit.value = true
  dialogVisible.value = true
  console.log(vectorSource, 'vectorSource')
  if (vectorSource) {
    vectorSource.forEachFeature((feature: any) => {
      if (curTab.value === 1) {
        feature.setStyle(
          new Style({
            fill: new Fill({
              color: 'rgba(255, 255, 255, 0.6)'
            }),
            stroke: new Stroke({
              color: '#319FD3',
              width: 1
            })
          })
        )
      }
    })
    let selectFeature = vectorSource?.getFeatureById(option.id)
    if (selectFeature) {
      if (curTab.value === 1) {
        selectFeature.setStyle(
          new Style({
            fill: new Fill({
              color: 'rgba(0, 0, 255, 0.1)'
            }),
            stroke: new Stroke({
              color: '#ff0000'
            })
          })
        )
      } else {
        map.getView().fit(selectFeature.getGeometry())
        map.getView().setZoom(8)
      }
    }
    nextTick(() => {
      addDialogRef.value.addModify(selectFeature, option)
    })
  }
}
function removeFeature(params: any, feature: any) {
  let featurePre = vectorSource?.getFeatureById(params.id)
  if (featurePre) {
    vectorSource.removeFeature(featurePre)
  }
  mapFeatures.value.splice(mapFeatures.value.indexOf(params.id), 1)
  // vectorSource.addFeature(feature)
}
onMounted(() => {
  getAreaList()
  eventBus.on('clickTree', params => {
    dialogVisible.value = false
    const json = params.locationJson
    drawPolygon(params)
  })
  eventBus.on('changeTab', val => {
    curTab.value = val
    vectorSource.refresh()
    mapFeatures.value = []
    getAreaList()
  })
})

onBeforeUnmount(() => {
  eventBus.off('clickTree')
  eventBus.off('changeTab')
})

defineExpose({
  map,
  dataListRef,
  vectorSource
})
</script>

<style scoped lang="scss">
.data-maintenance {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  position: relative;
  .data-maintenance-map {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
  }
  .maintenance-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: url(/src/assets/images/common/content-header.png) no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 9px 12px 9px 26px;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
    }
  }
  .map-container {
    flex: 1;
    position: relative;
  }
}
</style>
