import { FeatureCollection } from 'geojson'

/**
 * 去除 points 数组
 * @param geojson
 */
export function makesureNoPointList(geojson: FeatureCollection) {
  geojson.features.forEach(feature => {
    if (!feature.properties) {
      return
    }
    const points = feature.properties?.points
    if (points !== void 0 && points !== null && Array.isArray(points)) {
      Reflect.deleteProperty(feature.properties, 'points')
    }
  })
}

/**
 * 移除低值
 * @param geojson
 * @param lowValue
 */
export function removeLowerValueFeature(
  geojson: FeatureCollection,
  lowValue: number
) {
  const arr: number[] = []
  geojson.features.forEach((feature, i) => {
    if (!feature.properties) {
      return
    }
    const value = feature.properties.value
    if (typeof value === 'number') {
      if (value <= lowValue) {
        arr.push(i)
      }
    }
  })
  arr.sort((a, b) => b - a)
  arr.forEach(value => {
    Reflect.deleteProperty(geojson.features, value)
  })
}
