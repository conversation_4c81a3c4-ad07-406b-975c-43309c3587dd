/**
 * 台风实时路径信息
 */
export interface ITyphoonInfo {
  id: string
  tfbh: string
  time: string
  lng: number
  lat: number
  strong: string
  power: number
  speed: number
  moveDir: string
  moveSpeed: number
  pressure: number
  radius7: null
  radius10: null
  radius12: null
  radius7Quad: null
  radius10Quad: null
  radius12Quad: null
  ybsj: null
  tm: null
  forecast?: ITyphoonForecastInfo[]
}

/**
 * 台风预测路径信息
 */
export interface ITyphoonForecastInfo {
  tm: string
  forecastpoints: ITyphoonInfo[]
}

/**
 * 实况观测站点信息
 */
export interface IRealtimeStationItem {
  id: string
  code: string
  name: string
  stationTypeCode: string
  regionCode: string
  locationGeo: string
  locationJson: string
  createUserId: string
  createUser: null
  createTime: string
  updateUserId: string
  updateUser: string
  updateTime: string
  datumYellowWarn: number
  datumBlueWarn: number
  datumRedWarn: number
  datumOrangeWarn: number
  tideDatumYellowWarn: number
  tideDatumBlueWarn: number
  tideDatumRedWarn: number
  tideDatumOrangeWarn: number
  gaugeZeroYellowWarn: number
  gaugeZeroBlueWarn: number
  gaugeZeroRedWarn: number
  gaugeZeroOrangeWarn: number
  type: string
  relationStation: string
  enable: boolean
}

export interface IBuoyStationReq {
  buoyStationCodeList: string[]
  element: string
  endTime: string
  relationStation: string
  startTime: string
  type: string
}
