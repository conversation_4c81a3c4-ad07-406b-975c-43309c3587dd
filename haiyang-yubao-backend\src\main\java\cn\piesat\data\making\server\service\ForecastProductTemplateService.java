package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.ForecastProductTemplateDTO;
import cn.piesat.data.making.server.entity.ForecastProductTemplate;
import cn.piesat.data.making.server.vo.ForecastProductTemplateVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预报产品模板表服务接口
 *
 * <AUTHOR>
 */
public interface ForecastProductTemplateService extends IService<ForecastProductTemplate> {

    /**
     * 查询列表
     */
    List<ForecastProductTemplateVO> getList(ForecastProductTemplateDTO dto);

    /**
     * 查询详情
     */
    ForecastProductTemplateVO getInfoById(Long id);

    /**
     * 保存
     */
    Long save(ForecastProductTemplateDTO dto);

    /**
     * 上传文件
     *
     * @return
     */
    String uploadFile(MultipartFile multipartFile);

    /**
     * 下载文件
     */
    void downloadFile(Long id, HttpServletResponse response);

    /**
     * 删除
     */
    void deleteById(Long id);
}




