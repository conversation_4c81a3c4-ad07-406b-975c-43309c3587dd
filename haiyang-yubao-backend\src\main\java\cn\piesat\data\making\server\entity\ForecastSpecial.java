package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 公共服务-专项预报实体类
 *
 */
@TableName("fm_forecast_special")
public class ForecastSpecial implements Serializable {
    private static final long serialVersionUID = -1;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 发往单位名称
     */
    private String sendUnitName;
    /**
     * 数据源(grid,nmefc)
     */
    private String sourceCode;
    /**
     * 起报时间
     */
    private Date startTime;
    /**
     * 预报开始时间
     */
    private Date forecastTimeS;
    /**
     * 预报结束时间
     */
    private Date forecastTimeE;
    /**
     * 预报区域
     */
    private String forecastArea;
    /**
     * 预报区域-自定义
     */
    private String forecastAreaCustomize;
    /**
     * 生成时间
     */
    private Date createTime;
    /**
     * 生成时间（0、未开始，1、正在生成，2、生成完成）
     */
    private Integer status;
    /**
     * 报告路径
     */
    private String reportPath;
    /**
     * 要素名称
     */
    private String elementName;
    /**
     * 预报nc文件
     */
    private String forecastNcPath;
    /**
     * 生成时间
     */
    private Date modifyTime;

    /**
     * 时间类型
     * 逐小时 H 、逐天 D
     */
    private String timeType;

    /**
     * 图片路径
     */
    private String imagePath;
    /**
     * 海浪注释
     */
    private String waveAnnotation;
    /**
     * 防护措施建议
     */
    private String suggestion;
    /**
     * 牧场名称
     */
    private String ranchName;
    /**
     * 牧场内容
     */
    private String ranchValue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSendUnitName() {
        return sendUnitName;
    }

    public void setSendUnitName(String sendUnitName) {
        this.sendUnitName = sendUnitName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getForecastTimeS() {
        return forecastTimeS;
    }

    public void setForecastTimeS(Date forecastTimeS) {
        this.forecastTimeS = forecastTimeS;
    }

    public Date getForecastTimeE() {
        return forecastTimeE;
    }

    public void setForecastTimeE(Date forecastTimeE) {
        this.forecastTimeE = forecastTimeE;
    }

    public String getForecastArea() {
        return forecastArea;
    }

    public void setForecastArea(String forecastArea) {
        this.forecastArea = forecastArea;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReportPath() {
        return reportPath;
    }

    public void setReportPath(String reportPath) {
        this.reportPath = reportPath;
    }

    public String getElementName() {
        return elementName;
    }

    public void setElementName(String elementName) {
        this.elementName = elementName;
    }

    public String getForecastNcPath() {
        return forecastNcPath;
    }

    public void setForecastNcPath(String forecastNcPath) {
        this.forecastNcPath = forecastNcPath;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getForecastAreaCustomize() {
        return forecastAreaCustomize;
    }

    public void setForecastAreaCustomize(String forecastAreaCustomize) {
        this.forecastAreaCustomize = forecastAreaCustomize;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getWaveAnnotation() {
        return waveAnnotation;
    }

    public void setWaveAnnotation(String waveAnnotation) {
        this.waveAnnotation = waveAnnotation;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getRanchName() {
        return ranchName;
    }

    public void setRanchName(String ranchName) {
        this.ranchName = ranchName;
    }

    public String getRanchValue() {
        return ranchValue;
    }

    public void setRanchValue(String ranchValue) {
        this.ranchValue = ranchValue;
    }
}

