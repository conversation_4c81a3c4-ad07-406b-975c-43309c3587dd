package cn.piesat.data.making.server.service;

import cn.piesat.common.utils.PageResultBean;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.piesat.data.making.server.entity.AlarmLevel;

/**
 * 警报等级信息表(AlarmLevel)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:16
 */
public interface AlarmLevelService extends IService<AlarmLevel> {
    PageResult pageList(Integer pageNum, Integer pageSize);
}

