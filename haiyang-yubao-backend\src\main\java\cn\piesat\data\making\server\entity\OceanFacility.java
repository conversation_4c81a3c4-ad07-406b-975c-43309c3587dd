package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋设施表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_ocean_facility_b")
public class OceanFacility implements Serializable {

    private static final long serialVersionUID = -19015469330112838L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 类型
     **/
    @TableField("type")
    private String type;
    /**
     * 编码
     **/
    @TableField("code")
    private String code;
    /**
     * 纬度
     **/
    @TableField("latitude")
    private Double latitude;
    /**
     * 经度
     **/
    @TableField("longitude")
    private Double longitude;
    /**
     * 高程值
     **/
    @TableField("height")
    private Double height;
    /**
     * 类型 网箱（WX）、海上气田(HSQT)
     */
    @TableField("category")
    private String category;
}



