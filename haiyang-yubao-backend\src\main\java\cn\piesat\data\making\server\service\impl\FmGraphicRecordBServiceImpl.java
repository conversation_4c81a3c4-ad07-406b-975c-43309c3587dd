package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.dao.*;
import cn.piesat.data.making.server.dto.FmGraphicRecordBDTO;
import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.entity.FmGraphicRecordB;
import cn.piesat.data.making.server.entity.FmTyphoonB;
import cn.piesat.data.making.server.entity.FmTyphoonForecast;
import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.mapper.FmGraphicRecordBMapper;
import cn.piesat.data.making.server.mapper.FmTyphoonBMapper;
import cn.piesat.data.making.server.mapper.FmTyphoonForecastMapper;
import cn.piesat.data.making.server.mapper.FmTyphoonRealBMapper;
import cn.piesat.data.making.server.service.FmGraphicRecordBService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.vo.FmGraphicRecordBVO;
import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import cn.piesat.data.making.server.vo.FmTyphoonForecastVO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 图形记录表服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:38
 */
@Service
@Slf4j
public class FmGraphicRecordBServiceImpl extends ServiceImpl<FmGraphicRecordBDao, FmGraphicRecordB> implements FmGraphicRecordBService {

    @Resource
    private FmGraphicRecordBDao fmGraphicRecordBDao;
    @Resource
    private ForecastTaskDao forecastTaskDao;
    @Resource
    private FmTyphoonBDao fmTyphoonBDao;
    @Resource
    private FmTyphoonRealBDao fmTyphoonRealBDao;

    @Resource
    private FmTyphoonForecastDao fmTyphoonForecastDao;
    @Value("${graphicOutPutPath:}")
    private String outPutPath;


    @Override
    public PageResult<FmGraphicRecordBVO> getPage(FmGraphicRecordBDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmGraphicRecordBVO> getList(FmGraphicRecordBDTO dto) {
        LambdaQueryWrapper<FmGraphicRecordB> queryWrapper = new LambdaQueryWrapper<>();
        if (dto.getGraphicTemplateId() != null) {
            queryWrapper.eq(FmGraphicRecordB::getGraphicTemplateId, dto.getGraphicTemplateId());
        }
        if (StringUtils.isNotBlank(dto.getName())) {
            queryWrapper.eq(FmGraphicRecordB::getName, dto.getName());
        }
        queryWrapper.orderByDesc(FmGraphicRecordB::getCreateTime);
        List<FmGraphicRecordB> fmGraphicRecordBS = this.list(queryWrapper);
        return FmGraphicRecordBMapper.INSTANCE.entityListToVoList(fmGraphicRecordBS);
    }

    @Override
    public List<FmGraphicRecordBVO> getInfoList(FmGraphicRecordBDTO dto) {
        LambdaQueryWrapper<FmGraphicRecordB> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            queryWrapper.like(FmGraphicRecordB::getName, dto.getName());
        }
        queryWrapper.select(FmGraphicRecordB::getId, FmGraphicRecordB::getName);
        queryWrapper.orderByDesc(FmGraphicRecordB::getCreateTime);
        List<FmGraphicRecordB> fmGraphicRecordBS = this.list(queryWrapper);
        return FmGraphicRecordBMapper.INSTANCE.entityListToVoList(fmGraphicRecordBS);
    }

    @Override
    public FmGraphicRecordBVO getInfoById(Long id) {
        FmGraphicRecordB record = this.getById(id);
        return FmGraphicRecordBMapper.INSTANCE.entityToVo(record);
    }

    @Override
    public void save(FmGraphicRecordBDTO dto) {
        FmGraphicRecordB fmGraphicRecordB = FmGraphicRecordBMapper.INSTANCE.dtoToEntity(dto);
        //增加逻辑，在保存的时候，就将base64image转换为图片，存入数据库
        String base64Image = fmGraphicRecordB.getFileUrl().split(",")[1];
        String base64ImageColor = fmGraphicRecordB.getFileUrlColorful().split(",")[1];
        byte[] imageBytes = Base64.getDecoder().decode(base64Image);
        byte[] imageByteColor = Base64.getDecoder().decode(base64ImageColor);
        try {
            // 创建指向指定目录下输出文件的输出流
            String filepath = outPutPath + new Date().getTime() + ".png";
            FileOutputStream fos = new FileOutputStream(filepath);
            fos.write(imageBytes);
            String colorFilePath = outPutPath + new Date().getTime() + "Color.png";
            FileOutputStream fos1 = new FileOutputStream(colorFilePath);
            fos1.write(imageByteColor);
            fmGraphicRecordB.setFileUrl(filepath);
            fmGraphicRecordB.setFileUrlColorful(colorFilePath);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        fmGraphicRecordB.setCreateTime(new Date());
        fmGraphicRecordBDao.insert(fmGraphicRecordB);
    }

    @Override
    public void saveList(List<FmGraphicRecordBDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }

    @Override
    public void submit(FmGraphicRecordBDTO dto) {
        fmGraphicRecordBDao.updateCheckStatusById(dto.getId());
        FmGraphicRecordB fmGraphicRecordB = FmGraphicRecordBMapper.INSTANCE.dtoToEntity(dto);
//        String base64Image = dto.getFileUrl().split(",")[1];
//        String base64ImageColor = dto.getFileUrlColorful().split(",")[1];
//        byte[] imageBytes = Base64.getDecoder().decode(base64Image);
//        byte[] imageBytesColor = Base64.getDecoder().decode(base64ImageColor);
//        try {
//            // 创建指向指定目录下输出文件的输出流
//            String filepath = outPutPath + new Date().getTime() + ".png";
//            FileOutputStream fos = new FileOutputStream(filepath);
//            fos.write(imageBytes);
//            String colorFilePath = outPutPath + new Date().getTime() + "Color.png";
//            FileOutputStream fos1 = new FileOutputStream(colorFilePath);
//            fos1.write(imageBytesColor);
//            fmGraphicRecordB.setFileUrl(filepath);
//            fmGraphicRecordB.setFileUrlColorful(colorFilePath);
//            fos.close();
//        } catch (IOException e) {
//            System.out.println("图片转换失败：" + e.getMessage());
//            e.printStackTrace();
//        }
        fmGraphicRecordB.setChecked(true);
        fmGraphicRecordB.setUpdateTime(new Date());
        fmGraphicRecordBDao.updateById(fmGraphicRecordB);
    }

    @Override
    public List<FmTyphoonBVO> getTyphoonList(String year) {
        LambdaQueryWrapper<FmTyphoonB> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(year),FmTyphoonB::getYear,year);
        wrapper.orderByDesc(FmTyphoonB::getTfbh);
        List<FmTyphoonB> fmTyphoonBS = fmTyphoonBDao.selectList(wrapper);
        return FmTyphoonBMapper.INSTANCE.entityListToVoList(fmTyphoonBS);
    }

    @Override
    public List<FmTyphoonRealBVO> getTyphoonByCode(FmTyphoonRealBDTO fmTyphoonRealBDTO) {
        LambdaQueryWrapper<FmTyphoonRealB> queryWrapper = createQueryWrapper(fmTyphoonRealBDTO);
        List<FmTyphoonRealB> fmTyphoonRealBS = fmTyphoonRealBDao.selectList(queryWrapper);
        if (fmTyphoonRealBS == null || fmTyphoonRealBS.isEmpty()) return null;

        List<FmTyphoonRealBVO> typhoonRealBVOList = FmTyphoonRealBMapper.INSTANCE.entityListToVoList(fmTyphoonRealBS);

        //查询预报数据
        FmTyphoonRealB fmTyphoonRealB = fmTyphoonRealBS.get(fmTyphoonRealBS.size() - 1);
        String tfbh = fmTyphoonRealB.getTfbh();
        Date time = fmTyphoonRealB.getTime();
        LambdaQueryWrapper<FmTyphoonForecast> forecastLambdaQueryWrapper = new LambdaQueryWrapper<>();
        forecastLambdaQueryWrapper.eq(FmTyphoonForecast::getTfbh, tfbh).eq(FmTyphoonForecast::getTime, time);
        List<FmTyphoonForecast> fmTyphoonForecasts = fmTyphoonForecastDao.selectList(forecastLambdaQueryWrapper);
        if (fmTyphoonForecasts == null || fmTyphoonForecasts.isEmpty()) return typhoonRealBVOList;

        List<FmTyphoonForecastVO> typhoonForecastVOList = FmTyphoonForecastMapper.INSTANCE.entitiesToVOS(fmTyphoonForecasts);
        FmTyphoonRealBVO lastPoint = typhoonRealBVOList.get(typhoonRealBVOList.size() - 1);
        lastPoint.setForecast(typhoonForecastVOList);
        return typhoonRealBVOList;
    }

    @Override
    public List<FmTyphoonForecastVO> findTyphoonForecastByCode(FmTyphoonRealBDTO fmTyphoonRealBDTO) {
        LambdaQueryWrapper<FmTyphoonForecast> forecastLambdaQueryWrapper = new LambdaQueryWrapper<>();
        forecastLambdaQueryWrapper.eq(FmTyphoonForecast::getTfbh, fmTyphoonRealBDTO.getTfbh()).eq(FmTyphoonForecast::getTime, fmTyphoonRealBDTO.getTime());
        List<FmTyphoonForecast> fmTyphoonForecasts = fmTyphoonForecastDao.selectList(forecastLambdaQueryWrapper);
        if (fmTyphoonForecasts == null || fmTyphoonForecasts.isEmpty()) return null;

        List<FmTyphoonForecastVO> typhoonForecastVOList = FmTyphoonForecastMapper.INSTANCE.entitiesToVOS(fmTyphoonForecasts);
        return typhoonForecastVOList;
    }

    private LambdaQueryWrapper<FmTyphoonRealB> createQueryWrapper(FmTyphoonRealBDTO dto) {
        LambdaQueryWrapper<FmTyphoonRealB> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getTfbh())) {
            queryWrapper.eq(FmTyphoonRealB::getTfbh, dto.getTfbh());
        }
        queryWrapper.orderByAsc(FmTyphoonRealB::getTime);
        return queryWrapper;
    }
}
