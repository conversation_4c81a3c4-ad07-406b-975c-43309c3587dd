package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanSmallShallowseauoyODao;
import cn.piesat.data.making.server.entity.OceanSmallShallowseauoyO;
import cn.piesat.data.making.server.service.OceanSmallShallowseauoyOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 浅海小型浮标数据-原始数据（文件编码标识BL）服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanSmallShallowseauoyOServiceImpl extends ServiceImpl<OceanSmallShallowseauoyODao, OceanSmallShallowseauoyO>
        implements OceanSmallShallowseauoyOService {

    @Resource
    private OceanSmallShallowseauoyODao oceanSmallShallowseauoyODao;

    @Override
    public List<OceanSmallShallowseauoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanSmallShallowseauoyODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanSmallShallowseauoyODao.getMaxCreateTime();
    }
}





