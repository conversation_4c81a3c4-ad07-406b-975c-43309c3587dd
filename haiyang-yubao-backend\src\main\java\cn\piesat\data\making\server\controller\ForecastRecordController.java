package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.ForecastRecordDTO;
import cn.piesat.data.making.server.service.ForecastRecordService;
import cn.piesat.data.making.server.vo.ForecastRecordVO;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 预报记录表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forecastRecord")
public class ForecastRecordController {

    @Resource
    private ForecastRecordService forecastRecordService;

    /**
     * 保存预报记录
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报记录管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {ForecastRecordDTO.Save.class}) @RequestBody ForecastRecordDTO dto) {
        forecastRecordService.save(dto);
    }

    /**
     * 提交预报记录
     *
     * @param dto
     * @return
     */
    @PostMapping("/submit")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报记录管理", operateType = OperateType.INSERT)
    public void submit(@Validated(value = {ForecastRecordDTO.Submit.class}) @RequestBody ForecastRecordDTO dto) {
        forecastRecordService.submit(dto);
    }

    /**
     * 根据预报任务id、数据源查询预报记录
     *
     * @param taskId     预报任务id
     * @param dataSource 数据源：数值预报forecast 智能网格grid
     * @param reportTime 起报时间
     * @return
     */
    @GetMapping("/info/{taskId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报记录管理", operateType = OperateType.SELECT)
    public ForecastRecordVO getInfoByTaskId(@PathVariable("taskId") Long taskId, @RequestParam(value = "dataSource") String dataSource,
                                            @RequestParam(value = "reportTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date reportTime) {
        return forecastRecordService.getInfoByTaskId(taskId, dataSource, reportTime);
    }

    /**
     * 根据预报任务id、数据源查询预报记录
     *
     * @param taskId     预报任务id
     * @param reportTime 起报时间
     * @return
     */
    @GetMapping("/info/new/{taskId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报记录管理", operateType = OperateType.SELECT)
    public ForecastRecordVO getInfoByTaskId(@PathVariable("taskId") Long taskId,
                                            @RequestParam(value = "reportTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date reportTime) {
        return forecastRecordService.getInfoByTaskId(taskId, reportTime);
    }

    /**
     * 加载上一期
     *
     * @param taskId     预报任务id
     * @param dataSource 数据源：数值预报forecast 智能网格grid
     * @param reportTime 起报时间
     * @return
     */
    @GetMapping("/loadPre/{taskId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报记录管理", operateType = OperateType.SELECT)
    public ForecastRecordVO loadPre(@PathVariable("taskId") Long taskId, @RequestParam(value = "dataSource") String dataSource,
                                    @RequestParam(value = "reportTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date reportTime) {
        return forecastRecordService.loadPre(taskId, dataSource, reportTime);
    }

    /**
     * 重置
     *
     * @param taskId     预报任务id
     * @param dataSource 数据源：数值预报forecast 智能网格grid
     * @param reportTime 起报时间
     * @return
     */
    @GetMapping("/reset/{taskId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报记录管理", operateType = OperateType.SELECT)
    public ForecastRecordVO reset(@PathVariable("taskId") Long taskId, @RequestParam(value = "dataSource") String dataSource, @RequestParam(value =
            "reportTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date reportTime) {
        return forecastRecordService.reset(taskId, dataSource, reportTime);
    }

    /**
     *查询预报数据信息，同步到预报数据推送表中 提交预报
     * @param taskId     预报任务id
     * @param dataSource 数据源：数值预报forecast 智能网格grid
     * @param reportTime 起报时间
     * @return
     */
    @GetMapping("/syncDataToPublish/{taskId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪预报发布推送", operateType = OperateType.SELECT)
    public ForecastRecordVO syncDataToPublish(@PathVariable("taskId") Long taskId, @RequestParam(value = "dataSource") String dataSource,
                                            @RequestParam(value = "reportTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date reportTime) {
        return forecastRecordService.syncDataToPublish(taskId, dataSource, reportTime);
    }
}

