package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:38
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_tools")
public class FmGraphicTools implements Serializable {

    private static final long serialVersionUID = -30530506216290852L;


    @TableField("id")
    private Long id;
    @TableField("type")
    private String type;
    @TableField("name")
    private String name;
    @TableField("context")
    private String context;
    @TableField("url")
    private String url;
    @TableField("active_url")
    private String activeUrl;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getActiveUrl() {
        return activeUrl;
    }

    public void setActiveUrl(String activeUrl) {
        this.activeUrl = activeUrl;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
