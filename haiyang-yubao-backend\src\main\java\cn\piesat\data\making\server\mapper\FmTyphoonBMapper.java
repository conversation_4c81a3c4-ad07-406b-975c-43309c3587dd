package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmTyphoonBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonB;
import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 台风信息Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:21
 */
@Mapper(componentModel = "spring")
public interface FmTyphoonBMapper {

    FmTyphoonBMapper INSTANCE = Mappers.getMapper(FmTyphoonBMapper.class);

    /**
     * entity-->vo
     */
    FmTyphoonBVO entityToVo(FmTyphoonB entity);

    /**
     * dto-->entity
     */
    FmTyphoonB dtoToEntity(FmTyphoonBDTO dto);

    /**
     * entityList-->voList
     */
    List<FmTyphoonBVO> entityListToVoList(List<FmTyphoonB> list);

    /**
     * dtoList-->entityList
     */
    List<FmTyphoonB> dtoListToEntityList(List<FmTyphoonBDTO> dtoList);
}
