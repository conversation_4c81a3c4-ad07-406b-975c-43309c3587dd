<template>
  <div class="xqgj">
    <div class="title">
      <span>目标选择</span>
      <n-popover ref="tipPopover" trigger="click">
        <template #trigger>
          <n-icon size="14">
            <InformationCircle></InformationCircle>
          </n-icon>
        </template>
        <div>目标选择-点选：按住ctrl进行多个近岸基础单元选中操作。</div>
      </n-popover>
    </div>
    <div class="query-item">
      <div class="query-title">填充颜色：</div>
      <div class="color-picker">
        <i
          v-for="item in colorList"
          :key="item.level"
          class="level-icon"
          :class="[item.className, item.level === currentLevel ? 'active' : '']"
          @click="changeColorOnly(item)"
        ></i>
        <!-- <n-color-picker
          v-model:value="fillColor"
          :actions="['confirm']"
          class="color-item"
          :swatches="[
            '#F90102',
            '#FE7B0E',
            '#FFF300',
            '#0083FD',
            'rgba(0, 0, 0, 0)',
            '#FFFFFF'
          ]"
          @update:value="changeColor"
        >
        </n-color-picker> -->
      </div>
    </div>
    <div class="btns">
      <div
        class="my-btn"
        :class="[isSelect ? 'active' : '']"
        @click="selectFeature"
      >
        {{ isSelect ? '确认选区' : '点选' }}
      </div>
      <div class="my-btn" @click="clearFeature">清除</div>
    </div>
  </div>
  <div class="xqgj">
    <div class="title">
      <span>文本标注</span>
    </div>
    <div class="btns">
      <div
        class="my-btn"
        :class="[isTextSelect ? 'active' : '']"
        @click="textPoint"
      >
        点选
      </div>
      <div class="my-btn" @click="clearTextFeature">清除</div>
    </div>
  </div>
<!--  <div class="xqgj">-->
<!--    <div class="title">-->
<!--      <span>提示</span>-->
<!--    </div>-->
<!--    <div class="text">-->
<!--      目标选择-点选：按住ctrl进行多个近岸基础单元选中操作。-->
<!--    </div>-->
<!--  </div>-->
  <n-modal
    v-model:show="showModal"
    :close-on-esc="false"
    :mask-closable="false"
  >
    <n-card
      style="width: 300px"
      title="请输入标注内容"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div class="text-container">
        <n-input v-model:value="text" placeholder="请输入" />
        <span>厘米</span>
      </div>
      <template #footer>
        <qx-button @click="cancle">取消</qx-button>
        <qx-button @click="mark">确定</qx-button>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup>
import { onMounted, ref, inject, onUnmounted, useTemplateRef } from 'vue'
import Feature from 'ol/Feature.js'
import GeoJSON from 'ol/format/GeoJSON.js'
import { Vector as VectorSource } from 'ol/source.js'
import { Vector as VectorLayer } from 'ol/layer'
import { Select } from 'ol/interaction'
import { click, platformModifierKeyOnly } from 'ol/events/condition'
import Draw from 'ol/interaction/Draw'
import { Fill, Stroke, Style, Text, Icon, Circle } from 'ol/style'
import { unByKey } from 'ol/Observable'
import Point from 'ol/geom/Point.js'
import { QxButton } from 'src/components/QxButton'
import img from 'src/assets/images/common/aside-header.png'
import changeStore from './changeStore.js'
import Collection from 'ol/Collection'
import { InformationCircle } from '@vicons/ionicons5'
import { NPopover } from 'naive-ui'
import { useMultiSelectTip } from 'src/components/OpenlayersMap/components/XQGJ-hook/useMultiSelectTip'

// hooks
const tipPopover = ref(null)
useMultiSelectTip(tipPopover)

const getMap = inject('getMap')
const fillColor = ref('#F90102')
let isSelect = ref(false)
/** @type {VectorLayer} */
let layer = null
let vectorSource = null

let currentLevel = ref(null)
const colorList = [
  {
    level: 'I',
    color: '#F90102',
    className: 'red',
    name: '红色警报'
  },
  {
    level: 'II',
    color: '#FE7B0E',
    className: 'orange',
    name: '橙色警报'
  },
  {
    level: 'III',
    color: '#FFF300',
    className: 'yellow',
    name: '黄色警报'
  },
  {
    level: 'IV',
    color: '#0083FD',
    className: 'blue',
    name: '蓝色警报'
  },
  {
    level: 'V',
    color: '#c4c4c4',
    className: 'grey',
    name: '警报解除'
  }
]
let select = null // 区域点选
let selectedFeatures = null // 区域选择的features
/** @type {Ref<{level: string, color: string, className: string, name: string}>>} */
const colorSelect = ref(colorList[0]) // 颜色选择
currentLevel.value = colorList[0].level // 默认选择第一个颜色
/**
 * 仅仅保存修改的颜色而不产生其他副作用
 * @param obj {{level: string, color: string, className: string, name: string}}
 */
function changeColorOnly(obj) {
  colorSelect.value = obj
  currentLevel.value = obj.level
}
function changeColor(obj) {
  const level = obj.level
  const color = obj.color
  if (select) {
    getMap(map => {
      map.removeInteraction(select)
    })
  }

  currentLevel.value = level
  selectedFeatures.getArray().forEach(item => {
    layer
      .getSource()
      .getFeatures()
      .forEach(f => {
        if (item.ol_uid === f.ol_uid) {
          console.log(f)
          f.setStyle(
            new Style({
              fill: new Fill({
                color
              }),
              text: new Text({
                text: level,
                font: 'bold 20px Calibri,sans-serif',
                fill: new Fill({
                  color: 'black'
                }),
                stroke: new Stroke({
                  color: 'white',
                  width: 2
                })
              })
            })
          )
          f.setProperties({
            name: '风暴潮' + obj.name + '区域',
            category: '1',
            color: color,
            fillColor: color, // 兼容 color
            level: obj.name,
            transparency: '1',
            opacity: 100 // 兼容 transparency
          })
        }
      })
  })
  select.getFeatures().clear()
  isSelect.value = false
  getMap(map => {
    changeStore(map)
  })
}

function addSelectInteraction() {
  currentLevel.value = null
  //condition：设置此交互为点击选择，必须满足此条件
  //toggleCondition：设置当按住ctrl再点击时，可多选图层
  select = new Select({
    condition: click,
    toggleCondition: platformModifierKeyOnly
  })
  selectedFeatures = select.getFeatures()
  getMap(map => {
    map.addInteraction(select)
    select.on('select', function (e) {
      selectedFeatures = e.target.getFeatures()
      e.target
        .getFeatures()
        .getArray()
        .forEach(item => {
          // console.log(item.values_)
        })
    })
  })
}
function remvoeSelectInteraction() {
  if (select) {
    getMap(map => {
      map.removeInteraction(select)
    })
  }
  if (selectedFeatures) {
    selectedFeatures.getArray().forEach(item => {
      item.setStyle(
        new Style({
          fill: new Fill({
            color: 'rgba(0,170,235,0.1)'
          }),
          stroke: new Stroke({
            color: '#000000',
            width: 1
          })
        })
      )
      item.setProperties({
        name: '',
        category: '',
        color: 'rgba(0,170,235,0.1)',
        fillColor: 'rgba(0,170,235,0.1)',
        strokeColor: '#000000',
        strokeWith: 1,
        level: '',
        transparency: '',
        opacity: 100
      })
    })
    selectedFeatures.clear()
  }
}
function selectFeature() {
  if (isSelect.value && colorSelect.value) {
    changeColor(colorSelect.value)
    return
  }
  isSelect.value = true
  // remvoeSelectInteraction()
  addSelectInteraction()
}
function clearFeature() {
  isSelect.value = false
  currentLevel.value = null
  remvoeSelectInteraction()
  /** @type {VectorLayer} */
  layer
    .getSource()
    .getFeatures()
    .forEach(feature => {
      /** @type {Feature} */
      const _fea = feature
      const style1 = new Style({
        fill: new Fill({
          color: 'rgba(0,170,235,0.1)'
        }),
        stroke: new Stroke({
          color: '#000000',
          width: 1
        })
      })
      _fea.setStyle(style1)
    })
  getMap(map => {
    changeStore(map)
  })
}

let isTextSelect = ref(false)
let showModal = ref(false)
const text = ref('')
let draw = null
let curDrawFeature = null
// 添加文字区域点选
function addTextSelectInteraction() {
  if (draw) {
    unByKey(draw)
  }
  if (getMap) {
    getMap(map => {
      draw = map.on('click', function (evt) {
        console.log(evt)
        if (evt.dragging) {
          return
        }
        const pixel = map.getEventPixel(evt.originalEvent)
        const feature = map.forEachFeatureAtPixel(pixel, function (feature) {
          return feature
        })
        // const features = layer.getSource().getFeatures()
        if (feature) {
          const info = feature.getProperties()
          curDrawFeature = feature
          if (info) {
            showModal.value = true
          }
        }
      })
    })
  }
}

function drawCanvas(x, y, width, height, radius, strokeStyle, fillStyle, size) {
  var canvas = document.createElement('canvas')
  var ctx = canvas.getContext('2d')

  ctx.strokeStyle = strokeStyle
  ctx.fillStyle = fillStyle
  ctx.beginPath()

  // 绘制弧长、直线
  ctx.arc(x + radius, y + radius, radius, Math.PI, (Math.PI * 3) / 2, false)
  ctx.lineTo(x + width - radius, y)

  ctx.arc(
    x + width - radius,
    y + radius,
    radius,
    (Math.PI * 3) / 2,
    Math.PI * 2,
    false
  ) // 115 45 5
  ctx.lineTo(x + width, y + height - radius) // 120 65

  ctx.arc(
    x + width - radius,
    y + height - radius,
    radius,
    0,
    Math.PI / 2,
    false
  ) //115 65 5
  ctx.lineTo(x + width - 40, y + height) // 50 70
  ctx.lineTo(x + width - radius, y + height + 15) // 30 85
  ctx.lineTo(x + width - 10, y + height) // 35 70

  ctx.arc(x + radius, y + height - radius, radius, Math.PI / 2, Math.PI, false)
  ctx.lineTo(x, y + radius)

  ctx.closePath()
  ctx.fill()

  // 绘制文字
  var textX = 45
  var textY = 55
  ctx.font = 'bold 14px Arial'
  ctx.fillStyle = '#000'
  ctx.fillText(size + '厘米', textX, textY)

  // 绘制图片
  // var img = document.createElement("img");
  // ctx.drawImage(img, 0, 80);

  // 返回
  return canvas
}

function mark() {
  showModal.value = false
  const extent = curDrawFeature.getGeometry().getExtent()
  const iconFeature = new Feature({
    geometry: new Point([
      (extent[0] + extent[2]) / 2,
      (extent[1] + extent[3]) / 2
    ])
  })
  var canvas = drawCanvas(30, 30, 90, 40, 5, '#000000', '#fff', text.value)
  const iconStyle = new Style({
    image: new Icon({
      src: canvas.toDataURL(),
      imgSize: [300, 200],
      offset: [0, 0]
    })
    // text: new Text({
    //   text: text.value + '厘米',
    //   font: 'bold 20px Calibri,sans-serif',
    //   fill: new Fill({
    //     color: 'black'
    //   }),
    //   stroke: new Stroke({
    //     color: 'white',
    //     width: 2
    //   }),
    //   // backgroundFill: new Fill({      // 填充背景
    //   //   color:'rgba(255,255,255,1)',
    //   // }),
    //   // padding: [5, 5, 5, 5],
    //   offsetY: -50, // 文字偏移量
    //   offsetX: -40, // 文字偏移量
    // })
  })
  const pointStyle = new Style({
    image: new Circle({
      radius: 7,
      fill: new Fill({
        color: 'black'
      }),
      stroke: new Stroke({
        color: 'white',
        width: 2
      })
    })
  })
  iconFeature.setStyle([iconStyle])
  iconFeature.set('text', text.value)
  source.addFeature(iconFeature)
  unByKey(draw)
  text.value = ''
  isTextSelect.value = false
  getMap(map => {
    changeStore(map)
  })
}
function cancle() {
  showModal.value = false
  curDrawFeature = null
  text.value = ''
  unByKey(draw)
  isTextSelect.value = false
}
function remvoeTextSelectInteraction() {
  unByKey(draw)
}
function textPoint() {
  if (isTextSelect.value) {
    isTextSelect.value = false
    remvoeTextSelectInteraction()
    return
  }
  isTextSelect.value = !isTextSelect.value
  addTextSelectInteraction()
}
function clearTextFeature() {
  isTextSelect.value = false
  if (draw) {
    unByKey(draw)
  }
  source.refresh()
}
const source = new VectorSource()
const textLayer = new VectorLayer({
  source: source,
  zIndex: 11
})
textLayer.setProperties({
  layerType: '选取工具文字',
  canRedo: true
})
const style = new Style({
  fill: new Fill({
    color: 'rgba(0,170,235,0.1)'
  }),
  stroke: new Stroke({
    color: '#000000',
    width: 1
  })
})
const sourceVec = new VectorSource()
layer = new VectorLayer({
  source: sourceVec,
  properties: {
    name: 'xqgj'
  }
})
async function addXQLayer() {
  let json = await fetch(
    config.onlyOfficeServerUrl + '/qixiang/Data/ZPEIYQ/map/buffer.json'
  ).then(response => {
    return response.json()
  })
  const featuresJson = new GeoJSON().readFeatures(json)
  featuresJson.forEach(item => {
    item.setStyle(style)
    sourceVec.addFeature(item)
  })
}
onMounted(() => {
  getMap(async map => {
    await addXQLayer()
    layer.setProperties({
      layerType: '区域范围',
      canRedo: true
    })
    map.addLayer(layer)
    map.addLayer(textLayer)
    changeStore(map)
  })
})
onUnmounted(() => {
  getMap(map => {
    map.removeLayer(layer)
    map.removeLayer(textLayer)
    unByKey(draw)
    remvoeSelectInteraction()
  })
})
</script>

<style lang="scss" scoped>
.xqgj {
  // width: 346px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 5px 0px;
  .title {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    margin: 7px 16px;
    position: relative;
    display: flex;
    align-items: center;

    span {
      &::before {
        content: '';
        position: absolute;
        left: -10px;
        top: 4px;
        width: 4px;
        height: 8px;
        background: #567bff;
      }
    }
  }
  .btns {
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 10px;
    .my-btn {
      width: 150px;
      height: 32px;
      background: #1c81f8;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      cursor: default;
      &.active {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
      &:hover {
        background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
      }
    }
  }
  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 16px;
    .query-title {
      white-space: nowrap;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
    .query-info {
      width: 346px;
    }
    .color-picker {
      display: flex;
      .color-item {
        width: 30px;
        height: 30px;
        margin-right: 5px;
      }
    }
  }
  .text {
    margin-left: 16px;
    margin-bottom: 10px;
  }

  .level-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
    display: inline-block;
    &:nth-last-child(1) {
      margin-right: 0;
    }
    &.active {
      border: 2px solid #000000;
    }
  }
}
.text-container {
  display: flex;
  align-items: center;
  span {
    white-space: nowrap;
    margin-left: 10px;
  }
}

.red {
  background: #f90102;
}
.orange {
  background: #fe7b0e;
}
.yellow {
  background: #fff300;
}
.blue {
  background: #0083fd;
}
.grey {
  background: #c4c4c4;
}
</style>
