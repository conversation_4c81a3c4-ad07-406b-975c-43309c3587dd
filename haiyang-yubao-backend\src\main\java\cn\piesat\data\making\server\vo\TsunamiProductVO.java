package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 海啸产品表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TsunamiProductVO implements Serializable {

    private static final long serialVersionUID = 502935564971738423L;

    /**
     * id
     **/
    private Long id;
    /**
     * 名称
     **/
    private String name;
    /**
     * 文件地址
     **/
    private String fileUrl;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}



