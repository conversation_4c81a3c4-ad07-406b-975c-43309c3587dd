package cn.piesat.data.making.server.controller;

import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.service.FmTideDialyService;
import cn.piesat.data.making.server.vo.FmTideDialyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("fmTideDialy")
public class FmTideDialyController {

    @Autowired
    private FmTideDialyService fmTideDialyServiceImpl;

    /**
     * 跟据条件查询
     *
     * @param fmTideDialyVO
     * @return
     */
    @GetMapping("/queryListByParam")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "公共服务", operateType = OperateType.SELECT)
    public List<FmTideDialyVO> queryListByParam(FmTideDialyVO fmTideDialyVO){
        return fmTideDialyServiceImpl.queryListByParam(fmTideDialyVO);
    }
}
