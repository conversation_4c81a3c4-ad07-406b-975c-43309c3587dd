import axios from 'axios'

const platformApi = {
  getLayerInfo(data) {
    return axios({
      url: `/zhongtaiApi/service/api/dsjj_dlhax_2021`,
      method: 'post',
      headers: {
        appKey: data.appKey,
        appParam: data.appParam,
        appSign: data.appSign
      }
    })
  },
  getShipInfoBig() {
    return axios({
      url: `/zhongtaiShipApi/webservice/20452/service?token=eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJzdWIiOiIyMDgxfDIwNDUyfDEwLjEzMi4xMDguMTJ8fDB8bnVsbHwxfOeuoeeQhuWRmCIsImlhdCI6MTc0MDU1MTQwNSwiZXhwIjoxNzQzMTQzNDA1fQ.Yja-nSRdbGrv4mQw-PH9m9kgP-vknmFPiwas5rwJX4EzPeUeBYMj4Su6wfuX3po1eigTbQg1rGFQ0oOj9I59iw`,
      method: 'get'
    })
  },
  getShipInfoSmall(){
    return axios({
      url: `/zhongtaiShipApi/webservice/20451/service?token=eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJzdWIiOiIyMDgxfDIwNDUxfDEwLjEzMi4xMDguMTJ8fDB8bnVsbHwxfOeuoeeQhuWRmCIsImlhdCI6MTc0MDU1MTcwOSwiZXhwIjoxNzQzMTQzNzA5fQ.hG81FRHWXSH_UH79DyJFBm43VoEzQ8JIG1YBWFHCg4owMlm7mrTWbUH7h9Ru5quwKJT0xXhKr3ZoDKrZhSZTUg`,
      method: 'get'
    })
  }
}
export default platformApi
