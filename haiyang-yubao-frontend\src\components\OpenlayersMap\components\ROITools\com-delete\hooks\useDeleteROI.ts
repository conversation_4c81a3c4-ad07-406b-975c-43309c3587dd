import { useROIInject } from '../../hooks/useROIInject'
import { Select } from 'ol/interaction'
import VectorSource from 'ol/source/Vector'
import VectorLayer from 'ol/layer/Vector'
import { SimpleGeometry } from 'ol/geom'
import Popup from 'src/utils/olPopup/ol-popup'
import { onMounted, onUnmounted, Ref } from 'vue'
import { generateID } from 'src/utils/util'
import eventBus from 'src/utils/eventBus'
import { roiToolBus } from '../../hooks/types'
import { roiStyle } from '../../hooks/useVector'
import { Feature } from 'ol'
import { ActionType, type IROIOptions } from '../../hooks/useROIOptions'
import { getLandPoint } from '../../com-draw/hooks/useDrawROI'

/**
 * 落图工具 - 删除工具
 */
export function useDeleteROITools() {
  const vectorSource = useROIInject('vectorSource').injectROI()
  const vectorLayer = useROIInject('vectorLayer').injectROI()
  const intersectPointLayer = useROIInject('intersectPointLayer').injectROI()
  const roiOptions = useROIInject('roiOptions').injectROI()
  const currentAction = useROIInject('currentAction').injectROI()
  const getMap = useROIInject('getMap').injectROI()

  if (
    !vectorSource ||
    !vectorLayer ||
    !getMap ||
    !intersectPointLayer ||
    !roiOptions ||
    !currentAction
  ) {
    throw new Error('注入失败')
  }
  const { select, popup, deleteVecSource, deleteVecLayer } = createSelect({
    vectorSource,
    vectorLayer,
    roiOptions
  })

  onMounted(() => {
    eventBus.on(roiToolBus.saveModify, () => {
      saveDeleteAction({ deleteVecSource, vectorSource })
      getLandPoint({ intersectPointLayer, vectorSource })
      currentAction.value = ActionType.NONE
    })
    getMap(map => {
      intersectPointLayer.setVisible(false)
      vectorLayer.setVisible(false)
      map.addInteraction(select)
      map.addLayer(deleteVecLayer)
      map.addOverlay(popup)
    })
  })

  onUnmounted(() => {
    eventBus.off(roiToolBus.saveModify)
    getMap(map => {
      intersectPointLayer.setVisible(true)
      vectorLayer.setVisible(true)
      map.removeInteraction(select)
      map.removeLayer(deleteVecLayer)
      map.removeOverlay(popup)
    })
  })
}

function createSelect(opt: {
  vectorSource: VectorSource<SimpleGeometry>
  vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
  roiOptions: Ref<IROIOptions>
}) {
  const deleteVecSource = new VectorSource<SimpleGeometry>()
  const deleteVecLayer = new VectorLayer({
    source: deleteVecSource,
    style: roiStyle({ ...opt })
  })
  opt.vectorSource.getFeatures().forEach(feature => {
    deleteVecSource.addFeature(feature.clone())
  })

  const select = new Select({
    layers: [deleteVecLayer]
  })

  const popup = new Popup()

  select.on('select', e => {
    if (e.selected.length === 0) {
      return
    }
    const coordinate = e.mapBrowserEvent.coordinate

    const deleteBtnClicked = () => {
      e.selected.forEach(feature => {
        deleteVecSource.removeFeature(feature as Feature<SimpleGeometry>)
      })
      popup.clearContent()
      popup.hide()
    }
    const id = generateID()
    const content = () => `
      <div>
        <h3>删除选中要素？</h3>
        <button id="${id}">确认</button>
      </div>
    `
    popup.show(coordinate, content())
    const selector = document.querySelector<HTMLButtonElement>(`#${id}`)
    selector && (selector.onclick = deleteBtnClicked)
  })

  return { select, popup, deleteVecSource, deleteVecLayer }
}

function saveDeleteAction(opt: {
  vectorSource: VectorSource<SimpleGeometry>
  deleteVecSource: VectorSource<SimpleGeometry>
}) {
  opt.vectorSource.clear()
  opt.deleteVecSource.getFeatures().forEach(feature => {
    opt.vectorSource.addFeature(feature.clone())
  })
}
