package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.ForecastProductTemplateDTO;
import cn.piesat.data.making.server.dto.ForecastTemplateDTO;
import cn.piesat.data.making.server.entity.ForecastProductTemplate;
import cn.piesat.data.making.server.service.ForecastProductTemplateService;
import cn.piesat.data.making.server.vo.ForecastProductTemplateVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预报产品模板表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forecastProductTemplate")
public class ForecastProductTemplateController {

    @Resource
    private ForecastProductTemplateService forecastProductTemplateService;

    /**
     * 查询产品模板列表
     *
     * @param status   状态
     * @param fileType 文件类型
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.SELECT)
    public List<ForecastProductTemplateVO> getList(@RequestParam(required = false) Boolean status,
                                                   @RequestParam(required = false) String fileType) {
        ForecastProductTemplateDTO dto = new ForecastProductTemplateDTO();
        dto.setStatus(status);
        dto.setFileType(fileType);
        return forecastProductTemplateService.getList(dto);
    }

    /**
     * 根据产品模板id查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.SELECT)
    public ForecastProductTemplateVO getById(@PathVariable("id") Long id) {
        return forecastProductTemplateService.getInfoById(id);
    }

    /**
     * 保存产品模板
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.INSERT)
    public Long save(@Validated(value = {ForecastProductTemplateDTO.Save.class}) @RequestBody ForecastProductTemplateDTO dto) {
        return forecastProductTemplateService.save(dto);
    }

    /**
     * 根据产品模板id修改产品模板状态
     *
     * @param id
     * @param status 状态
     * @return
     */
    @PostMapping("/update/{id}/{status}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.UPDATE)
    public void update(@PathVariable Long id, @PathVariable Boolean status) {
        ForecastProductTemplate entity = new ForecastProductTemplate();
        entity.setId(id);
        entity.setStatus(status);
        forecastProductTemplateService.updateById(entity);
    }

    /**
     * 模板上传
     *
     * @param multipartFile 模板
     * @return
     **/
    @PostMapping("/upload")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.SELECT)
    public String uploadFile(@RequestParam("multipartFile") MultipartFile multipartFile) {
        return forecastProductTemplateService.uploadFile(multipartFile);
    }

    /**
     * 模板下载
     *
     * @param id
     * @return
     **/
    @GetMapping("/download/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.SELECT)
    public void downloadFile(@PathVariable("id") Long id, HttpServletResponse response) {
        forecastProductTemplateService.downloadFile(id, response);
    }

    /**
     * 根据产品模板id删除产品模板
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        forecastProductTemplateService.deleteById(id);
    }

    /**
     * 根据产品模板id修改产品模板名称
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateName")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品模板管理", operateType = OperateType.UPDATE)
    public void updateName(@RequestBody ForecastTemplateDTO dto) {
        ForecastProductTemplate entity = new ForecastProductTemplate();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        forecastProductTemplateService.updateById(entity);
    }
}

