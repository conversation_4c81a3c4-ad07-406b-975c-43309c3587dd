package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dto.AlarmProductTemplateDTO;
import cn.piesat.data.making.server.enums.AlarmTemplateCodeEnum;
import cn.piesat.data.making.server.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.piesat.data.making.server.dao.AlarmProductTemplateDao;
import cn.piesat.data.making.server.entity.AlarmProductTemplate;
import cn.piesat.data.making.server.service.AlarmProductTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 警报产品模板(AlarmProductTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:17
 */
@Service("alarmProductTemplateService")
public class AlarmProductTemplateServiceImpl extends ServiceImpl<AlarmProductTemplateDao, AlarmProductTemplate> implements AlarmProductTemplateService {

    private static Map<Integer,String> templateType= new HashMap<Integer,String>()
    {{put(1,"海浪警报制作");put(2,"海浪警报消息");put(3,"风暴潮警报制作");put(4,"风暴潮警报消息");}};

    @Override
    public PageResult pageList(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<AlarmProductTemplate> wrapper = new LambdaQueryWrapper<>();
        Page<AlarmProductTemplate> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }

    @Override
    public AlarmProductTemplate selectByCode(AlarmTemplateCodeEnum templateCode) {
        LambdaQueryWrapper<AlarmProductTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AlarmProductTemplate::getStatus,StatusEnum.OPEN.getStatus());
        wrapper.eq(AlarmProductTemplate::getTemplateCode,templateCode.getTemplateCode());
        wrapper.eq(AlarmProductTemplate::getTemplateType,templateCode.getTemplateType());
        return this.getOne(wrapper,false);
    }

    @Override
    public List<AlarmProductTemplateDTO> list(Integer status) {
        LambdaQueryWrapper<AlarmProductTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(status),AlarmProductTemplate::getStatus,status);
        List<AlarmProductTemplate> list = this.list(wrapper);
        Map<Integer, List<AlarmProductTemplate>> templateMap = list.stream().collect(Collectors.groupingBy(AlarmProductTemplate::getTemplateType));

        ArrayList<AlarmProductTemplateDTO> templateDTOS = new ArrayList<>();
        templateType.forEach((key, value) -> templateDTOS.add(new AlarmProductTemplateDTO(value, key, templateMap.get(key))));
        return templateDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setOpen(Long id) {
        AlarmProductTemplate productTemplate = this.getById(id);
        this.update(new LambdaUpdateWrapper<AlarmProductTemplate>()
                .eq(AlarmProductTemplate::getTemplateType,productTemplate.getTemplateType())
                .eq(AlarmProductTemplate::getTemplateCode,productTemplate.getTemplateCode())
                .eq(AlarmProductTemplate::getStatus, StatusEnum.OPEN.getStatus())
                .set(AlarmProductTemplate::getStatus,StatusEnum.CLOSE.getStatus()));
        productTemplate.setStatus(StatusEnum.OPEN.getStatus());
        this.updateById(productTemplate);
    }

}

