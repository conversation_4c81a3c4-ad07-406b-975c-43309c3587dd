package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.FmPushResult;
import cn.piesat.data.making.server.service.FmSenderService;
import org.springframework.stereotype.Service;

//@Service
public class FmSenderServiceImpl implements FmSenderService {

    @Override
    public void sender(FmPushResult fmPushResult) {
        if(fmPushResult!=null){

        }
    }
}
