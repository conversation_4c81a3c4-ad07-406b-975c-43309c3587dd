package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName("fm_publish_alarm")
public class FmPublishAlarm {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("time_publish")
    private Date timePublish;

    @TableField("title")
    private String title;

    @TableField("code")
    private String code;

    @TableField("level")
    private String level;

    @TableField("color")
    private String color;

    @TableField("content")
    private String content;

    @TableField("\"table\"")
    private String table;

    @TableField("img")
    private String img;

    @TableField("img_title")
    private String imgTitle;

    @TableField("warning_type")
    private Integer warningType;

    @TableField("alarm_type")
    private String alarmType;

    @TableField("time")
    private String time;

    public Long getId() {
        return id;
    }

    public FmPublishAlarm setId(Long id) {
        this.id = id;
        return this;
    }

    public Date getTimePublish() {
        return timePublish;
    }

    public FmPublishAlarm setTimePublish(Date timePublish) {
        this.timePublish = timePublish;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public FmPublishAlarm setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getCode() {
        return code;
    }

    public FmPublishAlarm setCode(String code) {
        this.code = code;
        return this;
    }

    public String getLevel() {
        return level;
    }

    public FmPublishAlarm setLevel(String level) {
        this.level = level;
        return this;
    }

    public String getColor() {
        return color;
    }

    public FmPublishAlarm setColor(String color) {
        this.color = color;
        return this;
    }

    public String getContent() {
        return content;
    }

    public FmPublishAlarm setContent(String content) {
        this.content = content;
        return this;
    }

    public String getTable() {
        return table;
    }

    public FmPublishAlarm setTable(String table) {
        this.table = table;
        return this;
    }

    public String getImg() {
        return img;
    }

    public FmPublishAlarm setImg(String img) {
        this.img = img;
        return this;
    }

    public String getImgTitle() {
        return imgTitle;
    }

    public FmPublishAlarm setImgTitle(String imgTitle) {
        this.imgTitle = imgTitle;
        return this;
    }

    public Integer getWarningType() {
        return warningType;
    }

    public FmPublishAlarm setWarningType(Integer warningType) {
        this.warningType = warningType;
        return this;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public FmPublishAlarm setAlarmType(String alarmType) {
        this.alarmType = alarmType;
        return this;
    }

    public String getTime() {
        return time;
    }

    public FmPublishAlarm setTime(String time) {
        this.time = time;
        return this;
    }
}
