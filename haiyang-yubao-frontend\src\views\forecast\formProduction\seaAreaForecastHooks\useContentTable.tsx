import { DataTableColumn, NInput, useMessage } from 'naive-ui'
import { computed, h, inject, onMounted, Ref, ref, watch } from 'vue'
import QxTableEdit from 'src/components/QxTableEdit'
import { NSelect } from 'naive-ui'
import Api from 'src/requests/forecast'
import {
  defaultC<PERSON>umn<PERSON><PERSON><PERSON>,
  swimMeanColumnRenderer,
  unionByWaveHeight,
  useTableParser
} from 'src/views/forecast/formProduction/forecastProductHooks/useTableParser'
import { DataSource as dataSource } from 'src/views/analysis/components'
import {
  bridgeKey,
  IForecastProductBridge
} from 'src/views/forecast/formProduction/forecastProductHooks/types'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import { Refresh } from '@vicons/ionicons5'
import { getWaveLevelByRange, waveLevelInfoArr } from 'src/config/waveLevel'

export type RowData = {
  swhInterval: string
  mwpMean: string
  index: string
  langjiInterval: string
}

export function useContentTable(options: {
  numberArr: Ref<{ name: string; index: number }[]>
  setParagraph: (text: string) => void
}) {
  const message = useMessage()
  const currentProduct = inject<Ref<{ id: string }>>('currentProduct')!
  const dataSource = inject<Ref<string>>('dataSource')!
  const reportTime = inject<Ref<string>>('reportTime')!
  const tabIndex = inject<Ref<number>>('tabIndex')!
  const isEdit = inject<Ref<boolean>>('isEdit')!
  const loading = inject<Ref<boolean>>('loading')!

  const contentTable = ref<RowData[]>([defaultContentRow()])
  const contentColumns = ref(useColumns(contentTable, options.numberArr))

  watch(options.numberArr, newValue => {
    console.log(newValue)
  })

  onMounted(() => {
    // 已制作加载
    if (tabIndex.value === 2) {
      loadData({
        tableData: contentTable,
        tableColumns: contentColumns,
        currentProduct,
        dataSource,
        reportTime,
        isEdit,
        loading,
        message,
        setParagraph: options.setParagraph
      })
    }
  })
  return {
    contentTable,
    contentColumns
  }
}

export function useColumns(
  customTable: Ref<RowData[]>,
  numbers: Ref<{ name: string; index: number }[]>
): DataTableColumn<RowData>[] {
  const bridge = inject<IBridge<IForecastProductBridge>>(bridgeKey)
  return [
    {
      title: '编号',
      key: '',
      render(row: RowData, index: number) {
        return h(QxTableEdit, {
          value: row.index,
          isFocus: true,
          onUpdateValue(v: any) {
            row.index = v
          }
        })
      }
    },
    {
      title: '浪高区间(m)',
      key: '',
      render(row: RowData, index: number) {
        return h(QxTableEdit, {
          value: row.swhInterval,
          isFocus: true,
          onUpdateValue(v: any) {
            row.swhInterval = v
          }
        })
      }
    },
    {
      title: '波周期',
      key: '',
      render(row: RowData, index: number) {
        return h(QxTableEdit, {
          value: row.mwpMean,
          isFocus: true,
          onUpdateValue(v: any) {
            row.mwpMean = v
          }
        })
      }
    },
    {
      title: '浪级',
      key: '',
      render(row: RowData, index: number) {
        row.langjiInterval = getWaveLevelByRange(row.swhInterval)
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>{row.langjiInterval}</span>
          </div>
        )
        // return h(QxTableEdit, {
        //   value: row.langjiInterval,
        //   isFocus: true,
        //   onUpdateValue(v: any) {
        //     row.langjiInterval = v
        //   }
        // })
      }
    },
    {
      title: '操作',
      key: '',
      render(row: RowData, index: number) {
        return [
          h(
            'span',
            {
              size: 'small',
              class: 'operate-btn',
              style: { cursor: 'pointer' },
              onClick: () => onAdd(customTable, row)
            },
            { default: () => '添加' }
          ),
          h(
            'span',
            {
              size: 'small',
              class: 'operate-btn del',
              style: { cursor: 'pointer' },
              onClick: () => onDel(customTable, row, index)
            },
            { default: () => '删除' }
          )
        ]
      }
    }
  ]
}

function defaultContentRow(): RowData {
  return {
    mwpMean: '', //波周期
    swhInterval: '', //浪高区间
    index: '', //序号
    langjiInterval: ''
  }
}

function onDel(customTable: Ref<RowData[]>, row: RowData, index: number) {
  if (customTable.value.length === 1) {
    useMessage().warning('最少保留一条')
    return false
  }
  customTable.value.splice(index, 1)
}

function onAdd(customTable: Ref<RowData[]>, row: RowData) {
  const obj: RowData = defaultContentRow()
  customTable.value.push(obj)
}

function loadData(opt: {
  tableData: Ref<any[]>
  tableColumns: Ref<DataTableColumn<RowData>[]>
  currentProduct: Ref<{ id: string }>
  dataSource: Ref<string>
  reportTime: Ref<string>
  isEdit: Ref<boolean>
  loading: Ref<boolean>
  message: ReturnType<typeof useMessage>
  setParagraph: (text: string) => void
}) {
  opt.tableData.value = []

  const parmas = {
    taskId: opt.currentProduct.value.id,
    dataSource: opt.dataSource.value,
    reportTime: opt.reportTime.value
  }

  opt.loading.value = true
  Api.forecastRecordInfoV3(parmas as any)
    .then(res => {
      opt.setParagraph((res.data as any).forecastContent)
      const data = res.data.detailList
      const dataParser = useTableParser(data)
      opt.tableData.value = unionByWaveHeight(dataParser.tableData)
      const columns = dataParser.tableColumns
      defaultColumnRenderer(columns, opt.isEdit)
      swimMeanColumnRenderer(columns, opt.isEdit)
      opt.tableColumns.value = columns
    })
    .finally(() => {
      opt.loading.value = false
    })
    .catch(err => {
      console.error(err, 'errr---')
      opt.message.error('获取数据失败')
    })
}
