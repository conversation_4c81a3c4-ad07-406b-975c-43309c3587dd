<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-22 17:36:06
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2024-11-23 20:34:27
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/building.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="building">
    <div class="text-center">
      <div class="img"></div>
      <p>功能建设中</p>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped lang="scss">
.building {
  width: 100%;
  height: 100%;
  display: flex;
  place-items: center;
  justify-content: center;
  .img {
    width: 396px;
    height: 407px;
    background: url(src/assets/images/common/building.png) no-repeat;
    background-size: 100% 100%;
  }
  p {
    font-size: 20px;
    color: #7f7f7f;
    margin-top: -20px;
  }
}
</style>