package cn.piesat.data.making.server.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 图形模板表数据库访问层
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:40
 */
public interface FmGraphicTemplateBDao extends BaseMapper<FmGraphicTemplateB> {
    @Select({
            "<script>",
            "SELECT * FROM fm_graphic_template_b",
            "<where>",
            "<if test='parentId!= null'>",
            "AND parent_id = '${parentId}'",
            "</if>",
            "<if test='status!= null'>",
            "AND status = '%${status}%'",
            "</if>",
            "</where>",
            "</script>"
    })
    List<FmGraphicTemplateB> selectByParentId(Long parentId,Boolean status);
    @Select("SELECT * FROM fm_graphic_template_b")
    List<FmGraphicTemplateB> selectAll();
    @Select("SELECT * FROM fm_graphic_template_b where parent_id is null")
    List<FmGraphicTemplateB> selectParentIsNull();

    @Update("update fm_graphic_template_b set status = 'false' where template_type = #{templateType}")
    void updateStatusFalse(String templateType);

}
