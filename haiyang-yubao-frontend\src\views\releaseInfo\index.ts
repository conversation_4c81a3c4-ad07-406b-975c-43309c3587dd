import PublishList from './publishList.vue'
import { h } from 'vue'

export { PublishList }

export const forecastColumns = [
  {
    title: '序号',
    align: 'center',
    width: 55,
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    title: '任务名称',
    key: 'taskName',
    align: 'center'
  },
  {
    title() {
      return h('div', [
        '发布数量',
        h('div', { class: 'title-status' }, '(成功|处理中|失败)')
      ])
    },
    align: 'center',
    width: 120,
    render(row: any) {
      return [
        h(
          'span',
          {
            class: 'operate-btn published'
          },
          { default: () => row.successCount }
        ),
        h(
          'span',
          {
            class: 'operate-btn wait border'
          },
          { default: () => row.pushingCount }
        ),
        h(
          'span',
          {
            class: 'operate-btn fail'
          },
          { default: () => row.failedCount }
        )
      ]
    }
  },
  {
    title: '发布时间',
    key: 'publishTime',
    align: 'center'
  }
]

export const alarmColumns = [
  {
    title: '序号',
    align: 'center',
    width: 60,
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    title: '标题',
    key: 'title',
    align: 'center'
  },
  {
    title: '发布时间',
    key: 'releaseTime',
    align: 'center'
  }
]

export const forecastInfoColumns = [
  {
    title: '序号',
    width: 60,
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    title: '名称',
    key: 'fileName',
    align: 'center'
  },
  {
    title: '格式',
    key: 'fileType',
    align: 'center'
  },
  {
    title: '发布渠道',
    key: 'pushType',
    align: 'center'
  },
  {
    title: '发布状态',
    align: 'center',
    render(row: any) {
      return h(
        'span',
        { class: 'alarm-level alarm-level-' + row.state },
        row.state === 1
          ? '成功'
          : row.state === 2
          ? '失败'
          : row.state === -1
          ? '待推送'
          : '推送中'
      )
    }
  }
]

export const alarmInfoColumns = [
  {
    title: '序号',
    width: 60,
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    title: '标题',
    key: 'title',
    align: 'center'
  },
  {
    title: '发布时间',
    key: 'releaseTime',
    align: 'center'
  },
  {
    title: '签发人',
    key: 'signUserName',
    align: 'center',
    width: 100
  },
  {
    title: '制作人',
    key: 'makeUserName',
    align: 'center',
    width: 100
  },
  {
    title: '警报内容',
    key: 'alarmContent',
    width: 300,
    align: 'center',
    ellipsis: {
      tooltip: {
        maxWidth: 300
      },
      lineClamp: 3
    }
  },
  {
    title: '短信内容',
    key: 'smsContent',
    width: 300,
    align: 'center',
    ellipsis: {
      tooltip: {
        maxWidth: 300
      },
      lineClamp: 3
    }
  }
]

export const reportColumns = [
  {
    title: '序号',
    width: 60,
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    title: '标题',
    key: 'title',
    align: 'center'
  },
  {
    title: '发布时间',
    key: 'releaseTime',
    align: 'center'
  }
]
