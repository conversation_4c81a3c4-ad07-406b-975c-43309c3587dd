package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

/**
 * 台风预报信息表
 */
@TableName("fm_typhoon_forecast")
public class FmTyphoonForecast {

    /**
     * 主键
     */
    @TableId
    private Long id;

    private String tfbh;

    private Date time;

    private String tm;

    @TableField(value = "points_json")
    private String pointsJson;


    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getPointsJson() {
        return pointsJson;
    }

    public void setPointsJson(String pointsJson) {
        this.pointsJson = pointsJson;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }
}
