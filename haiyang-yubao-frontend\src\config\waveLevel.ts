/**
 * 海浪级别信息
 */
export interface IWaveLevelInfo {
  condition: (val: number) => boolean
  name: string
  shortName: string
}
export const waveLevelInfoArr: IWaveLevelInfo[] = [
  {
    condition: val => val < 0.1,
    name: '微浪',
    shortName: '微'
  },
  {
    condition: val => val >= 0.1 && val < 0.5,
    name: '小浪',
    shortName: '小'
  },
  {
    condition: val => val >= 0.5 && val < 1.25,
    name: '轻浪',
    shortName: '轻'
  },
  {
    condition: val => val >= 1.25 && val < 2.5,
    name: '中浪',
    shortName: '中'
  },
  {
    condition: val => val >= 2.5 && val < 4,
    name: '大浪',
    shortName: '大'
  },
  {
    condition: val => val >= 4 && val < 6,
    name: '巨浪',
    shortName: '巨'
  },
  {
    condition: val => val >= 6 && val < 9,
    name: '狂浪',
    shortName: '狂浪'
  },
  {
    condition: val => val >= 9 && val < 14,
    name: '狂涛',
    shortName: '狂涛'
  },
  {
    condition: val => val >= 14,
    name: '怒涛',
    shortName: '怒涛'
  }
]

/**
 * 根据浪高查询浪级
 * @param heightStr
 */
export function getWaveLevelByHeight(heightStr: string) {
  const find = waveLevelInfoArr.find(i => i.condition(Number(heightStr)))
  return find?.name || '-'
}

/**
 * 根据浪高区间获取浪级
 * @param waveHeightRangeStr
 */
export function getWaveLevelByRange(waveHeightRangeStr: string): string {
  const result = waveHeightRangeStr.match(/\d+(\.\d+)*/g)
  if (result?.length !== 2) {
    return '-'
  }
  const firstWaveHeight = result[0]
  const lastWaveHeight = result[1]
  const firstInfo = waveLevelInfoArr.find(i =>
    i.condition(Number(firstWaveHeight))
  )
  const lastInfo = waveLevelInfoArr.find(i =>
    i.condition(Number(lastWaveHeight))
  )
  if (firstInfo && lastInfo) {
    if (firstInfo.name !== lastInfo.name) {
      return `${firstInfo.shortName}到${lastInfo.name}`
    }
    return firstInfo.name
  } else if (firstInfo && !lastInfo) {
    return firstInfo.name
  } else if (!firstInfo && lastInfo) {
    lastInfo.name
  } else {
    return '-'
  }
  return '-'
}


export interface IBoatAdviceLevelInfo {
  condition: (val: number) => boolean
  advice: string
}
export const boatAdviceLevel: IBoatAdviceLevelInfo[] = [
  {
    advice: '海况良好，适宜海上作业。',
    condition: val => val < 2
  },
  {
    advice: '小船作业注意安全。',
    condition: val => val >= 2 && val < 2.4
  },
  {
    advice: '作业船只注意避浪。',
    condition: val => val >= 2.4 && val < 3.9
  },
  {
    advice: '海况恶劣，海上作业船只及时回港避浪。',
    condition: val => val >= 3.9
  }
]

export function getBoatAdviceLevelByHeight(heightStr: string) {
  const result = heightStr.match(/\d+(\.\d+)*/g)
  if (result?.length !== 2) {
    return '-'
  }
  const firstWaveHeight = Number(result[0])
  const lastWaveHeight = Number(result[1])
  const max = Math.max(firstWaveHeight, lastWaveHeight)
  const find = boatAdviceLevel.find(i => i.condition(max))
  return find?.advice || ''
}
