package cn.piesat.data.making.server.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 站点导入模板
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class StationImport {
    /**
     * 名称
     */
    @ExcelProperty(index = 0, value = "名称")
    private String name;
    /**
     * 行政编码
     */
    @ExcelProperty(index = 1, value = "行政区划")
    private String regionCode;
    /**
     * 经度
     */
    @ExcelProperty(index = 2, value = "经度")
    private String lng;
    /**
     * 纬度
     */
    @ExcelProperty(index = 3, value = "纬度")
    private String lat;
    /**
     * 编码
     */
    @ExcelProperty(index = 4, value = "编码")
    private String code;
}
