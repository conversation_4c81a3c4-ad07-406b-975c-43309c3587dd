package cn.piesat.data.making.server.dto.dataSearch;

import javax.annotation.Nullable;
import java.util.Date;

/**
 * @program: data-manage-search
 * @description:
 * @author: bobo
 * @create: 2024-04-28 18:29
 */
public class RecordBaseResultTypeForm {
    @Nullable
    private Date beginTime;

    @Nullable
    private Date endTime;

    @Nullable
    private String producer;

    @Nullable
    private String dataAttr;

    /**
     * 0-文件索引信息对象，1-文件存储路径字符串
     */
    @Nullable
    private Integer resultType;

    /**
     * 空间查询条件
     */
    @Nullable
    private String geom;

    /**
     * 几何关系
     */
    @Nullable
    private String geomRelation;

    /**
     * 排序方向
     */
    @Nullable
    private String sort;

    /**
     * 排序字段
     */
    @Nullable
    private String orderBy;

    @Nullable
    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(@Nullable Date beginTime) {
        this.beginTime = beginTime;
    }

    @Nullable
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(@Nullable Date endTime) {
        this.endTime = endTime;
    }

    @Nullable
    public String getProducer() {
        return producer;
    }

    public void setProducer(@Nullable String producer) {
        this.producer = producer;
    }

    @Nullable
    public Integer getResultType() {
        return resultType;
    }

    public void setResultType(@Nullable Integer resultType) {
        this.resultType = resultType;
    }

    @Nullable
    public String getDataAttr() {
        return dataAttr;
    }

    public void setDataAttr(@Nullable String dataAttr) {
        this.dataAttr = dataAttr;
    }

    @Nullable
    public String getGeom() {
        return geom;
    }

    public void setGeom(@Nullable String geom) {
        this.geom = geom;
    }

    @Nullable
    public String getGeomRelation() {
        return geomRelation;
    }

    public void setGeomRelation(@Nullable String geomRelation) {
        this.geomRelation = geomRelation;
    }

    @Nullable
    public String getSort() {
        return sort;
    }

    public void setSort(@Nullable String sort) {
        this.sort = sort;
    }

    @Nullable
    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(@Nullable String orderBy) {
        this.orderBy = orderBy;
    }

    @Override
    public String toString() {
        return "RecordBaseResultTypeForm{" +
                "beginTime=" + beginTime +
                ", endTime=" + endTime +
                ", producer='" + producer + '\'' +
                ", dataAttr='" + dataAttr + '\'' +
                ", resultType=" + resultType +
                ", geom='" + geom + '\'' +
                ", geomRelation='" + geomRelation + '\'' +
                ", sort='" + sort + '\'' +
                ", orderBy='" + orderBy + '\'' +
                '}';
    }
}