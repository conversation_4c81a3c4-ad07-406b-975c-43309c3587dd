package cn.piesat.data.making.server.controller;

import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.dto.FmGraphicRecordBDTO;
import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.service.FmGraphicRecordBService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.vo.FmGraphicRecordBVO;
import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import cn.piesat.data.making.server.vo.FmTyphoonForecastVO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import freemarker.template.SimpleDate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 图形记录表控制层
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:38
 */
@RestController
@RequestMapping("fmGraphicRecordB")
public class FmGraphicRecordBController {

    @Resource
    private FmGraphicRecordBService fmGraphicRecordBService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public PageResult<FmGraphicRecordBVO> getPage(@RequestParam(required = false) Long id,
                                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        FmGraphicRecordBDTO dto = new FmGraphicRecordBDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmGraphicRecordBService.getPage(dto, pageParam);
    }

    /**
     * 根据图形模板id、图形记录名称查询列表
     *
     * @param graphicTemplateId 图形模板id
     * @param name              图形记录名称
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public List<FmGraphicRecordBVO> getList(@RequestParam(required = false) Long graphicTemplateId, @RequestParam(required = false) String name) {
        FmGraphicRecordBDTO dto = new FmGraphicRecordBDTO();
        dto.setGraphicTemplateId(graphicTemplateId);
        dto.setName(name);
        return fmGraphicRecordBService.getList(dto);
    }

    /**
     * 根据图形记录名称查询列表
     *
     * @param name 图形记录名称
     * @return
     */
    @GetMapping("/infoList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public List<FmGraphicRecordBVO> getInfoList(@RequestParam(required = false) String name) {
        FmGraphicRecordBDTO dto = new FmGraphicRecordBDTO();
        dto.setName(name);
        return fmGraphicRecordBService.getInfoList(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public FmGraphicRecordBVO getInfoById(@PathVariable Long id) {
        return fmGraphicRecordBService.getInfoById(id);
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {FmGraphicRecordBDTO.Save.class}) @RequestBody FmGraphicRecordBDTO dto) {
        fmGraphicRecordBService.save(dto);
    }

    /**
     * 提交数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/submit")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.INSERT)
    public void submit(@Validated(value = {FmGraphicRecordBDTO.Save.class}) @RequestBody FmGraphicRecordBDTO dto) {
        //todo提交逻辑需要确认 1。图片记录需不需要状态 2。预报任务中的templateID是 图形模版的id还是图形记录的id
        fmGraphicRecordBService.submit(dto);
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmGraphicRecordBService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmGraphicRecordBService.deleteByIdList(idList);
    }

    /**
     * 查询台风里列表
     *
     * @return
     */
    @GetMapping("/getTyphoonList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.DELETE)
    public List<FmTyphoonBVO> getTyphoonList(@RequestParam(required = false) String year) {
        return fmGraphicRecordBService.getTyphoonList(year);
    }

    /**
     * 根据台风编号查询台风具体信息
     *
     * @param code 台风编号
     * @return
     */
    @GetMapping("/getTyphoonByCode")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public List<FmTyphoonRealBVO> getTyphoonListByCode(@RequestParam String code) {
        FmTyphoonRealBDTO fmTyphoonRealBDTO = new FmTyphoonRealBDTO();
        fmTyphoonRealBDTO.setTfbh(code);
        return fmGraphicRecordBService.getTyphoonByCode(fmTyphoonRealBDTO);
    }

    /**
     * 根据台风编号查询台风具体信息
     *
     * @param code 台风编号
     * @return
     */
    @GetMapping("/findTyphoonForecastByCode")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public List<FmTyphoonForecastVO> findTyphoonForecastByCode(@RequestParam String code, @RequestParam String timeStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        FmTyphoonRealBDTO fmTyphoonRealBDTO = new FmTyphoonRealBDTO();
        fmTyphoonRealBDTO.setTfbh(code);
        try {
            fmTyphoonRealBDTO.setTime(sdf.parse(timeStr));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return fmGraphicRecordBService.findTyphoonForecastByCode(fmTyphoonRealBDTO);
    }
}