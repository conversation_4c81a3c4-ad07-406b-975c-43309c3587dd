<template>
  <div class="common-container forecast">
    <header-bar id="system" :name="systemName" className="system">
      <template #logo>
        <img src="src/assets/images/common/forecast.png" alt="" />
        <h2>系统管理</h2>
      </template>
    </header-bar>
    <router-view></router-view>
  </div>
</template>

<script setup lang="ts">
import { HeaderBar } from 'src/components/HeaderBar'
import { ref } from 'vue'
const systemName = ref('systemManage')
</script>

<style scoped>
</style>