import { inject } from 'vue'
import { bridgeKey, IBridgeData } from '../../alarmMakingHooks/bridge.type'
import type { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import { EAffectType } from 'src/views/alarm/components/making-hooks/enums'
import ApiForecast from 'src/requests/forecast'
import ToolApi from 'src/requests/toolRequest'
import moment from 'moment'


export function useAlarmContent() {
  const bridge = inject<IBridge<IBridgeData>>(bridgeKey)
  if (!bridge) {
    throw new Error(`无法注入${bridgeKey}`)
  }

  return {
    getAlarmContent: () => getAlarmContent(bridge),
    getCityContent: () => getCityContent(bridge),
    getSMSContent: (alarmContent: string, defenseGuardContent: string) => getSMSContent(bridge, alarmContent, defenseGuardContent)
  }
}

/**
 * 获取警报内容
 * @param bridge
 */
async function getAlarmContent(bridge: IBridge<IBridgeData>): Promise<string> {
  const { model, currentType } = bridge.collect()
  if (!model) {
    return ''
  }
  let str = ''

  if (model.alarmLevel == '5') {
    // 警报解除
    if (model.sourceType === EAffectType.COLD_AIR) {
      str += `冷空气`
    } else if (model.sourceType === EAffectType.TYPHOON) {
      const typhoonList = await ToolApi.getTyList()
      const find = typhoonList.find(i => i.tfbh === model.typhoonNo)
      str += `${model.typhoonNo}台风“${find?.name}”`
    } else if (model.sourceType === EAffectType.BOTH) {
      const typhoonList = await ToolApi.getTyList()
      const find = typhoonList.find(i => i.tfbh === model.typhoonNo)
      str += `冷空气和${model.typhoonNo}台风"${find?.name}"`
    }
    str += `已逐渐减弱，目前南海受影响海域的浪高已减小到警报发布标准以下，`
    str += `但今天白天南海大部海域仍将维持xx～xx米的xx到xx浪区，请在上述海域作业的船只注意安全。`
    return str
  }

  if (model.sourceType === EAffectType.COLD_AIR) {
    str += `受冷空气影响`
  } else if (model.sourceType === EAffectType.TYPHOON) {
    const typhoonList = await ToolApi.getTyList()
    const find = typhoonList.find(i => i.tfbh === model.typhoonNo)
    str += `受台风“${find?.name}”影响`
  } else if (model.sourceType === EAffectType.BOTH) {
    const typhoonList = await ToolApi.getTyList()
    const find = typhoonList.find(i => i.tfbh === model.typhoonNo)
    str += `受冷空气和台风"${find?.name}"的影响`
  }
  str += `，${model.alarmTime}`
  const forecastDoneArr = [
    ...await ApiForecast.queryForecastTaskList(2),
    ...await ApiForecast.queryForecastTaskList(3)
  ]
  const seaForecast = forecastDoneArr.find(i => i.name === '海区预报')
  const reportTime = await ApiForecast.getLastForecastTime({ dataSource: 'grid' })
  const res = await ApiForecast.forecastRecordInfoMade({ reportTime, taskId: seaForecast!.id })
  str += `：${res.data.forecastContent}`
  return str
}

/**
 * 生成城市内容
 * @param bridge
 */
async function getCityContent(bridge: IBridge<IBridgeData>) {
  const { model } = bridge.collect()
  if (!model) {
    return ''
  }
  let str = ''

  const forecastDoneArr = [
    ...(await ApiForecast.queryForecastTaskList(2)),
    ...(await ApiForecast.queryForecastTaskList(3))
  ]
  const cityForecast = forecastDoneArr.find(i => i.name === '近岸预报')
  const reportTime = await ApiForecast.getLastForecastTimeV2()
  const res = await ApiForecast.forecastRecordInfoMade({ reportTime, taskId: cityForecast!.id })
  str += res.data.forecastContent
  return str
}

function getSMSContent(bridge: IBridge<IBridgeData>, alarmContent: string, defenseGuardContent: string) {
  const { model } = bridge.collect()
  if (!model) {
    return ''
  }
  let str = ''
  str += `海南省海洋预报台`
  const m = moment(model.releaseTime)
  str += `${m.format('YYYY年MM月DD日HH时')}发布${model.title}`
  str += `:${alarmContent}`
  str += defenseGuardContent
  return str
}
