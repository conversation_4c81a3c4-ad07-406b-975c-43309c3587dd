package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.OceanStationDataDTO;
import cn.piesat.data.making.server.entity.OceanStation;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.OceanStationDataVO;
import cn.piesat.data.making.server.vo.TideDailyHourDataVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站表服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationService extends IService<OceanStation> {

    /**
     * 查询列表
     */
    List<StationInfo> getList(String name);

    /**
     * 查询海洋站数据
     */
    List<OceanStationDataVO> getDataList(Date startTime, Date endTime);

    /**
     * 查询海洋站小时数据
     */
    List<OceanStationDataVO> getHourDataList(Date startTime, Date endTime, List<String> oceanStationCodeList);

    /**
     * 查询海洋站分钟数据
     */
    List<OceanStationDataVO> getMinuteDataList(Date startTime, Date endTime, List<String> oceanStationCodeList);

    /**
     * 查询列表
     */
    FillMapVO getGeoRangeDataList(OceanStationDataDTO dto);

    /**
     * 查询列表
     */
    void syncOceanAndSmallBuoyData(String table, LocalDateTime startTimeL, LocalDateTime endTimeL);

    /**
     * 查询列表
     */
    void syncLargeBuoyData();

    List<TideDailyHourDataVO> getTideList(String stationNum, Date startTime, Date endTime);
}




