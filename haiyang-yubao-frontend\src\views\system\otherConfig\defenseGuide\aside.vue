<template>
  <div class="defence-guide-aside common-aside">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>防御指南列表</h3>
        <qx-button class="primary" @click="dialogVisible = true"
          >新建</qx-button
        >
      </div>

      <Tab :active="tabIndex" :tab-list="tabList" @change="changeTab" />
    </div>
    <qx-tree
      :tree-data="treeData"
      :default-props="defaultProps"
      :is-switch="isSwitch"
      ref="treeRef"
      :default-expand-all="true"
      :default-selected-keys="defaultSelectedKeys"
      :edit="true"
      :switch-disabled="true"
      :have-right-menu="true"
      :is-rename="false"
      @change-switch="changeSwitch"
      @selected="selectHandler"
      @delete-node="deleteNode"
    />
  </div>

  <qx-dialog
    title="新建指南"
    :visible="dialogVisible"
    width="345px"
    class="add-defence-guide"
    @update:visible="dialogVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="formRef"
          class="forecast-temp-form"
          :model="dialogForm"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="指南名称" path="guideName">
            <n-input
              v-model:value="dialogForm.guideName"
              placeholder="请输入"
              clearable
            />
          </n-form-item>
          <n-form-item label="指南类型" path="alarmType">
            <n-radio-group
              v-model:value="dialogForm.alarmType"
              name="radiogroup"
            >
              <n-radio :value="1">海浪</n-radio>
              <n-radio :value="2">风暴潮</n-radio>
            </n-radio-group>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="dialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onDialogSave">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { Tab } from 'src/components/Tab'
import { ref, onMounted, reactive } from 'vue'
import { QxTree } from 'src/components/QxTree'
import type { TreeOption } from 'naive-ui'
import Api from 'src/requests/forecast'
import { QxDialog } from 'src/components/QxDialog'
import { useMessage } from 'naive-ui'
import { getFirstLeafNode } from 'src/utils/util'
import { createDiscreteApi } from 'naive-ui'
const { dialog } = createDiscreteApi(['dialog'])

const emits = defineEmits(['selected'])
const message = useMessage()
const treeRef = ref()

// tab start
const tabIndex = ref<number>(3)
const tabList = ref<any[]>([
  {
    label: '全部',
    id: 3,
    status: 0
  },
  {
    label: '禁用',
    id: 1,
    status: 1
  },
  {
    label: '启用',
    id: 2,
    status: 2
  }
])

function changeTab(val: number) {
  tabIndex.value = val
  defaultSelectedKeys.value = []
  getTreeData()
}
// tab end

// tree start
const isSwitch = ref<boolean>(true)
const treeData = ref([])
const isAdd = ref(false)
const defaultProps = ref({
  key: 'id',
  label: 'guideName',
  children: 'guideList'
})

let defaultSelectedKeys = ref<any[]>([])

function getTreeData() {
  let status: number | string = ''
  if (tabIndex.value === 1) {
    status = 0
  } else if (tabIndex.value === 2) {
    status = 1
  } else {
    status = ''
  }
  Api.getAlarmDefenseGuide({ status })
    .then((res: any) => {
      if (res) {
        res.forEach((item: any, index: number) => {
          item.id = index + 1
          item.guideName = item.alarmTypeName
        })
        treeData.value = res
        if (!isAdd.value) {
          let result = getFirstLeafNode(res, 'guideList')
          console.log(result, 'result')
          if (result) {
            selectHandler('', result)
            defaultSelectedKeys.value.push(result?.id)
          }
        }
      }
    })
    .catch(e => {
      console.error(e, 'getAlarmDefenseGuide')
    })
}

function selectHandler(val: string, option: any) {
  emits('selected', val, option)
}

function changeSwitch(val: boolean, option: TreeOption) {
  let id = option?.id as string
  Api.editAlarmDefenseGuideStatus(id)
    .then((res: any) => {
      getTreeData()
      message.success('操作成功')
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '操作失败')
    })
}
// tree end

// dialog start
let dialogVisible = ref(false)
const dialogForm = reactive({
  alarmType: 1,
  guideName: '',
  status: 0
})

const rules = {
  guideName: { required: true, message: '请输入指南名称', trigger: 'blur' },
  alarmType: { required: true, message: '请选择指南类型', trigger: 'change' }
}

function onDialogSave() {
  Api.addAlarmDefenseGuide(dialogForm)
    .then((res: any) => {
      message.success('操作成功')
      dialogVisible.value = false
      defaultSelectedKeys.value = [res?.id]
      getTreeData()
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// dialog end

function deleteNode(option: any) {
  console.log(option, 'options---')
  dialog.warning({
    title: '提示',
    content: `是否删除${option.guideName}`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      Api.delAlarmDefenseGuide(option.id)
        .then((res: any) => {
          console.log(res)
          message.success('删除成功')
          getTreeData()
          treeRef.value.hiddenRightMenu()
        })
        .catch(() => {})
    },
    onNegativeClick: () => {
      treeRef.value.hiddenRightMenu()
    }
  })
}
onMounted(() => {
  getTreeData()
})
</script>

<style scoped lang="scss">
.add-defence-guide {
  .form-container {
    box-sizing: border-box;
    padding: 14px 27px 0;
  }
  .forecast-temp-form {
    padding: 0 !important;
  }
  .btns {
    box-sizing: border-box;
    padding: 10px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
</style>
