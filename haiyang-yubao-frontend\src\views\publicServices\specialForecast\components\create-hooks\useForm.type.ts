export interface Form {
  productName: string
  sendUnitName: string
  sourceCode: string
  elementName: string | null | string[]
  forecastTimeS: string | null
  forecastTimeE: string | null
  startTime: string | null
  forecastArea: any
  forecastAreaCustomize: any[]
  forecastNcPath: ForecastNcPathItem[]
  timeType: string
  imagePath: string
  driftingImagePath: string
}

export interface ForecastNcPathItem {
  ncPath: string
  product: {
    productId: string
    productName: string
  }[]
}
