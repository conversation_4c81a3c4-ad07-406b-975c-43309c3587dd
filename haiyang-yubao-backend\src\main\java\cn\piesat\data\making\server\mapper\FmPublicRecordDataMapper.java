package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmPublicRecordData;
import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmPublicRecordDataMapper {

    FmPublicRecordDataMapper INSTANCE = Mappers.getMapper(FmPublicRecordDataMapper.class);

    List<FmPublicRecordDataVO> entityListToVoList(List<FmPublicRecordData> list);

    List<FmPublicRecordData> voListToEntityList(List<FmPublicRecordDataVO> list);
}
