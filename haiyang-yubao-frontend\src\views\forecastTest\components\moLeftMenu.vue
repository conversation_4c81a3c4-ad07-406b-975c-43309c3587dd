<template>
  <div class="mo-left-menu">
    <div class="mo-left-menu-list">
      <div
        v-for="item in props.list"
        :key="item.value"
        class="mo-left-menu-item"
        :class="{ active: props.value === item.productId }"
        @click="handleClickChange(item)"
      >
        <span class="mo-left-menu-item-text">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  value: {
    type: String,
    default: ''
  },
  list: {
    type: Array<any>,
    default: () => {
      return []
    }
  }
})
const emit = defineEmits(['click'])
const handleClickChange = (item: any) => {
  emit('click', item)
}
</script>

<style scoped lang="scss">
.mo-left-menu {
  width: 270px;
  height: 100%;
  margin-right: 20px;
  background: #ffffff;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px 8px 8px 8px;
  padding-top: 26px;
  .mo-left-menu-item {
    font-weight: 400;
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
    margin-bottom: 18px;
    padding-left: 47px;
    &.active {
      color: #0d53e1;
    }
  }
}
</style>
