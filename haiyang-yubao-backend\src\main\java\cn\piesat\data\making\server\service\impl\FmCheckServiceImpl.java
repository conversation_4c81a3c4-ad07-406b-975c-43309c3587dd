package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dto.dataSearch.RecordBaseResultTypeForm;
import cn.piesat.data.making.server.enums.CheckEnum;
import cn.piesat.data.making.server.fegin.DataManageSearchFegin;
import cn.piesat.data.making.server.service.FmCheckService;
import cn.piesat.data.making.server.service.FmGraphicRecordBService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service("fmCheckService")
public class FmCheckServiceImpl implements FmCheckService {
    @Resource
    private DataManageSearchFegin dataManageSearchFegin;

    @Override
    public Object selectByProductIdAndTimeRange(String type, Date startTime, Date endTime) {
        String productId = type +"_OTHER_JPG";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        RecordBaseResultTypeForm recordBaseResultTypeForm = new RecordBaseResultTypeForm();
        recordBaseResultTypeForm.setBeginTime(startTime);
        recordBaseResultTypeForm.setEndTime(endTime);
        Object forecast = dataManageSearchFegin.queryByProductIdAndTimeRange("forecast", productId,recordBaseResultTypeForm );
        return forecast;
    }
}
