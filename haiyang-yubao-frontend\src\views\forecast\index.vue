<template>
  <div class="common-container forecast">
    <header-bar id="system" :name="systemName" class-name="system">
      <template #logo>
        <img src="src/assets/images/common/forecast.png" alt="" />
        <h2>预报警报制作发布系统</h2>
      </template>
    </header-bar>
    <router-view></router-view>
  </div>
</template>

<script setup lang="ts">
import { HeaderBar } from 'src/components/HeaderBar'
import { ref } from 'vue'
const systemName = ref('forecast')
</script>

<style scoped lang="scss">
.forecast {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  & > div:not(.header-bar) {
    flex: 1;
  }
}
</style>
