package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourBpODao;
import cn.piesat.data.making.server.entity.OceanStationHourBpO;
import cn.piesat.data.making.server.service.OceanStationHourBpOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-气压-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourBpOServiceImpl extends ServiceImpl<OceanStationHourBpODao, OceanStationHourBpO>
        implements OceanStationHourBpOService {

    @Resource
    private OceanStationHourBpODao oceanStationHourBpODao;

    @Override
    public List<OceanStationHourBpO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourBpODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourBpODao.getMaxCreateTime();
    }
}





