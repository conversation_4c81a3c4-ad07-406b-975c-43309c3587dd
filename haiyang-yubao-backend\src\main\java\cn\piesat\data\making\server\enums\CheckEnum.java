package cn.piesat.data.making.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CheckEnum {

    /**
     * 风暴潮警报检验
     */
    TIDE_ALA("TIDE_ALA_OTHER_JPG"),
    /**
     * 海浪警报检验
     */
    WAVE_ALA("WAVE_ALA_OTHER_JPG"),
    /**
     * 海温预报检验
     */
    TEMP_FCT("TEMP_FCT_OTHER_JPG"),
    /**
     * 海浪预报检验
     */
    WAVE_FCT("WAVE_FCT_OTHER_JPG"),
    /**
     * 海风预报检验
     */
    WIND_FCT("WIND_FCT_OTHER_JPG");

    private String value;

    private final String abbreviation;

    CheckEnum(String abbreviation) {
        this.abbreviation = abbreviation;
    }

    public static CheckEnum fromAbbreviation(String abbreviation) {
        for (CheckEnum weekday : CheckEnum.values()) {
            if (weekday.abbreviation.equals(abbreviation)) {
                return weekday;
            }
        }
        throw new IllegalArgumentException("无效的星期缩写");
    }
}
