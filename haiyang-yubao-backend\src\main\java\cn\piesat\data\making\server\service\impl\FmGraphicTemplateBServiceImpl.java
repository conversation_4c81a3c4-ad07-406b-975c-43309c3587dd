package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmGraphicTemplateToolsDao;
import cn.piesat.data.making.server.dao.FmGraphicTmplateSignDao;
import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.dto.FmGraphicTmplateSignDTO;
import cn.piesat.data.making.server.entity.Area;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import cn.piesat.data.making.server.dto.FmGraphicTemplateBDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateTools;
import cn.piesat.data.making.server.entity.FmGraphicTmplateSign;
import cn.piesat.data.making.server.mapper.FmGraphicTemplateToolsMapper;
import cn.piesat.data.making.server.mapper.FmGraphicTmplateSignMapper;
import cn.piesat.data.making.server.vo.FmGraphicTemplateBVO;
import cn.piesat.data.making.server.dao.FmGraphicTemplateBDao;
import cn.piesat.data.making.server.service.FmGraphicTemplateBService;
import cn.piesat.data.making.server.mapper.FmGraphicTemplateBMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.apache.commons.io.FileUtils;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * 图形模板表服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:40
 */
@Service
@Slf4j
public class FmGraphicTemplateBServiceImpl extends ServiceImpl<FmGraphicTemplateBDao, FmGraphicTemplateB> implements FmGraphicTemplateBService {

    @Resource
    private FmGraphicTemplateBDao fmGraphicTemplateBDao;
    @Resource
    private FmGraphicTmplateSignDao fmGraphicTmplateSignDao;
    @Resource
    private FmGraphicTemplateToolsDao fmGraphicTemplateToolsDao;
    @Value(("${graphicUploadUrl}"))
    private String baseFilePath;

    @Value(("${piesat.serverId}"))
    private String serverIp;


    @Override
    public PageResult<FmGraphicTemplateBVO> getPage(FmGraphicTemplateBDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmGraphicTemplateBVO> getList(FmGraphicTemplateBDTO dto) {
        return null;
    }

    @Override
    public List<FmGraphicTemplateBVO> getListParent(FmGraphicTemplateBDTO dto) {
        List<FmGraphicTemplateB> fmGraphicTemplateBS = fmGraphicTemplateBDao.selectParentIsNull();
        return FmGraphicTemplateBMapper.INSTANCE.entityListToVoList(fmGraphicTemplateBS);
    }

    @Override
    public List<FmGraphicTemplateB> getListAll(FmGraphicTemplateBDTO dto) {
        LambdaQueryWrapper<FmGraphicTemplateB> queryWrapper = createQueryWrapper(dto);
        List<FmGraphicTemplateB> fmGraphicTemplateBS = fmGraphicTemplateBDao.selectList(queryWrapper);
        List<FmGraphicTemplateB> result = new ArrayList<>();
        return fmGraphicTemplateBS;
    }

    @Override
    public FmGraphicTemplateBVO getById(Long id) {
        FmGraphicTemplateB fmGraphicTemplateB = fmGraphicTemplateBDao.selectById(id);
        List<FmGraphicTmplateSign> fmGraphicTmplateSigns = fmGraphicTmplateSignDao.selectByTemplateId(id);
        List<FmGraphicTemplateTools> fmGraphicTemplateTools = fmGraphicTemplateToolsDao.selectByTemplateId(id);
        fmGraphicTemplateB.setTools(fmGraphicTemplateTools);
        fmGraphicTemplateB.setSign(fmGraphicTmplateSigns);
        return FmGraphicTemplateBMapper.INSTANCE.entityToVo(fmGraphicTemplateB);
    }

    @Override
    public List<FmGraphicTemplateBVO> getByParentId(Long parentId) {
        List<FmGraphicTemplateB> fmGraphicTemplateB = fmGraphicTemplateBDao.selectByParentId(parentId,true);
        List<FmGraphicTemplateB> result = new ArrayList<>();
        for (FmGraphicTemplateB graphicTemplateB : fmGraphicTemplateB) {
            List<FmGraphicTmplateSign> fmGraphicTmplateSigns = fmGraphicTmplateSignDao.selectByTemplateId(graphicTemplateB.getId());
            graphicTemplateB.setSign(fmGraphicTmplateSigns);
            List<FmGraphicTemplateTools> fmGraphicTemplateTools = fmGraphicTemplateToolsDao.selectByTemplateId(graphicTemplateB.getId());
            graphicTemplateB.setTools(fmGraphicTemplateTools);
            result.add(graphicTemplateB);
        }
        return FmGraphicTemplateBMapper.INSTANCE.entityListToVoList(result);
    }

    @Override
    public void save(FmGraphicTemplateBDTO dto) {
        FmGraphicTemplateB fmGraphicTemplateB = FmGraphicTemplateBMapper.INSTANCE.dtoToEntity(dto);
        //获取工具和标签
        List<FmGraphicTmplateSignDTO> sign = dto.getSign();
        List<FmGraphicTemplateToolsDTO> tools = dto.getTools();
        List<FmGraphicTmplateSign> fmGraphicTmplateSigns = FmGraphicTmplateSignMapper.INSTANCE.dtoListToEntityList(sign);
        List<FmGraphicTemplateTools> fmGraphicTemplateTools = FmGraphicTemplateToolsMapper.INSTANCE.dtoListToEntityList(tools);
        //保存模版
        int insert = fmGraphicTemplateBDao.insert(fmGraphicTemplateB);

        //保存标签
        if(fmGraphicTmplateSigns != null){
            for (FmGraphicTmplateSign fmGraphicTmplateSign : fmGraphicTmplateSigns) {
                fmGraphicTmplateSign.setTemplateId((long)insert);
                fmGraphicTmplateSignDao.insert(fmGraphicTmplateSign);
            }
        }
        //保存工具
        if(fmGraphicTemplateTools != null){
            for (FmGraphicTemplateTools tool : fmGraphicTemplateTools) {
                tool.setTemplateId((long)insert);
                fmGraphicTemplateToolsDao.insert(tool);
            }
        }
    }

    @Override
    public void saveList(List<FmGraphicTemplateBDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {
        fmGraphicTemplateBDao.deleteById(id);
    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }

    @Override
    public String uploadImageBase(String base64ImageData) {
        try{
            byte[] imageBytes = Base64.getDecoder().decode(base64ImageData);
            Date date = new Date();
            String fileName = date.getTime() + ".png";

            FileUtils.writeByteArrayToFile(new File(baseFilePath+fileName),imageBytes);
            return baseFilePath+fileName;
        }catch(Exception e){
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadImageFile(MultipartFile file) {
        try{
            Date date = new Date();
            String fileName = date.getTime() + ".png";
            File dest = new File(baseFilePath + fileName);
            file.transferTo(dest);
            //return "http://**********"+dest.getAbsolutePath();
            return serverIp+dest.getAbsolutePath();
        }catch(Exception e){
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void updateStatus(FmGraphicTemplateBDTO dto) {
        FmGraphicTemplateB fmGraphicTemplateB = FmGraphicTemplateBMapper.INSTANCE.dtoToEntity(dto);
        if(fmGraphicTemplateB.getStatus().equals(false)){
            fmGraphicTemplateBDao.updateById(fmGraphicTemplateB);
        }else{
            fmGraphicTemplateBDao.updateStatusFalse(fmGraphicTemplateB.getTemplateType());
            fmGraphicTemplateBDao.updateById(fmGraphicTemplateB);
        }
    }

    @Override
    public void updateName(FmGraphicTemplateBDTO dto) {
        FmGraphicTemplateB fmGraphicTemplateB = fmGraphicTemplateBDao.selectById(dto.getId());
        fmGraphicTemplateB.setName(dto.getName());
        fmGraphicTemplateBDao.updateById(fmGraphicTemplateB);
    }

    private LambdaQueryWrapper<FmGraphicTemplateB> createQueryWrapper(FmGraphicTemplateBDTO dto) {
        LambdaQueryWrapper<FmGraphicTemplateB> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getProductType())) {
            queryWrapper.eq(FmGraphicTemplateB::getProductType, dto.getProductType());
        }
        if (dto.getStatus() != null) {
            queryWrapper.eq(FmGraphicTemplateB::getStatus, dto.getStatus());
        }
        if (dto.getTemplateType() != null) {
            queryWrapper.eq(FmGraphicTemplateB::getTemplateType, dto.getTemplateType());
        }
        return queryWrapper;
    }
}
