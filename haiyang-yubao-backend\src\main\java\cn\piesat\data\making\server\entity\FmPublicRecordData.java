package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName("fm_public_record_data_b")
public class FmPublicRecordData {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("record_id")
    private Long recordId;

    @TableField("tide_time")
    private Date tideTime;

    @TableField("height")
    private Integer height;

    @TableField("type")
    private Integer type;

    @TableField("station_name")
    private String stationName;

    @TableField("station_id")
    private Long stationId;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("datum")
    private String datum;

    @TableField("warn_height")
    private Integer warnHeight;

    @TableField("level")
    private Integer level;

    @TableField("datum_blue_dif")
    private Integer datumBlueDif;

    @TableField("datum_blue_warn")
    private Integer datumBlueWarn;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Date getTideTime() {
        return tideTime;
    }

    public void setTideTime(Date tideTime) {
        this.tideTime = tideTime;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDatum() {
        return datum;
    }

    public void setDatum(String datum) {
        this.datum = datum;
    }

    public Integer getWarnHeight() {
        return warnHeight;
    }

    public void setWarnHeight(Integer warnHeight) {
        this.warnHeight = warnHeight;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getDatumBlueDif() {
        return datumBlueDif;
    }

    public void setDatumBlueDif(Integer datumBlueDif) {
        this.datumBlueDif = datumBlueDif;
    }

    public Integer getDatumBlueWarn() {
        return datumBlueWarn;
    }

    public void setDatumBlueWarn(Integer datumBlueWarn) {
        this.datumBlueWarn = datumBlueWarn;
    }
}
