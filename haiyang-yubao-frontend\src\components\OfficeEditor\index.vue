<!--
 * @Description:
 * @Author: dingmiao
 * @Date: 2024-08-08 13:56:40
 * @FilePath: \hainan-jianzai-web\src\components\OfficeEditor\index.vue
 * @LastEditTime: 2024-11-01 14:11:36
-->
<template>
  <div class="tinymceEditorClass">
    <div class="editor">
      <div id="onlyofficeId"></div>
    </div>
    <!-- <div class="btns">
        <el-button class="close-btn-el" @click="close">关闭</el-button>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import CryptoJS from 'crypto-js'
import { ref, onMounted, watch, nextTick } from 'vue'
let docEditor: any = null
let config: any = {}
let eventtype = ref('')
const emits = defineEmits(['close', 'key'])
const props = defineProps({
  editType: Boolean, //是否是编辑状态
  url: {
    //文档地址
    type: String,
    default: ''
  },
  documentType: {
    //文档格式(word,cell(excel),slide(ppt),pbf)
    type: String,
    default: 'word'
  },
  title: {
    //文档标题
    type: String,
    default: ''
  },
  callbackUrl: {
    //回调函数地址
    type: String,
    default: ''
  },
  forcesave: {
    type: Boolean,
    default: true
  }
})
//创建编辑器
function createOffice() {
  config = {
    document: {
      key: 'ma0w8wjNo1FjA1RLDap2UkRo7EBjPPuDR9ma3QGLS31EwA0J1rhQz973REBXsBmaD36v6vFDAdMy1w2z', //唯一标识
      title: props.title,
      fileType: 'docx',
      //"storage": "/data/template/jinkong_tendays_template.docx",
      permissions: {
        copy: true,
        download: true,
        edit: true,
        print: true
      },
      url: props.url
    },
    width: '100%',
    height: '100%',
    documentType: props.documentType, //设置类型
    editorConfig: {
      url: props.url,
      lang: 'zh-CN',
      mode: 'edit',
      callbackUrl: props.callbackUrl, // 回调路径跟服务端不一样会报错
      customization: {
        //"canRequestUsers": false,
        anonymous: {
          request: false,
          label: 'Guest'
        },
        features: {
          roles: false,
          spellcheck: false
        },
        autosave: false,
        forcesave: props.forcesave
      }
      // "customization": {
      //     "chat": true,
      //     "commentAuthorOnly": false,
      //     "comments": false,
      //     "compactHeader": false,
      //     "compactToolbar": false,
      //     "feedback": {
      //         "visible": true
      //     },
      //     forcesave: true,
      //     autosave: false,
      //     "goback": true,
      //     "help": false,
      //     "hideRightMenu": false,
      //     "showReviewChanges": false,
      //     "toolbarNoTabs": false,
      //     "zoom": 100,
      //     "about": true
      // },
    },
    //事件
    events: {
      onRequestInsertImage: onRequestInsertImage //插入图片
    }
  }
  if (props.url != '') {
    if (props.title == '') {
      //如果没有标题
      let fileName = props.url.split('/')[props.url.split('/').length - 1]
      config.document.title = fileName
    }
  }
  // let key = this.md5HexHash(fileName);
  let key = (
    new Date().getTime() + Math.floor(Math.random() * 10000)
  ).toString()
  config.document.key = key
  emits('key', key)
  docEditor = window.DocsAPI.DocEditor('onlyofficeId', config)
}
//关闭编辑器
// close(){
//     this.docEditor.destroyEditor()
//     this.$emit('close',true)
// },
//插入图片
function onRequestInsertImage(event: any) {
  eventtype.value = event.data.c
  // props.dialogvisible = true
  // getFileData()
}
function select(row: any) {
  docEditor.insertImage({
    c: eventtype,
    images: [
      {
        fileType: 'png',
        url: process.env.VUE_APP_BASE_API_URL + row.resultThemeUrl
      }
    ],
    token: ''
  })
  // props.dialogvisible = false
}
//另存为
function md5HexHash(input: any) {
  // 计算 MD5
  const hash = CryptoJS.MD5(input)
  // 输出十六进制字符串
  return hash.toString(CryptoJS.enc.Hex)
}
//取消
function cancel() {
  emits('close', true)
}

onMounted(() => {
  try{
    createOffice()
    console.log('执行了')
  }catch(e){
    console.error(e)
  }
})

watch(
  () => props.url,
  val => {
    console.log(props.url, 'url=-----')
    if (val != '') {
      docEditor.destroyEditor()
      nextTick(() => {
        createOffice()
      })
    }
  }
)
</script>

<style lang="scss">
.tinymceEditorClass {
  height: 100%;

  .editor {
    height: 100%;
    // height: calc(100% - 42px);
  }

  .btns {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .close-btn-el {
      width: 56px;
      height: 32px;
      padding: 0px !important;
      background: url('@/assets/img/productReport/cancle.png') no-repeat;
      background-size: 100% 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff !important;
    }
  }
}
</style>
