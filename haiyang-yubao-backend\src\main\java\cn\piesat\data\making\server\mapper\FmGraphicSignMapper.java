package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicSignDTO;
import cn.piesat.data.making.server.entity.FmGraphicSign;
import cn.piesat.data.making.server.vo.FmGraphicSignVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:38
 */
@Mapper(componentModel = "spring")
public interface FmGraphicSignMapper {

    FmGraphicSignMapper INSTANCE = Mappers.getMapper(FmGraphicSignMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicSignVO entityToVo(FmGraphicSign entity);

    /**
     * dto-->entity
     */
    FmGraphicSign dtoToEntity(FmGraphicSignDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicSignVO> entityListToVoList(List<FmGraphicSign> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicSign> dtoListToEntityList(List<FmGraphicSignDTO> dtoList);
}
