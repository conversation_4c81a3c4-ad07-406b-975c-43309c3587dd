package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;

import java.util.List;

public interface FmPublicRecordDataService {

    List<FmPublicRecordDataVO> getListByRecordId(Long resultId);

    void saveList(List<FmPublicRecordDataVO> list);

    void updateList(List<FmPublicRecordDataVO> list);

    void delete(Long id);

    void deleteAll(Long resultId);
}
