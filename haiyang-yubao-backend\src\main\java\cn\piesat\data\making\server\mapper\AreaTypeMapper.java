package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.AreaTypeDTO;
import cn.piesat.data.making.server.entity.AreaType;
import cn.piesat.data.making.server.vo.AreaTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AreaTypeMapper {

    AreaTypeMapper INSTANCE = Mappers.getMapper(AreaTypeMapper.class);

    /**
     * entity-->vo
     */
    AreaTypeVO entityToVo(AreaType entity);

    /**
     * dto-->entity
     */
    AreaType dtoToEntity(AreaTypeDTO dto);

    /**
     * entityList-->voList
     */
    List<AreaTypeVO> entityListToVoList(List<AreaType> list);

    /**
     * dtoList-->entityList
     */
    List<AreaType> dtoListToEntityList(List<AreaTypeDTO> dtoList);
}
