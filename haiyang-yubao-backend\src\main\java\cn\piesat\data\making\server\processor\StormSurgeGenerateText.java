package cn.piesat.data.making.server.processor;

import cn.piesat.data.making.server.dto.GenerateWordDTO;
import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;

import java.util.List;

public class StormSurgeGenerateText extends GenerateWordDTO {
    /**
     * 预警等级id
     */
    private Long alarmLevelId;
    /**
     * 预报增水量
     */
    private String forecastSurge;
    /**
     * 台站预警信息
     */
    private List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> stationWarning;

    public String getForecastSurge() {
        return forecastSurge;
    }

    public void setForecastSurge(String forecastSurge) {
        this.forecastSurge = forecastSurge;
    }

    public List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> getStationWarning() {
        return stationWarning;
    }

    public void setStationWarning(List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> stationWarning) {
        this.stationWarning = stationWarning;
    }

    public Long getAlarmLevelId() {
        return alarmLevelId;
    }

    public void setAlarmLevelId(Long alarmLevelId) {
        this.alarmLevelId = alarmLevelId;
    }
}
