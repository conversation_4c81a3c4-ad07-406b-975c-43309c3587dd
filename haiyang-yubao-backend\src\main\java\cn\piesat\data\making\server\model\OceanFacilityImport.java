package cn.piesat.data.making.server.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 海洋设施导入模板
 *
 * <AUTHOR>
 */
@Data
public class OceanFacilityImport {

    /**
     * 类型
     */
    @ExcelProperty(index = 0, value = "类型")
    private String type;
    /**
     * 编码
     **/
    @ExcelProperty(index = 1, value = "编码")
    private String code;
    /**
     * 纬度
     **/
    @ExcelProperty(index = 5, value = "纬度")
    private Double latitude;
    /**
     * 经度
     **/
    @ExcelProperty(index = 4, value = "经度")
    private Double longitude;
    /**
     * 高程值
     **/
    @ExcelProperty(index = 6, value = "高程值")
    private Double height;
}



