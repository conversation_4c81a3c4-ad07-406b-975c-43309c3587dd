import getAxios from '../utils/axios'
import { IRegionData, IRegionTree } from 'src/requests/alarm.type'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)
const axiosInstanceMicroblog = getAxios('')
const productUrl = import.meta.env.VITE_PRODUCT_BASE_URL
const axiosProductInstance = getAxios(productUrl)
const Alarm: any = {
  /**
   * @abstract 获取防御指南
   * @param type
   * @returns
   */
  getAlarmDefenseGuide(type: any) {
    return axiosInstance({
      url: `/alarmDefenseGuide/selectByType/${type}`,
      method: 'GET'
    })
  },
  deleteStormSurge(data: any) {
    return axiosInstance({
      url: `/stormSurgeAlarm/delete`,
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 发布风暴潮警报
   * @param id 警报id
   * @returns
   */
  releaseStormSurge(id: any) {
    return axiosInstance({
      url: `/stormSurgeAlarm/release/${id}`,
      method: 'POST'
    })
  },
  /**
   * 推送风暴潮
   * @param id
   */
  pushStormSurge(id: any) {
    return axiosInstance({
      url: `/stormSurgeAlarm/pushSelectOne/${id}`,
      method: 'GET'
    })
  },
  /**
   * @abstract 新建海浪警报
   * @param data
   * @returns
   */
  createSeaWave(data: any) {
    return axiosInstance({
      url: '/seaWaveAlarm/save',
      method: 'POST',
      data
    })
  },
  /**
   * 提交海浪警报
   * @param id
   * @param data
   * @returns
   */
  publishSeaWave(id: string) {
    return axiosInstance({
      url: `/seaWaveAlarm/release/${id}`,
      method: 'POST'
    })
  },
  /**
   * 推送海浪警报
   * @param id
   */
  pushSeaWave(id: string) {
    return axiosInstance({
      url: `/seaWaveAlarm/pushSelectOne/${id}`,
      method: 'GET'
    })
  },
  /**
   * @abstract 更新风暴潮警报
   * @param data
   * @returns
   */
  updateStormSurge(data: any) {
    return axiosInstance({
      url: '/stormSurgeAlarm/update',
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 新建风暴潮警报
   * @param data
   * @returns
   */
  createStormSurge(data: any) {
    return axiosInstance({
      url: '/stormSurgeAlarm/save',
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 获取风暴潮警报列表
   * @returns
   */
  getStormSurge(params: any) {
    return axiosInstance({
      url: '/stormSurgeAlarm/pageList',
      method: 'GET',
      params
    })
  },
  /**
   * @abstract 生成文字
   * @param data
   * @returns
   */
  generateText(data: any) {
    return axiosInstance({
      url: '/seaWaveAlarm/generateText',
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 获取nc文件列表
   * @param params
   * @returns
   */
  getNcFileList(params: any) {
    return axiosInstance({
      url: '/fmForecastProductData/waveStartTimeList',
      method: 'GET',
      params
    })
  },
  /**
   * @abstract 更新警报
   * @param data
   * @returns
   */
  updateAlarm(data: any) {
    return axiosInstance({
      url: '/seaWaveAlarm/update',
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 发布警报
   * @param id
   * @returns
   */
  publishAlarm(id: any) {
    return axiosInstance({
      url: `/seaWaveAlarm/release/${id}`,
      method: 'POST'
    })
  },
  /**
   * @abstract 删除警报
   * @param data
   * @returns
   */
  deleteSeaWaveAlarm(data: any) {
    return axiosInstance({
      url: `/seaWaveAlarm/delete`,
      method: 'POST',
      data
    })
  },
  uploadImage(data: any) {
    return axiosInstance({
      url: '/upload/image',
      method: 'POST',
      data
    })
  },
  upLoadBase64Image(data: any) {
    return axiosInstance({
      url: '/upload/imageBase64',
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 获取警报级别列表
   */
  getAlarmLevelList() {
    return axiosInstance({
      url: `/alarmLevel/list `,
      method: 'GET'
    })
  },

  /**
   * 获取海浪警报分页列表
   * @param params
   * @returns
   */
  getSeaWaveAlarmPageList(params: any) {
    return axiosInstance({
      url: '/seaWaveAlarm/pageList',
      method: 'GET',
      params
    })
  },
  /**
   * @abstract 获取海浪警报列表
   * @returns
   */
  getSeaWaveAlarm() {
    return axiosInstance({
      url: `/seaWaveAlarm/list`,
      method: 'GET'
    })
  },

  /**
   * 获取海浪警报信息
   * @param id
   * @returns
   */
  getSeaWaveAlarmInfoById(id: string) {
    return axiosInstance({
      url: `/seaWaveAlarm/info/${id}`,
      method: 'GET'
    })
  },
  /**
   * 获取风暴潮历史警报列表分页数据
   * @param params
   * @returns
   */
  getStormSurgePageList(params: any) {
    return axiosInstance({
      url: '/stormSurgeAlarm/pageList',
      method: 'GET',
      params
    })
  },
  /**
   * 获取风暴潮警报信息
   * @param id
   * @returns
   */
  getStormSurgeAlarmInfoById(id: string) {
    return axiosInstance({
      url: `/stormSurgeAlarm/info/${id}`,
      method: 'GET'
    })
  },

  /**
   * 获取海浪警报消息列表
   * @returns
   */
  getSeaWaveMessageList(params: any) {
    return axiosInstance({
      url: `/seaWaveMessage/pageList`,
      method: 'GET',
      params
    })
  },

  /**
   * 保存警报消息
   * @param data
   * @returns
   */
  saveSeaWaveMessage(data: any) {
    return axiosInstance({
      url: '/seaWaveMessage/save',
      method: 'POST',
      data
    })
  },

  /**
   * 提交警报消息
   * @param data
   * @returns
   */
  submitSeaWaveMessage(data: any) {
    return axiosInstance({
      url: '/seaWaveMessage/release',
      method: 'POST',
      data
    })
  },

  /**
   * 修改警报消息
   * @param data
   * @returns
   */
  updateSeaWaveMessage(data: any) {
    return axiosInstance({
      url: '/seaWaveMessage/update',
      method: 'POST',
      data
    })
  },

  /**
   * 通过主键查询警报消息
   * @param id
   * @returns
   */
  getAlarmInfoById(id: string) {
    return axiosInstance({
      url: `/seaWaveMessage/info/${id}`,
      method: 'GET'
    })
  },

  /**
   * 删除警报消息
   * @param data
   * @returns
   */
  deleteAlarm(data: any) {
    return axiosInstance({
      url: '/seaWaveMessage/delete',
      method: 'POST',
      data
    })
  },

  /**
   * 获取风暴潮历史消息列表
   * @param params
   * @returns
   */
  getStormSurgeMessageList(params: any) {
    return axiosInstance({
      url: '/stormSurgeMessage/pageList',
      method: 'GET',
      params
    })
  },

  /**
   * 通过id获取风暴潮警报消息详情
   * @param id
   * @returns
   */
  getStormSurgeMessageInfoById(id: string) {
    return axiosInstance({
      url: `/stormSurgeMessage/info/${id}`,
      method: 'GET'
    })
  },

  /**
   * 保存风暴潮消息
   * @param data
   * @returns
   */
  saveStormSurgeMessage(data: any) {
    return axiosInstance({
      url: '/stormSurgeMessage/save',
      method: 'POST',
      data
    })
  },

  /**
   * 发布风暴潮消息
   * @param data
   * @returns
   */
  submitStormSurgeMessage(data: any) {
    return axiosInstance({
      url: '/stormSurgeMessage/release',
      method: 'POST',
      data
    })
  },

  /**
   * 修改风暴潮消息
   * @param data
   * @returns
   */
  updateStormSurgeMessage(data: any) {
    return axiosInstance({
      url: '/stormSurgeMessage/release',
      method: 'POST',
      data
    })
  },

  /**
   * 修改风暴潮消息
   * @param data
   * @returns
   */
  deleteStormSurgeMessage(data: any) {
    return axiosInstance({
      url: '/stormSurgeMessage/delete',
      method: 'POST',
      data
    })
  },
  /**
   * @abstract 获取防御指南列表
   * @param id 获取防御指南类型
   * @returns
   */
  getDefenseGuideList(id: any) {
    return axiosInstance({
      url: `/alarmDefenseGuide/selectByType/${id}`,
      method: 'GET'
    })
  },
  getStationTree(params: any) {
    return axiosInstance<IRegionTree[]>({
      url: `/stationType/treeList`,
      method: 'GET',
      params
    })
  },
  getStationData(params: any) {
    return axiosInstance<IRegionData[]>({
      url: `/tide/daily_data`,
      method: 'GET',
      params
    })
  },
  /**
   * @abstract 生成风暴潮警报文本
   */
  generateStormText(data: any) {
    return axiosInstance({
      url: '/stormSurgeAlarm/generateText ',
      method: 'POST',
      data
    })
  },
  /**
   * 微博
   * @param data
   */
  microblog(data: { describe: string; imgPath: string }) {
    return axiosInstanceMicroblog({
      url: '/api/weibo/weibo/share',
      method: 'POST',
      data
    })
  }
}

export default Alarm
