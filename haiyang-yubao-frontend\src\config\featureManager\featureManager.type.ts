import type { Feature, Geometry, FeatureCollection } from 'geojson'
import OlFeature from 'ol/Feature'
import OlGeometry from 'ol/geom/Geometry'
import { StyleLike } from 'ol/style/Style'
import { GeoJSON } from 'ol/format'
import { Point, SimpleGeometry } from 'ol/geom'

export interface IInternalProperties {
  $type: string
  $version: string
}

export interface IExternalProperties {
  [_: string]: unknown
}

export type IProperties = IInternalProperties & IExternalProperties

export type IFeature = Feature<Geometry, IProperties>

export type IFeatureCollection = FeatureCollection<Geometry, IProperties>

/**
 * 属性操作接口
 */
export interface IPropertiesHandler {
  /**
   * 获取内部属性
   */
  getInternalProperties(): IInternalProperties

  /**
   * 获取外部属性
   */
  getExternalProperties(): IExternalProperties

  /**
   * 获取 json 属性
   */
  getJSONProperties(): IProperties
}

/**
 * 样式处理接口
 */
export interface IStyleHandler {
  /**
   * 重置样式
   */
  resetStyle: () => void
  /**
   * 默认样式
   */
  defaultStyle: () => StyleLike | undefined
  /**
   * 选中样式
   */
  selectedStyle: () => StyleLike | undefined
}

/**
 * 要素读取接口
 */
export interface IFeatureReadable {
  initFeature: () => void
}

export class FeaturePro<T extends SimpleGeometry = SimpleGeometry>
  extends OlFeature
  implements IPropertiesHandler, IStyleHandler, IFeatureReadable
{
  static $type = 'feature.pro'
  static $version = 'v1'


  constructor(protected _geoJSONFeature: IFeature) {
    super()
    super.set('$type', FeaturePro.$type)
    super.set('$version', FeaturePro.$version)
    this.initFeature()
  }

  override set(key: string, value: any, silent?: boolean | undefined) {
    if (!key.startsWith('$')) {
      super.set(key, value, silent)
    }
  }

  getExternalProperties(): IExternalProperties {
    const properties = this.getProperties()
    const externalProperties: IExternalProperties = {}
    const keys = Reflect.ownKeys(properties) as string[]
    for (const key of keys) {
      if (!key.startsWith('$') && key !== 'geometry') {
        Reflect.set(externalProperties, key, properties[key])
      }
    }
    return externalProperties
  }

  getInternalProperties(): IInternalProperties {
    const properties = this.getProperties()
    const internalProperties: IInternalProperties = {
      $type: properties.$type,
      $version: properties.$version
    }
    const keys = Reflect.ownKeys(properties) as string[]
    for (const key of keys) {
      if (key.startsWith('$')) {
        Reflect.set(internalProperties, key, properties[key])
      }
    }
    return internalProperties
  }

  getJSONProperties(): IProperties {
    const _properties: IProperties = {
      ...this.getExternalProperties(),
      ...this.getInternalProperties()
    }
    return _properties
  }

  defaultStyle() {
    return undefined
  }

  resetStyle(): void {
    this.setStyle(this.defaultStyle())
  }

  selectedStyle() {
    return undefined
  }

  initFeature() {
    const geoJSON = new GeoJSON()
    const feature = geoJSON.readFeature(
      this._geoJSONFeature
    ) as OlFeature<SimpleGeometry>
    this.setGeometry(feature.getGeometry())
    const properties = feature.getProperties()
    this.setProperties(properties)
  }
}
