/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-25 09:38:03
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-16 18:01:02
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import CreateDialog from './create.vue'
import PreviewDialog from './preview.vue'
import MapTools from './mapTools.vue'
import CreateText from './createText.vue'
import LayerChoose from './layerChoose.vue'
import AddPointPosition from './addPoint.vue'

const nmefcElementList = [
  {
    name: '浪高',
    code: 'regWave',
    identity: 'FORECAST_REGWAVE_SWH_GEOJSON'
  },
  {
    name: '浪向',
    code: 'regWave',
    identity: 'FORECAST_REGWAVE_MWD_GEOJSON'
  },
  {
    name: '风速',
    code: 'wind',
    identity: 'FORECAST_WIND_WINDSPEED_GEOJSON'
  },
  {
    name: '风向',
    code: 'wind',
    identity: 'FORECAST_WIND_WINDDIR_GEOJSON'
  },
  {
    name: '海温',
    code: 'regTSC-t',
    identity: 'FORECAST_REGTSC-T_TEMP_GEOJSON'
  },
  {
    name: '海流',
    code: 'regTSC-c',
    identity: 'FORECAST_REGTSC-C_CURRENTSPEED_GEOJSON'
  },
  {
    name: '盐度',
    code: 'regTSC-s',
    identity: 'FORECAST_REGTSC-S_SALINITY_GEOJSON'
  }
]

const gridElementList = [
  {
    name: '浪高',
    nameWithUnit: '浪高(米)',
    code: 'grid-wave5',
    identity: 'GRID_GRID-WAVE5_SWH_GEOJSON'
  },
  // {
  //   name: '浪向',
  //   code: 'grid-wave5',
  //   identity: 'GRID_GRID-WAVE5_SWH_GEOJSON'
  // },
  {
    name: '风速',
    nameWithUnit: '风速(m/s)',
    code: 'grid-wind',
    identity: 'GRID_GRID-WIND_WINDSPEED_GEOJSON'
  },
  // {
  //   name: '风向',
  //   code: 'grid-wind',
  //   identity: 'GRID_GRID-WIND_WINDDIR_GEOJSON'
  // },
  {
    name: '海温',
    nameWithUnit: '海温(度)',
    code: 'grid-t',
    identity: 'GRID_GRID-T_SST_GEOJSON'
  }
  // {
  //   name: '海流',
  //   code: 'grid-c',
  //   identity: 'GRID_GRID-C_CURRENTSPEED_GEOJSON'
  // }
]

export {
  CreateDialog,
  PreviewDialog,
  gridElementList,
  nmefcElementList,
  MapTools,
  CreateText,
  LayerChoose,
  AddPointPosition
}
