* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  outline: none;
  line-height: 1;
}

img {
  border: 0;
}

html,
body,
#vite-app {
  width: 100%;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC',
    'Hiragino Sans GB', 'Heiti SC', 'Microsoft YaHei', 'WenQuanYi Micro Hei',
    sans-serif;

  .n-config-provider {
    width: 100%;
    height: 100%;
    position: relative;
  }
}

ul,
ol {
  list-style: none;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track-piece {
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}

micro-app,
micro-app-body {
  height: 100%;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.n-button:not(.n-button--disabled):hover {
  background: #1c81f8 !important;
}

.d-flex {
  display: flex !important;
}

.flex-justify-between {
  justify-content: space-between !important;
}

.flex-justify-center {
  justify-content: center !important;
}

.flex-justify-end {
  justify-content: flex-end !important;
}

.flex-align-center {
  align-items: center !important;
}

.flex1 {
  flex: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-shrink {
  flex-shrink: 0 !important;
}

.common-container {
  background: #f0f5fb;
  border-radius: 0px 0px 0px 0px;
  height: 100%;

  .header-bar.system {
    width: 100%;
    height: 64px;
    background: url(src/assets/images/common/header-bg.png) no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 20px 25px 24px;

    .logo {
      display: flex;
      align-items: center;
      width: auto;
      .logo-title{
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      h2 {
        // font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        // font-weight: 400;
        // font-size: 36px;
        // line-height: 42px;
        // letter-spacing: 3px;
        // text-align: left;
        // font-style: normal;
        // text-transform: none;
        // background-image: linear-gradient(90deg, #ffffff 0%, #bedcff 100%);
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
        width: 330.35px;
        height: 36.86px;
        background: url(src/assets/images/common/header-title.png) no-repeat;
        background-size: 100% 100%;
      }
      .sub-title{
        width: 199px;
        height: 16.15px;
        background: url(src/assets/images/common/sub-title.png) no-repeat;
        background-size: 100% 100%;
      }

      img {
        width: 50px;
        height: 46px;
        margin-right: 13px;
      }
    }

    .drop-down {
      width: auto;
      margin-top: 0;
      margin-right: 0;
      display: flex;
      align-items: center;
      .icon-down{
        margin-left: 5px;
        margin-right: 0;
      }
    }
  }

  .common-content {
    display: flex;
    height: 100%;

    .content-container {
      flex: 1;

      &.dataMaintenance {
        padding: 0;
      }

      &>div {
        height: 100%;
      }
    }
  }
}

.aside-header {
  background: url(src/assets/images/common/aside-header.png) no-repeat;
  background-size: 100% 100%;
  border-bottom: 1px solid #dfdfdf;

  .title-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 11px 16px 19px 26px;

    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
      font-style: normal;
      text-transform: none;
    }
  }
}

.n-radio-group.qx-radio-group {
  display: flex;
  align-items: center;
}

.n-radio.qx-radio {
  display: flex;
  align-items: center;

  .n-radio__dot-wrapper {
    width: 16px;
    height: 16px;
    // border: 1px solid #d9d9d9;
  }

  .n-radio__dot.n-radio__dot--checked {
    background: var(--n-dot-color-active);

    &::before {
      width: 7px;
      height: 7px;
      background: #fff;
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -50%, 0);
    }
  }

  .n-radio__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 600;
    font-size: 14px;
    color: #666666;
  }
}

.common-aside {
  width: 360px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  background: #fff;
}

.column {
  width: 558px;

  .column-content {
    background: #fafcfe;
    border-radius: 4px;
    border: 1px solid #e7f0fb;
    margin-top: 14px;
    box-sizing: border-box;
    padding: 20px 30px 0 30px;
  }

  .btns {
    box-sizing: border-box;
    padding: 11px 0;
    text-align: right;
  }
}

.column-title {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 600;
  font-size: 14px;
  color: #222222;
  padding-left: 10px;
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background: #567bff;
    position: absolute;
    top: -2px;
    left: 0px;
  }
}

.common-header {
  width: 100%;
  background: url(src/assets/images/common/content-header.png) no-repeat;
  background-size: 100% 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  padding: 18px 0 8px 26px;
  flex-shrink: 0;

  h3 {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 18px;
    color: #222222;
    line-height: 21px;
  }
}

.qx-input {
  .n-input__input-el {
    height: 32px !important;
    line-height: 32px !important;
  }
}

.operate-btn {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #2489ff;
  line-height: 14px;
  margin-right:10px;
  &:not(:nth-child(1)),
  &:not(:nth-last-child(1)) {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
  }
  &:last-child(1){
    margin-right: 0;
  }
  &.del{
    color:#FF3838;
  }
}

micro-app[name] {
  .pooled-content {
    margin-top: 0 !important;
  }

  .ht-breadcrumb {
    position: static !important;
  }

  .content-right {
    display: flex;
    flex-direction: column;

    &>.pooled-content {
      flex: 1;
    }
  }
}

.n-color-picker-trigger__value {
  display: none;
}

.n-color-picker-action {
  .n-button {
    background: #1c81f8;

    &:hover {
      color: #fff;
    }
  }
}

.qx-table-edit-wrapper {
  .n-input {
    width: 100%;
  }
}

.n-color-picker-panel {
  .n-button {
    .n-button__content {
      color: #fff;
    }
  }
}

.qx-drawer{
  .n-drawer-header{
    padding: 9px 20px!important;
  }
  .n-drawer-header__main{
    font-size: 18px!important;
  }
}