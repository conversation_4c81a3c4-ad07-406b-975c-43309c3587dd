/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-13 10:25:00
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-03 17:14:23
 * @FilePath: /hainan-jianzai-web/src/views/analysis/components/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import MapTypes from './mapTypes.vue'
import DataSource from './dataSource.vue'
import StormSurgeProvincial from './stormSurge/provincial.vue'
import WebGLFlowField from './webGLFlowField.vue'
import ExportDialog from './exportDialog.vue'
import { reactive } from 'vue'
import Legend from './legend.vue'
import Typhoon from './typhoon.vue'

const req1 = import.meta.glob('src/assets/images/analysis/*.*', { eager: true })
const req = reactive<any>({ ...req1 })

interface ImgMap {
  [key: string]: any
}

const imagesMap = reactive<ImgMap>({})
// 循环所有图片，将图片名设置成键，值为导入该图片的地址
for (const key in req) {
  // let name = key.replace(/(\.\/images\/|\..*)/g, '')
  const name = key.split('/').slice(-1)[0].split('.')[0]
  // 抛出图片大对象后，文件页面直接引入后将图片的具体名称作为属性就能导入该图片
  imagesMap[name] = req[key].default
}
const mapTypeList = [
  {
    name: '海面风场',
    value: 'seaWind',
    icon: imagesMap['sea-wind'],
    activeIcon: imagesMap['sea-wind-active'],
    modeType: [],
    grid: {
      json: 'grid-wind_0_json',
      png: 'grid-wind_0_png',
      tiff: 'grid-wind_windspeed_0_tiff'
    },
    nmefc: {
      json: 'wind_0_json',
      png: 'wind_0_png',
      tiff: 'wind_windspeed_0_tiff'
    },
    isoline: {
      default: 'wind_slp_geojson'
    },
    unit: 'm/s',
    unitLabel: '风速'
  },
  {
    name: '海浪',
    value: 'oceanWave',
    icon: imagesMap['ocean-wave'],
    activeIcon: imagesMap['ocean-wave-active'],
    modeType: [
      {
        name: '精细化',
        value: 'refinement'
      },
      {
        name: '区域',
        value: 'area'
      }
    ],
    grid: 'grid-wave5_swh_0_tiff',
    nmefc: {
      refinement: 'fineWave_swh_0_tiff',
      area: 'regWave_swh_0_tiff',
      'direction-area': 'regWave_mwd_0_tiff', // 风向
      'direction-refinement': 'fineWave_mwd_0_tiff', // 风向
      'particle-flow-area-png': 'regWave_mwd_0_png', // 风向粒子流
      'particle-flow-area-json': 'regWave_mwd_0_json', // 风向粒子流
      'particle-flow-refinement-png': 'fineWave_mwd_0_png', // 风向粒子流
      'particle-flow-refinement-json': 'fineWave_mwd_0_json' // 风向粒子流
    },
    unit: 'm',
    unitLabel: '有效波高'
  },
  {
    name: '盐度',
    value: 'salinity',
    icon: imagesMap['salinity'],
    activeIcon: imagesMap['salinity-active'],
    modeType: [
      {
        name: '精细化',
        value: 'refinement'
      },
      {
        name: '区域',
        value: 'area'
      }
    ],
    grid: '',
    nmefc: {
      refinement: 'fineTSC_salinity_0_tiff',
      area: 'regTSC-s_salinity_0_tiff'
    },
    unit: 'psu',
    unitLabel: '表层盐度'
  },
  {
    name: '海温',
    value: 'seaTemp',
    icon: imagesMap['sea-temp'],
    activeIcon: imagesMap['sea-temp-active'],
    modeType: [
      {
        name: '精细化',
        value: 'refinement'
      },
      {
        name: '区域',
        value: 'area'
      }
    ],
    nmefc: {
      refinement: 'fineTSC_temp_0_tiff',
      area: 'regTSC-t_temp_0_tiff'
    },
    grid: 'grid-t_temp_0_tiff',
    unit: '℃',
    unitLabel: '表层海温'
  },
  {
    name: '海流',
    value: 'oceanCurrents',
    icon: imagesMap['ocean-currents'],
    activeIcon: imagesMap['ocean-currents-active'],
    modeType: [
      {
        name: '精细化',
        value: 'refinement'
      },
      {
        name: '区域',
        value: 'area'
      }
    ],
    nmefc: {
      refinement: {
        json: 'fineTSC_0_json',
        png: 'fineTSC_0_png',
        tiff: 'fineTSC_currentspeed_0_tiff'
      },
      area: {
        json: 'regTSC-c_0_json',
        png: 'regTSC-c_0_png',
        tiff: 'regTSC-c_currentspeed_0_tiff'
      }
    },
    grid: {
      json: 'grid-c_0_json',
      png: 'grid-c_0_png',
      tiff: 'grid-c_currentspeed_0_tiff'
    },
    unit: 'm/s',
    unitLabel: '流速'
  },
  {
    name: '台风',
    value: 'typhoon',
    icon: imagesMap['typhoon'],
    activeIcon: imagesMap['typhoon-active'],
    modeType: [
      {
        name: '搜救',
        value: 'rescue'
      },
      {
        name: '溢油',
        value: 'oilSpills'
      }
    ]
  },
  {
    name: '风暴潮',
    value: 'stormSurge',
    icon: imagesMap['storm-surge'],
    activeIcon: imagesMap['storm-surge-active'],
    modeType: [
      {
        name: '县级街区',
        value: 'county'
      },
      {
        name: '省级',
        value: 'provincial'
      }
    ],
    nmefc: {
      provincial: 'stormSurge-f_stormSurgeh_0_tiff',
      county: 'stormSurgeFloodplain'
    },
    grid: {
      provincial: '',
      county: ''
    },
    unit: 'm',
    unitLabel: '增水'
  }
]

export interface ITiffItem {
  createTime: string
  dataTime: string
  fileId: string
  fileName: string
  filePath: string
  fileType: string
  forecastTime: string
  id: string
  productId: string
}

export {
  MapTypes,
  DataSource,
  StormSurgeProvincial,
  WebGLFlowField,
  ExportDialog,
  mapTypeList,
  Legend,
  Typhoon
}
