package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.TagDTO;
import cn.piesat.data.making.server.service.TagService;
import cn.piesat.data.making.server.vo.TagVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tag")
public class TagController {

    @Resource
    private TagService tagService;

    /**
     * 查询标签表列表
     *
     * @param id
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "标签表管理", operateType = OperateType.SELECT)
    public List<TagVO> getList(@RequestParam(required = false) Long id) {
        TagDTO dto = new TagDTO();
        dto.setId(id);
        return tagService.getList(dto);
    }

    /**
     * 保存标签表
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报制作系统", moduleName = "标签表管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {TagDTO.Save.class}) @RequestBody TagDTO dto) {
        tagService.save(dto);
    }

    /**
     * 根据标签表id删除标签表
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报制作系统", moduleName = "标签表管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        tagService.deleteById(id);
    }
}

