package cn.piesat.data.making.server.dto;


import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * DTO类
 *
 * <AUTHOR>
 * @date 2024-10-09 16:03:16
 */
public class FmGraphicTmplateSignDTO implements Serializable {

    private static final long serialVersionUID = 592413923779658609L;

    public interface Save {
    }

    private Long id;
    private String type;
    private String name;
    private String context;
    private String position;
    private boolean disabled;

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
