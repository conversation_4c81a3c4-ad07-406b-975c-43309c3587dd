<template>
  <n-form
    ref="formRef"
    class="forecast-temp-form"
    :model="form"
    :rules="rules"
    label-placement="left"
    label-width="auto"
    require-mark-placement="left"
  >
    <n-form-item label="列名" path="columnName">
      <n-input v-model:value="form.columnName" clearable placeholder="请输入" />
    </n-form-item>
    <n-form-item label="要素列" path="column">
      <n-select
        v-model:value="form.elementCode"
        placeholder="请选择"
        :options="elementList"
        label-field="name"
        value-field="code"
      />
    </n-form-item>

    <n-form-item label="数值处理" path="dataProcessing">
      <n-select
        v-model:value="form.valueHandle"
        placeholder="请选择"
        :options="dataProcessList"
      />
    </n-form-item>

    <div class="btns text-right">
      <!-- <qx-button>恢复</qx-button> -->
      <qx-button class="primary" @click="onSave">保存</qx-button>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  watch,
  PropType,
  onMounted,
  getCurrentInstance
} from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/forecast'
import { useMessage } from 'naive-ui'

// const { proxy } = getCurrentInstance() as any
// const parent = proxy.$parent

const message = useMessage()
const emits = defineEmits(['save'])
interface FormType {
  [propName: string]: any
}

const props = defineProps({
  info: {
    type: Object as PropType<FormType>,
    default() {
      return {}
    }
  },
  id: {
    type: String
  }
})

let form: FormType = ref({
  elementCode: '', //要素编码
  columnName: '', // 列名
  valueHandle: '', //数值处理
  forecastTemplateId: props.id, // 模板id
  display: true // 是否显示
})

watch(
  () => props.info,
  (val, oVal) => {
    if (val !== oVal) {
      form = val
    }
  },
  { deep: true, immediate: true }
)

const rules = reactive({})
const elementList = ref<any[]>([])

const dataProcessList = ref<any[]>([
  {
    label: '平均值',
    value: 'mean'
  },
  {
    label: '最大值',
    value: 'max'
  },
  {
    label: '最小值',
    value: 'min'
  },
  {
    label: '区间',
    value: 'interval'
  }
])
function getElementList() {
  Api.getElementList()
    .then((res: any) => {
      elementList.value = res
    })
    .catch(e => {
      let { msg = '' } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
}

onMounted(() => {
  getElementList()
})

function onSave() {
  emits('save', form)
}
</script>

<style lang="scss">
.forecast-temp-form {
  box-sizing: border-box;
  padding: 25px 20px;
  .n-form-item-label__text {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #222222;
    line-height: 14px;
    text-align: right;
  }
  .n-base-selection,
  .n-form-item-label {
    min-height: 32px;
    height: 32px;
  }
  .n-input .n-input__input-el {
    height: 32px;
  }
  .n-base-selection .n-base-selection-label {
    height: 32px;
  }
  .n-form-item .n-form-item-feedback-wrapper {
    line-height: 12px;
    min-height: 12px;
  }
  .time-range {
    .line {
      margin: 10px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
    }
  }
  .btns {
    margin-top: 44px;
  }
}
</style>
