package cn.piesat.data.making.server;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.entity.OceanStationMinuteSqO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.model.RequestData;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.utils.HttpClientUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class StationObserveDataServiceTest {

    @Resource
    private OceanStationHourAtOService oceanStationHourAtOService;
    @Resource
    private OceanStationHourBpOService oceanStationHourBpOService;
    @Resource
    private OceanStationHourHuOService oceanStationHourHuOService;
    @Resource
    private OceanStationHourRnOService oceanStationHourRnOService;
    @Resource
    private OceanStationHourSlOService oceanStationHourSlOService;
    @Resource
    private OceanStationHourVbOService oceanStationHourVbOService;
    @Resource
    private OceanStationHourWtOService oceanStationHourWtOService;
    @Resource
    private OceanStationHourWlDatOService oceanStationHourWlDatOService;
    @Resource
    private OceanStationHourWsDatOService oceanStationHourWsDatOService;
    @Resource
    private OceanStationHourWvOService oceanStationHourWvOService;
    @Resource
    private OceanStationHourWsOService oceanStationHourWsOService;
    @Resource
    private OceanStationOntimeDataOService oceanStationOntimeDataOService;
    @Resource
    private GroupOceanLargeDeepseaMooringbuoyOService groupOceanLargeDeepseaMooringbuoyOService;
    @Resource
    private OceanLargeDeepseaMooringbuoyOService oceanLargeDeepseaMooringbuoyOService;
    @Resource
    private OceanStationHourWlOService oceanStationHourWlOService;
    @Resource
    private OceanStationMinuteSqOService oceanStationMinuteSqOService;
    @Resource
    private OceanSmallShallowseauoyOService oceanSmallShallowseauoyOService;
    @Resource
    private StationService stationService;
    @Resource
    private ShipDataService shipDataService;

    @lombok.Data
    static class Wrapper {
        private String msg;
        private Integer code;
        private Data data;
    }

    @lombok.Data
    static class Nt {
        private String name;
        private String type;
    }

    @lombok.Data
    static class Data {
        private Integer pageSize;
        private Integer pageNum;
        private List<Nt> columns;
        private Integer totalCount;
        private String encryKey;
        private List<OceanStationMinuteSqO> resultList;
    }

    @Test
    public void test() throws IOException {
        List<OceanStationMinuteSqO> list = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        //文件路径
        String filePath = "C:\\Users\\<USER>\\Desktop\\data\\ocean_station_minute_sq_o.json";
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
                sb.append("\n");
            }
            String jsonString = sb.toString().trim();
            TypeReference<Wrapper> typeRef = new TypeReference<Wrapper>() {
            };
            Wrapper wrapper = mapper.readValue(jsonString, typeRef);
            list = wrapper.getData().getResultList();
        }
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(o -> oceanStationMinuteSqOService.save(o));
        }
//        if (!CollectionUtils.isEmpty(list)) {
//            int limit = 10;
//            int listSize = list.size();
//            List<OceanStationMinuteSqO> newList = Lists.newArrayList();
//            for (int i = 0; i < listSize; i++) {
//                newList.add(list.get(i));
//                if (limit == newList.size() || i == listSize - 1) {
//                    oceanStationMinuteSqOService.saveBatch(newList);
//                    newList.clear();
//                }
//            }
//        }
    }

    //调用中台api查询数据并导出到文件
    private static final String base_url = "http://10.132.108.150:8089/service/api/hygcybsjycpglxt_";
    private static final List<String> tableList = Arrays.asList(
            //站点
            "ocean_station_minute_sq_o",
            "ocean_station_hour_at_o",
            "ocean_station_hour_bp_o",
            "ocean_station_hour_hu_o",
            "ocean_station_hour_rn_o",
            "ocean_station_hour_sl_o",
            "ocean_station_hour_vb_o",
            "ocean_station_hour_wl_dat_o",
            "ocean_station_hour_wl_o",
            "ocean_station_hour_ws_dat_o",
            "ocean_station_hour_ws_o",
            "ocean_station_hour_wt_o",
            "ocean_station_hour_wv_o",
            "ocean_station_ontime_data_o",
            //浮标
            "ocean_small_shallowsea_buoy_o",
            "orderbyasc_ocean_large_deepsea_mooringbuoy_o",
            "ocean_large_deepsea_mooringbuoy_o"
    );

    public static void main(String[] args) throws IOException {
        String url;
        String appKey = "ace6aa751bfc35ce79b8f8a0ab6b18ee51cad1fa";
        String sign = "b5f3b0cf79a44b919f744fe06d79f3d6";

        RequestData requestData = new RequestData();
        RequestData.Page page = new RequestData.Page();
        RequestData.Param param = new RequestData.Param();
        LocalDateTime lastCreatetime = LocalDateTime.of(2025, 01, 01, 00, 00, 00);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        param.setStartTime("'" + lastCreatetime.format(formatter) + "'");
        param.setEndTime("'" + LocalDateTime.of(2025, 03, 15, 00, 00, 00).format(formatter) + "'");
//        param.setEndTime("'" + LocalDateTime.now().format(formatter) + "'");
        requestData.setParam(param);

        Set<Long> set = new HashSet<>();
        int pageNum = 1;
        boolean hasMoreData = true;
        while (hasMoreData) {
            page.setPageNum(pageNum);
            page.setPageSize(1000);
            requestData.setPage(page);
            String paramStr = JsonUtil.object2Json(requestData);
            String appParam = Base64.getEncoder().encodeToString(paramStr.getBytes());
            String contactStr = String.join("&", appKey, sign, appParam);
            String appSign = HttpClientUtil.md5(contactStr);

            //将数据导出到文件
            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());

            for (String table : tableList) {
                try {
                    url = base_url + table;
                    String response = HttpClientUtil.post(url, "", appKey, appParam, appSign);
                    String jsonString = response.trim();
                    TypeReference<Wrapper> typeRef = new TypeReference<Wrapper>() {
                    };
                    Wrapper wrapper = mapper.readValue(jsonString, typeRef);
                    if (wrapper.getCode() == 0) {
                        if (wrapper.getData() != null) {
                            log.debug("{} 过滤条件: {}, 总条数: {}", table, paramStr, wrapper.getData().getTotalCount());
                            List<OceanStationMinuteSqO> newData = wrapper.getData().getResultList();
                            if (!CollectionUtils.isEmpty(newData)) {
                                newData.forEach(o -> set.add(o.getId()));
                                System.out.println("size===========" + set.size());
                                String filePath = "C:\\Users\\<USER>\\Desktop\\data\\" + table + pageNum + "_" + ".json";
                                //将 JSON 字符串写入文件
                                writeJsonToFile(response, filePath);
                            } else {
                                hasMoreData = false;
                            }
                        } else {
                            hasMoreData = false;
                        }
                    } else {
                        hasMoreData = false;
                    }
                    pageNum++;
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println("结束=====" + set.size());
    }

    //读取文件数据并入库
//    private static final Map<String, Class> tableMap = new HashMap<>();
//
//    static {
//        tableMap.put("ocean_station_minute_sq_o", OceanStationMinuteSqO.class);
//        tableMap.put("ocean_station_hour_at_o", OceanStationHourAtO.class);
//        tableMap.put("ocean_station_hour_bp_o", OceanStationHourBpO.class);
//        tableMap.put("ocean_station_hour_hu_o", OceanStationHourHuO.class);
//        tableMap.put("ocean_station_hour_rn_o", OceanStationHourRnO.class);
//        tableMap.put("ocean_station_hour_sl_o", OceanStationHourSlO.class);
//        tableMap.put("ocean_station_hour_vb_o", OceanStationHourVbO.class);
//        tableMap.put("ocean_station_hour_wl_dat_o", OceanStationHourWlDatO.class);
//        tableMap.put("ocean_station_hour_wl_o", OceanStationHourWlO.class);
//        tableMap.put("ocean_station_hour_ws_dat_o", OceanStationHourWsDatO.class);
//        tableMap.put("ocean_station_hour_ws_o", OceanStationHourWsO.class);
//        tableMap.put("ocean_station_hour_wt_o", OceanStationHourWtO.class);
//        tableMap.put("ocean_station_hour_wv_o", OceanStationHourWvO.class);
//        tableMap.put("ocean_station_ontime_data_o", OceanStationOntimeDataO.class);
//        tableMap.put("ocean_small_shallowsea_buoy_o", OceanSmallShallowseauoyO.class);
//        tableMap.put("orderbyasc_ocean_large_deepsea_mooringbuoy_o", GroupOceanLargeDeepseaMooringbuoyO.class);
//        tableMap.put("ocean_large_deepsea_mooringbuoy_o", OceanLargeDeepseaMooringbuoyO.class);
//
//    }
//
//    public static void main(String[] args) throws IOException {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.registerModule(new JavaTimeModule());
//        //文件路径 实体类中Date转成ZoneDateTime
//        String filePath = "C:\\Users\\<USER>\\Desktop\\data\\ocean_station_minute_sq_o.json";
//        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
//            StringBuilder sb = new StringBuilder();
//            String line;
//            while ((line = br.readLine()) != null) {
//                sb.append(line);
//                sb.append("\n");
//            }
//            String jsonString = sb.toString().trim();
//            TypeReference<Wrapper> typeRef = new TypeReference<Wrapper>() {
//            };
//            Wrapper wrapper = mapper.readValue(jsonString, typeRef);
//            List<OceanStationMinuteSqO> list = wrapper.getData().getResultList();
//            System.out.println(list.size());
//        }
//    }

    /**
     * 将 JSON 字符串写入文件
     *
     * @param jsonString JSON 字符串
     * @param filePath   文件路径（例如：data/output.json）
     * @throws IOException 如果写入文件失败
     */
    public static void writeJsonToFile(String jsonString, String filePath) throws IOException {
        //确保目录存在
        Files.createDirectories(Paths.get(filePath).getParent());
        //将 JSON 字符串写入文件
        Files.write(Paths.get(filePath), jsonString.getBytes());
        log.info("JSON 数据已写入文件: " + filePath);
    }

    @Test
    public void readAndSaveShipData() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\ship_data";
        shipDataService.readAndSaveData(filePath);
    }

    @Test
    public void saveShip() {
        List<String> shipList = shipDataService.getShipList();
        List<Station> stations = new ArrayList<>();
        for (String name : shipList) {
            Station station = new Station();
            station.setName(name);
            station.setStationTypeCode("ship");
            station.setRelationStation(name);
            stations.add(station);
        }
        stationService.saveBatch(stations);
    }
}
