/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> xuli<PERSON><EMAIL>
 * @Date: 2024-10-15 13:44:23
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-10 16:05:10
 * @FilePath: \hainan-jianzai-web\src\requests\toolRequest.ts
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import type {
  IResponseItem,
  paramsType
} from 'src/components/ElementStatistics/components/types'
import getAxios from '../utils/axios'
import moment from 'moment'
import {
  IBuoyStationReq,
  IRealtimeStationItem,
  ITyphoonForecastInfo,
  ITyphoonInfo
} from 'src/requests/toolRequest.type'
import {
  EBuoyStationType,
  EOceanStationType
} from 'src/components/ElementStatistics/components/liveMonitoring-hooks/useRealtimeStationType.type'

const productUrl = import.meta.env.VITE_PRODUCT_BASE_URL
const axiosProductInstance = getAxios(productUrl)

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
// VITE_FORECAST_BASE_API_URL
const axiosInstance = getAxios(baseUrl)

function getReAnalysis(productId: string, params: any) {
  return axiosProductInstance({
    url: `/api/product/record/range/forecast/${productId}`,
    method: 'GET',
    params
  })
}

/**
 * @abstract 获取船舶列表（范围）
 * @param data
 */
function getShipListR(data: any) {
  return axiosInstance({
    url: `/shipData/rangeList`,
    method: 'POST',
    data
  })
}

/**
 * @abstract 获取海洋站列表（分页）
 */
function getBuoyStationsR(data: any) {
  return axiosInstance({
    url: `/buoyStation/geoRangeDataList`,
    method: 'POST',
    data
  })
}

/**
 * 查询预报模式数据源
 * @param
 * @returns
 */
function getDataSourceList() {
  return axiosInstance({
    url: `/fmGraphicDataSource/list`,
    method: 'GET'
  })
}

function getContourLine(params: any) {
  return axiosInstance({
    url: `/fmIsolineData/list`,
    method: 'GET',
    params
  })
}

// 获取台风列表
function getTyList(params?: any) {
  //
  return axiosInstance({
    url: `/fmGraphicRecordB/getTyphoonList`,
    method: 'GET',
    params
  }) as unknown as Promise<{
    ename: string
    id: string
    ident: string
    name: string
    status: number
    tfbh: string
  }[]>
}

function getTyphoonInfo(params: any): Promise<ITyphoonInfo[]> {
  const res = axiosInstance({
    url: `/fmGraphicRecordB/getTyphoonByCode`,
    method: 'GET',
    params
  }).then(res => {
    return res
  })
  return res as unknown as Promise<ITyphoonInfo[]>
}

function getForecastTyphoonByTime(params: { code: string; timeStr: string }): Promise<ITyphoonForecastInfo[]> {
  const res = axiosInstance({
    url: `/fmGraphicRecordB/findTyphoonForecastByCode`,
    method: 'GET',
    params
  })
  return res as unknown as Promise<ITyphoonForecastInfo[]>
}

/**
 * 获取形似台风
 */
function getSimilarTyphoon(params: any) {
  return axiosInstance({
    url: `/fmTyphoonB/similar`,
    method: 'GET',
    params
  })
}

/**
 * @abstract 获取海洋站列表（分页）
 * @param params
 * @returns
 */
function getOceanStatioPage(params: any) {
  if (params.type === 'liveObservation') {
    // liveObservation 是海洋站老版本的全部条件, 现在想查询全部, 需要删除 type 属性
    delete params.type
  }
  return axiosInstance<{pageResult: IRealtimeStationItem[]}>({
    url: `/station/page`,
    method: 'GET',
    params
  }).then(res => {
    // 过滤掉 tide
    const arr = (res as any).pageResult.filter((i: any) => i.type !== 'tide')
    ;(res as any).pageResult = arr
    return res
  })
}

async function getOceanStationPageV2(params: {
  pageNum: number
  pageSize: number
  stationTypeCode: string
  type: (EOceanStationType | EBuoyStationType)[]
}) {
  const realTimeStationArr: IRealtimeStationItem[] = []
  for (let i = 0; i < params.type.length; i++) {
    const type = params.type[i]
    const res = await getOceanStatioPage({
      ...params,
      type
    })
    realTimeStationArr.push(...res.pageResult)
  }
  return realTimeStationArr
}

/**
 * @abstract 获取海洋站列表
 * @param params
 * @returns
 */
function getOceanStatio(params: any = {}) {
  const map = new Map([
    ['sheng', '省级'],
    ['shi', '市县'],
    ['chao', '潮位简易站'],
    ['waveSpectrum', '波浪谱'],
    ['3m', '3米'],
    ['10m', '10米']
  ])
  return axiosInstance<IRealtimeStationItem[]>({
    url: `/station/list`,
    method: 'GET',
    params
  }).then(res => {
    // 数据库里有屎
    for (let i = res.length - 1; i >= 0; i--) {
      const item = res[i]
      if (item.type === 'tide') {
        res.splice(i, 1)
      }
    }
    res.forEach(i => {
      Reflect.set(i, 'typeName', map.get(i.type))
    })
    return res
  })
}

/**
 * @abstract 获取海洋站(站点)数据小时统计
 * @param data
 * @returns
 */
async function getOceanStationStatisticsHour(data: paramsType) {
  const res: ReturnType<typeof emptyOceanStation>[] = await axiosInstance({
    url: `/oceanStation/hourDataList`,
    method: 'POST',
    data
  })
  const systemTime = moment()
  let lastDatetime
  if (res.length >= 1) {
    lastDatetime = moment(res[res.length - 1].time)
  } else {
    lastDatetime = moment(data.startTime).startOf('hours')
    const station = emptyOceanStation()
    station.time = lastDatetime.format('YYYY-MM-DD HH:mm:ss')
    res.push(station)
  }
  lastDatetime.add(1, 'hours')
  while (lastDatetime.isBefore(systemTime)) {
    const station = emptyOceanStation()
    station.time = lastDatetime.format('YYYY-MM-DD HH:mm:ss')
    res.push(station)
    lastDatetime.add(1, 'hours')
  }
  return res
}

/**
 * 获取海洋站(站点)数据分钟统计
 * @param data
 * @returns
 */
async function getOceanStationStatisticsMinute(data: paramsType) {
  const res: IResponseItem[] = await axiosInstance({
    url: `/oceanStation/minuteDataList`,
    method: 'POST',
    data
  })
  const systemTime = moment()
  let lastDatetime
  if (res.length >= 1) {
    lastDatetime = moment(res[res.length - 1].time)
  } else {
    lastDatetime = moment(data.startTime).startOf('minutes')
    const station = emptyOceanStation()
    station.time = lastDatetime.format('YYYY-MM-DD HH:mm:ss')
    res.push(station)
  }
  lastDatetime.add(1, 'minutes')
  while (lastDatetime.isBefore(systemTime)) {
    const station = emptyOceanStation()
    station.time = lastDatetime.format('YYYY-MM-DD HH:mm:ss')
    res.push(station)
    lastDatetime.add(1, 'minutes')
  }
  return res
}

/**
 * @abstract 获取海洋站(区域)数据统计
 * @param data
 * @returns
 */
function getOceanStationStatisticsR(data: any) {
  return axiosInstance({
    url: `/oceanStation/geoRangeDataList`,
    method: 'POST',
    data
  })
}

/**
 * 获取船舶分页
 * @param params
 * @returns
 */
function getShipPage(params: any) {
  return axiosInstance({
    url: '/shipData/page',
    method: 'GET',
    params
  })
}

/**
 * 获取船舶航行信息
 * @param data
 * @returns
 */
function getShipInfo(data: any) {
  return axiosInstance({
    url: '/shipData/list',
    method: 'POST',
    data
  })
}

/**
 * 获取船舶列表
 * @returns
 */
function getShipList() {
  return axiosInstance({
    url: '/ship/list',
    method: 'GET'
  })
}

/**
 * 获取船舶统计数据
 * @param data
 * @returns
 */
function getShipStatistics(data: any) {
  return axiosInstance({
    url: '/ship/shipDataList',
    method: 'POST',
    data
  })
}

/**
 * 获取浮标站分页列表
 * @param params
 * @returns
 */
function getBuoyStationPage(params: any) {
  return axiosInstance({
    url: '/buoyStation/page',
    method: 'GET',
    params
  })
}

/**
 * 获取浮标站列表
 * @param params
 * @returns
 */
function getBuoyStationList() {
  return axiosInstance({
    url: '/buoyStation/list',
    method: 'GET'
  })
}

/**
 * 浮标站 根据时间范围、站点查询查询观测结果
 * @param data
 * @returns
 */
async function getBuoyStationStatistics(data: Partial<IBuoyStationReq>) {
  const res: any[] = await axiosInstance({
    //url: '/buoyStation/stationDataList',
    url: '/buoyStation/dataList',
    method: 'POST',
    data
  })
  const systemTime = moment()
  let lastDatetime
  if (res.length >= 1) {
    lastDatetime = moment(res[res.length - 1].time)
  } else {
    lastDatetime = moment(data.startTime).startOf('hours')
    const station = emptyBuoyStation()
    station.time = lastDatetime.format('YYYY-MM-DD HH:mm:ss')
    res.push(station)
  }
  lastDatetime.add(1, 'hours')
  while (lastDatetime.isBefore(systemTime)) {
    const station = emptyBuoyStation()
    station.time = lastDatetime.format('YYYY-MM-DD HH:mm:ss')
    res.push(station)
    lastDatetime.add(1, 'hours')
  }
  return res
}

/**
 * 获取观测要素列表
 */
function getELementList(data: any) {
  return axiosInstance({
    url: '/liveObserveElement/list',
    method: 'POST',
    data
  })
}

function getLandPoint(data: any) {
  return axiosInstance({
    url: '/geo/globalNationalBoundary/intersection',
    method: 'POST',
    data
  })
}

/**
 * 查询船舶列表 数据维护使用
 * @returns
 */
function getShipDataList() {
  return axiosInstance({
    url: '/shipData/shipList',
    method: 'GET'
  })
}

type OceanStationKeys =
  | 'airPressure'
  | 'airTemperature'
  | 'humidity'
  | 'id'
  | 'oceanStationCode'
  | 'oceanStationLocationJson'
  | 'oceanStationName'
  | 'rain'
  | 'salinity'
  | 'seaTemperature'
  | 'tide'
  | 'time'
  | 'visibility'
  | 'windDir'
  | 'windSpeed'
  | 'windWaveHeight'
  | 'windWavePeriod'

export function emptyOceanStation(): Record<OceanStationKeys, string | null> {
  return {
    airPressure: null,
    airTemperature: null,
    humidity: null,
    id: null,
    oceanStationCode: null,
    oceanStationLocationJson: null,
    oceanStationName: null,
    rain: null,
    salinity: null,
    seaTemperature: null,
    tide: null,
    time: null,
    visibility: null,
    windDir: null,
    windSpeed: null,
    windWaveHeight: null,
    windWavePeriod: null
  }
}

type BuoyStationKeys =
  | 'id'
  | 'buoyStationCode'
  | 'time'
  | 'buoydataWs'
  | 'buoydataWd'
  | 'buoydataWsm'
  | 'buoydataWdm'
  | 'buoydataWsa'
  | 'buoydataWda'
  | 'buoydataWsh'
  | 'buoydataWdh'
  | 'buoydataBg'
  | 'buoydataBx'
  | 'buoydataZq'
  | 'buoydataYbg'
  | 'buoydataYzq'
  | 'buoydataZbg'
  | 'buoydataZzq'
  | 'buoydataAt'
  | 'buoydataBp'
  | 'buoydataHu'
  | 'buoydataWt'
  | 'buoydataSl'
  | 'buoydataNjd'
  | 'buoydataCs'
  | 'buoydataCd'
  | 'buoyStationName'
  | 'buoyStationLocationJson'
  | 'puPbg'
  | 'puPfzq'
  | 'ylpuPbg'
  | 'ylpuPfzq'

export function emptyBuoyStation(): Record<BuoyStationKeys, string | null> {
  return {
    id: null,
    buoyStationCode: null,
    time: null,
    buoydataWs: null,
    buoydataWd: null,
    buoydataWsm: null,
    buoydataWdm: null,
    buoydataWsa: null,
    buoydataWda: null,
    buoydataWsh: null,
    buoydataWdh: null,
    buoydataBg: null,
    buoydataBx: null,
    buoydataZq: null,
    buoydataYbg: null,
    buoydataYzq: null,
    buoydataZbg: null,
    buoydataZzq: null,
    buoydataAt: null,
    buoydataBp: null,
    buoydataHu: null,
    buoydataWt: null,
    buoydataSl: null,
    buoydataNjd: null,
    buoydataCs: null,
    buoydataCd: null,
    buoyStationName: null,
    buoyStationLocationJson: null,
    puPbg: null,
    puPfzq: null,
    ylpuPbg: null,
    ylpuPfzq: null
  }
}

export default {
  getShipListR,
  getBuoyStationsR,
  getDataSourceList,
  getContourLine,
  getTyList,
  getTyphoonInfo,
  getOceanStatioPage,
  getOceanStatio,
  getOceanStationStatisticsHour,
  getOceanStationStatisticsMinute,
  getOceanStationStatisticsR,
  getShipPage,
  getShipInfo,
  getShipList,
  getShipStatistics,
  getBuoyStationPage,
  getBuoyStationList,
  getBuoyStationStatistics,
  getReAnalysis,
  getSimilarTyphoon,
  getELementList,
  getLandPoint,
  getShipDataList,
  getForecastTyphoonByTime,
  getOceanStationPageV2
}
