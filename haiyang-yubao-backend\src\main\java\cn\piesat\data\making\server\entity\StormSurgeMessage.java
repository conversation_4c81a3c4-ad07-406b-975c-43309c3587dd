package cn.piesat.data.making.server.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 风暴潮消息制作(StormSurgeMessageB)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:35
 */
@TableName(value = "fm_storm_surge_message_b")
public class StormSurgeMessage implements Serializable {
    private static final long serialVersionUID = -1;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 发布时间
     */
    private Date releaseTime;
    /**
     * 编号
     */
    private String number;
    /**
     * 签发人
     */
    private Long signUser;
    /**
     * 制作人
     */
    private Long makeUser;
    /**
     * 警报内容
     */
    private String alarmContent;
    /**
     * 短信
     */
    private String smsContent;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 签发人名
     */
    private String signUserName;
    /**
     * 制作人名
     */
    private String makeUserName;
    /**
     * 台风编号
     */
    private String typhoonNo;
    /**
     * 台风时间
     */
    private Date typhoonTime;
    /**
     * 删除标记
     */
    private Integer deleteFlag = 0;
    /**
     * 源头类型  1冷空气 2台风
     */
    private Integer sourceType;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 推送任务ID
     */
    private Long pushTaskId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public Long getSignUser() {
        return signUser;
    }

    public void setSignUser(Long signUser) {
        this.signUser = signUser;
    }

    public Long getMakeUser() {
        return makeUser;
    }

    public void setMakeUser(Long makeUser) {
        this.makeUser = makeUser;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public String getMakeUserName() {
        return makeUserName;
    }

    public void setMakeUserName(String makeUserName) {
        this.makeUserName = makeUserName;
    }

    public String getTyphoonNo() {
        return typhoonNo;
    }

    public void setTyphoonNo(String typhoonNo) {
        this.typhoonNo = typhoonNo;
    }

    public Date getTyphoonTime() {
        return typhoonTime;
    }

    public void setTyphoonTime(Date typhoonTime) {
        this.typhoonTime = typhoonTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getPushTaskId() {
        return pushTaskId;
    }

    public void setPushTaskId(Long pushTaskId) {
        this.pushTaskId = pushTaskId;
    }
}

