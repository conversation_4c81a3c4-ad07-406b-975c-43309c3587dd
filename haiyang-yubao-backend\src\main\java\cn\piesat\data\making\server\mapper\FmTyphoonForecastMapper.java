package cn.piesat.data.making.server.mapper;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.entity.FmTyphoonForecast;
import cn.piesat.data.making.server.vo.FmTyphoonForecastVO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBTVO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;

import com.fasterxml.jackson.core.type.TypeReference;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring")
public interface FmTyphoonForecastMapper {

    FmTyphoonForecastMapper INSTANCE = Mappers.getMapper(FmTyphoonForecastMapper.class);

    @Mapping(source = "tm" ,target = "tm")
    @Mapping(source = "pointsJson",target = "forecastpoints",qualifiedByName = "forecastPoints")
    FmTyphoonForecastVO entityToVO(FmTyphoonForecast entity);

    List<FmTyphoonForecastVO> entitiesToVOS(List<FmTyphoonForecast> entities);

    @Named("forecastPoints")
    default List<FmTyphoonRealBVO> forecastPoints(String pointsJson) throws IOException {
        String substr = pointsJson.substring(pointsJson.lastIndexOf("time")+6,pointsJson.lastIndexOf("time")+26);
        List<FmTyphoonRealBVO> points = new ArrayList<>();
        if (substr.contains("T")) {
            List<FmTyphoonRealBTVO> tPoints = JsonUtil.json2ObjectList(pointsJson, new TypeReference<List<FmTyphoonRealBTVO>>(){});
            for (FmTyphoonRealBTVO tvo:tPoints){
                FmTyphoonRealBVO vo = new FmTyphoonRealBVO();
                vo.setTime(tvo.getTime());
                vo.setLat(tvo.getLat());
                vo.setLng(tvo.getLng());
                vo.setStrong(tvo.getStrong());
                vo.setPower(tvo.getPower());
                vo.setSpeed(tvo.getSpeed());
                vo.setMoveDir(tvo.getMoveDir());
                vo.setMoveSpeed(tvo.getMoveSpeed());
                vo.setPressure(tvo.getPressure());
                vo.setRadius7(tvo.getRadius7());
                vo.setRadius7Quad(tvo.getRadius7Quad());
                vo.setRadius10(tvo.getRadius10());
                vo.setRadius10Quad(tvo.getRadius10Quad());
                vo.setRadius12(tvo.getRadius12());
                vo.setRadius12Quad(tvo.getRadius12Quad());
                vo.setYbsj(tvo.getYbsj());
                vo.setTm(tvo.getTm());
                vo.setForecast(tvo.getForecast());
                vo.setId(tvo.getId());
                vo.setTfbh(tvo.getTfbh());
                points.add(vo);
            }
        } else {
             points = JsonUtil.json2ObjectList(pointsJson, new TypeReference<List<FmTyphoonRealBVO>>(){});
        }
        return points;
    }
}
