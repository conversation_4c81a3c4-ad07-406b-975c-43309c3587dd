package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.model.AlertUpdateInfo;
import cn.piesat.data.making.server.service.AlertUploadService;
//import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
public class AlertUploadServiceImpl implements AlertUploadService {

    @Value("${piesat.countySuApiUrl}")
    private String url = "http://10.132.113.9:8088/upload";

    //private static final OkHttpClient client = new OkHttpClient();

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public Map<String, Object> sendMsg(AlertUpdateInfo alertUpdateInfo) throws IOException{
        // 创建请求体
        //MediaType JSON = MediaType.get("application/json; charset=utf-8");
//        String jsonStr = JsonUtil.object2Json(alertUpdateInfo);
//        System.out.println(jsonStr);
//        //RequestBody jsonBody = RequestBody.create(jsonStr, JSON);
//        RequestBody fileBody = RequestBody.create(alertUpdateInfo.getFile(),MediaType.parse("application/xml"));
//        //RequestBody textBody = RequestBody.create(jsonStr,JSON);
//
//        RequestBody body = new MultipartBody.Builder()
//                .setType(MultipartBody.FORM)//注意!需要在此处添加Content-Type
//                .addFormDataPart("File", alertUpdateInfo.getFileName(), fileBody)
//                .addFormDataPart("productType",alertUpdateInfo.getProductType())
//                .addFormDataPart("requestId",alertUpdateInfo.getRequestId())
//                .addFormDataPart("authorize",alertUpdateInfo.getAuthorize())
//                .addFormDataPart("unit",alertUpdateInfo.getUnit())
//                .addFormDataPart("filePath",alertUpdateInfo.getFilePath())
//                .addFormDataPart("fileName",alertUpdateInfo.getFileName())
//                //.addPart(Headers.of("Content-Disposition", "form-data; name=\"json\""),textBody)
//                .build();
//        // 创建请求
//        Request request = new Request.Builder()
//                .url(url)
//                .post(body)
//                .build();
//
//        // 发送请求并获取响应
//        try (Response response = client.newCall(request).execute()) {
//            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code " + response);
//            }
//            return response.body().string();
//        }
        try {
            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建多部分表单数据
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文件部分
            body.add("file", new FileSystemResource(alertUpdateInfo.getFile()));

            // 添加其他参数
//            if (content != null) {
//                body.add("content", content);
//            }
            body.add("fileName", alertUpdateInfo.getFileName());
            body.add("filePath", alertUpdateInfo.getFilePath());
            body.add("produceType", alertUpdateInfo.getProductType());
            body.add("requestId", alertUpdateInfo.getRequestId());
            body.add("unit", alertUpdateInfo.getUnit());
            body.add("authorize", alertUpdateInfo.getAuthorize());
//            if (whiteListIp != null) {
//                body.add("whiteListIp", whiteListIp);
//            }

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity =
                    new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            return response.getBody();

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("returnCode", 1);
            errorResult.put("returnMessage", "请求发送失败: " + e.getMessage());
            //e.printStackTrace();
            return errorResult;
        }
    }
}
