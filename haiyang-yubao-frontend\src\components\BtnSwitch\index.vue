<template>
  <span class="btn-switch d-flex">
    <span
      class="switch-item"
      v-for="(item, index) in props.switchBtns"
      :key="item.type"
      :class="index === active ? 'active' : ''"
      @click="changeSwitch(item, index)"
    >
      {{ item.label }}
    </span>
  </span>
</template>

<script setup lang="ts">
import { ref, PropType } from 'vue'

interface btnType {
  label: string
  type: string
}

const props = defineProps({
  switchBtns: {
    type: Array as PropType<btnType[]>,
    default() {
      return [
        {
          label: 'TXT',
          type: 'txt'
        },
        {
          label: 'WORD',
          type: 'word'
        }
      ]
    }
  },
  active: {
    type: Number,
    default: 0
  }
})
const emit = defineEmits(['change'])

type optionType = {
  label:string,
  type:string
}
function changeSwitch(item:optionType, index:number) {
  emit('change', index)
}
</script>

<style lang="scss">
.btn-switch {
  width: 144px;
  // box-sizing: border-box;
  padding: 4px;
  background: #f7f7f7;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  .switch-item {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 600;
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    text-align: center;
    border-radius: 4px;
    padding: 7px 0;
    flex: 1;
    cursor: pointer;
    &:nth-child(1) {
      margin-right: 4px;
    }
    &.active {
      color: #ffffff;
      background: #1c81f8;
    }
  }
}
</style>