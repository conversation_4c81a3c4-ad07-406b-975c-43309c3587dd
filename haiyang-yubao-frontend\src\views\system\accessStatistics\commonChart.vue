<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-25 14:32:21
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2024-11-25 16:03:44
 * @FilePath: /hainan-jianzai-web/src/views/system/accessStatistics/trafficAnalysis.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="traffic-analysis d-flex">
    <div class="chart">
      <qx-echarts :option="chartOption" />
    </div>
    <div class="table-container">
      <n-data-table
        :bordered="false"
        :single-line="true"
        :columns="props.columns"
        :data="props.data"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { QxEcharts } from 'src/components/QxEcharts'
import { onMounted, reactive, ref,PropType } from 'vue'
import type {DataTableColumn} from 'naive-ui'

const props = defineProps({
  data:{
    type:Array<object>,
    default:()=>[]
  },
  columns:{
    type:Array<DataTableColumn>,
    default:()=>[]
  }
})

let chartOption = ref({})

function initChart() {
  chartOption.value = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: -5,
      left: 'center',
      icon:'circle'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        data: props.data
      }
    ]
  }
}



onMounted(() => {
  initChart()
})
</script>

<style scoped lang="scss">
.traffic-analysis {
  flex: 1;
  .chart {
    width: 40%;
    height: 100%;
  }
  .table-container {
    width:calc(60% - 12px);
    margin-left: 12px;
  }
}
</style>