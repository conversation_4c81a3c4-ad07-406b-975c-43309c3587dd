import { computed, MaybeRefOrGetter, onMounted, ref, Ref, toValue } from 'vue'
import { NInput } from 'naive-ui'


/**
 * 字数统计 hook
 * @param content
 * @param inputRef
 */
export function useParagraphStatistics(
  content: MaybeRefOrGetter<string>,
  inputRef: Ref<InstanceType<typeof NInput> | null>
) {
  const selectedContent = ref('')
  onMounted(() => {
    const target = inputRef.value?.inputElRef || inputRef.value?.textareaElRef
    if (target) {
      target.addEventListener('selectionchange', e => {
        const contentValue = toValue(content)
        const start = target.selectionStart || 0
        const end = target.selectionEnd || 0
        selectedContent.value = contentValue.substring(start, end)
      })
    }
  })
  // 总字数
  const cptParagraphStatistics = computed(() => {
    const contentValue = toValue(content)
    const selectedContentValue = toValue(selectedContent)
    return {
      all: contentValue.length,
      chinese: getChineseCharacters(contentValue).length,
      selected: selectedContentValue.length,
      selectedChinese: getChineseCharacters(selectedContentValue).length
    }
  })
  return {
    cptParagraphStatistics
  }
}

/**
 * 获取所有汉字
 * @param content
 */
function getChineseCharacters(content: string) {
  // 北部湾南部、海南abc岛以东附近海面有1.9-2.2米的中浪。小船作业注意安全。
  const reg = /[\u4E00-\u9FA5]+/g
  const match = content.matchAll(reg)
  let s = ''
  for (const match1 of match) {
    s += match1[0]
  }
  return s
}
