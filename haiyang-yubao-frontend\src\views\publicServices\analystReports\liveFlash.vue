<template>
  <!-- 信息快报 -->
  <div class="public-container">
    <div class="top">
      <div class="top-left">
        <div
          :class="['top-tab', activeTab === 0 ? 'active' : '']"
          @click="activeTab = 0"
        >
          防御快报
        </div>
        <div
          :class="['top-tab', activeTab === 1 ? 'active' : '']"
          @click="activeTab = 1"
        >
          实况速报
        </div>
      </div>
      <div class="top-right">
        <span class="date">保存：{{ saveDate }}</span>
        <span class="date">提交：{{ commitDate }}</span>
        <qx-button class="primary">保存</qx-button>
        <qx-button class="warning">提交</qx-button>
      </div>
    </div>
    <div class="content" :class="activeTab === 0 ? 'flex-column' : ''">
      <template v-if="activeTab === 0">
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">海浪方面</div>
              <span>（上一期：2025-01-22）</span>
            </div>
            <div class="header-right">
              <qx-button class="primary">更新</qx-button>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">风暴潮方面</div>
              <span>（上一期：2025-01-22）</span>
            </div>
            <div class="header-right">
              <qx-button class="primary">更新</qx-button>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">建议措施</div>
              <span>（上一期：2025-01-22）</span>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
      </template>
      <div v-if="activeTab === 1" class="left">
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">潮位观测实况分析</div>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">波浪观测实况分析</div>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">表1-海南岛沿岸潮位实况表</div>
            </div>
          </div>
          <div class="card-info">
            <CommonTable :table-head="TideTableHead" :data="tideData" />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">表2海南岛沿岸波浪实况表</div>
            </div>
          </div>
          <div class="card-info">
            <CommonTable :table-head="waveTableHead" :data="waveData" />
          </div>
        </div>
      </div>
      <div v-if="activeTab === 1" class="right">
        <div class="query-item">
          <n-date-picker v-model:value="timeRange" type="daterange" clearable />
        </div>
        <div class="query-item">
          <n-input
            v-model:value="stationName"
            type="text"
            placeholder="请输入"
          />
        </div>
        <div class="query-item d-flex">
          <div
            class="query-item__tab"
            :class="{ active: dataSourceType === 0 }"
            @click="changeDataSource(0)"
          >
            潮位
          </div>
          <div
            class="query-item__tab"
            :class="{ active: dataSourceType === 1 }"
            @click="changeDataSource(1)"
          >
            波浪
          </div>
        </div>

        <div class="station-list">
          <n-checkbox-group>
            <n-grid :y-gap="8" :cols="1">
              <n-gi>
                <n-checkbox value="Pushes Open" label="推开" />
              </n-gi>
              <n-gi>
                <n-checkbox value="The Window" label="窗户" />
              </n-gi>
              <n-gi>
                <n-checkbox value="And Raises" label="举起" />
              </n-gi>
              <n-gi>
                <n-checkbox value="The Spyglass" label="望远镜" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </div>
        <div class="query-btn">
          <qx-button>重置</qx-button>
          <qx-button class="primary">确定</qx-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { CommonTable } from './components'

const activeTab = ref(0)
const saveDate = ref('2025-01-23 12:00')
const commitDate = ref('2025-01-23 12:00')
const seaConditionAnalysis = ref(
  '1月22日08时～1月23日08时，南海大部分海域出现了大于3.0米的大浪区，其中中部和南部海域出现了大于4.0米的巨浪区。海南岛北部近岸海域出现了1.0-2.0米的轻到中浪，东部近岸海域出现了1.0-2.0米的轻到中浪，南部近岸海域出现了1.0-2.0米的轻到中浪，西部近岸海域出现了1.0-2.0米的轻到中浪。'
)
const timeRange = ref<[number, number]>([1183135260000, Date.now()])
const stationName = ref('')

//潮位实况表头
const TideTableHead = ref([
  {
    title: '站点',
    key: 'stationName'
  },
  {
    title: '时间',
    key: 'dataTime'
  },
  {
    title: '天文潮(cm)',
    key: 'stationName'
  },
  {
    title: '实测潮位(cm)',
    key: 'stationName'
  },
  {
    title: '增水值(cm)',
    key: 'stationName'
  },
  {
    title: '蓝色警戒潮位(cm)',
    key: 'stationName'
  },
  {
    title: '超警情况',
    key: 'stationName'
  }
])

const waveTableHead = ref([
  {
    title: '站点',
    key: 'stationName'
  },
  {
    title: '时间',
    key: 'dataTime'
  },
  {
    title: '有效波高',
    key: 'stationName'
  },
  {
    title: '最大波高',
    key: 'stationName'
  }
])

let tideData = ref<any[]>([])
let waveData = ref<any[]>([])

let dataSourceType = ref(0)
function changeDataSource(type: number) {
  dataSourceType.value = type
}
</script>

<style lang="scss">
@import url('./style.scss');
</style>
