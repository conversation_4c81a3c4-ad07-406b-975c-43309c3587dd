package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.AlarmProductTemplateDTO;
import cn.piesat.data.making.server.enums.AlarmTemplateCodeEnum;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.piesat.data.making.server.entity.AlarmProductTemplate;

import java.util.List;

/**
 * 警报产品模板(AlarmProductTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:17
 */
public interface AlarmProductTemplateService extends IService<AlarmProductTemplate> {
    PageResult pageList(Integer pageNum, Integer pageSize);

    AlarmProductTemplate selectByCode(AlarmTemplateCodeEnum templateCode);

    List<AlarmProductTemplateDTO> list(Integer status);

    void setOpen(Long id);

}

