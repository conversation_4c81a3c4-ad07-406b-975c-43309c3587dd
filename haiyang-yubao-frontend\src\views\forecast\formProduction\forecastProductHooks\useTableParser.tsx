import type { IForecastProductItem } from './types'
import { DataTableBaseColumn, NInput, NSelect } from 'naive-ui'
import { getColumns, getTableData } from './utils'
import { computed, ref, Ref } from 'vue'
import QxTableEdit from 'src/components/QxTableEdit'
import { Refresh } from '@vicons/ionicons5'
import { getWaveLevelByHeight, getWaveLevelByRange, waveLevelInfoArr } from 'src/config/waveLevel'

export function useTableParser(data: IForecastProductItem[]) {
  const obj: {
    tableData: any[]
    tableColumns: DataTableBaseColumn[]
  } = {
    tableData: [],
    tableColumns: []
  }
  obj.tableColumns = getColumns(data)
  obj.tableData = getTableData(data)
  return obj
}

/**
 * 默认渲染器
 * @param tableColumns
 * @param isEdit
 */
export function defaultColumnRenderer(
  tableColumns: DataTableBaseColumn[],
  isEdit: Ref<boolean>
) {
  tableColumns.map(column => {
    column.render = (rowData, rowIndex) => {
      return (
        <QxTableEdit
          value={rowData[column.key] as string}
          isEdit={isEdit.value}
          onUpdateValue={(val: any) =>
            (rowData[column.key] = val)
          }></QxTableEdit>
      )
    }
  })
  return tableColumns
}

export function swimMeanColumnRenderer(
  tableColumns: DataTableBaseColumn[],
  isEdit: Ref<boolean>
) {
  const options = [
    { label: '适宜', value: '适宜' },
    { label: '较适宜', value: '较适宜' },
    { label: '不适宜', value: '不适宜' }
  ]

  tableColumns.forEach(column => {
    if (column.key === 'swimMean') {
      column.render = (rowData, rowIndex) => {
        if (!rowData[column.key]) {
          rowData[column.key] = options[0].value
        }
        return (
          <NSelect
            options={options}
            value={rowData[column.key] as string}
            disabled={!isEdit.value}
            onUpdateValue={(val: string) =>
              (rowData[column.key] = val)
            }></NSelect>
        )
      }
    }
  })
  return tableColumns
}

export function waveLevelColumnRenderer(
  tableColumns: DataTableBaseColumn[],
  isEdit: Ref<boolean>
) {

  tableColumns.forEach(column => {
    if (column.key === 'langjiMean') {
      column.render = (rowData, rowIndex) => {
        rowData.langjiMean = getWaveLevelByHeight(rowData.swhMean as string)
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>{rowData.langjiMean}</span>
          </div>
        )
      }
    }
  })
  return tableColumns
}

/**
 * 判断海区和城市是否应该被合并
 * @param arg0
 * @param arg1
 */
function isSameForecastArea(
  arg0: Record<string, string | null>,
  arg1: Record<string, string | null>
): boolean {
  return (
    arg0.swhInterval === arg1.swhInterval &&
    arg0.mwpMean === arg1.mwpMean &&
    arg0.langjiInterval === arg1.langjiInterval
  )
}

/**
 * 根据浪高区间聚合表格数据
 * @param tableData
 */
export function unionByWaveHeight(
  tableData: Record<string, string | null>[]
): Record<string, string | null>[] {
  const arr: Record<string, string | null>[] = []
  tableData.forEach((row, index) => {
    const find = arr.find(item => isSameForecastArea(item, row))
    if (!find) {
      Reflect.set(row, 'name', row.name)
      arr.push(row)
    } else {
      find.name += `,${row.name}`
    }
  })
  return arr
}
