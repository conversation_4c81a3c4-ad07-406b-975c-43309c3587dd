import VectorSource from 'ol/source/Vector'
import { SimpleGeometry } from 'ol/geom'
import VectorLayer from 'ol/layer/Vector'
import { Fill, Stroke, Style, Text } from 'ol/style'
import { useSegmentInject } from './useSegmentInject'
import { onMounted, onUnmounted, Ref, watch } from 'vue'
import { DragBox, Select } from 'ol/interaction'
import Map from 'ol/Map'
import { ISegmentOptions, Tool } from '../types'
import { SelectEvent } from 'ol/interaction/Select'
import { Feature } from 'ol'
import { GeoJSON } from 'ol/format'
import Collection from 'ol/Collection.js'
import { platformModifierKeyOnly } from 'ol/events/condition'
import { DragBoxEvent } from 'ol/interaction/DragBox'
import { getWidth } from 'ol/extent'

export function useSelectVector(opt: { segmentOptions: Ref<ISegmentOptions> }) {
  const getMap = useSegmentInject('getMap').inject()
  if (!getMap) {
    throw new Error('注入失败')
  }

  const vectorSource = new VectorSource<SimpleGeometry>()
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: SelectManager.defaultSegmentStyle()
  })

  let selectManager: SelectManager

  onMounted(() => {
    loadSegmentData({ vectorSource })
    getMap(map => {
      // TODO: DEBUG
      Reflect.set(window, 'map', map)
      Reflect.set(window, 'vecSource', vectorSource)
      selectManager = new SelectManager({
        map,
        vectorLayer,
        vectorSource,
        ...opt
      })
      map.addLayer(vectorLayer)
    })
  })

  onUnmounted(() => {
    getMap(map => {
      map.removeLayer(vectorLayer)
    })
  })

  watch(
    () => opt.segmentOptions.value.currentTool,
    (value, oldValue) => {
      if (oldValue === Tool.SELECT && value === Tool.NONE) {
        selectManager.close()
      } else if (oldValue === Tool.NONE && value === Tool.SELECT) {
        selectManager.start()
      }
    }
  )

  return {
    vectorSource,
    vectorLayer,
    clearSelectStyle: () => {
      selectManager.clearSelectStyle()
    }
  }
}

/**
 * 加载岸段数据
 * @param opt
 */
async function loadSegmentData(opt: {
  vectorSource: VectorSource<SimpleGeometry>
}) {
  opt.vectorSource.clear()
  const json = await (
    await fetch(
      // TODO: 硬编码
      config.onlyOfficeServerUrl + '/qixiang/Data/ZPEIYQ/map/buffer.json'
    )
  ).json()
  const featuresJson = new GeoJSON().readFeatures(
    json
  ) as Feature<SimpleGeometry>[]
  featuresJson.forEach(item => {
    opt.vectorSource.addFeature(item)
  })
}

class SelectManager {
  protected select: Select | null = null
  dragBox: DragBox | null = null

  constructor(
    protected opt: {
      map: Map
      vectorSource: VectorSource<SimpleGeometry>
      vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
      segmentOptions: Ref<ISegmentOptions>
    }
  ) {}

  start() {
    this.initSelect()
    this.initDragBox()
  }

  initSelect() {
    this.select = new Select({
      layers: [this.opt.vectorLayer]
    })
    this.select.setActive(true)
    this.opt.map.addInteraction(this.select)
    this.select.on('select', this.onSelectEnd.bind(this))
  }

  initDragBox() {
    this.dragBox = new DragBox({
      condition: platformModifierKeyOnly
    })
    this.opt.map.addInteraction(this.dragBox)
    this.dragBox.on('boxend', this.onDragBoxEnd.bind(this))
  }

  onSelectEnd(e: SelectEvent) {
    const selected = e.selected
    this.setColor(selected)
  }

  setColor(features: Feature[]) {
    this.close()
    features.forEach((feature: Feature) => {
      feature.setStyle(this.selectedSegmentStyle())
    })
    this.start()

  }

  /**
   * box选择结束
   * @see {https://openlayers.org/en/latest/examples/box-selection.html} Box Selection
   * @param e
   */
  onDragBoxEnd(e: DragBoxEvent) {
    if (!this.dragBox || !this.select) {
      return
    }
    const selectedFeatures = this.select.getFeatures()
    const boxExtent = this.dragBox.getGeometry().getExtent()

    // if the extent crosses the antimeridian process each world separately
    const worldExtent = this.opt.map.getView().getProjection().getExtent()
    const worldWidth = getWidth(worldExtent)
    const startWorld = Math.floor((boxExtent[0] - worldExtent[0]) / worldWidth)
    const endWorld = Math.floor((boxExtent[2] - worldExtent[0]) / worldWidth)

    for (let world = startWorld; world <= endWorld; ++world) {
      const left = Math.max(boxExtent[0] - world * worldWidth, worldExtent[0])
      const right = Math.min(boxExtent[2] - world * worldWidth, worldExtent[2])
      const extent = [left, boxExtent[1], right, boxExtent[3]]

      const boxFeatures = this.opt.vectorSource
        .getFeaturesInExtent(extent)
        .filter(
          feature =>
            !selectedFeatures.getArray().includes(feature) &&
            feature.getGeometry()?.intersectsExtent(extent)
        )

      const rotation = this.opt.map.getView().getRotation()
      const oblique = rotation % (Math.PI / 2) !== 0

      if (oblique) {
        const anchor = [0, 0]
        const geometry = this.dragBox.getGeometry().clone()
        geometry.translate(-world * worldWidth, 0)
        geometry.rotate(-rotation, anchor)
        const extent = geometry.getExtent()
        boxFeatures.forEach(feature => {
          const geom = feature.getGeometry()
          if (!geom) {
            return
          }
          const geometry = geom.clone()
          geometry.rotate(-rotation, anchor)
          if (geometry.intersectsExtent(extent)) {
            selectedFeatures.push(feature)
          }
        })
      } else {
        selectedFeatures.extend(boxFeatures)
        this.setColor(selectedFeatures.getArray())
      }
    }
  }

  close() {
    this.select && this.opt.map.removeInteraction(this.select)
    this.dragBox && this.opt.map.removeInteraction(this.dragBox)
    this.select = null
    this.dragBox = null
  }

  /**
   * 未选中时岸段的样式
   */
  static defaultSegmentStyle() {
    return new Style({
      fill: new Fill({
        color: 'rgba(0,170,235,0.1)'
      }),
      stroke: new Stroke({
        color: '#000000',
        width: 1
      })
    })
  }

  /**
   * 选中的岸段样式
   */
  selectedSegmentStyle() {
    return new Style({
      fill: new Fill({
        color: this.opt.segmentOptions.value.colorItem.color
      }),
      text: new Text({
        text: this.opt.segmentOptions.value.colorItem.level,
        font: 'bold 20px Calibri,sans-serif',
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        })
      })
    })
  }

  /**
   * 清空已经赋予颜色的要素样式
   */
  clearSelectStyle() {
    this.opt.vectorSource.getFeatures().forEach((feature: Feature) => {
      feature.setStyle()
    })
  }
}
