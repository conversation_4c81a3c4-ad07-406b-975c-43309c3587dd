package cn.piesat.data.making.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数值处理类型
 */
@AllArgsConstructor
@Getter
public enum ValueHandle {

    /**
     * 最小值
     */
    MIN("min", "最小值"),
    /**
     * 最大值
     */
    MAX("max", "最大值"),
    /**
     * 均值
     */
    MEAN("mean", "均值"),
    /**
     * 区间值
     */
    INTERVAL("interval", "区间值");

    private String value;
    private String desc;
}
