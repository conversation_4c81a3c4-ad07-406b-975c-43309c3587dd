package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站分钟数据-每分钟实时资料-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_minute_sq_o")
public class OceanStationMinuteSqO implements Serializable {

    private static final long serialVersionUID = -67030029736403299L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 监测日期
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 水温
     **/
    @JsonProperty("water_temperature")
    @TableField("water_temperature")
    private String waterTemperature;
    /**
     * 水温质量符
     **/
    @JsonProperty("water_temperature_Qc2")
    @TableField("water_temperature_qc2")
    private Integer waterTemperatureQc2;
    /**
     * 盐度
     **/
    @JsonProperty("salinity")
    @TableField("salinity")
    private String salinity;
    /**
     * 盐度质量符
     **/
    @JsonProperty("salinity_Qc2")
    @TableField("salinity_qc2")
    private Integer salinityQc2;
    /**
     * 潮位
     **/
    @JsonProperty("tidal_level")
    @TableField("tidal_level")
    private String tidalLevel;
    /**
     * 潮位质量符
     **/
    @JsonProperty("tidal_level_Qc2")
    @TableField("tidal_level_qc2")
    private Integer tidalLevelQc2;
    /**
     * 气温
     **/
    @JsonProperty("air_temperature")
    @TableField("air_temperature")
    private String airTemperature;
    /**
     * 气温质量符
     **/
    @JsonProperty("air_temperature_Qc2")
    @TableField("air_temperature_qc2")
    private Integer airTemperatureQc2;
    /**
     * 气压
     **/
    @JsonProperty("atmospheric_pressure")
    @TableField("atmospheric_pressure")
    private String atmosphericPressure;
    /**
     * 气压质量符
     **/
    @JsonProperty("atmospheric_pressure_Qc2")
    @TableField("atmospheric_pressure_qc2")
    private Integer atmosphericPressureQc2;
    /**
     * 湿度
     **/
    @JsonProperty("humidity")
    @TableField("humidity")
    private String humidity;
    /**
     * 湿度质量符
     **/
    @JsonProperty("humidity_Qc2")
    @TableField("humidity_qc2")
    private Integer humidityQc2;
    /**
     * 降水量20-08
     **/
    @JsonProperty("precipitation_20_08")
    @TableField("precipitation_20_08")
    private String precipitation2008;
    /**
     * 降水量20-08质量符
     **/
    @JsonProperty("precipitation_20_08_Qc2")
    @TableField("precipitation_20_08_qc2")
    private Integer precipitation2008Qc2;
    /**
     * 降水量08-20
     **/
    @JsonProperty("precipitation_08_20")
    @TableField("precipitation_08_20")
    private String precipitation0820;
    /**
     * 降水量08-20质量符
     **/
    @JsonProperty("precipitation_08_20_Qc2")
    @TableField("precipitation_08_20_qc2")
    private Integer precipitation0820Qc2;
    /**
     * 阵风速
     **/
    @JsonProperty("gust_wind_speed")
    @TableField("gust_wind_speed")
    private String gustWindSpeed;
    /**
     * 阵风速质量符
     **/
    @JsonProperty("gust_wind_speed_Qc2")
    @TableField("gust_wind_speed_qc2")
    private Integer gustWindSpeedQc2;
    /**
     * 阵风向
     **/
    @JsonProperty("gust_wind_dir")
    @TableField("gust_wind_dir")
    private String gustWindDir;
    /**
     * 阵风向质量符
     **/
    @JsonProperty("gust_wind_dir_Qc2")
    @TableField("gust_wind_dir_qc2")
    private Integer gustWindDirQc2;
    /**
     * 平均风速
     **/
    @JsonProperty("avg_wind_speed")
    @TableField("avg_wind_speed")
    private String avgWindSpeed;
    /**
     * 平均风速质量符
     **/
    @JsonProperty("avg_wind_speed_Qc2")
    @TableField("avg_wind_speed_qc2")
    private Integer avgWindSpeedQc2;
    /**
     * 平均风向
     **/
    @JsonProperty("avg_wind_dir")
    @TableField("avg_wind_dir")
    private String avgWindDir;
    /**
     * 平均风向质量符
     **/
    @JsonProperty("avg_wind_dir_Qc2")
    @TableField("avg_wind_dir_qc2")
    private Integer avgWindDirQc2;
    /**
     * 最大风速
     **/
    @JsonProperty("max_wind_speed")
    @TableField("max_wind_speed")
    private String maxWindSpeed;
    /**
     * 最大风速质量符
     **/
    @JsonProperty("max_wind_speed_Qc2")
    @TableField("max_wind_speed_qc2")
    private Integer maxWindSpeedQc2;
    /**
     * 最大风风向
     **/
    @JsonProperty("max_wind_dir")
    @TableField("max_wind_dir")
    private String maxWindDir;
    /**
     * 最大风风向质量符
     **/
    @JsonProperty("max_wind_dir_Qc2")
    @TableField("max_wind_dir_qc2")
    private Integer maxWindDirQc2;
    /**
     * 最大风出现时间
     **/
    @JsonProperty("max_wind_time")
    @TableField("max_wind_time")
    private String maxWindTime;
    /**
     * 最大风出现时间质量符
     **/
    @JsonProperty("max_wind_time_Qc2")
    @TableField("max_wind_time_qc2")
    private Integer maxWindTimeQc2;
    /**
     * 极大风的风速
     **/
    @JsonProperty("extreme_wind_speed")
    @TableField("extreme_wind_speed")
    private String extremeWindSpeed;
    /**
     * 极大风的风速质量符
     **/
    @JsonProperty("extreme_wind_speed_Qc2")
    @TableField("extreme_wind_speed_qc2")
    private Integer extremeWindSpeedQc2;
    /**
     * 极大风的风向
     **/
    @JsonProperty("extreme_wind_dir")
    @TableField("extreme_wind_dir")
    private String extremeWindDir;
    /**
     * 极大风的风向质量符
     **/
    @JsonProperty("extreme_wind_dir_Qc2")
    @TableField("extreme_wind_dir_qc2")
    private Integer extremeWindDirQc2;
    /**
     * 极大风的出现的时间
     **/
    @JsonProperty("extreme_wind_time")
    @TableField("extreme_wind_time")
    private String extremeWindTime;
    /**
     * 极大风的出现的时间质量符
     **/
    @JsonProperty("extreme_wind_time_Qc2")
    @TableField("extreme_wind_time_qc2")
    private Integer extremeWindTimeQc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间 ZonedDateTime
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 能见度
     **/
    @JsonProperty("vb")
    @TableField("vb")
    private String vb;
    /**
     * 能见度质量符
     **/
    @JsonProperty("vb_Qc2")
    @TableField("vb_qc2")
    private Integer vbQc2;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
}



