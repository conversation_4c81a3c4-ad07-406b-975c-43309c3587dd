spring:
  server:
    port: 8080
  freemarker:
    #    request-context-attribute: req  #req访问request
    suffix: .ftl  #后缀名
    content-type: text/html
    enabled: true
    cache: false #缓存配置
#    template-loader-path: E:\template #模板加载路径 按需配置
    charset: UTF-8 #编码格式
    settings:
      number_format: '0.##'   #数字格式化，无小数点
  main:
    # 解决Bean重复定义问题
    allow-bean-definition-overriding: true
  datasource:
    name: ds
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: jdbc:postgresql://${DB_HOST:*************:5432}/forecast_making
      username: ${DB_USER:postgres}
      password: ${DB_PWD:P@stGres!96}
      # 初始化大小
      initial-size: 2
      # 最小
      min-idle: 2
      # 最大
      max-active: 10
      # 获取连接等待超时的时间
      max-wait: 60000
      # 间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 一个连接在池中最小的生存时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: "SELECT 'x'"
      test-on-borrow: false
      test-on-return: false
      test-while-idle: false
      # 关闭PSCache，并且指定每个连接上的PSCache的大小
      pool-prepared-statement: false
      max-pool-prepared-statement-per-connection-size: 1
      # 监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 合并多个DruidDataSource的监控
      use-global-data-source-stat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # mybatis plus
  mybatis-plus:
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    type-enums-package: cn.piesat.data.making.server.enums
    global-config:
      banner: false
      db-config:
        logic-delete-value: 1
        logic-not-delete-value: 0
    mapper-locations: classpath:/mappers/*Mapper.xml

  # 注册中心
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: Nacos1234!@#$
        enabled: ${NACOS_DISCOVERY:true}
  #        namespace: 7e55ba08-492e-4f80-924b-a8a7c1dbf91f

  #redisson
  redisson:
    mode: single
    singleServerConfig:
      #address: "${REDIS_HOST:rediss://*************:7001}"
      address: "${REDIS_HOST:redis://*************:6379}"
      password: ${REDIS_PWD:Redis1234!@#}
      #password: ${REDIS_PWD:Htht@123456}
      #username: ${REDIS_USERNAME:htht}
      database: 1
      sslEnableEndpointIdentification: false
      #sslTruststore: file:\D:\01.work\34.海南\redis.crt\redis-truststore.jks
      #sslTruststorePassword: "Htht@123456"
      sslKeystore: file:\D:\01.work\34.海南\redis.crt\redis.p12
      sslKeystorePassword: "Htht@123456"
  kafka:
    bootstrap-servers: *************:9092
    producer:
      retries: 0
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      enable-auto-commit: false
      auto-commit-interval: 100
      auto-offset-reset: earliest
      max-poll-records: 3
    properties:
      session:
        timeout:
          ms: 60000

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

server:
  tomcat:
    max-http-post-size: 50MB

#jwt
piesat:
  security:
    enable: false
    jwt-secret-key: dq2020001
    filte-rules:
      - /station/syncRelationStation/**-->anon
      - /oceanStation/syncOceanAndSmallBuoyData-->anon
      - /oceanStation/syncLargeBuoyData-->anon
      - /upload/onlyOfficeCallback/**-->anon
      - /forecastTask/schedule-->anon
      - /outside/alarm/**-->anon
      - /area/generateAreaJson-->anon
      - /**/**-->jwt
    multi: false
    ucenter-server-url: http://*************/api/pers
  datapush:
    search-url: http://**********:34080
  base-output-path: D:\06.data\template\
  make:
    fax: 68662241
    wave-alarm-template: E:\项目相关\海南海灾\海浪警报模板.docx
    surges-alarm-template: E:\项目相关\海南海灾\风暴潮警报模板.docx
    wave-message-template: E:\项目相关\海南海灾\海浪警报模板.docx
    surges-message-template: E:\项目相关\海南海灾\风暴潮警报模板.docx
    forecast-special-template: E:\项目相关\海南海灾\专项预报模板.docx
    areaJsonFilePath: E:\项目相关\海南海灾\area.json
    globalNationalBoundaryFilePath: E:\系统资源\Desktop\globalNationalBoundary.geojson
  serverId: https://*************:80/
  mailServerUrl: http://127.0.0.1:19000/hainan_manager/mail/sendMail
  faxServerUrl: http://*************:9081/fax-server/fax/send
  kkfileViewUrl: https://*************:80/kkfileView/
  faxTmpPath: /opt/tmp/
  localUrl: https://*************:80/api/data/
  alertXmlAuthorize: EDAC6C3E-F5E0-4922-89FD-D7E020074C4B
  countySuApiUrl: http://10.132.113.9:8088/upload
# 系统日志配置
logging:
  level:
    root: info
    cn.piesat: debug
    com.baomidou.mybatisplus: error
  path: logs
  config: classpath:logback-spring.xml


syslog:
  # 发送到kafka的topic名称
  topic: system-operate-log

schedule:
  cron:
    createForecastTask: 0 0 1 * * ?
    syncOceanAndSmallBuoyData: 0 0 3 * * ?
    syncLargeBuoyData: 0 0 2 * * ?
    createPublicTask: 0 0 1 * * ?
    syncPublishDate: 0 * * * * ?
  alg:
    server-url: http://*************/api/ncc
    script-path: /root/run/project/tctb_ncclip.sh
data:
  manage:
    search-url: http://**********:8088

graphicUploadUrl: "/qixiang/Data/ZPEIYQ/upload/"
excelUrl: "/qixiang/Data/ZPEIYQ/excel/"
graphicOutPutPath: "/qixiang/Data/ZPEIYQ/graphic/"

thrid:
  station-data-base-url: "http://**************:8089/service/api/hygcybsjycpglxt_"