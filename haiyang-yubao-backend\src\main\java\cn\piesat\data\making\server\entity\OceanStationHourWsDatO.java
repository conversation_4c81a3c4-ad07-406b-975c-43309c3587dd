package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站整点-从测量日界开始历时风速风向-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_hour_ws_dat_o")
public class OceanStationHourWsDatO implements Serializable {

    private static final long serialVersionUID = 469930575272348843L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 监测日期
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 21点风向测值
     **/
    @JsonProperty("wind_dir_21")
    @TableField("wind_dir_21")
    private String windDir21;
    /**
     * 21点风向测值质控符
     **/
    @JsonProperty("wind_dir_21_Qc2")
    @TableField("wind_dir_21_qc2")
    private Integer windDir21Qc2;
    /**
     * 21点风速测值
     **/
    @JsonProperty("wind_speed_21")
    @TableField("wind_speed_21")
    private String windSpeed21;
    /**
     * 21点风速测值质控符
     **/
    @JsonProperty("wind_speed_21_Qc2")
    @TableField("wind_speed_21_qc2")
    private Integer windSpeed21Qc2;
    /**
     * 22点风向测值
     **/
    @JsonProperty("wind_dir_22")
    @TableField("wind_dir_22")
    private String windDir22;
    /**
     * 22点风向测值质控符
     **/
    @JsonProperty("wind_dir_22_Qc2")
    @TableField("wind_dir_22_qc2")
    private Integer windDir22Qc2;
    /**
     * 22点风速测值
     **/
    @JsonProperty("wind_speed_22")
    @TableField("wind_speed_22")
    private String windSpeed22;
    /**
     * 22点风速测值质控符
     **/
    @JsonProperty("wind_speed_22_Qc2")
    @TableField("wind_speed_22_qc2")
    private Integer windSpeed22Qc2;
    /**
     * 23点风向测值
     **/
    @JsonProperty("wind_dir_23")
    @TableField("wind_dir_23")
    private String windDir23;
    /**
     * 23点风向测值质控符
     **/
    @JsonProperty("wind_dir_23_Qc2")
    @TableField("wind_dir_23_qc2")
    private Integer windDir23Qc2;
    /**
     * 23点风速测值
     **/
    @JsonProperty("wind_speed_23")
    @TableField("wind_speed_23")
    private String windSpeed23;
    /**
     * 23点风速测值质控符
     **/
    @JsonProperty("wind_speed_23_Qc2")
    @TableField("wind_speed_23_qc2")
    private Integer windSpeed23Qc2;
    /**
     * 00点风向测值
     **/
    @JsonProperty("wind_dir_00")
    @TableField("wind_dir_00")
    private String windDir00;
    /**
     * 00点风向测值质控符
     **/
    @JsonProperty("wind_dir_00_Qc2")
    @TableField("wind_dir_00_qc2")
    private Integer windDir00Qc2;
    /**
     * 00点风速测值
     **/
    @JsonProperty("wind_speed_00")
    @TableField("wind_speed_00")
    private String windSpeed00;
    /**
     * 00点风速测值质控符
     **/
    @JsonProperty("wind_speed_00_Qc2")
    @TableField("wind_speed_00_qc2")
    private Integer windSpeed00Qc2;
    /**
     * 01点风向测值
     **/
    @JsonProperty("wind_dir_01")
    @TableField("wind_dir_01")
    private String windDir01;
    /**
     * 01点风向测值质控符
     **/
    @JsonProperty("wind_dir_01_Qc2")
    @TableField("wind_dir_01_qc2")
    private Integer windDir01Qc2;
    /**
     * 01点风速测值
     **/
    @JsonProperty("wind_speed_01")
    @TableField("wind_speed_01")
    private String windSpeed01;
    /**
     * 01点风速测值质控符
     **/
    @JsonProperty("wind_speed_01_Qc2")
    @TableField("wind_speed_01_qc2")
    private Integer windSpeed01Qc2;
    /**
     * 02点风向测值
     **/
    @JsonProperty("wind_dir_02")
    @TableField("wind_dir_02")
    private String windDir02;
    /**
     * 02点风向测值质控符
     **/
    @JsonProperty("wind_dir_02_Qc2")
    @TableField("wind_dir_02_qc2")
    private Integer windDir02Qc2;
    /**
     * 02点风速测值
     **/
    @JsonProperty("wind_speed_02")
    @TableField("wind_speed_02")
    private String windSpeed02;
    /**
     * 02点风速测值质控符
     **/
    @JsonProperty("wind_speed_02_Qc2")
    @TableField("wind_speed_02_qc2")
    private Integer windSpeed02Qc2;
    /**
     * 03点风向测值
     **/
    @JsonProperty("wind_dir_03")
    @TableField("wind_dir_03")
    private String windDir03;
    /**
     * 03点风向测值质控符
     **/
    @JsonProperty("wind_dir_03_Qc2")
    @TableField("wind_dir_03_qc2")
    private Integer windDir03Qc2;
    /**
     * 03点风速测值
     **/
    @JsonProperty("wind_speed_03")
    @TableField("wind_speed_03")
    private String windSpeed03;
    /**
     * 03点风速测值质控符
     **/
    @JsonProperty("wind_speed_03_Qc2")
    @TableField("wind_speed_03_qc2")
    private Integer windSpeed03Qc2;
    /**
     * 04点风向测值
     **/
    @JsonProperty("wind_dir_04")
    @TableField("wind_dir_04")
    private String windDir04;
    /**
     * 04点风向测值质控符
     **/
    @JsonProperty("wind_dir_04_Qc2")
    @TableField("wind_dir_04_qc2")
    private Integer windDir04Qc2;
    /**
     * 04点风速测值
     **/
    @JsonProperty("wind_speed_04")
    @TableField("wind_speed_04")
    private String windSpeed04;
    /**
     * 04点风速测值质控符
     **/
    @JsonProperty("wind_speed_04_Qc2")
    @TableField("wind_speed_04_qc2")
    private Integer windSpeed04Qc2;
    /**
     * 05点风向测值
     **/
    @JsonProperty("wind_dir_05")
    @TableField("wind_dir_05")
    private String windDir05;
    /**
     * 05点风向测值质控符
     **/
    @JsonProperty("wind_dir_05_Qc2")
    @TableField("wind_dir_05_qc2")
    private Integer windDir05Qc2;
    /**
     * 05点风速测值
     **/
    @JsonProperty("wind_speed_05")
    @TableField("wind_speed_05")
    private String windSpeed05;
    /**
     * 05点风速测值质控符
     **/
    @JsonProperty("wind_speed_05_Qc2")
    @TableField("wind_speed_05_qc2")
    private Integer windSpeed05Qc2;
    /**
     * 06点风向测值
     **/
    @JsonProperty("wind_dir_06")
    @TableField("wind_dir_06")
    private String windDir06;
    /**
     * 06点风向测值质控符
     **/
    @JsonProperty("wind_dir_06_Qc2")
    @TableField("wind_dir_06_qc2")
    private Integer windDir06Qc2;
    /**
     * 06点风速测值
     **/
    @JsonProperty("wind_speed_06")
    @TableField("wind_speed_06")
    private String windSpeed06;
    /**
     * 06点风速测值质控符
     **/
    @JsonProperty("wind_speed_06_Qc2")
    @TableField("wind_speed_06_qc2")
    private Integer windSpeed06Qc2;
    /**
     * 07点风向测值
     **/
    @JsonProperty("wind_dir_07")
    @TableField("wind_dir_07")
    private String windDir07;
    /**
     * 07点风向测值质控符
     **/
    @JsonProperty("wind_dir_07_Qc2")
    @TableField("wind_dir_07_qc2")
    private Integer windDir07Qc2;
    /**
     * 07点风速测值
     **/
    @JsonProperty("wind_speed_07")
    @TableField("wind_speed_07")
    private String windSpeed07;
    /**
     * 07点风速测值质控符
     **/
    @JsonProperty("wind_speed_07_Qc2")
    @TableField("wind_speed_07_qc2")
    private Integer windSpeed07Qc2;
    /**
     * 08点风向测值
     **/
    @JsonProperty("wind_dir_08")
    @TableField("wind_dir_08")
    private String windDir08;
    /**
     * 08点风向测值质控符
     **/
    @JsonProperty("wind_dir_08_Qc2")
    @TableField("wind_dir_08_qc2")
    private Integer windDir08Qc2;
    /**
     * 08点风速测值
     **/
    @JsonProperty("wind_speed_08")
    @TableField("wind_speed_08")
    private String windSpeed08;
    /**
     * 08点风速测值质控符
     **/
    @JsonProperty("wind_speed_08_Qc2")
    @TableField("wind_speed_08_qc2")
    private Integer windSpeed08Qc2;
    /**
     * 09点风向测值
     **/
    @JsonProperty("wind_dir_09")
    @TableField("wind_dir_09")
    private String windDir09;
    /**
     * 09点风向测值质控符
     **/
    @JsonProperty("wind_dir_09_Qc2")
    @TableField("wind_dir_09_qc2")
    private Integer windDir09Qc2;
    /**
     * 09点风速测值
     **/
    @JsonProperty("wind_speed_09")
    @TableField("wind_speed_09")
    private String windSpeed09;
    /**
     * 09点风速测值质控符
     **/
    @JsonProperty("wind_speed_09_Qc2")
    @TableField("wind_speed_09_qc2")
    private Integer windSpeed09Qc2;
    /**
     * 10点风向测值
     **/
    @JsonProperty("wind_dir_10")
    @TableField("wind_dir_10")
    private String windDir10;
    /**
     * 10点风向测值质控符
     **/
    @JsonProperty("wind_dir_10_Qc2")
    @TableField("wind_dir_10_qc2")
    private Integer windDir10Qc2;
    /**
     * 10点风速测值
     **/
    @JsonProperty("wind_speed_10")
    @TableField("wind_speed_10")
    private String windSpeed10;
    /**
     * 10点风速测值质控符
     **/
    @JsonProperty("wind_speed_10_Qc2")
    @TableField("wind_speed_10_qc2")
    private Integer windSpeed10Qc2;
    /**
     * 11点风向测值
     **/
    @JsonProperty("wind_dir_11")
    @TableField("wind_dir_11")
    private String windDir11;
    /**
     * 11点风向测值质控符
     **/
    @JsonProperty("wind_dir_11_Qc2")
    @TableField("wind_dir_11_qc2")
    private Integer windDir11Qc2;
    /**
     * 11点风速测值
     **/
    @JsonProperty("wind_speed_11")
    @TableField("wind_speed_11")
    private String windSpeed11;
    /**
     * 11点风速测值质控符
     **/
    @JsonProperty("wind_speed_11_Qc2")
    @TableField("wind_speed_11_qc2")
    private Integer windSpeed11Qc2;
    /**
     * 12点风向测值
     **/
    @JsonProperty("wind_dir_12")
    @TableField("wind_dir_12")
    private String windDir12;
    /**
     * 12点风向测值质控符
     **/
    @JsonProperty("wind_dir_12_Qc2")
    @TableField("wind_dir_12_qc2")
    private Integer windDir12Qc2;
    /**
     * 12点风速测值
     **/
    @JsonProperty("wind_speed_12")
    @TableField("wind_speed_12")
    private String windSpeed12;
    /**
     * 12点风速测值质控符
     **/
    @JsonProperty("wind_speed_12_Qc2")
    @TableField("wind_speed_12_qc2")
    private Integer windSpeed12Qc2;
    /**
     * 13点风向测值
     **/
    @JsonProperty("wind_dir_13")
    @TableField("wind_dir_13")
    private String windDir13;
    /**
     * 13点风向测值质控符
     **/
    @JsonProperty("wind_dir_13_Qc2")
    @TableField("wind_dir_13_qc2")
    private Integer windDir13Qc2;
    /**
     * 13点风速测值
     **/
    @JsonProperty("wind_speed_13")
    @TableField("wind_speed_13")
    private String windSpeed13;
    /**
     * 13点风速测值质控符
     **/
    @JsonProperty("wind_speed_13_Qc2")
    @TableField("wind_speed_13_qc2")
    private Integer windSpeed13Qc2;
    /**
     * 14点风向测值
     **/
    @JsonProperty("wind_dir_14")
    @TableField("wind_dir_14")
    private String windDir14;
    /**
     * 14点风向测值质控符
     **/
    @JsonProperty("wind_dir_14_Qc2")
    @TableField("wind_dir_14_qc2")
    private Integer windDir14Qc2;
    /**
     * 14点风速测值
     **/
    @JsonProperty("wind_speed_14")
    @TableField("wind_speed_14")
    private String windSpeed14;
    /**
     * 14点风速测值质控符
     **/
    @JsonProperty("wind_speed_14_Qc2")
    @TableField("wind_speed_14_qc2")
    private Integer windSpeed14Qc2;
    /**
     * 15点风向测值
     **/
    @JsonProperty("wind_dir_15")
    @TableField("wind_dir_15")
    private String windDir15;
    /**
     * 15点风向测值质控符
     **/
    @JsonProperty("wind_dir_15_Qc2")
    @TableField("wind_dir_15_qc2")
    private Integer windDir15Qc2;
    /**
     * 15点风速测值
     **/
    @JsonProperty("wind_speed_15")
    @TableField("wind_speed_15")
    private String windSpeed15;
    /**
     * 15点风速测值质控符
     **/
    @JsonProperty("wind_speed_15_Qc2")
    @TableField("wind_speed_15_qc2")
    private Integer windSpeed15Qc2;
    /**
     * 16点风向测值
     **/
    @JsonProperty("wind_dir_16")
    @TableField("wind_dir_16")
    private String windDir16;
    /**
     * 16点风向测值质控符
     **/
    @JsonProperty("wind_dir_16_Qc2")
    @TableField("wind_dir_16_qc2")
    private Integer windDir16Qc2;
    /**
     * 16点风速测值
     **/
    @JsonProperty("wind_speed_16")
    @TableField("wind_speed_16")
    private String windSpeed16;
    /**
     * 16点风速测值质控符
     **/
    @JsonProperty("wind_speed_16_Qc2")
    @TableField("wind_speed_16_qc2")
    private Integer windSpeed16Qc2;
    /**
     * 17点风向测值
     **/
    @JsonProperty("wind_dir_17")
    @TableField("wind_dir_17")
    private String windDir17;
    /**
     * 17点风向测值质控符
     **/
    @JsonProperty("wind_dir_17_Qc2")
    @TableField("wind_dir_17_qc2")
    private Integer windDir17Qc2;
    /**
     * 17点风速测值
     **/
    @JsonProperty("wind_speed_17")
    @TableField("wind_speed_17")
    private String windSpeed17;
    /**
     * 17点风速测值质控符
     **/
    @JsonProperty("wind_speed_17_Qc2")
    @TableField("wind_speed_17_qc2")
    private Integer windSpeed17Qc2;
    /**
     * 18点风向测值
     **/
    @JsonProperty("wind_dir_18")
    @TableField("wind_dir_18")
    private String windDir18;
    /**
     * 18点风向测值质控符
     **/
    @JsonProperty("wind_dir_18_Qc2")
    @TableField("wind_dir_18_qc2")
    private Integer windDir18Qc2;
    /**
     * 18点风速测值
     **/
    @JsonProperty("wind_speed_18")
    @TableField("wind_speed_18")
    private String windSpeed18;
    /**
     * 18点风速测值质控符
     **/
    @JsonProperty("wind_speed_18_Qc2")
    @TableField("wind_speed_18_qc2")
    private Integer windSpeed18Qc2;
    /**
     * 19点风向测值
     **/
    @JsonProperty("wind_dir_19")
    @TableField("wind_dir_19")
    private String windDir19;
    /**
     * 19点风向测值质控符
     **/
    @JsonProperty("wind_dir_19_Qc2")
    @TableField("wind_dir_19_qc2")
    private Integer windDir19Qc2;
    /**
     * 19点风速测值
     **/
    @JsonProperty("wind_speed_19")
    @TableField("wind_speed_19")
    private String windSpeed19;
    /**
     * 19点风速测值质控符
     **/
    @JsonProperty("wind_speed_19_Qc2")
    @TableField("wind_speed_19_qc2")
    private Integer windSpeed19Qc2;
    /**
     * 20点风向测值
     **/
    @JsonProperty("wind_dir_20")
    @TableField("wind_dir_20")
    private String windDir20;
    /**
     * 20点风向测值质控符
     **/
    @JsonProperty("wind_dir_20_Qc2")
    @TableField("wind_dir_20_qc2")
    private Integer windDir20Qc2;
    /**
     * 20点风速测值
     **/
    @JsonProperty("wind_speed_20")
    @TableField("wind_speed_20")
    private String windSpeed20;
    /**
     * 20点风速测值质控符
     **/
    @JsonProperty("wind_speed_20_Qc2")
    @TableField("wind_speed_20_qc2")
    private Integer windSpeed20Qc2;
    /**
     * 20-02极大风对应的风向
     **/
    @JsonProperty("wind_dir_20_02")
    @TableField("wind_dir_20_02")
    private String windDir2002;
    /**
     * 20-02极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_20_02_Qc2")
    @TableField("wind_dir_20_02_qc2")
    private Integer windDir2002Qc2;
    /**
     * 20-02极大风对应的风速
     **/
    @JsonProperty("wind_speed_20_02")
    @TableField("wind_speed_20_02")
    private String windSpeed2002;
    /**
     * 20-02极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_20_02_Qc2")
    @TableField("wind_speed_20_02_qc2")
    private Integer windSpeed2002Qc2;
    /**
     * 02-08极大风对应的风向
     **/
    @JsonProperty("wind_dir_02_08")
    @TableField("wind_dir_02_08")
    private String windDir0208;
    /**
     * 02-08极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_02_08_Qc2")
    @TableField("wind_dir_02_08_qc2")
    private Integer windDir0208Qc2;
    /**
     * 02-08极大风对应的风速
     **/
    @JsonProperty("wind_speed_02_08")
    @TableField("wind_speed_02_08")
    private String windSpeed0208;
    /**
     * 02-08极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_02_08_Qc2")
    @TableField("wind_speed_02_08_qc2")
    private Integer windSpeed0208Qc2;
    /**
     * 08-14极大风对应的风向
     **/
    @JsonProperty("wind_dir_08_14")
    @TableField("wind_dir_08_14")
    private String windDir0814;
    /**
     * 08-14极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_08_14_Qc2")
    @TableField("wind_dir_08_14_qc2")
    private Integer windDir0814Qc2;
    /**
     * 08-14极大风对应的风速
     **/
    @JsonProperty("wind_speed_08_14")
    @TableField("wind_speed_08_14")
    private String windSpeed0814;
    /**
     * 08-14极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_08_14_Qc2")
    @TableField("wind_speed_08_14_qc2")
    private Integer windSpeed0814Qc2;
    /**
     * 14-20极大风对应的风向
     **/
    @JsonProperty("wind_dir_14_20")
    @TableField("wind_dir_14_20")
    private String windDir1420;
    /**
     * 14-20极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_14_20_Qc2")
    @TableField("wind_dir_14_20_qc2")
    private Integer windDir1420Qc2;
    /**
     * 14-20极大风对应的风速
     **/
    @JsonProperty("wind_speed_14_20")
    @TableField("wind_speed_14_20")
    private String windSpeed1420;
    /**
     * 14-20极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_14_20_Qc2")
    @TableField("wind_speed_14_20_qc2")
    private Integer windSpeed1420Qc2;
    /**
     * 最大风的风向
     **/
    @JsonProperty("max_wind_dir")
    @TableField("max_wind_dir")
    private String maxWindDir;
    /**
     * 最大风的风向质控符
     **/
    @JsonProperty("max_wind_dir_Qc2")
    @TableField("max_wind_dir_qc2")
    private Integer maxWindDirQc2;
    /**
     * 最大风的风速
     **/
    @JsonProperty("max_wind_speed")
    @TableField("max_wind_speed")
    private String maxWindSpeed;
    /**
     * 最大风的风速质控符
     **/
    @JsonProperty("max_wind_speed_Qc2")
    @TableField("max_wind_speed_qc2")
    private Integer maxWindSpeedQc2;
    /**
     * 最大风的出现的时间
     **/
    @JsonProperty("max_wind_time")
    @TableField("max_wind_time")
    private String maxWindTime;
    /**
     * 最大风的出现的时间质控符
     **/
    @JsonProperty("max_wind_time_Qc2")
    @TableField("max_wind_time_qc2")
    private Integer maxWindTimeQc2;
    /**
     * 极大风的风向
     **/
    @JsonProperty("extreme_wind_dir")
    @TableField("extreme_wind_dir")
    private String extremeWindDir;
    /**
     * 极大风的风向质控符
     **/
    @JsonProperty("extreme_wind_dir_Qc2")
    @TableField("extreme_wind_dir_qc2")
    private Integer extremeWindDirQc2;
    /**
     * 极大风的风速
     **/
    @JsonProperty("extreme_wind_speed")
    @TableField("extreme_wind_speed")
    private String extremeWindSpeed;
    /**
     * 极大风的风速质控符
     **/
    @JsonProperty("extreme_wind_speed_Qc2")
    @TableField("extreme_wind_speed_qc2")
    private Integer extremeWindSpeedQc2;
    /**
     * 极大风的出现的时间
     **/
    @JsonProperty("extreme_wind_time")
    @TableField("extreme_wind_time")
    private String extremeWindTime;
    /**
     * 极大风的出现的时间质控符
     **/
    @JsonProperty("extreme_wind_time_Qc2")
    @TableField("extreme_wind_time_qc2")
    private Integer extremeWindTimeQc2;
    /**
     * 大于17m/s风速出现的起止时间1
     **/
    @JsonProperty("wind_speeds_gt17_1")
    @TableField("wind_speeds_gt17_1")
    private String windSpeedsGt171;
    /**
     * 大于17m/s风速出现的起止时间1质控符
     **/
    @JsonProperty("wind_speeds_gt17_1_Qc2")
    @TableField("wind_speeds_gt17_1_qc2")
    private Integer windSpeedsGt171Qc2;
    /**
     * 大于17m/s风速出现的起止时间2
     **/
    @JsonProperty("wind_speeds_gt17_2")
    @TableField("wind_speeds_gt17_2")
    private String windSpeedsGt172;
    /**
     * 大于17m/s风速出现的起止时间2质控符
     **/
    @JsonProperty("wind_speeds_gt17_2_Qc2")
    @TableField("wind_speeds_gt17_2_qc2")
    private Integer windSpeedsGt172Qc2;
    /**
     * 大于17m/s风速出现的起止时间3
     **/
    @JsonProperty("wind_speeds_gt17_3")
    @TableField("wind_speeds_gt17_3")
    private String windSpeedsGt173;
    /**
     * 大于17m/s风速出现的起止时间3质控符
     **/
    @JsonProperty("wind_speeds_gt17_3_Qc2")
    @TableField("wind_speeds_gt17_3_qc2")
    private Integer windSpeedsGt173Qc2;
    /**
     * 大于17m/s风速出现的起止时间4
     **/
    @JsonProperty("wind_speeds_gt17_4")
    @TableField("wind_speeds_gt17_4")
    private String windSpeedsGt174;
    /**
     * 大于17m/s风速出现的起止时间4质控符
     **/
    @JsonProperty("wind_speeds_gt17_4_Qc2")
    @TableField("wind_speeds_gt17_4_qc2")
    private Integer windSpeedsGt174Qc2;
    /**
     * 大于17m/s风速出现的起止时间5
     **/
    @JsonProperty("wind_speeds_gt17_5")
    @TableField("wind_speeds_gt17_5")
    private String windSpeedsGt175;
    /**
     * 大于17m/s风速出现的起止时间5质控符
     **/
    @JsonProperty("wind_speeds_gt17_5_Qc2")
    @TableField("wind_speeds_gt17_5_qc2")
    private Integer windSpeedsGt175Qc2;
    /**
     * 大于17m/s风速出现的起止时间6
     **/
    @JsonProperty("wind_speeds_gt17_6")
    @TableField("wind_speeds_gt17_6")
    private String windSpeedsGt176;
    /**
     * 大于17m/s风速出现的起止时间6质控符
     **/
    @JsonProperty("wind_speeds_gt17_6_Qc2")
    @TableField("wind_speeds_gt17_6_qc2")
    private Integer windSpeedsGt176Qc2;
    /**
     * 大于17m/s风速出现的起止时间7
     **/
    @JsonProperty("wind_speeds_gt17_7")
    @TableField("wind_speeds_gt17_7")
    private String windSpeedsGt177;
    /**
     * 大于17m/s风速出现的起止时间7质控符
     **/
    @JsonProperty("wind_speeds_gt17_7_Qc2")
    @TableField("wind_speeds_gt17_7_qc2")
    private Integer windSpeedsGt177Qc2;
    /**
     * 大于17m/s风速出现的起止时间8
     **/
    @JsonProperty("wind_speeds_gt17_8")
    @TableField("wind_speeds_gt17_8")
    private String windSpeedsGt178;
    /**
     * 大于17m/s风速出现的起止时间8质控符
     **/
    @JsonProperty("wind_speeds_gt17_8_Qc2")
    @TableField("wind_speeds_gt17_8_qc2")
    private Integer windSpeedsGt178Qc2;
    /**
     * 大于17m/s风速出现的起止时间9
     **/
    @JsonProperty("wind_speeds_gt17_9")
    @TableField("wind_speeds_gt17_9")
    private String windSpeedsGt179;
    /**
     * 大于17m/s风速出现的起止时间9质控符
     **/
    @JsonProperty("wind_speeds_gt17_9_Qc2")
    @TableField("wind_speeds_gt17_9_qc2")
    private Integer windSpeedsGt179Qc2;
    /**
     * 大于17m/s风速出现的起止时间10
     **/
    @JsonProperty("wind_speeds_gt17_10")
    @TableField("wind_speeds_gt17_10")
    private String windSpeedsGt1710;
    /**
     * 大于17m/s风速出现的起止时间10质控符
     **/
    @JsonProperty("wind_speeds_gt17_10_Qc2")
    @TableField("wind_speeds_gt17_10_qc2")
    private Integer windSpeedsGt1710Qc2;
    /**
     * 大于17m/s风速出现的起止时间11
     **/
    @JsonProperty("wind_speeds_gt17_11")
    @TableField("wind_speeds_gt17_11")
    private String windSpeedsGt1711;
    /**
     * 大于17m/s风速出现的起止时间11质控符
     **/
    @JsonProperty("wind_speeds_gt17_11_Qc2")
    @TableField("wind_speeds_gt17_11_qc2")
    private Integer windSpeedsGt1711Qc2;
    /**
     * 大于17m/s风速出现的起止时间12
     **/
    @JsonProperty("wind_speeds_gt17_12")
    @TableField("wind_speeds_gt17_12")
    private String windSpeedsGt1712;
    /**
     * 大于17m/s风速出现的起止时间12质控符
     **/
    @JsonProperty("wind_speeds_gt17_12_Qc2")
    @TableField("wind_speeds_gt17_12_qc2")
    private Integer windSpeedsGt1712Qc2;
    /**
     * 大于17m/s风速出现的起止时间13
     **/
    @JsonProperty("wind_speeds_gt17_13")
    @TableField("wind_speeds_gt17_13")
    private String windSpeedsGt1713;
    /**
     * 大于17m/s风速出现的起止时间13质控符
     **/
    @JsonProperty("wind_speeds_gt17_13_Qc2")
    @TableField("wind_speeds_gt17_13_qc2")
    private Integer windSpeedsGt1713Qc2;
    /**
     * 大于17m/s风速出现的起止时间1
     **/
    @JsonProperty("wind_speeds_gt17_14")
    @TableField("wind_speeds_gt17_14")
    private String windSpeedsGt1714;
    /**
     * 大于17m/s风速出现的起止时间14质控符
     **/
    @JsonProperty("wind_speeds_gt17_14_Qc2")
    @TableField("wind_speeds_gt17_14_qc2")
    private Integer windSpeedsGt1714Qc2;
    /**
     * 大于17m/s风速出现的起止时间15
     **/
    @JsonProperty("wind_speeds_gt17_15")
    @TableField("wind_speeds_gt17_15")
    private String windSpeedsGt1715;
    /**
     * 大于17m/s风速出现的起止时间15质控符
     **/
    @JsonProperty("wind_speeds_gt17_15_Qc2")
    @TableField("wind_speeds_gt17_15_qc2")
    private Integer windSpeedsGt1715Qc2;
    /**
     * 大于17m/s风速出现的起止时间16
     **/
    @JsonProperty("wind_speeds_gt17_16")
    @TableField("wind_speeds_gt17_16")
    private String windSpeedsGt1716;
    /**
     * 大于17m/s风速出现的起止时间16质控符
     **/
    @JsonProperty("wind_speeds_gt17_16_Qc2")
    @TableField("wind_speeds_gt17_16_qc2")
    private Integer windSpeedsGt1716Qc2;
    /**
     * 大于17m/s风速出现的起止时间17
     **/
    @JsonProperty("wind_speeds_gt17_17")
    @TableField("wind_speeds_gt17_17")
    private String windSpeedsGt1717;
    /**
     * 大于17m/s风速出现的起止时间17质控符
     **/
    @JsonProperty("wind_speeds_gt17_17_Qc2")
    @TableField("wind_speeds_gt17_17_qc2")
    private Integer windSpeedsGt1717Qc2;
    /**
     * 大于17m/s风速出现的起止时间18
     **/
    @JsonProperty("wind_speeds_gt17_18")
    @TableField("wind_speeds_gt17_18")
    private String windSpeedsGt1718;
    /**
     * 大于17m/s风速出现的起止时间18质控符
     **/
    @JsonProperty("wind_speeds_gt17_18_Qc2")
    @TableField("wind_speeds_gt17_18_qc2")
    private Integer windSpeedsGt1718Qc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
}



