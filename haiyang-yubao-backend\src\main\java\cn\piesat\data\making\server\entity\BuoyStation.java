package cn.piesat.data.making.server.entity;

import cn.piesat.data.making.server.config.PgGeometryTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 浮标站表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_buoy_station_b")
public class BuoyStation implements Serializable {

    private static final long serialVersionUID = -20403206691977656L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     **/
    @TableField("code")
    private String code;
    /**
     * 名称
     **/
    @TableField("name")
    private String name;
    /**
     * 中文名称
     **/
    @TableField("cn_name")
    private String cnName;
    /**
     * 经度
     **/
    @TableField("lon")
    private Double lon;
    /**
     * 纬度
     **/
    @TableField("lat")
    private Double lat;
    /**
     * 位置
     **/
    @TableField(value = "location_geo", typeHandler = PgGeometryTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private String locationGeo;
    /**
     * 位置
     **/
    @TableField("location_json")
    private String locationJson;
}



