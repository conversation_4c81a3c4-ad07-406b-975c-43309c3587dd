!(function (e, t) {
  'object' == typeof exports && 'undefined' != typeof module
    ? (module.exports = t())
    : 'function' == typeof define && define.amd
    ? define(t)
    : ((e =
        'undefined' != typeof globalThis ? globalThis : e || self).ExportGif =
        t())
})(this, function () {
  'use strict'
  function e(e, t) {
    var n = Object.keys(e)
    if (Object.getOwnPropertySymbols) {
      var i = Object.getOwnPropertySymbols(e)
      t &&
        (i = i.filter(function (t) {
          return Object.getOwnPropertyDescriptor(e, t).enumerable
        })),
        n.push.apply(n, i)
    }
    return n
  }
  function t(t) {
    for (var n = 1; n < arguments.length; n++) {
      var i = null != arguments[n] ? arguments[n] : {}
      n % 2
        ? e(Object(i), !0).forEach(function (e) {
            s(t, e, i[e])
          })
        : Object.getOwnPropertyDescriptors
        ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(i))
        : e(Object(i)).forEach(function (e) {
            Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(i, e))
          })
    }
    return t
  }
  function n(e, t) {
    if (!(e instanceof t))
      throw new TypeError('Cannot call a class as a function')
  }
  function i(e, t) {
    for (var n = 0; n < t.length; n++) {
      var i = t[n]
      ;(i.enumerable = i.enumerable || !1),
        (i.configurable = !0),
        'value' in i && (i.writable = !0),
        Object.defineProperty(e, i.key, i)
    }
  }
  function r(e, t, n) {
    return (
      t && i(e.prototype, t),
      n && i(e, n),
      Object.defineProperty(e, 'prototype', { writable: !1 }),
      e
    )
  }
  function s(e, t, n) {
    return (
      t in e
        ? Object.defineProperty(e, t, {
            value: n,
            enumerable: !0,
            configurable: !0,
            writable: !0
          })
        : (e[t] = n),
      e
    )
  }
  function o(e) {
    return (
      (o = Object.setPrototypeOf
        ? Object.getPrototypeOf.bind()
        : function (e) {
            return e.__proto__ || Object.getPrototypeOf(e)
          }),
      o(e)
    )
  }
  function a(e, t) {
    return (
      (a = Object.setPrototypeOf
        ? Object.setPrototypeOf.bind()
        : function (e, t) {
            return (e.__proto__ = t), e
          }),
      a(e, t)
    )
  }
  function l(e) {
    if (void 0 === e)
      throw new ReferenceError(
        "this hasn't been initialised - super() hasn't been called"
      )
    return e
  }
  function h(e, t) {
    if (t && ('object' == typeof t || 'function' == typeof t)) return t
    if (void 0 !== t)
      throw new TypeError(
        'Derived constructors may only return object or undefined'
      )
    return l(e)
  }
  function c(e) {
    var t = (function () {
      if ('undefined' == typeof Reflect || !Reflect.construct) return !1
      if (Reflect.construct.sham) return !1
      if ('function' == typeof Proxy) return !0
      try {
        return (
          Boolean.prototype.valueOf.call(
            Reflect.construct(Boolean, [], function () {})
          ),
          !0
        )
      } catch (e) {
        return !1
      }
    })()
    return function () {
      var n,
        i = o(e)
      if (t) {
        var r = o(this).constructor
        n = Reflect.construct(i, arguments, r)
      } else n = i.apply(this, arguments)
      return h(this, n)
    }
  }
  function u(e, t) {
    return (function (e, t) {
      if (t.get) return t.get.call(e)
      return t.value
    })(e, d(e, t, 'get'))
  }
  function p(e, t, n) {
    return (
      (function (e, t, n) {
        if (t.set) t.set.call(e, n)
        else {
          if (!t.writable)
            throw new TypeError('attempted to set read only private field')
          t.value = n
        }
      })(e, d(e, t, 'set'), n),
      n
    )
  }
  function d(e, t, n) {
    if (!t.has(e))
      throw new TypeError(
        'attempted to ' + n + ' private field on non-instance'
      )
    return t.get(e)
  }
  function f(e, t, n) {
    !(function (e, t) {
      if (t.has(e))
        throw new TypeError(
          'Cannot initialize the same private elements twice on an object'
        )
    })(e, t),
      t.set(e, n)
  }
  function g(e) {
    throw new Error(
      'Could not dynamically require "' +
        e +
        '". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.'
    )
  }
  var v = { exports: {} }
  !(function (e, t) {
    e.exports = (function e(t, n, i) {
      function r(o, a) {
        if (!n[o]) {
          if (!t[o]) {
            if (!a && g) return g(o)
            if (s) return s(o, !0)
            var l = new Error("Cannot find module '" + o + "'")
            throw ((l.code = 'MODULE_NOT_FOUND'), l)
          }
          var h = (n[o] = { exports: {} })
          t[o][0].call(
            h.exports,
            function (e) {
              var n = t[o][1][e]
              return r(n || e)
            },
            h,
            h.exports,
            e,
            t,
            n,
            i
          )
        }
        return n[o].exports
      }
      for (var s = g, o = 0; o < i.length; o++) r(i[o])
      return r
    })(
      {
        1: [
          function (e, t, n) {
            function i() {
              ;(this._events = this._events || {}),
                (this._maxListeners = this._maxListeners || void 0)
            }
            function r(e) {
              return 'function' == typeof e
            }
            function s(e) {
              return 'number' == typeof e
            }
            function o(e) {
              return 'object' == typeof e && null !== e
            }
            function a(e) {
              return void 0 === e
            }
            ;(t.exports = i),
              (i.EventEmitter = i),
              (i.prototype._events = void 0),
              (i.prototype._maxListeners = void 0),
              (i.defaultMaxListeners = 10),
              (i.prototype.setMaxListeners = function (e) {
                if (!s(e) || e < 0 || isNaN(e))
                  throw TypeError('n must be a positive number')
                return (this._maxListeners = e), this
              }),
              (i.prototype.emit = function (e) {
                var t, n, i, s, l, h
                if (
                  (this._events || (this._events = {}),
                  'error' === e &&
                    (!this._events.error ||
                      (o(this._events.error) && !this._events.error.length)))
                ) {
                  if ((t = arguments[1]) instanceof Error) throw t
                  var c = new Error(
                    'Uncaught, unspecified "error" event. (' + t + ')'
                  )
                  throw ((c.context = t), c)
                }
                if (a((n = this._events[e]))) return !1
                if (r(n))
                  switch (arguments.length) {
                    case 1:
                      n.call(this)
                      break
                    case 2:
                      n.call(this, arguments[1])
                      break
                    case 3:
                      n.call(this, arguments[1], arguments[2])
                      break
                    default:
                      ;(s = Array.prototype.slice.call(arguments, 1)),
                        n.apply(this, s)
                  }
                else if (o(n))
                  for (
                    s = Array.prototype.slice.call(arguments, 1),
                      i = (h = n.slice()).length,
                      l = 0;
                    l < i;
                    l++
                  )
                    h[l].apply(this, s)
                return !0
              }),
              (i.prototype.addListener = function (e, t) {
                var n
                if (!r(t)) throw TypeError('listener must be a function')
                return (
                  this._events || (this._events = {}),
                  this._events.newListener &&
                    this.emit('newListener', e, r(t.listener) ? t.listener : t),
                  this._events[e]
                    ? o(this._events[e])
                      ? this._events[e].push(t)
                      : (this._events[e] = [this._events[e], t])
                    : (this._events[e] = t),
                  o(this._events[e]) &&
                    !this._events[e].warned &&
                    (n = a(this._maxListeners)
                      ? i.defaultMaxListeners
                      : this._maxListeners) &&
                    n > 0 &&
                    this._events[e].length > n &&
                    ((this._events[e].warned = !0),
                    console.error(
                      '(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.',
                      this._events[e].length
                    ),
                    'function' == typeof console.trace && console.trace()),
                  this
                )
              }),
              (i.prototype.on = i.prototype.addListener),
              (i.prototype.once = function (e, t) {
                if (!r(t)) throw TypeError('listener must be a function')
                var n = !1
                function i() {
                  this.removeListener(e, i),
                    n || ((n = !0), t.apply(this, arguments))
                }
                return (i.listener = t), this.on(e, i), this
              }),
              (i.prototype.removeListener = function (e, t) {
                var n, i, s, a
                if (!r(t)) throw TypeError('listener must be a function')
                if (!this._events || !this._events[e]) return this
                if (
                  ((s = (n = this._events[e]).length),
                  (i = -1),
                  n === t || (r(n.listener) && n.listener === t))
                )
                  delete this._events[e],
                    this._events.removeListener &&
                      this.emit('removeListener', e, t)
                else if (o(n)) {
                  for (a = s; a-- > 0; )
                    if (n[a] === t || (n[a].listener && n[a].listener === t)) {
                      i = a
                      break
                    }
                  if (i < 0) return this
                  1 === n.length
                    ? ((n.length = 0), delete this._events[e])
                    : n.splice(i, 1),
                    this._events.removeListener &&
                      this.emit('removeListener', e, t)
                }
                return this
              }),
              (i.prototype.removeAllListeners = function (e) {
                var t, n
                if (!this._events) return this
                if (!this._events.removeListener)
                  return (
                    0 === arguments.length
                      ? (this._events = {})
                      : this._events[e] && delete this._events[e],
                    this
                  )
                if (0 === arguments.length) {
                  for (t in this._events)
                    'removeListener' !== t && this.removeAllListeners(t)
                  return (
                    this.removeAllListeners('removeListener'),
                    (this._events = {}),
                    this
                  )
                }
                if (r((n = this._events[e]))) this.removeListener(e, n)
                else if (n)
                  for (; n.length; ) this.removeListener(e, n[n.length - 1])
                return delete this._events[e], this
              }),
              (i.prototype.listeners = function (e) {
                return this._events && this._events[e]
                  ? r(this._events[e])
                    ? [this._events[e]]
                    : this._events[e].slice()
                  : []
              }),
              (i.prototype.listenerCount = function (e) {
                if (this._events) {
                  var t = this._events[e]
                  if (r(t)) return 1
                  if (t) return t.length
                }
                return 0
              }),
              (i.listenerCount = function (e, t) {
                return e.listenerCount(t)
              })
          },
          {}
        ],
        2: [
          function (e, t, n) {
            var i, r, s, o, a
            ;(a = navigator.userAgent.toLowerCase()),
              (o = navigator.platform.toLowerCase()),
              (s =
                'ie' ===
                  (i = a.match(
                    /(opera|ie|firefox|chrome|version)[\s\/:]([\w\d\.]+)?.*?(safari|version[\s\/:]([\w\d\.]+)|$)/
                  ) || [null, 'unknown', 0])[1] && document.documentMode),
              ((r = {
                name: 'version' === i[1] ? i[3] : i[1],
                version:
                  s || parseFloat('opera' === i[1] && i[4] ? i[4] : i[2]),
                platform: {
                  name: a.match(/ip(?:ad|od|hone)/)
                    ? 'ios'
                    : (a.match(/(?:webos|android)/) ||
                        o.match(/mac|win|linux/) || ['other'])[0]
                }
              })[r.name] = !0),
              (r[r.name + parseInt(r.version, 10)] = !0),
              (r.platform[r.platform.name] = !0),
              (t.exports = r)
          },
          {}
        ],
        3: [
          function (e, t, n) {
            var i,
              r,
              s,
              o = function (e, t) {
                for (var n in t) a.call(t, n) && (e[n] = t[n])
                function i() {
                  this.constructor = e
                }
                return (
                  (i.prototype = t.prototype),
                  (e.prototype = new i()),
                  (e.__super__ = t.prototype),
                  e
                )
              },
              a = {}.hasOwnProperty,
              l =
                [].indexOf ||
                function (e) {
                  for (var t = 0, n = this.length; t < n; t++)
                    if (t in this && this[t] === e) return t
                  return -1
                },
              h = [].slice
            ;(i = e('events').EventEmitter),
              (s = e('./browser.coffee')),
              (r = (function (e) {
                var t, n
                function i(e) {
                  var n, i, r
                  for (i in ((this.running = !1),
                  (this.options = {}),
                  (this.frames = []),
                  (this.freeWorkers = []),
                  (this.activeWorkers = []),
                  this.setOptions(e),
                  t))
                    (r = t[i]), null == (n = this.options)[i] && (n[i] = r)
                }
                return (
                  o(i, e),
                  (t = {
                    workerScript: 'gif.worker.js',
                    workers: 2,
                    repeat: 0,
                    background: '#fff',
                    quality: 10,
                    width: null,
                    height: null,
                    transparent: null,
                    debug: !1,
                    dither: !1
                  }),
                  (n = { delay: 500, copy: !1 }),
                  (i.prototype.setOption = function (e, t) {
                    if (
                      ((this.options[e] = t),
                      null != this._canvas && ('width' === e || 'height' === e))
                    )
                      return (this._canvas[e] = t)
                  }),
                  (i.prototype.setOptions = function (e) {
                    var t, n, i
                    for (t in ((n = []), e))
                      a.call(e, t) && ((i = e[t]), n.push(this.setOption(t, i)))
                    return n
                  }),
                  (i.prototype.addFrame = function (e, t) {
                    var i, r
                    for (r in (null == t && (t = {}),
                    ((i = {}).transparent = this.options.transparent),
                    n))
                      i[r] = t[r] || n[r]
                    if (
                      (null == this.options.width &&
                        this.setOption('width', e.width),
                      null == this.options.height &&
                        this.setOption('height', e.height),
                      'undefined' != typeof ImageData &&
                        null !== ImageData &&
                        e instanceof ImageData)
                    )
                      i.data = e.data
                    else if (
                      ('undefined' != typeof CanvasRenderingContext2D &&
                        null !== CanvasRenderingContext2D &&
                        e instanceof CanvasRenderingContext2D) ||
                      ('undefined' != typeof WebGLRenderingContext &&
                        null !== WebGLRenderingContext &&
                        e instanceof WebGLRenderingContext)
                    )
                      t.copy
                        ? (i.data = this.getContextData(e))
                        : (i.context = e)
                    else {
                      if (null == e.childNodes) throw new Error('Invalid image')
                      t.copy ? (i.data = this.getImageData(e)) : (i.image = e)
                    }
                    return this.frames.push(i)
                  }),
                  (i.prototype.render = function () {
                    var e, t, n
                    if (this.running) throw new Error('Already running')
                    if (
                      null == this.options.width ||
                      null == this.options.height
                    )
                      throw new Error(
                        'Width and height must be set prior to rendering'
                      )
                    if (
                      ((this.running = !0),
                      (this.nextFrame = 0),
                      (this.finishedFrames = 0),
                      (this.imageParts = function () {
                        var e, t, n
                        for (
                          n = [], e = 0, t = this.frames.length;
                          0 <= t ? e < t : e > t;
                          0 <= t ? ++e : --e
                        )
                          n.push(null)
                        return n
                      }.call(this)),
                      (t = this.spawnWorkers()),
                      !0 === this.options.globalPalette)
                    )
                      this.renderNextFrame()
                    else
                      for (
                        e = 0, n = t;
                        0 <= n ? e < n : e > n;
                        0 <= n ? ++e : --e
                      )
                        this.renderNextFrame()
                    return this.emit('start'), this.emit('progress', 0)
                  }),
                  (i.prototype.abort = function () {
                    for (var e; null != (e = this.activeWorkers.shift()); )
                      this.log('killing active worker'), e.terminate()
                    return (this.running = !1), this.emit('abort')
                  }),
                  (i.prototype.spawnWorkers = function () {
                    var e, t, n, i
                    return (
                      (e = Math.min(this.options.workers, this.frames.length)),
                      function () {
                        n = []
                        for (
                          var i = (t = this.freeWorkers.length);
                          t <= e ? i < e : i > e;
                          t <= e ? i++ : i--
                        )
                          n.push(i)
                        return n
                      }
                        .apply(this)
                        .forEach(
                          ((i = this),
                          function (e) {
                            var t
                            return (
                              i.log('spawning worker ' + e),
                              ((t = new Worker(
                                i.options.workerScript
                              )).onmessage = function (e) {
                                return (
                                  i.activeWorkers.splice(
                                    i.activeWorkers.indexOf(t),
                                    1
                                  ),
                                  i.freeWorkers.push(t),
                                  i.frameFinished(e.data)
                                )
                              }),
                              i.freeWorkers.push(t)
                            )
                          })
                        ),
                      e
                    )
                  }),
                  (i.prototype.frameFinished = function (e) {
                    var t, n
                    if (
                      (this.log(
                        'frame ' +
                          e.index +
                          ' finished - ' +
                          this.activeWorkers.length +
                          ' active'
                      ),
                      this.finishedFrames++,
                      this.emit(
                        'progress',
                        this.finishedFrames / this.frames.length
                      ),
                      (this.imageParts[e.index] = e),
                      !0 === this.options.globalPalette &&
                        ((this.options.globalPalette = e.globalPalette),
                        this.log('global palette analyzed'),
                        this.frames.length > 2))
                    )
                      for (
                        t = 1, n = this.freeWorkers.length;
                        1 <= n ? t < n : t > n;
                        1 <= n ? ++t : --t
                      )
                        this.renderNextFrame()
                    return l.call(this.imageParts, null) >= 0
                      ? this.renderNextFrame()
                      : this.finishRendering()
                  }),
                  (i.prototype.finishRendering = function () {
                    var e, t, n, i, r, s, o, a, l, h, c, u, p, d, f, g
                    for (
                      a = 0, r = 0, l = (d = this.imageParts).length;
                      r < l;
                      r++
                    )
                      a += ((t = d[r]).data.length - 1) * t.pageSize + t.cursor
                    for (
                      a += t.pageSize - t.cursor,
                        this.log(
                          'rendering finished - filesize ' +
                            Math.round(a / 1e3) +
                            'kb'
                        ),
                        e = new Uint8Array(a),
                        u = 0,
                        s = 0,
                        h = (f = this.imageParts).length;
                      s < h;
                      s++
                    )
                      for (
                        n = o = 0, c = (g = (t = f[s]).data).length;
                        o < c;
                        n = ++o
                      )
                        (p = g[n]),
                          e.set(p, u),
                          n === t.data.length - 1
                            ? (u += t.cursor)
                            : (u += t.pageSize)
                    return (
                      (i = new Blob([e], { type: 'image/gif' })),
                      this.emit('finished', i, e)
                    )
                  }),
                  (i.prototype.renderNextFrame = function () {
                    var e, t, n
                    if (0 === this.freeWorkers.length)
                      throw new Error('No free workers')
                    if (!(this.nextFrame >= this.frames.length))
                      return (
                        (e = this.frames[this.nextFrame++]),
                        (n = this.freeWorkers.shift()),
                        (t = this.getTask(e)),
                        this.log(
                          'starting frame ' +
                            (t.index + 1) +
                            ' of ' +
                            this.frames.length
                        ),
                        this.activeWorkers.push(n),
                        n.postMessage(t)
                      )
                  }),
                  (i.prototype.getContextData = function (e) {
                    return e.getImageData(
                      0,
                      0,
                      this.options.width,
                      this.options.height
                    ).data
                  }),
                  (i.prototype.getImageData = function (e) {
                    var t
                    return (
                      null == this._canvas &&
                        ((this._canvas = document.createElement('canvas')),
                        (this._canvas.width = this.options.width),
                        (this._canvas.height = this.options.height)),
                      ((t = this._canvas.getContext('2d')).setFill =
                        this.options.background),
                      t.fillRect(0, 0, this.options.width, this.options.height),
                      t.drawImage(e, 0, 0),
                      this.getContextData(t)
                    )
                  }),
                  (i.prototype.getTask = function (e) {
                    var t, n
                    if (
                      ((n = {
                        index: (t = this.frames.indexOf(e)),
                        last: t === this.frames.length - 1,
                        delay: e.delay,
                        transparent: e.transparent,
                        width: this.options.width,
                        height: this.options.height,
                        quality: this.options.quality,
                        dither: this.options.dither,
                        globalPalette: this.options.globalPalette,
                        repeat: this.options.repeat,
                        canTransfer: 'chrome' === s.name
                      }),
                      null != e.data)
                    )
                      n.data = e.data
                    else if (null != e.context)
                      n.data = this.getContextData(e.context)
                    else {
                      if (null == e.image) throw new Error('Invalid frame')
                      n.data = this.getImageData(e.image)
                    }
                    return n
                  }),
                  (i.prototype.log = function () {
                    var e
                    if (
                      ((e = 1 <= arguments.length ? h.call(arguments, 0) : []),
                      this.options.debug)
                    )
                      return console.log.apply(console, e)
                  }),
                  i
                )
              })(i)),
              (t.exports = r)
          },
          { './browser.coffee': 2, events: 1 }
        ]
      },
      {},
      [3]
    )(3)
  })(v)
  var m = v.exports,
    y = new WeakMap(),
    b = new WeakMap(),
    w = new WeakMap(),
    x = (function () {
      function e(t) {
        n(this, e),
          s(this, 'parentDom', void 0),
          s(this, 'doms', {
            mask: null,
            ltDrag: null,
            rbDrag: null,
            rect: null
          }),
          f(this, y, { writable: !0, value: null }),
          f(this, b, { writable: !0, value: {} }),
          f(this, w, {
            writable: !0,
            value: {
              left: null,
              top: null,
              width: null,
              height: null,
              pageX: null,
              pageY: null
            }
          }),
          this.ensureContainer(t.el) &&
            (p(this, b, t), this.createDom(), this.bindEvent())
      }
      return (
        r(e, [
          {
            key: 'ensureContainer',
            value: function (e) {
              var t = document.querySelector(e)
              return !!t && ((this.parentDom = t), !0)
            }
          },
          {
            key: 'changeTypeSelect',
            value: function (e) {
              if (e.target.value) {
                var t = document.getElementsByClassName('label-gif'),
                  n = document.getElementsByClassName('label-pic')
                if (t && n)
                  return 'pic' === e.target.value
                    ? ((t[0].style.display = 'none'),
                      void (n[0].style.display = 'flex'))
                    : 'gif' === e.target.value
                    ? ((t[0].style.display = 'flex'),
                      void (n[0].style.display = 'none'))
                    : void 0
              }
            }
          },
          {
            key: 'createDom',
            value: function () {
              var e = this.parentDom.offsetWidth,
                t = this.parentDom.offsetHeight,
                n = document.createElement('div')
              Object.assign(n.style, {
                position: 'absolute',
                left: 0,
                top: 0,
                zIndex: 101,
                width: '100%',
                height: '100%',
                display: 'none',
                cursor: 'pointer'
              }),
                this.parentDom.appendChild(n),
                (this.doms.mask = n)
              var i = document.createElement('div')
              i.classList.add('export-rect')
              var r = {
                  position: 'absolute',
                  left: ''.concat(e / 4, 'px'),
                  top: ''.concat(t / 4, 'px'),
                  zIndex: 100,
                  width: ''.concat(e / 2, 'px'),
                  height: ''.concat(t / 2, 'px'),
                  display: 'none',
                  boxSizing: 'border-box',
                  border: '1px solid #333'
                },
                s = u(this, b)
              s.width && (r.width = s.width),
                s.height && (r.height = s.height),
                Object.assign(i.style, r)
              var o = document.createElement('div'),
                a = document.createElement('div'),
                l = {
                  position: 'absolute',
                  zIndex: 100,
                  background: '#333',
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer'
                }
              Object.assign(o.style, l, { left: '-8px', top: '-8px' }),
                Object.assign(a.style, l, { right: '-8px', bottom: '-8px' }),
                i.appendChild(o),
                i.appendChild(a),
                (this.doms.ltDrag = o),
                (this.doms.rbDrag = a)
              var h = document.createElement('div'),
                c = {
                  position: 'absolute',
                  zIndex: 111,
                  width: '360px',
                  borderRadius: '4px',
                  fontSize: '14px',
                  padding: '10px',
                  boxSizing: 'border-box',
                  color: '#fff',
                  background: 'rgba(27,27,27,.95)',
                  border: '1px solid #575758'
                }
              s.placement &&
                s.placement.split('-').map(function (e) {
                  c[e] = 0
                })
              h.classList.add('actions-wrap'), Object.assign(h.style, c)
              var p = '',
                d = '',
                f = '',
                g = ''
              if (
                (this.gifCheckboxActive() &&
                  ((p =
                    '<label class="gif" style="flex: 1; padding: 10px;">\n                          <input class="type-gif" type="radio" value="gif" name="type" checked />\n                          <span class="label-text">导出动画</span>\n                        </label>'),
                  (f =
                    ' <div class="preview label-gif" style="height: 40px; color: #ddd; display: flex;">\n        <span style="padding-top: 10px;" class="item-name">快速预览：</span>\n        <label style="flex: 1; padding: 10px;" class="label-1">\n          <input type="radio" value="100" name="speed" />\n          <span class="label-text">快</span>\n        </label>\n        <label style="flex: 1; padding: 10px;" class="label-2">\n          <input type="radio" value="500" name="speed" checked />\n          <span class="label-text">中</span>\n        </label>\n        <label style="flex: 1; padding: 10px;" class="label-3">\n          <input type="radio" value="1000" name="speed" />\n          <span class="label-text">慢</span>\n        </label>\n      </div>')),
                this.picCheckboxActive())
              ) {
                var v = this.gifCheckboxActive() ? '' : 'checked'
                ;(g =
                  ' <div class="preview label-pic" style="height: 40px; color: #ddd; display: '.concat(
                    v ? 'flex' : 'none',
                    ';">\n      <span style="padding-top: 10px;">图片格式：</span>\n      <label style="flex: 1; padding: 10px;">\n        <input type="radio" value="png" name="imgType" />png\n      </label>\n      <label style="flex: 1; padding: 10px;">\n        <input type="radio" value="jpg" name="imgType" checked />jpg\n      </label>\n      <label style="flex: 1; padding: 10px;">\n        <input type="radio" value="bmp" name="imgType" />bmp\n      </label>\n    </div>'
                  )),
                  (d =
                    '<label class="radio-export-pic" style="flex: 1; padding: 10px;">\n                          <input class="type-gif" type="radio" value="pic" name="type" '.concat(
                      v,
                      ' />\n                          <span class="label-text">导出图片</span>\n                        </label>'
                    ))
              }
              var m =
                'color: #ddd; font-size: 14px; border-radius: 4px; cursor: pointer; padding: 2% 6%'
              ;(h.innerHTML =
                '\n      <div class="type-select" style="height: 40px; color: #ddd; display: flex;">\n      <span style="padding-top: 10px;" class="item-name">导出类型：</span>\n        '
                  .concat(p, '\n        ')
                  .concat(d, '\n      </div>\n      ')
                  .concat(f, '\n      ')
                  .concat(
                    g,
                    '\n      <div style="height: 40px; text-align: center; padding-top: 6px;">\n        <button class="btn-ensure" style="border: 1px solid #97C3F7; background: #2878ff; '
                  )
                  .concat(
                    m,
                    '">确认</button>\n        <button class="btn-cancel" style="border: 1px solid #C4CAD8; background: #8B97B0; '
                  )
                  .concat(m, '">取消</button>\n      </div>\n    ')),
                i.appendChild(h),
                this.parentDom.appendChild(i),
                (this.doms.rect = i)
            }
          },
          {
            key: 'gifCheckboxActive',
            value: function () {
              return u(this, b).btns.includes('gif')
            }
          },
          {
            key: 'picCheckboxActive',
            value: function () {
              return u(this, b).btns.includes('pic')
            }
          },
          {
            key: 'setMaskVisible',
            value: function (e) {
              this.doms.mask.style.display = e ? 'block' : 'none'
            }
          },
          {
            key: 'setRectVisible',
            value: function (e) {
              this.doms.rect.style.display = e ? 'block' : 'none'
            }
          },
          {
            key: 'setRectSizePosition',
            value: function (e) {
              var t = e.nextLeft,
                n = void 0 === t ? null : t,
                i = e.nextTop,
                r = void 0 === i ? null : i,
                s = e.nextWidth,
                o = void 0 === s ? null : s,
                a = e.nextHeight,
                l = void 0 === a ? null : a
              null !== n && (this.doms.rect.style.left = ''.concat(n, 'px')),
                null !== r && (this.doms.rect.style.top = ''.concat(r, 'px')),
                null !== o && (this.doms.rect.style.width = ''.concat(o, 'px')),
                null !== l && (this.doms.rect.style.height = ''.concat(l, 'px'))
            }
          },
          {
            key: 'setModel',
            value: function () {
              var e =
                arguments.length > 0 && void 0 !== arguments[0]
                  ? arguments[0]
                  : null
              p(this, y, e)
            }
          },
          {
            key: 'noteRectInfo',
            value: function () {
              var e = this.doms.rect,
                t = e.offsetWidth,
                n = e.offsetHeight,
                i = Number(e.style.left.replace('px', '')),
                r = Number(e.style.top.replace('px', ''))
              Object.assign(u(this, w), {
                width: t,
                height: n,
                left: i,
                top: r
              })
            }
          },
          {
            key: 'notePageXY',
            value: function (e) {
              var t = e.pageX,
                n = e.pageY
              Object.assign(u(this, w), { pageX: t, pageY: n })
            }
          },
          {
            key: 'dragMouseDown',
            value: function (e, t) {
              e.stopPropagation(),
                this.setModel(t),
                this.noteRectInfo(),
                this.notePageXY(e),
                this.setMaskVisible(!0)
            }
          },
          {
            key: 'ltDragMousedown',
            value: function (e) {
              this.dragMouseDown(e, 'ltResize')
            }
          },
          {
            key: 'rbDragMousedown',
            value: function (e) {
              this.dragMouseDown(e, 'rbResize')
            }
          },
          {
            key: 'maskMousemove',
            value: function (e) {
              var t = e.pageX,
                n = e.pageY,
                i = u(this, y),
                r = u(this, w).pageX,
                s = u(this, w).pageY,
                o = u(this, w).width,
                a = u(this, w).height,
                l = u(this, w).left,
                h = u(this, w).top
              if ('ltResize' !== i)
                if ('rbResize' !== i) {
                  if ('drag' === i) {
                    var c = l + (t - r),
                      p = h + (n - s)
                    this.setRectSizePosition({ nextLeft: c, nextTop: p })
                  }
                } else {
                  var d = o + (t - r),
                    f = a + (n - s)
                  this.setRectSizePosition({ nextWidth: d, nextHeight: f })
                }
              else {
                var g = t - r,
                  v = n - s,
                  m = l + g,
                  b = h + v,
                  x = o - g,
                  k = a - v
                this.setRectSizePosition({
                  nextLeft: m,
                  nextTop: b,
                  nextWidth: x,
                  nextHeight: k
                })
              }
            }
          },
          {
            key: 'maskMouseout',
            value: function () {
              this.setMaskVisible(!1)
            }
          },
          {
            key: 'maskMouseup',
            value: function () {
              this.setMaskVisible(!1)
            }
          },
          {
            key: 'rectMousedown',
            value: function (e) {
              e.target.classList.contains('export-rect') &&
                this.dragMouseDown(e, 'drag')
            }
          },
          {
            key: 'clickEnsureBtn',
            value: function () {
              var e = this.doms.rect,
                t = e.offsetWidth,
                n = e.offsetHeight,
                i = Number(e.style.left.replace('px', '')),
                r = Number(e.style.top.replace('px', '')),
                s = this.parentDom.querySelectorAll('input[name=type]'),
                o = null
              s.forEach(function (e) {
                e.checked && (o = e.value)
              })
              var a = this.parentDom.querySelectorAll('input[name=speed]'),
                l = null
              a.forEach(function (e) {
                e.checked && (l = Number(e.value))
              })
              var h = this.parentDom.querySelectorAll('input[name=imgType]'),
                c = null
              h.forEach(function (e) {
                e.checked && (c = e.value)
              }),
                u(this, b).ensure({
                  x: i,
                  y: r,
                  width: t,
                  height: n,
                  type: o,
                  speed: l,
                  imgType: c
                })
            }
          },
          {
            key: 'clickCancelBtn',
            value: function () {
              u(this, b).cancel(), this.setRectVisible(!1)
            }
          },
          {
            key: 'bindEvent',
            value: function () {
              this.doms.ltDrag.addEventListener(
                'mousedown',
                this.ltDragMousedown.bind(this)
              ),
                this.doms.rbDrag.addEventListener(
                  'mousedown',
                  this.rbDragMousedown.bind(this)
                ),
                this.doms.mask.addEventListener(
                  'mousemove',
                  this.maskMousemove.bind(this)
                ),
                this.doms.mask.addEventListener(
                  'mouseout',
                  this.maskMouseout.bind(this)
                ),
                this.doms.mask.addEventListener(
                  'mouseup',
                  this.maskMouseup.bind(this)
                ),
                this.doms.rect.addEventListener(
                  'mousedown',
                  this.rectMousedown.bind(this)
                ),
                this.parentDom
                  .querySelector('.type-select')
                  .addEventListener(
                    'mousedown',
                    this.changeTypeSelect.bind(this)
                  )
              var e = this.parentDom.querySelector('.btn-ensure'),
                t = this.parentDom.querySelector('.btn-cancel')
              e.addEventListener('mousedown', this.clickEnsureBtn.bind(this)),
                t.addEventListener('mousedown', this.clickCancelBtn.bind(this))
            }
          },
          {
            key: 'unbindEvent',
            value: function () {
              this.doms.ltDrag.removeEventListener(
                'mousedown',
                this.ltDragMousedown
              ),
                this.doms.rbDrag.removeEventListener(
                  'mousedown',
                  this.rbDragMousedown
                ),
                this.doms.mask.removeEventListener(
                  'mousemove',
                  this.maskMousemove
                ),
                this.doms.mask.removeEventListener(
                  'mouseout',
                  this.maskMouseout
                ),
                this.doms.mask.removeEventListener('mouseup', this.maskMouseup),
                this.doms.rect.removeEventListener(
                  'mousedown',
                  this.rectMousedown
                ),
                this.parentDom
                  .querySelector('.type-select')
                  .removeEventListener('mousedown', this.clickEnsureBtn)
              var e = this.parentDom.querySelector('.btn-ensure'),
                t = this.parentDom.querySelector('.btn-cancel')
              e.removeEventListener('mousedown', this.clickEnsureBtn),
                t.removeEventListener('mousedown', this.clickCancelBtn)
            }
          },
          {
            key: 'removeDoms',
            value: function () {
              this.parentDom.removeChild(this.doms.rect),
                this.parentDom.removeChild(this.doms.mask)
            }
          },
          {
            key: 'destory',
            value: function () {
              this.unbindEvent(), this.removeDoms()
            }
          }
        ]),
        e
      )
    })(),
    k = new WeakMap(),
    E = (function () {
      function e() {
        n(this, e), f(this, k, { writable: !0, value: {} })
      }
      return (
        r(e, [
          {
            key: 'bindEvent',
            value: function (e, t) {
              var n =
                  arguments.length > 2 && void 0 !== arguments[2]
                    ? arguments[2]
                    : {},
                i = { type: e, func: t, data: n }
              Object.prototype.hasOwnProperty.call(u(this, k), e)
                ? u(this, k)[e].push(i)
                : (u(this, k)[e] = [i])
            }
          },
          {
            key: 'triggleEvent',
            value: function (e, n) {
              Object.prototype.hasOwnProperty.call(u(this, k), e) &&
                u(this, k)[e].forEach(function (e) {
                  e.func(t(t({}, e.data), n))
                })
            }
          },
          {
            key: 'unbindEvent',
            value: function (e, t) {
              if (Object.prototype.hasOwnProperty.call(u(this, k), e))
                for (var n = 0; n < u(this, k)[e].length; n++)
                  t === u(this, k)[e][n].func && u(this, k)[e].splice(n, 1)
            }
          }
        ]),
        e
      )
    })(),
    _ = new WeakMap(),
    D = new WeakMap(),
    L = new WeakMap()
  return (function (e) {
    !(function (e, t) {
      if ('function' != typeof t && null !== t)
        throw new TypeError(
          'Super expression must either be null or a function'
        )
      ;(e.prototype = Object.create(t && t.prototype, {
        constructor: { value: e, writable: !0, configurable: !0 }
      })),
        Object.defineProperty(e, 'prototype', { writable: !1 }),
        t && a(e, t)
    })(i, e)
    var t = c(i)
    function i(e) {
      var r
      return (
        n(this, i),
        f(l((r = t.call(this))), _, {
          writable: !0,
          value: {
            el: null,
            croppingTagName: null,
            cropping: !0,
            works: 16,
            quality: 10,
            preview: !0,
            btns: ['gif', 'pic'],
            workerScript: './'
          }
        }),
        f(l(r), D, { writable: !0, value: null }),
        f(l(r), L, { writable: !0, value: {} }),
        s(l(r), 'gif', null),
        s(l(r), 'config', {}),
        r.combineConf(e),
        r.config.cropping && r.initSelectArea(),
        r
      )
    }
    return (
      r(i, [
        {
          key: 'combineConf',
          value: function (e) {
            this.config = Object.assign({}, u(this, _), e)
          }
        },
        {
          key: 'initGIF',
          value: function () {
            var e = this.config.workerScript,
              t = this,
              n = {
                workers: this.config.workers,
                quality: this.config.quality,
                debug: !0,
                workerScript: e
              }
            ;(this.gif = new m(n)),
              this.gif.on('finished', function (e) {
                t.triggleEvent('exportGifDone', {
                  blob: e,
                  url: URL.createObjectURL(e)
                }),
                  t.config.preview && window.open(URL.createObjectURL(e))
              })
          }
        },
        {
          key: 'initSelectArea',
          value: function () {
            var e = this,
              t = this.config,
              n = {
                el: t.el,
                btns: t.btns,
                placement: t.placement,
                width: t.width,
                height: t.height,
                ensure: function (t) {
                  p(e, L, t)
                  var n = t.type
                  if ('gif' === n) e.initGIF()
                  else e.exportImage(n)
                  e.triggleEvent('ensure', { type: n })
                },
                cancel: function () {
                  e.triggleEvent('close')
                }
              }
            p(this, D, new x(n))
          }
        },
        {
          key: 'open',
          value: function () {
            u(this, D).setRectVisible(!0)
          }
        },
        {
          key: 'close',
          value: function () {
            u(this, D).setRectVisible(!1)
          }
        },
        {
          key: 'add',
          value: function () {
            var e = this,
              t = u(this, L),
              n = t.x,
              i = t.y,
              r = t.width,
              s = t.height,
              o = t.type,
              a = t.speed,
              l = this.config.croppingTagName
            if ('gif' === o) {
              this.triggleEvent('beforeCreateGifCanvas', {
                x: n,
                y: i,
                width: r,
                height: s,
                type: o
              })
              var h = l,
                c = document.createElement('canvas'),
                p = c.getContext('2d'),
                d = document.querySelectorAll(
                  ''.concat(this.config.el, ' ').concat(h)
                ),
                f = window.devicePixelRatio
              c.setAttribute('width', r * f),
                c.setAttribute('height', s * f),
                d.forEach(function (e) {
                  p.drawImage(e, n * f, i * f, r * f, s * f, 0, 0, r * f, s * f)
                }),
                this.triggleEvent('beforeAddGif', {
                  canvas: c,
                  ctx: p,
                  x: n,
                  y: i,
                  width: r,
                  height: s,
                  type: o,
                  scale: f,
                  next: function () {
                    e.gif.addFrame(c, { copy: !0, delay: a }),
                      e.triggleEvent('afterAddGif', {
                        canvas: c,
                        ctx: p,
                        x: n,
                        y: i,
                        width: r,
                        height: s,
                        type: o,
                        scale: f
                      })
                  }
                })
            }
          }
        },
        {
          key: 'done',
          value: function () {
            'gif' === u(this, L).type &&
              (this.gif.render(), u(this, D).setRectVisible(!1))
          }
        },
        {
          key: 'encodeData',
          value: function (e) {
            if (!window.btoa) throw 'btoa undefined'
            var t = ''
            if ('string' == typeof e) t = e
            else
              for (var n = 0; n < e.length; n++) t += String.fromCharCode(e[n])
            return btoa(t)
          }
        },
        {
          key: 'genBitmapImage',
          value: function (e) {
            var t = e.width,
              n = e.height,
              i = t * n * 3,
              r = i + 54,
              s = [
                66,
                77,
                255 & r,
                (r >> 8) & 255,
                (r >> 16) & 255,
                (r >> 24) & 255,
                0,
                0,
                0,
                0,
                54,
                0,
                0,
                0
              ],
              o = [
                40,
                0,
                0,
                0,
                255 & t,
                (t >> 8) & 255,
                (t >> 16) & 255,
                (t >> 24) & 255,
                255 & n,
                (n >> 8) & 255,
                (n >> 16) & 255,
                (n >> 24) & 255,
                1,
                0,
                24,
                0,
                0,
                0,
                0,
                0,
                255 & i,
                (i >> 8) & 255,
                (i >> 16) & 255,
                (i >> 24) & 255,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
              ],
              a = (4 - ((3 * t) % 4)) % 4,
              l = e.data,
              h = '',
              c = t << 2,
              u = n,
              p = String.fromCharCode
            do {
              for (var d = c * (u - 1), f = '', g = 0; g < t; g++) {
                var v = g << 2
                f += p(l[d + v + 2]) + p(l[d + v + 1]) + p(l[d + v])
              }
              for (var m = 0; m < a; m++) f += String.fromCharCode(0)
              h += f
            } while (--u)
            return this.encodeData(s.concat(o)) + this.encodeData(h)
          }
        },
        {
          key: 'getImageData',
          value: function (e) {
            var t = e.width,
              n = e.height
            return e.getContext('2d').getImageData(0, 0, t, n)
          }
        },
        {
          key: 'scaleCanvas',
          value: function (e, t, n) {
            var i = e.width,
              r = e.height
            null == t && (t = i), null == n && (n = r)
            var s = document.createElement('canvas'),
              o = s.getContext('2d')
            return (
              (s.width = t),
              (s.height = n),
              o.drawImage(e, 0, 0, i, r, 0, 0, t, n),
              s
            )
          }
        },
        {
          key: 'getDataURL',
          value: function (e, t, n, i) {
            return (e = this.scaleCanvas(e, n, i)).toDataURL(t)
          }
        },
        {
          key: 'setImgURL',
          value: function (e, t, n, i) {
            var r = null
            if (/bmp/.test(i)) {
              var s = this.getImageData(this.scaleCanvas(e, t, n))
              return (r = this.genBitmapImage(s)), this.makeURI(r, 'image/bmp')
            }
            return (r = this.getDataURL(e, i, t, n))
          }
        },
        {
          key: 'fixType',
          value: function (e) {
            return (
              'image/' +
              (e = e.toLowerCase().replace(/jpg/i, 'jpeg')).match(
                /png|jpeg|bmp|gif/
              )[0]
            )
          }
        },
        {
          key: 'genImage',
          value: function (e) {
            var t = document.createElement('img')
            return (t.src = e), t
          }
        },
        {
          key: 'makeURI',
          value: function (e, t) {
            return 'data:' + t + ';base64,' + e
          }
        },
        {
          key: 'exportImage',
          value: function () {
            var e = this,
              t = u(this, L),
              n = t.x,
              i = t.y,
              r = t.width,
              s = t.height,
              o = t.type,
              a = t.imgType
            if (['pic', 'thematic'].includes(o)) {
              this.triggleEvent('beforeCreatePicCanvas', {
                x: n,
                y: i,
                width: r,
                height: s,
                type: o,
                imgType: a
              })
              var l = this.config.croppingTagName,
                h = document.createElement('canvas'),
                c = h.getContext('2d'),
                p = document.querySelectorAll(
                  ''.concat(this.config.el, ' ').concat(l)
                ),
                d = window.devicePixelRatio
              h.setAttribute('width', r * d),
                h.setAttribute('height', s * d),
                p.forEach(function (e) {
                  c.drawImage(e, n * d, i * d, r * d, s * d, 0, 0, r * d, s * d)
                }),
                this.triggleEvent('beforeExportPic', {
                  canvas: h,
                  ctx: c,
                  x: n,
                  y: i,
                  width: r,
                  height: s,
                  type: o,
                  scale: d,
                  imgType: a,
                  next: function () {
                    c.scale(1 / d, 1 / d)
                    var t = null
                    null == a && (t = 'png'), (t = e.fixType(a))
                    var n = e.setImgURL(h, r, s, t),
                      i = document.createElement('a')
                    ;(i.href = n),
                      (i.download = 'pic'),
                      i.click(),
                      u(e, D).setRectVisible(!1)
                  }
                })
            }
          }
        },
        {
          key: 'destory',
          value: function () {
            u(this, D).destory()
          }
        }
      ]),
      i
    )
  })(E)
})
//# sourceMappingURL=exportImg.js.map
