package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanStation;
import cn.piesat.data.making.server.model.StationInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 海洋站表表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanStationDao extends BaseMapper<OceanStation> {

    @Select({"<script>",
            "select code, name from (SELECT row_number() over (partition by code order by name) as rn, code, name " +
                    "FROM (SELECT station_num as code,name FROM ocean_station_hour_at_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_bp_o where name is not null  and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_hu_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_rn_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_sl_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_vb_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_wl_dat_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_ws_dat_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_wt_o where name is not null and station_num is not null " +
                    "UNION ALL " +
                    "SELECT station_num as code,name FROM ocean_station_hour_wv_o where name is not null and station_num is not null) as temp where 1=1 " +
                    "<if test='stationName != null'> " +
                    " and temp.name like '%${stationName}%'" +
                    "</if>" +
                    ") as o where rn = 1;",
            "</script>"})
    List<StationInfo> getList(String stationName);
}
