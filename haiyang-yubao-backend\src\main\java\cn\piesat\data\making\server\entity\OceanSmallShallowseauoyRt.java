package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 浅海小型浮标数据-实时数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_small_shallowsea_buoy_rt")
public class OceanSmallShallowseauoyRt implements Serializable {

    private static final long serialVersionUID = -74729883997783508L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 浮标站ID
     **/
    @JsonProperty("BuoyInfo_Id")
    @TableField("buoyinfo_id")
    private String buoyinfoId;
    /**
     * 浮标类型
     **/
    @JsonProperty("BuoyInfo_Type")
    @TableField("buoyinfo_type")
    private String buoyinfoType;
    /**
     * 浮标名称
     **/
    @JsonProperty("BuoyInfo_Name")
    @TableField("buoyinfo_name")
    private String buoyinfoName;
    /**
     * 浮标编号
     **/
    @JsonProperty("BuoyInfo_No")
    @TableField("buoyinfo_no")
    private String buoyinfoNo;
    /**
     * 浮标种类
     **/
    @JsonProperty("BuoyInfo_Kind")
    @TableField("buoyinfo_kind")
    private String buoyinfoKind;
    /**
     * 采样率
     **/
    @JsonProperty("CommInfo_Sample")
    @TableField("comminfo_sample")
    private String comminfoSample;
    /**
     * 波浪传感器状态 1：正常；0：异常
     **/
    @JsonProperty("RunningStatus_status")
    @TableField("runningstatus_status")
    private String runningstatusStatus;
    /**
     * 内部温度
     **/
    @JsonProperty("RunningStatus_InTem")
    @TableField("runningstatus_intem")
    private String runningstatusIntem;
    /**
     * 系统未发送数据条数
     **/
    @JsonProperty("RunningStatus_NoSend")
    @TableField("runningstatus_nosend")
    private String runningstatusNosend;
    /**
     * 工作模式
     **/
    @JsonProperty("RunningStatus_Mode")
    @TableField("runningstatus_mode")
    private String runningstatusMode;
    /**
     * 浮标序列号
     **/
    @JsonProperty("RunningStatus_ID")
    @TableField("runningstatus_id")
    private String runningstatusId;
    /**
     * 风速（预留）
     **/
    @JsonProperty("BuoyData_WS")
    @TableField("buoydata_ws")
    private String buoydataWs;
    /**
     * 风速要素时间
     **/
    @JsonProperty("BuoyData_WS_rt")
    @TableField("buoydata_ws_rt")
    private Date buoydataWsRt;
    /**
     * 风向（预留）
     **/
    @JsonProperty("BuoyData_WD")
    @TableField("buoydata_wd")
    private String buoydataWd;
    /**
     * 风向要素时间
     **/
    @JsonProperty("BuoyData_WD_rt")
    @TableField("buoydata_wd_rt")
    private Date buoydataWdRt;
    /**
     * 气温（预留）
     **/
    @JsonProperty("BuoyData_AT")
    @TableField("buoydata_at")
    private String buoydataAt;
    /**
     * 气温要素时间
     **/
    @JsonProperty("BuoyData_AT_rt")
    @TableField("buoydata_at_rt")
    private Date buoydataAtRt;
    /**
     * 气压（预留）
     **/
    @JsonProperty("BuoyData_BP")
    @TableField("buoydata_bp")
    private String buoydataBp;
    /**
     * 气压要素时间
     **/
    @JsonProperty("BuoyData_BP_rt")
    @TableField("buoydata_bp_rt")
    private Date buoydataBpRt;
    /**
     * 相对湿度（预留）
     **/
    @JsonProperty("BuoyData_HU")
    @TableField("buoydata_hu")
    private String buoydataHu;
    /**
     * 相对湿度要素时间
     **/
    @JsonProperty("BuoyData_HU_rt")
    @TableField("buoydata_hu_rt")
    private Date buoydataHuRt;
    /**
     * 表层水温（预留）
     **/
    @JsonProperty("BuoyData_WT")
    @TableField("buoydata_wt")
    private String buoydataWt;
    /**
     * 表层水温要素时间
     **/
    @JsonProperty("BuoyData_WT_rt")
    @TableField("buoydata_wt_rt")
    private Date buoydataWtRt;
    /**
     * 表层盐度（预留）
     **/
    @JsonProperty("BuoyData_SL")
    @TableField("buoydata_sl")
    private String buoydataSl;
    /**
     * 表层盐度要素时间
     **/
    @JsonProperty("BuoyData_SL_rt")
    @TableField("buoydata_sl_rt")
    private Date buoydataSlRt;
    /**
     * 平均波高
     **/
    @JsonProperty("BuoyData_BG")
    @TableField("buoydata_bg")
    private String buoydataBg;
    /**
     * 平均波高要素时间
     **/
    @JsonProperty("BuoyData_BG_rt")
    @TableField("buoydata_bg_rt")
    private Date buoydataBgRt;
    /**
     * 波向
     **/
    @JsonProperty("BuoyData_BX")
    @TableField("buoydata_bx")
    private String buoydataBx;
    /**
     * 波向要素时间
     **/
    @JsonProperty("BuoyData_BX_rt")
    @TableField("buoydata_bx_rt")
    private Date buoydataBxRt;
    /**
     * 平均波周期
     **/
    @JsonProperty("BuoyData_ZQ")
    @TableField("buoydata_zq")
    private String buoydataZq;
    /**
     * 平均波周期要素时间
     **/
    @JsonProperty("BuoyData_ZQ_rt")
    @TableField("buoydata_zq_rt")
    private Date buoydataZqRt;
    /**
     * 有效波高
     **/
    @JsonProperty("BuoyData_YBG")
    @TableField("buoydata_ybg")
    private String buoydataYbg;
    /**
     * 有效波高要素时间
     **/
    @JsonProperty("BuoyData_YBG_rt")
    @TableField("buoydata_ybg_rt")
    private Date buoydataYbgRt;
    /**
     * 有效波周期
     **/
    @JsonProperty("BuoyData_YZQ")
    @TableField("buoydata_yzq")
    private String buoydataYzq;
    /**
     * 有效波周期要素时间
     **/
    @JsonProperty("BuoyData_YZQ_rt")
    @TableField("buoydata_yzq_rt")
    private Date buoydataYzqRt;
    /**
     * 十分之一波高
     **/
    @JsonProperty("BuoyData_TenthBG")
    @TableField("buoydata_tenthbg")
    private String buoydataTenthbg;
    /**
     * 十分之一波高要素时间
     **/
    @JsonProperty("BuoyData_TenthBG_rt")
    @TableField("buoydata_tenthbg_rt")
    private Date buoydataTenthbgRt;
    /**
     * 十分之一波周期
     **/
    @JsonProperty("BuoyData_TenthZQ")
    @TableField("buoydata_tenthzq")
    private String buoydataTenthzq;
    /**
     * 十分之一波周期要素时间
     **/
    @JsonProperty("BuoyData_TenthZQ_rt")
    @TableField("buoydata_tenthzq_rt")
    private Date buoydataTenthzqRt;
    /**
     * 最大波高
     **/
    @JsonProperty("BuoyData_ZBG")
    @TableField("buoydata_zbg")
    private String buoydataZbg;
    /**
     * 最大波高要素时间
     **/
    @JsonProperty("BuoyData_ZBG_rt")
    @TableField("buoydata_zbg_rt")
    private Date buoydataZbgRt;
    /**
     * 最大波周期
     **/
    @JsonProperty("BuoyData_ZZQ")
    @TableField("buoydata_zzq")
    private String buoydataZzq;
    /**
     * 最大波周期要素时间
     **/
    @JsonProperty("BuoyData_ZZQ_rt")
    @TableField("buoydata_zzq_rt")
    private Date buoydataZzqRt;
    /**
     * 波数
     **/
    @JsonProperty("BuoyData_BS")
    @TableField("buoydata_bs")
    private String buoydataBs;
    /**
     * 波数要素时间
     **/
    @JsonProperty("BuoyData_BS_rt")
    @TableField("buoydata_bs_rt")
    private Date buoydataBsRt;
    /**
     * 谱有效波高
     **/
    @JsonProperty("Pu_PBG")
    @TableField("pu_pbg")
    private String puPbg;
    /**
     * 谱有效波高要素时间
     **/
    @JsonProperty("Pu_PBG_rt")
    @TableField("pu_pbg_rt")
    private Date puPbgRt;
    /**
     * 谱峰周期
     **/
    @JsonProperty("Pu_PFZQ")
    @TableField("pu_pfzq")
    private String puPfzq;
    /**
     * 谱峰周期要素时间
     **/
    @JsonProperty("Pu_PFZQ_rt")
    @TableField("pu_pfzq_rt")
    private Date puPfzqRt;
    /**
     * 谱平均周期
     **/
    @JsonProperty("Pu_PPJZQ")
    @TableField("pu_ppjzq")
    private String puPpjzq;
    /**
     * 谱平均周期要素时间
     **/
    @JsonProperty("Pu_PPJZQ_rt")
    @TableField("pu_ppjzq_rt")
    private Date puPpjzqRt;
    /**
     * 谱峰波向
     **/
    @JsonProperty("Pu_PFBX")
    @TableField("pu_pfbx")
    private String puPfbx;
    /**
     * 谱峰波向要素时间
     **/
    @JsonProperty("Pu_PFBX_rt")
    @TableField("pu_pfbx_rt")
    private Date puPfbxRt;
    /**
     * 谱平均波向
     **/
    @JsonProperty("Pu_PPJBX")
    @TableField("pu_ppjbx")
    private String puPpjbx;
    /**
     * 谱平均波向要素时间
     **/
    @JsonProperty("Pu_PPJBX_rt")
    @TableField("pu_ppjbx_rt")
    private Date puPpjbxRt;
    /**
     * 风浪谱有效波高
     **/
    @JsonProperty("FLPu_PBG")
    @TableField("flpu_pbg")
    private String flpuPbg;
    /**
     * 风浪谱有效波高要素时间
     **/
    @JsonProperty("FLPu_PBG_rt")
    @TableField("flpu_pbg_rt")
    private Date flpuPbgRt;
    /**
     * 风浪谱峰周期
     **/
    @JsonProperty("FLPu_PFZQ")
    @TableField("flpu_pfzq")
    private String flpuPfzq;
    /**
     * 风浪谱峰周期要素时间
     **/
    @JsonProperty("FLPu_PFZQ_rt")
    @TableField("flpu_pfzq_rt")
    private Date flpuPfzqRt;
    /**
     * 风浪谱平均周期
     **/
    @JsonProperty("FLPu_PPJZQ")
    @TableField("flpu_ppjzq")
    private String flpuPpjzq;
    /**
     * 风浪谱平均周期要素时间
     **/
    @JsonProperty("FLPu_PPJZQ_rt")
    @TableField("flpu_ppjzq_rt")
    private Date flpuPpjzqRt;
    /**
     * 风浪谱峰波向
     **/
    @JsonProperty("FLPu_PFBX")
    @TableField("flpu_pfbx")
    private String flpuPfbx;
    /**
     * 风浪谱峰波向要素时间
     **/
    @JsonProperty("FLPu_PFBX_rt")
    @TableField("flpu_pfbx_rt")
    private Date flpuPfbxRt;
    /**
     * 风浪谱平均波向
     **/
    @JsonProperty("FLPu_PPJBX")
    @TableField("flpu_ppjbx")
    private String flpuPpjbx;
    /**
     * 风浪谱平均波向要素时间
     **/
    @JsonProperty("FLPu_PPJBX_rt")
    @TableField("flpu_ppjbx_rt")
    private Date flpuPpjbxRt;
    /**
     * 涌浪谱有效波高
     **/
    @JsonProperty("YLPu_PBG")
    @TableField("ylpu_pbg")
    private String ylpuPbg;
    /**
     * 涌浪谱有效波高要素时间
     **/
    @JsonProperty("YLPu_PBG_rt")
    @TableField("ylpu_pbg_rt")
    private Date ylpuPbgRt;
    /**
     * 涌浪谱峰周期
     **/
    @JsonProperty("YLPu_PFZQ")
    @TableField("ylpu_pfzq")
    private String ylpuPfzq;
    /**
     * 涌浪谱峰周期要素时间
     **/
    @JsonProperty("YLPu_PFZQ_rt")
    @TableField("ylpu_pfzq_rt")
    private Date ylpuPfzqRt;
    /**
     * 涌浪谱平均周期
     **/
    @JsonProperty("YLPu_PPJZQ")
    @TableField("ylpu_ppjzq")
    private String ylpuPpjzq;
    /**
     * 涌浪谱平均周期要素时间
     **/
    @JsonProperty("YLPu_PPJZQ_rt")
    @TableField("ylpu_ppjzq_rt")
    private Date ylpuPpjzqRt;
    /**
     * 涌浪谱峰波向
     **/
    @JsonProperty("YLPu_PFBX")
    @TableField("ylpu_pfbx")
    private String ylpuPfbx;
    /**
     * 涌浪谱峰波向要素时间
     **/
    @JsonProperty("YLPu_PFBX_rt")
    @TableField("ylpu_pfbx_rt")
    private Date ylpuPfbxRt;
    /**
     * 涌浪谱平均波向
     **/
    @JsonProperty("YLPu_PPJBX")
    @TableField("ylpu_ppjbx")
    private String ylpuPpjbx;
    /**
     * 涌浪谱平均波向要素时间
     **/
    @JsonProperty("YLPu_PPJBX_rt")
    @TableField("ylpu_ppjbx_rt")
    private Date ylpuPpjbxRt;
    /**
     * 反演风速 m/s
     **/
    @JsonProperty("FY_WS")
    @TableField("fy_ws")
    private String fyWs;
    /**
     * 反演风速要素时间
     **/
    @JsonProperty("FY_WS_rt")
    @TableField("fy_ws_rt")
    private Date fyWsRt;
    /**
     * 反演风速 m/s
     **/
    @JsonProperty("FY_WD")
    @TableField("fy_wd")
    private String fyWd;
    /**
     * 反演风速要素时间
     **/
    @JsonProperty("FY_WD_rt")
    @TableField("fy_wd_rt")
    private Date fyWdRt;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    private Date createtime;
    /**
     * 更新时间
     **/
    @JsonProperty("UpdateTime")
    @TableField("updatetime")
    private Date updatetime;
    /**
     * 名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
}



