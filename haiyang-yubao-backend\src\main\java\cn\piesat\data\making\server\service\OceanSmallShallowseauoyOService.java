package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanSmallShallowseauoyO;
import cn.piesat.data.making.server.entity.OceanStationHourAtO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 浅海小型浮标数据-原始数据（文件编码标识BL）服务接口
 *
 * <AUTHOR>
 */
public interface OceanSmallShallowseauoyOService extends IService<OceanSmallShallowseauoyO> {

    List<OceanSmallShallowseauoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




