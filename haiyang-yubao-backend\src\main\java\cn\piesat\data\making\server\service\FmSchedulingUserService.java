package cn.piesat.data.making.server.service;

import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.entity.FmSchedulingUser;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-30 16:08:29
 */
public interface FmSchedulingUserService extends IService<FmSchedulingUser> {

    /**
     * 根据参数查询分页
     */
    PageResult<FmSchedulingUser> getPage(FmSchedulingUser dto, PageParam pageParam);

    /**
     * 根据参数查询列表
     */
    List<FmSchedulingUser> getList(FmSchedulingUser dto);

    /**
     * 保存数据
     */
    void saveUser(FmSchedulingUser dto);

}
