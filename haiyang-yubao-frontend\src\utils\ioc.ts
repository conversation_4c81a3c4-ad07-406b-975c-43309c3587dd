/*
 * @Author: <PERSON>
 * @LastEditors: Alex
 * @Date: 2024-01-18
 * @Description: 生产环境切勿使用 仅用于测试
 */
import { AuthService } from 'src/service/authService'
import type { App } from 'vue'

const bindings: Map<string, any> = new Map()

export function bind<T>(tag: string, any: T) {
  bindings.set(tag, any)
}
export function get<T>(tag: string): T {
  // console.log(bindings.has(tag))
  const target = bindings.get(tag)
  return target as T
}

export class IocPlugin {
  static #holder: IocPlugin | undefined = undefined
  private constructor() {
    //
  }

  install(app: App, options?: { setup: () => void }): any {
    if (options) {
      options.setup()
    }
    // bind
    bind('auth', new AuthService())
  }

  /**
   * 保持单例
   * @returns 实例
   */
  static of(): IocPlugin {
    if (this.#holder === undefined) {
      this.#holder = new IocPlugin()
    }
    return this.#holder
  }
}

export function createIoc(): IocPlugin {
  return IocPlugin.of()
}
