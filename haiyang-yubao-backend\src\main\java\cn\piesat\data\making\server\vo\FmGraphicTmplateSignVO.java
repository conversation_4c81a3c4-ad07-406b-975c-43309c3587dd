package cn.piesat.data.making.server.vo;


import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * VO类
 *
 * <AUTHOR>
 * @date 2024-10-09 16:03:16
 */
public class FmGraphicTmplateSignVO implements Serializable {

    private static final long serialVersionUID = -48246949017257335L;

    private Long id;
    private String type;
    private String name;
    private String context;
    private Long templateId;
    private String position;
    private boolean disabled;

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
