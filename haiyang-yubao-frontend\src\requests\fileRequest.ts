/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-28 16:01:15
 * @LastEditors: 'duliuxin' '<EMAIL>'
 * @LastEditTime: 2022-10-31 08:46:42
 * @Description: public或服务器上其他文件获取
 * Copyright (c) 2022 by piesat, All Rights Reserved.
 */
import getAxios from '../utils/axios'

const Axios = getAxios('')

/**
 * @description: 获取文本资源
 * @param {*}
 * @return {*}
 */
export function getText(path: string) {
  return Axios.get(path)
}

export function getBlob(path: string) {
  return Axios.get(path, { responseType: 'blob' })
}
