package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 风暴潮警报制作表信息(StormSurgeAlarmB)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:34
 */
@TableName(value = "fm_storm_surge_alarm_b")
public class StormSurgeAlarm implements Serializable {
    private static final long serialVersionUID = -1;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 历史预警
     */
    private String historyAlarmLevel;
    /**
     * 预警级别
     */
    private Integer alarmLevel;
    /**
     * 标题
     */
    private String title;
    /**
     * 发布时间
     */
    private Date releaseTime;
    /**
     * 编号
     */
    private String number;
    /**
     * 上期编号
     */
    private String lastNumber;
    /**
     * 签发人
     */
    private Long signUser;
    /**
     * 制作人
     */
    private Long makeUser;
    /**
     * 综述
     */
    private String summarize;
    /**
     * 警报内容
     */
    private String alarmContent;
    /**
     * 防御指南
     */
    private String defenseGuide;
    /**
     * 短信
     */
    private String smsContent;
    /**
     * 警报图
     */
    private String alarmImages;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 签发人名
     */
    private String signUserName;
    /**
     * 制作人名
     */
    private String makeUserName;
    /**
     * 台风编号
     */
    private String typhoonNo;
    /**
     * 台风时间
     */
    private Date typhoonTime;
    /**
     * 删除标记
     */
    private Integer deleteFlag = 0;
    /**
     * 源头类型  1冷空气 2台风
     */
    private Integer sourceType;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 推送任务ID
     */
    private Long pushTaskId;
    /**
     * 预报时效
     */
    private Integer forecastTime;
    /**
     * 预报增水量
     */
    private String forecastSurge;
    /**
     * 台站预警信息
     */
    private String stationWarning;
    /**
     * 警报word文件路径
     */
    private String wordFilePath;
    /**
     * 短信txt文件路径
     */
    private String smsFilePath;
    /**
     * 警报html文件路径
     */
    private String htmlFilePath;
    /**
     * 预警区域
     **/
    private String alarmArea;
    /**
     * 是否展示 0展示1不展示
     */
    private Integer display = 0;

    /**
     * 城市警报内容
     */
    private String cityAlarmContent;

    /**
     * 警报时段
     */
    private String alarmTime;

    private String lastIdentifier;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHistoryAlarmLevel() {
        return historyAlarmLevel;
    }

    public void setHistoryAlarmLevel(String historyAlarmLevel) {
        this.historyAlarmLevel = historyAlarmLevel;
    }

    public Integer getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(Integer alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getLastNumber() {
        return lastNumber;
    }

    public void setLastNumber(String lastNumber) {
        this.lastNumber = lastNumber;
    }

    public Long getSignUser() {
        return signUser;
    }

    public void setSignUser(Long signUser) {
        this.signUser = signUser;
    }

    public Long getMakeUser() {
        return makeUser;
    }

    public void setMakeUser(Long makeUser) {
        this.makeUser = makeUser;
    }

    public String getSummarize() {
        return summarize;
    }

    public void setSummarize(String summarize) {
        this.summarize = summarize;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getDefenseGuide() {
        return defenseGuide;
    }

    public void setDefenseGuide(String defenseGuide) {
        this.defenseGuide = defenseGuide;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public String getAlarmImages() {
        return alarmImages;
    }

    public void setAlarmImages(String alarmImages) {
        this.alarmImages = alarmImages;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public String getMakeUserName() {
        return makeUserName;
    }

    public void setMakeUserName(String makeUserName) {
        this.makeUserName = makeUserName;
    }

    public String getTyphoonNo() {
        return typhoonNo;
    }

    public void setTyphoonNo(String typhoonNo) {
        this.typhoonNo = typhoonNo;
    }

    public Date getTyphoonTime() {
        return typhoonTime;
    }

    public void setTyphoonTime(Date typhoonTime) {
        this.typhoonTime = typhoonTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getPushTaskId() {
        return pushTaskId;
    }

    public void setPushTaskId(Long pushTaskId) {
        this.pushTaskId = pushTaskId;
    }

    public Integer getForecastTime() {
        return forecastTime;
    }

    public void setForecastTime(Integer forecastTime) {
        this.forecastTime = forecastTime;
    }

    public String getForecastSurge() {
        return forecastSurge;
    }

    public void setForecastSurge(String forecastSurge) {
        this.forecastSurge = forecastSurge;
    }

    public String getStationWarning() {
        return stationWarning;
    }

    public void setStationWarning(String stationWarning) {
        this.stationWarning = stationWarning;
    }

    public String getWordFilePath() {
        return wordFilePath;
    }

    public void setWordFilePath(String wordFilePath) {
        this.wordFilePath = wordFilePath;
    }

    public String getSmsFilePath() {
        return smsFilePath;
    }

    public void setSmsFilePath(String smsFilePath) {
        this.smsFilePath = smsFilePath;
    }

    public String getHtmlFilePath() {
        return htmlFilePath;
    }

    public void setHtmlFilePath(String htmlFilePath) {
        this.htmlFilePath = htmlFilePath;
    }

    public String getAlarmArea() {
        return alarmArea;
    }

    public void setAlarmArea(String alarmArea) {
        this.alarmArea = alarmArea;
    }

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public String getCityAlarmContent() {
        return cityAlarmContent;
    }

    public StormSurgeAlarm setCityAlarmContent(String cityAlarmContent) {
        this.cityAlarmContent = cityAlarmContent;
        return this;
    }

    public String getAlarmTime() {
        return alarmTime;
    }

    public StormSurgeAlarm setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
        return this;
    }

    public String getLastIdentifier() {
        return lastIdentifier;
    }

    public void setLastIdentifier(String lastIdentifier) {
        this.lastIdentifier = lastIdentifier;
    }
}

