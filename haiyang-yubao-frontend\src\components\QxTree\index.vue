<template>
  <div class="qx-tree-wrap">
    <div v-if="search" class="tree-search d-flex align-items-center">
      <label for="">名称搜索</label>
      <n-input
        v-model:value="searchVal"
        class="qx-input"
        placeholder="请输入"
        clearable
      >
        <template #suffix>
          <Search class="icon-search" @click="onSearch" />
        </template>
      </n-input>
    </div>
    <n-tree
      id="qx-tree"
      block-line
      :data="props.treeData"
      :default-expanded-keys="defaultExpandedKeys"
      :selectable="true"
      :key-field="props.defaultProps.key"
      :label-field="props.defaultProps.label"
      :children-field="props.defaultProps.children"
      :render-switcher-icon="renderSwitchIcon"
      :render-suffix="props.edit ? renderSuffix : undefined"
      :default-expand-all="defaultExpandAll"
      :default-selected-keys="defaultSelectedKeys"
      :expand-on-click="true"
      :node-props="nodeProps"
      :watch-props="['defaultSelectedKeys']"
      :cancelable="false"
      cascade
      :checkable="props.checkable"
      :allow-checking-not-loaded="false"
      @update:expanded-keys="handleExpandedKeys"
      @update:selected-keys="handleSelectedKeys"
      @update:checked-keys="handleCheckedKeys"
    />
    <div
      v-show="rightMenu"
      class="right-menu"
      :style="{ top: yRef + 'px', left: xRef + 'px' }"
    >
      <div v-if="isRename" class="menu-item" @click="rename">重命名</div>
      <div class="menu-item delete" @click="deleteNode">删除</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NSwitch } from 'naive-ui'
import { h, ref } from 'vue'
import type { TreeOption } from 'naive-ui'
import { Search } from '@vicons/ionicons5'
const searchVal = ref<string>('')

const props = defineProps({
  defaultExpandedKeys: {
    type: Array<string>,
    default() {
      return []
    }
  },
  treeData: {
    type: Array<TreeOption>,
    default() {
      return []
    }
  },
  defaultProps: {
    type: Object,
    default() {
      return {
        key: 'id',
        label: 'label',
        children: 'children'
      }
    }
  },
  defaultExpandAll: {
    type: Boolean,
    default() {
      return false
    }
  },
  defaultSelectedKeys: {
    type: Array<string>,
    default() {
      return []
    }
  },
  search: {
    type: Boolean,
    default() {
      return false
    }
  },
  edit: {
    type: Boolean,
    default() {
      return false
    }
  },
  icons: {
    // 自定义图标类名
    type: Array<string>,
    default() {
      return ['icon-add', 'icon-remove']
    }
  },
  switchDisabled: {
    type: Boolean,
    default() {
      return false
    }
  },
  haveRightMenu: {
    type: Boolean,
    default() {
      return false
    }
  },
  isRename: {
    type: Boolean,
    default() {
      return true
    }
  },
  checkable: {
    type: Boolean,
    default() {
      return false
    }
  }
})
// 渲染自定义图标
function renderSwitchIcon(info: { option: TreeOption; expanded: boolean }) {
  const option: TreeOption = info.option
  const expanded: boolean = info.expanded
  const children = props.defaultProps.children

  if (option[children]) {
    return h('i', {
      class: `icon ${expanded ? props.icons[1] : props.icons[0]}`
    })
  } else {
    return h('i', {
      class: `icon ${props.icons.length === 3 ? props.icons[2] : ''}`,
      style: {
        display: props.icons[2] ? 'block' : 'none'
      }
    })
  }
}
const rightMenu = ref(false)
const xRef = ref(0)
const yRef = ref(0)
const curRightData = ref<TreeOption>()
function nodeProps({ option }: { option: TreeOption }) {
  return {
    onClick() {
      rightMenu.value = false
      emit('click', option)
    },
    onContextmenu(e: MouseEvent): void {
      const childrenLabel = props.defaultProps.children
      if (props.haveRightMenu && !option[childrenLabel]) {
        rightMenu.value = true
        xRef.value = e.clientX
        yRef.value = e.clientY
        curRightData.value = option
        e.preventDefault()
      }
    }
  }
}
// 右键渲染内容点击事件
function rename() {
  emit('reName', curRightData.value)
}
function deleteNode() {
  emit('deleteNode', curRightData.value)
}
function hiddenRightMenu() {
  rightMenu.value = false
}
// 渲染switch开关
function renderSuffix(info: { option: TreeOption }) {
  const option: TreeOption = info.option
  const children = props.defaultProps.children
  if (!option[children] && option[children] !== null) {
    return h(NSwitch, {
      value: option.status ? true : false,
      disabled: option.status ? props.switchDisabled : false,
      'on-update:value': (val: boolean) => changeSwitch(val, option),
      size: 'small'
    })
  }
}

const emit = defineEmits([
  'changeSwitch',
  'search',
  'click',
  'expanded',
  'selected',
  'onLoad',
  'click',
  'reName',
  'deleteNode',
  'checked'
])

function changeSwitch(val: boolean, option: TreeOption) {
  changeStatus(props.treeData, option)
  emit('changeSwitch', val, option)
}

function changeStatus(data: any[], option: TreeOption) {
  const children = props.defaultProps.children
  data.forEach(item => {
    if (item.id == option.id) {
      item.status = !option.status
    } else if (item[children] && item[children].length) {
      changeStatus(item[children], option)
    }
  })
}

function handleSelectedKeys(
  keys: string[],
  option: Array<TreeOption | null>,
  meta: any
) {
  if (option?.length) {
    console.log(option, 'optin----')
    emit('selected', keys, option[0], meta)
  }
}
function handleCheckedKeys(keys: string[],
  option: Array<TreeOption | null>,
  meta: any){
if (option?.length) {
    console.log(option, 'optin----')
    emit('checked', keys, option[0], meta)
  }
}

//获取当前点击数据key
function handleExpandedKeys(expandedKeys: string[]) {
  // emit('expanded', expandedKeys)
}

function onSearch() {
  emit('search', searchVal.value)
}
defineExpose({
  hiddenRightMenu
})
</script>

<style lang="scss">
.qx-tree-wrap {
  flex: 1;
}
#qx-tree {
  box-sizing: border-box;
  padding: 12px 20px;
  flex: 1;
  overflow-y: auto;
  .n-tree-node-switcher__icon {
    width: auto;
    height: auto;
    i.icon {
      width: 10px;
      height: 10px;
    }
  }
  .n-tree-node-content__text {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #000000;
    line-height: 20px;
    margin-left: 4px;
  }
  .n-tree-node-switcher--hide {
    visibility: visible;
  }
  .n-tree-node-switcher.n-tree-node-switcher--expanded {
    transform: rotate(0deg) !important;
  }
  // .n-switch{
  //   width: 32.76px;
  //   height: 16px;
  //   min-width: auto;
  //   .n-switch__rail{
  //     width:100%;
  //     height: 100%;
  //   }
  // }
}
.tree-search {
  box-sizing: border-box;
  padding: 0 20px;
  label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #222222;
    line-height: 32px;
    // width: 80px;
    flex-shrink: 0;
    text-align: right;
    margin-right: 15px;
  }
  .icon-search {
    width: 17px;
    height: 17px;
    cursor: pointer;
  }
}
.right-menu {
  position: absolute;
  background: #ffffff;
  border-radius: 3px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  .menu-item {
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      background-color: rgb(243, 243, 245);
    }
    // &.delete{
    //   color:#d03050;
    // }
  }
}
</style>
