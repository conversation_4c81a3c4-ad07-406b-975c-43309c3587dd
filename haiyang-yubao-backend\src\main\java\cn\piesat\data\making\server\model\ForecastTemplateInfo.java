package cn.piesat.data.making.server.model;

import cn.piesat.data.making.server.entity.ForecastTemplate;
import lombok.Data;

import java.util.List;

/**
 * 预报模板信息
 *
 * <AUTHOR>
 */
@Data
public class ForecastTemplateInfo {
    /**
     * 预报类型
     **/
    private String code;
    /**
     * 排序
     **/
    private Integer sort;
    /**
     * 预报类型名称
     **/
    private String name;
    /**
     * 预报模板列表
     **/
    private List<ForecastTemplate> forecastTemplateList;
}
