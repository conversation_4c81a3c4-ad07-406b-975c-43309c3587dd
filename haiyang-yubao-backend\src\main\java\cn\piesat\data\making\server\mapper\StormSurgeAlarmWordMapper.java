package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import cn.piesat.data.making.server.dto.generate.StormSurgeAlarmWordDTO;
import cn.piesat.data.making.server.dto.generate.StormSurgeMessageWordDTO;
import cn.piesat.data.making.server.entity.AlarmLevel;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StormSurgeAlarmWordMapper {
    StormSurgeAlarmWordMapper INSTANCE = Mappers.getMapper(StormSurgeAlarmWordMapper.class);

    @Mapping(target = "time", source = "stormSurgeAlarm.releaseTime", dateFormat = "yyyy年MM月dd日HH时")
    @Mapping(target = "stationWarning", source = "stationWarning")
    StormSurgeAlarmWordDTO toDTO(StormSurgeAlarm stormSurgeAlarm, AlarmLevel alarmLevel, UserInfoDTO userInfo, String fax, List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> stationWarning);
}
