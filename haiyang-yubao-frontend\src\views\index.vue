<template>
  <div class="index">
    <header-bar class="index-header">
      <template #title>
        <h2>海南海洋业务一体化平台</h2>
      </template>
    </header-bar>

    <video
      src="src/assets/images/index/bg.mp4"
      autoplay="true"
      loop="true"
      muted="true"
    ></video>

    <div class="index-content">
      <div class="duty-info">
        <div class="duty-item">
          <span><i class="icon icon-date"></i>今日值班</span>
          <span>{{ currentDay }} {{ week }}</span>
        </div>
        <ul class="duty-list">
          <li><span>岗位</span><span>值班人员</span></li>
          <li v-for="item in dutyList" :key="item.id">
            <span>{{ item.postType }}</span>
            <span>{{ item.name }}</span>
          </li>
        </ul>
      </div>

      <div class="menu-list">
        <div
          class="menu-item"
          v-for="item in menuList"
          :key="item.path"
          @click="changeSystem(item)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HeaderBar } from 'src/components/HeaderBar'
import moment from 'moment'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentDay = ref(moment(new Date()).format('YYYY-MM-DD'))
const week = ref(getWeek(new Date()))
function getWeek(date: Date) {
  // 参数时间戳
  let week = moment(date).day()
  switch (week) {
    case 1:
      return '星期一'
    case 2:
      return '星期二'
    case 3:
      return '星期三'
    case 4:
      return '星期四'
    case 5:
      return '星期五'
    case 6:
      return '星期六'
    case 0:
      return '星期日'
  }
}

const dutyList = ref<any>([
  {
    name: '李四',
    postType: '监控员'
  },
  {
    name: '李四',
    postType: '操作员'
  },
  {
    name: '李四',
    postType: '操作员'
  }
])

type menuType = {
  [key: string]: string
}

const menuList = ref<menuType[]>([
  {
    name: '业务制作',
    path: '/businessProduct'
  },
  {
    name: '预报检验',
    path: '/forecast'
  },
  {
    name: '系统管理',
    path: '/systemManage'
  },
  {
    name: '待建设',
    path: ''
  }
])

function changeSystem(item: menuType) {
  router.push(item.path)
}
</script>

<style scoped lang="scss">
.index {
  position: relative;
  background: #666;
  width: 100%;
  height: 100%;
  .index-content {
    width: 100%;
    height: calc(100% - 79px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 79px;
    left: 0;
    z-index: 1;
  }
  .index-header {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    h2 {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 40px;
      line-height: 47px;
      letter-spacing: 6px;
      // text-shadow: 0px 4px 4px rgba(0,0,0,0.25);
      text-align: center;
      font-style: normal;
      text-transform: none;
      background-image: linear-gradient(
        269.99999999996083deg,
        #9dd0ff 0%,
        #ffffff 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      flex: 1;
    }
  }
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
  .duty-info {
    position: absolute;
    top: 0;
    left: 59px;
    width: 412px;
    z-index: 1;
    border-radius: 9px;
    overflow: hidden;
    background: linear-gradient(180deg, #1a48a2 0%, #17346c 100%);
    .duty-item {
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 15px 16px;
      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
        color: #ffffff;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        .icon {
          margin-right: 8px;
        }
      }
    }
    .duty-list {
      li {
        display: flex;
        box-sizing: border-box;
        padding: 7px 0;
        &:nth-child(1) {
          padding: 12px 0;
        }
        &:nth-child(2n) {
          background: rgba(255, 255, 255, 0.05);
        }
        span {
          flex: 1;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 19px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          &:nth-child(1) {
            padding-left: 44px;
          }
        }
      }
    }
  }
  .menu-list {
    display: flex;
    align-items: center;
    .menu-item {
      background-image: url(src/assets/images/index/1.png);
      background-size: 100% 100%;
      margin-right: 67px;
      box-sizing: border-box;
      padding: 290px 106px 135px 104px;
      cursor: pointer;

      span {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 30px;
        line-height: 35px;
        letter-spacing: 3px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        background-image: linear-gradient(90deg, #ffffff 21%, #5ba7ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &:nth-child(2) {
        background-image: url(src/assets/images/index/2.png);
      }
      &:nth-child(3) {
        background-image: url(src/assets/images/index/3.png);
      }
      &:nth-child(4) {
        background-image: url(src/assets/images/index/4.png);
        margin-right: 0;
      }
    }
  }
}
</style>