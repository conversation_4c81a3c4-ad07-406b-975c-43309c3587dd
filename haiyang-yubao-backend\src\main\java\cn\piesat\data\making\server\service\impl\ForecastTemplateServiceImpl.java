package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.common.CodeGenerator;
import cn.piesat.data.making.server.dao.ForecastTemplateDao;
import cn.piesat.data.making.server.dto.ForecastTemplateDTO;
import cn.piesat.data.making.server.entity.AreaType;
import cn.piesat.data.making.server.entity.ForecastTemplate;
import cn.piesat.data.making.server.mapper.ForecastTemplateMapper;
import cn.piesat.data.making.server.model.ForecastTemplateInfo;
import cn.piesat.data.making.server.service.AreaTypeService;
import cn.piesat.data.making.server.service.ForecastTemplateColumnService;
import cn.piesat.data.making.server.service.ForecastTemplateRowService;
import cn.piesat.data.making.server.service.ForecastTemplateService;
import cn.piesat.data.making.server.vo.ForecastTemplateVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预报模板表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastTemplateServiceImpl extends ServiceImpl<ForecastTemplateDao, ForecastTemplate> implements ForecastTemplateService {

    @Resource
    private ForecastTemplateRowService forecastTemplateRowService;
    @Resource
    private ForecastTemplateColumnService forecastTemplateColumnService;
    @Resource
    private AreaTypeService areaTypeService;

    @Override
    public List<ForecastTemplateInfo> getTreeList(Boolean status) {
        List<ForecastTemplateInfo> list = new ArrayList<>();

        List<ForecastTemplate> templateList = this.list(createQueryWrapper(null, status, null));
        Map<String, List<ForecastTemplate>> templateMap = templateList.stream().collect(Collectors.groupingBy(ForecastTemplate::getForecastType));

        LinkedHashMap<String, AreaType> map = this.getForecastType();
        map.entrySet().forEach(entry -> {
            ForecastTemplateInfo info = new ForecastTemplateInfo();
            info.setCode(entry.getKey());
            info.setName(entry.getValue().getName());
            info.setSort(entry.getValue().getSort());
            info.setForecastTemplateList(templateMap.get(entry.getKey()));
            list.add(info);
        });
        return list;
    }

    @Override
    public List<ForecastTemplateVO> getList(Boolean status) {
        List<ForecastTemplate> list = this.list(createQueryWrapper(null, status, null));
        return ForecastTemplateMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public ForecastTemplateVO getInfoById(Long id) {
        ForecastTemplateVO vo = ForecastTemplateMapper.INSTANCE.entityToVo(this.getById(id));
        vo.setRowList(forecastTemplateRowService.getList(id));
        vo.setColumnList(forecastTemplateColumnService.getList(id));
        return vo;
    }

    @Override
    public Long save(ForecastTemplateDTO dto) {
        ForecastTemplate entity = ForecastTemplateMapper.INSTANCE.dtoToEntity(dto);
        if (dto.getId() == null) {
            List<ForecastTemplate> entityList = this.list(createQueryWrapper(entity.getName(), null, null));
            if (!CollectionUtils.isEmpty(entityList)) {
                throw new BusinessException("该名称已经存在！");
            }
            entity.setCode(entity.getForecastType() + "_" + CodeGenerator.getPinYinHeadChar(entity.getName()));
            this.save(entity);
        } else {
            this.updateById(entity);
            forecastTemplateRowService.saveList(entity.getId(), dto.getRowList());
            forecastTemplateColumnService.saveList(entity.getId(), dto.getColumnList());
        }
        return entity.getId();
    }

    @Override
    public LinkedHashMap<String, AreaType> getForecastType() {
        LinkedHashMap<String, AreaType> map = new LinkedHashMap<>();
        LambdaQueryWrapper<AreaType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(AreaType::getSort);
        List<AreaType> areaTypeList = areaTypeService.list(queryWrapper);
        areaTypeList.forEach(type -> {
            type.setName(type.getName() + "预报");
            map.put(type.getCode(), type);
        });
        AreaType areaType = new AreaType();
        areaType.setCode("station");
        areaType.setName("站点预报");
        map.put("station", areaType);
        return map;
    }

    @Override
    public void deleteById(Long id) {
        this.removeById(id);
    }

    private LambdaQueryWrapper<ForecastTemplate> createQueryWrapper(String name, Boolean status, String forecastType) {
        LambdaQueryWrapper<ForecastTemplate> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.eq(ForecastTemplate::getName, name);
        }
        if (status != null) {
            queryWrapper.eq(ForecastTemplate::getStatus, status);
        }
        if (StringUtils.isNotBlank(forecastType)) {
            queryWrapper.eq(ForecastTemplate::getForecastType, forecastType);
        }
        return queryWrapper.orderByAsc(ForecastTemplate::getSort, ForecastTemplate::getCreateTime);
    }
}





