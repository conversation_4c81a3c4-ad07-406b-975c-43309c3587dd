package cn.piesat.data.making.server.entity;

import java.util.Date;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import cn.piesat.common.utils.Constant;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:29
 */
@Data
@Accessors(chain = true)
@TableName("fm_isoline_data")
public class FmIsolineData implements Serializable {

    private static final long serialVersionUID = 367018270680654181L;


    @TableField("id")
    private Long id;
    @TableField("name")
    private String name;
    @TableField("local_geo")
    private String localGeo;
    @TableField("local_json")
    private String localJson;
    @TableField("start_report_time")
    private Date startReportTime;
    @TableField("forecast_time")
    private Date forecastTime;
    @TableField("data_source")
    private String dataSource;

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLocalGeo() {
        return localGeo;
    }

    public void setLocalGeo(String localGeo) {
        this.localGeo = localGeo;
    }

    public String getLocalJson() {
        return localJson;
    }

    public void setLocalJson(String localJson) {
        this.localJson = localJson;
    }

    public Date getStartReportTime() {
        return startReportTime;
    }

    public void setStartReportTime(Date startReportTime) {
        this.startReportTime = startReportTime;
    }

    public Date getForecastTime() {
        return forecastTime;
    }

    public void setForecastTime(Date forecastTime) {
        this.forecastTime = forecastTime;
    }
}
