package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.StormSurgeMessageDao;
import cn.piesat.data.making.server.dto.StormSurgeMessageDTO;
import cn.piesat.data.making.server.dto.generate.StormSurgeMessageWordDTO;
import cn.piesat.data.making.server.entity.StormSurgeMessage;
import cn.piesat.data.making.server.enums.ProductEnum;
import cn.piesat.data.making.server.enums.WarningMessageStatus;
import cn.piesat.data.making.server.mapper.StormSurgeMessageMapper;
import cn.piesat.data.making.server.mapper.StormSurgeMessageWordMapper;
import cn.piesat.data.making.server.service.GenerateProductService;
import cn.piesat.data.making.server.service.StormSurgeMessageService;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import cn.piesat.security.ucenter.starter.utils.UserUtils;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 风暴潮消息制作(StormSurgeMessageB)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:37
 */
@Service("stormSurgeMessageBService")
public class StormSurgeMessageServiceImpl extends ServiceImpl<StormSurgeMessageDao, StormSurgeMessage> implements StormSurgeMessageService {
    @Value("${piesat.make.fax}")
    private String fax;
    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Value("${piesat.make.surges-message-template}")
    private String surgesMessageTemplate;
    @Autowired
    private GenerateProductService generateProductService;
    @Override
    public PageResult pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime) {
        LambdaQueryWrapper<StormSurgeMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(StormSurgeMessage::getCreateTime);
        if (startTime != null) {
            wrapper.ge(StormSurgeMessage::getReleaseTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(StormSurgeMessage::getReleaseTime, endTime);
        }
        Page<StormSurgeMessage> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }

    @Override
    public void checkNumber(String number) {
        StormSurgeMessage stormSurgeMessage = this.selectByNumber(number);
        if(Objects.nonNull(stormSurgeMessage)) throw new BusinessException("该编号已存在");
    }

    @Override
    public StormSurgeMessage selectByNumber(String number) {
        LambdaQueryWrapper<StormSurgeMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StormSurgeMessage::getNumber,number);
        return this.getOne(wrapper, false);
    }

    @Override
    public StormSurgeMessage saveInfo(StormSurgeMessageDTO stormSurgeMessageDTO) {
        this.checkNumber(stormSurgeMessageDTO.getNumber());
        StormSurgeMessage stormSurgeMessage = StormSurgeMessageMapper.INSTANCE.toEntity(stormSurgeMessageDTO);
        stormSurgeMessage.setStatus(WarningMessageStatus.NOT_SUBMIT.getValue());
        this.save(stormSurgeMessage);
        return stormSurgeMessage;
    }

    @Override
    public StormSurgeMessage updateInfo(StormSurgeMessageDTO stormSurgeMessageDTO) {
        StormSurgeMessage stormSurgeMessage = Optional.ofNullable(this.getById(stormSurgeMessageDTO.getId())).orElseThrow(() -> new BusinessException("未找到对应警报消息"));
        if(WarningMessageStatus.SUBMIT.getValue().equals(stormSurgeMessage.getStatus())) throw new BusinessException("已提交状态警报消息不可修改");
        if(!stormSurgeMessageDTO.getNumber().equals(stormSurgeMessage.getNumber())){this.checkNumber(stormSurgeMessageDTO.getNumber());}
        stormSurgeMessage = StormSurgeMessageMapper.INSTANCE.toEntity(stormSurgeMessageDTO);
        this.updateById(stormSurgeMessage);
        return stormSurgeMessage;
    }

    @Override
    public void release(StormSurgeMessageDTO stormSurgeMessageDTO) {
        StormSurgeMessage stormSurgeMessage;
        if(Objects.nonNull(stormSurgeMessageDTO.getId())){
            stormSurgeMessage = this.updateInfo(stormSurgeMessageDTO);
        }else {
            stormSurgeMessage = this.saveInfo(stormSurgeMessageDTO);
        }
        //查询制作人信息
        UserInfoDTO userInfo = UserUtils.getUserInfo(stormSurgeMessage.getMakeUser());
        StormSurgeMessageWordDTO stormSurgeMessageWordDTO = StormSurgeMessageWordMapper.INSTANCE.toDTO(stormSurgeMessage, userInfo, fax);
        //拼接产出文件地址
        Date releaseTime = stormSurgeMessage.getReleaseTime();
        String wordFilePath = String.format(CommonConstant.MESSAGE_SURGES_WORD_FILE_NAME,baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateWord(wordFilePath,surgesMessageTemplate, stormSurgeMessageWordDTO);
        String htmlFilePath = String.format(CommonConstant.MESSAGE_SURGES_HTML_FILE_NAME,baseOutputPath,DateUtil.year(releaseTime), DateUtil.format(releaseTime,
                "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateHtml(wordFilePath,htmlFilePath);
        String smsFilePath = String.format(CommonConstant.MESSAGE_SURGES_TXT_FILE_NAME, baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateSms(smsFilePath,stormSurgeMessageDTO.getSmsContent());
        Long pushTaskId = generateProductService.sendPushServer("风暴潮消息产品推送", wordFilePath, htmlFilePath, smsFilePath, ProductEnum.SURGES_MESSAGE_WORD, ProductEnum.SURGES_MESSAGE_HTML,
                ProductEnum.SURGES_MESSAGE_SMS, releaseTime);
        stormSurgeMessage.setPushTaskId(pushTaskId);
        stormSurgeMessage.setStatus(WarningMessageStatus.SUBMIT.getValue());
        this.updateById(stormSurgeMessage);
    }
}

