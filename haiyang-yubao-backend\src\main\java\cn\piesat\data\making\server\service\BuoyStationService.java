package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.BuoyStationDataDTO;
import cn.piesat.data.making.server.entity.BuoyStation;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.vo.BuoyStationDataVO;
import cn.piesat.data.making.server.vo.FillMapVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 浮标站表服务接口
 *
 * <AUTHOR>
 */
public interface BuoyStationService extends IService<BuoyStation> {

    /**
     * 查询列表
     */
    List<StationInfo> getList(String name);

    /**
     * 查询浮标数据
     */
    List<BuoyStationDataVO> getDataList(BuoyStationDataDTO dto);

    /**
     * 查询浮标数据
     */
    FillMapVO getGeoRangeDataList(BuoyStationDataDTO dto);
}




