package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 深海大型锚系浮标实时数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_large_deepsea_mooringbuoy_rt")
public class OceanLargeDeepseaMooringbuoyRt implements Serializable {

    private static final long serialVersionUID = -49152895017294439L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 浮标站ID
     **/
    @JsonProperty("BuoyInfo_Id")
    @TableField("buoyinfo_id")
    private String buoyinfoId;
    /**
     * 浮标类型
     **/
    @JsonProperty("BuoyInfo_Type")
    @TableField("buoyinfo_type")
    private String buoyinfoType;
    /**
     * 浮标名称
     **/
    @JsonProperty("BuoyInfo_Name")
    @TableField("buoyinfo_name")
    private String buoyinfoName;
    /**
     * 浮标编号
     **/
    @JsonProperty("BuoyInfo_No")
    @TableField("buoyinfo_no")
    private String buoyinfoNo;
    /**
     * 浮标种类
     **/
    @JsonProperty("BuoyInfo_Kind")
    @TableField("buoyinfo_kind")
    private String buoyinfoKind;
    /**
     * 浮标运行状态
     **/
    @JsonProperty("RunningStatus_Style")
    @TableField("runningstatus_style")
    private String runningstatusStyle;
    /**
     * 浮标运行模式
     **/
    @JsonProperty("RunningStatus_Status")
    @TableField("runningstatus_status")
    private String runningstatusStatus;
    /**
     * 浮标电池电压
     **/
    @JsonProperty("RunningStatus_DY")
    @TableField("runningstatus_dy")
    private String runningstatusDy;
    /**
     * 浮标姿态斜度
     **/
    @JsonProperty("RunningStatus_lean")
    @TableField("runningstatus_lean")
    private Date runningstatusLean;
    /**
     * 浮标姿态方位
     **/
    @JsonProperty("RunningStatus_azimuth")
    @TableField("runningstatus_azimuth")
    private String runningstatusAzimuth;
    /**
     * 风速
     **/
    @JsonProperty("BuoyData_WS")
    @TableField("buoydata_ws")
    private String buoydataWs;
    /**
     * 风速要素时间
     **/
    @JsonProperty("BuoyData_WS_rt")
    @TableField("buoydata_ws_rt")
    private Date buoydataWsRt;
    /**
     * 风向
     **/
    @JsonProperty("BuoyData_WD")
    @TableField("buoydata_wd")
    private String buoydataWd;
    /**
     * 风向要素时间
     **/
    @JsonProperty("BuoyData_WD_rt")
    @TableField("buoydata_wd_rt")
    private Date buoydataWdRt;
    /**
     * 最大风速
     **/
    @JsonProperty("BuoyData_WSM")
    @TableField("buoydata_wsm")
    private String buoydataWsm;
    /**
     * 最大风速要素时间
     **/
    @JsonProperty("BuoyData_WSM_rt")
    @TableField("buoydata_wsm_rt")
    private Date buoydataWsmRt;
    /**
     * 最大风速的风向
     **/
    @JsonProperty("BuoyData_WDM")
    @TableField("buoydata_wdm")
    private String buoydataWdm;
    /**
     * 最大风速的风向要素时间
     **/
    @JsonProperty("BuoyData_WDM_rt")
    @TableField("buoydata_wdm_rt")
    private String buoydataWdmRt;
    /**
     * 瞬时风速
     **/
    @JsonProperty("BuoyData_WSA")
    @TableField("buoydata_wsa")
    private String buoydataWsa;
    /**
     * 瞬时风速要素时间
     **/
    @JsonProperty("BuoyData_WSA_rt")
    @TableField("buoydata_wsa_rt")
    private Date buoydataWsaRt;
    /**
     * 瞬时风向
     **/
    @JsonProperty("BuoyData_WDA")
    @TableField("buoydata_wda")
    private String buoydataWda;
    /**
     * 瞬时风向要素时间
     **/
    @JsonProperty("BuoyData_WDA_rt")
    @TableField("buoydata_wda_rt")
    private Date buoydataWdaRt;
    /**
     * 极大风速
     **/
    @JsonProperty("BuoyData_WSH")
    @TableField("buoydata_wsh")
    private String buoydataWsh;
    /**
     * 极大风速要素时间
     **/
    @JsonProperty("BuoyData_WSH_rt2")
    @TableField("buoydata_wsh_rt2")
    private Date buoydataWshRt2;
    /**
     * 极大风速的风向
     **/
    @JsonProperty("BuoyData_WDH")
    @TableField("buoydata_wdh")
    private String buoydataWdh;
    /**
     * 极大风速的风向要素时间
     **/
    @JsonProperty("BuoyData_WDH_rt")
    @TableField("buoydata_wdh_rt")
    private Date buoydataWdhRt;
    /**
     * 气温
     **/
    @JsonProperty("BuoyData_AT")
    @TableField("buoydata_at")
    private String buoydataAt;
    /**
     * 气温要素时间
     **/
    @JsonProperty("BuoyData_AT_rt")
    @TableField("buoydata_at_rt")
    private Date buoydataAtRt;
    /**
     * 气压
     **/
    @JsonProperty("BuoyData_BP")
    @TableField("buoydata_bp")
    private String buoydataBp;
    /**
     * 气压要素时间
     **/
    @JsonProperty("BuoyData_BP_rt")
    @TableField("buoydata_bp_rt")
    private Date buoydataBpRt;
    /**
     * 相对湿度
     **/
    @JsonProperty("BuoyData_HU")
    @TableField("buoydata_hu")
    private String buoydataHu;
    /**
     * 相对湿度要素时间
     **/
    @JsonProperty("BuoyData_HU_rt")
    @TableField("buoydata_hu_rt")
    private Date buoydataHuRt;
    /**
     * 表层水温
     **/
    @JsonProperty("BuoyData_WT")
    @TableField("buoydata_wt")
    private String buoydataWt;
    /**
     * 表层水温要素时间
     **/
    @JsonProperty("BuoyData_WT_rt")
    @TableField("buoydata_wt_rt")
    private Date buoydataWtRt;
    /**
     * 表层盐度
     **/
    @JsonProperty("BuoyData_SL")
    @TableField("buoydata_sl")
    private String buoydataSl;
    /**
     * 表层盐度要素时间
     **/
    @JsonProperty("BuoyData_SL_rt")
    @TableField("buoydata_sl_rt")
    private Date buoydataSlRt;
    /**
     * 平均波高
     **/
    @JsonProperty("BuoyData_BG")
    @TableField("buoydata_bg")
    private String buoydataBg;
    /**
     * 平均波高要素时间
     **/
    @JsonProperty("BuoyData_BG_rt")
    @TableField("buoydata_bg_rt")
    private Date buoydataBgRt;
    /**
     * 平均波向
     **/
    @JsonProperty("BuoyData_BX")
    @TableField("buoydata_bx")
    private String buoydataBx;
    /**
     * 平均波向要素时间
     **/
    @JsonProperty("BuoyData_BX_rt")
    @TableField("buoydata_bx_rt")
    private Date buoydataBxRt;
    /**
     * 平均波周期
     **/
    @JsonProperty("BuoyData_ZQ")
    @TableField("buoydata_zq")
    private String buoydataZq;
    /**
     * 平均波周期要素时间
     **/
    @JsonProperty("BuoyData_ZQ_rt")
    @TableField("buoydata_zq_rt")
    private Date buoydataZqRt;
    /**
     * 有效波高
     **/
    @JsonProperty("BuoyData_YBG")
    @TableField("buoydata_ybg")
    private String buoydataYbg;
    /**
     * 有效波高要素时间
     **/
    @JsonProperty("BuoyData_YBG_rt")
    @TableField("buoydata_ybg_rt")
    private Date buoydataYbgRt;
    /**
     * 有效波周期
     **/
    @JsonProperty("BuoyData_YZQ")
    @TableField("buoydata_yzq")
    private String buoydataYzq;
    /**
     * 有效波周期要素时间
     **/
    @JsonProperty("BuoyData_YZQ_rt")
    @TableField("buoydata_yzq_rt")
    private Date buoydataYzqRt;
    /**
     * 1/10波高
     **/
    @JsonProperty("BuoyData_TenthBG")
    @TableField("buoydata_tenthbg")
    private String buoydataTenthbg;
    /**
     * 1/10波高要素时间
     **/
    @JsonProperty("BuoyData_TenthBG_rt")
    @TableField("buoydata_tenthbg_rt")
    private Date buoydataTenthbgRt;
    /**
     * 1/10周期
     **/
    @JsonProperty("BuoyData_TenthZQ")
    @TableField("buoydata_tenthzq")
    private String buoydataTenthzq;
    /**
     * 1/10周期要素时间
     **/
    @JsonProperty("BuoyData_TenthZQ_rt")
    @TableField("buoydata_tenthzq_rt")
    private Date buoydataTenthzqRt;
    /**
     * 最大波高
     **/
    @JsonProperty("BuoyData_ZBG")
    @TableField("buoydata_zbg")
    private String buoydataZbg;
    /**
     * 最大波高要素时间
     **/
    @JsonProperty("BuoyData_ZBG_rt")
    @TableField("buoydata_zbg_rt")
    private Date buoydataZbgRt;
    /**
     * 最大波周期
     **/
    @JsonProperty("BuoyData_ZZQ")
    @TableField("buoydata_zzq")
    private String buoydataZzq;
    /**
     * 最大波周期要素时间
     **/
    @JsonProperty("BuoyData_ZZQ_rt")
    @TableField("buoydata_zzq_rt")
    private Date buoydataZzqRt;
    /**
     * 波数
     **/
    @JsonProperty("BuoyData_BS")
    @TableField("buoydata_bs")
    private String buoydataBs;
    /**
     * 波数要素时间
     **/
    @JsonProperty("BuoyData_BS_rt")
    @TableField("buoydata_bs_rt")
    private Date buoydataBsRt;
    /**
     * 叶绿素
     **/
    @JsonProperty("BuoyData_YLS")
    @TableField("buoydata_yls")
    private String buoydataYls;
    /**
     * 叶绿素要素时间
     **/
    @JsonProperty("BuoyData_YLS_rt")
    @TableField("buoydata_yls_rt")
    private Date buoydataYlsRt;
    /**
     * 浊度
     **/
    @JsonProperty("BuoyData_ZD")
    @TableField("buoydata_zd")
    private String buoydataZd;
    /**
     * 浊度要素时间
     **/
    @JsonProperty("BuoyData_ZD_rt")
    @TableField("buoydata_zd_rt")
    private Date buoydataZdRt;
    /**
     * 能见度
     **/
    @JsonProperty("BuoyData_NJD")
    @TableField("buoydata_njd")
    private String buoydataNjd;
    /**
     * 能见度要素时间
     **/
    @JsonProperty("BuoyData_NJD_rt")
    @TableField("buoydata_njd_rt")
    private Date buoydataNjdRt;
    /**
     * 表层流速
     **/
    @JsonProperty("BuoyData_CS")
    @TableField("buoydata_cs")
    private String buoydataCs;
    /**
     * 表层流速质控符
     **/
    @JsonProperty("BuoyData_CS_rt")
    @TableField("buoydata_cs_rt")
    private Date buoydataCsRt;
    /**
     * 表层流向
     **/
    @JsonProperty("BuoyData_CD")
    @TableField("buoydata_cd")
    private String buoydataCd;
    /**
     * 表层流向要素时间
     **/
    @JsonProperty("BuoyData_CD_rt")
    @TableField("buoydata_cd_rt")
    private Date buoydataCdRt;
    /**
     * 溶解氧
     **/
    @JsonProperty("BuoyData_DO")
    @TableField("buoydata_do")
    private String buoydataDo;
    /**
     * 溶解氧要素时间
     **/
    @JsonProperty("BuoyData_DO_rt")
    @TableField("buoydata_do_rt")
    private Date buoydataDoRt;
    /**
     * 叶绿素A
     **/
    @JsonProperty("BuoyData_CHLA")
    @TableField("buoydata_chla")
    private String buoydataChla;
    /**
     * 叶绿素A要素时间
     **/
    @JsonProperty("BuoyData_CHLA_rt")
    @TableField("buoydata_chla_rt")
    private Date buoydataChlaRt;
    /**
     * 氨氮
     **/
    @JsonProperty("BuoyData_NH4_N")
    @TableField("buoydata_nh4_n")
    private String buoydataNh4N;
    /**
     * 氨氮要素时间
     **/
    @JsonProperty("BuoyData_NH4_N_rt")
    @TableField("buoydata_nh4_n_rt")
    private Date buoydataNh4NRt;
    /**
     * 亚硝酸盐
     **/
    @JsonProperty("BuoyData_NO2_N")
    @TableField("buoydata_no2_n")
    private String buoydataNo2N;
    /**
     * 亚硝酸盐要素时间
     **/
    @JsonProperty("BuoyData_NO2_N_rt")
    @TableField("buoydata_no2_n_rt")
    private Date buoydataNo2NRt;
    /**
     * 硝酸盐
     **/
    @JsonProperty("BuoyData_NO3_N")
    @TableField("buoydata_no3_n")
    private String buoydataNo3N;
    /**
     * 硝酸盐要素时间
     **/
    @JsonProperty("BuoyData_NO3_N_rt")
    @TableField("buoydata_no3_n_rt")
    private Date buoydataNo3NRt;
    /**
     * 磷酸盐
     **/
    @JsonProperty("BuoyData_PO4_P")
    @TableField("buoydata_po4_p")
    private String buoydataPo4P;
    /**
     * 磷酸盐要素时间
     **/
    @JsonProperty("BuoyData_PO4_P_rt")
    @TableField("buoydata_po4_p_rt")
    private Date buoydataPo4PRt;
    /**
     * 硅酸盐
     **/
    @JsonProperty("BuoyData_SiO3_Si")
    @TableField("buoydata_sio3_si")
    private String buoydataSio3Si;
    /**
     * 硅酸盐要素时间
     **/
    @JsonProperty("BuoyData_SiO3_Si_rt")
    @TableField("buoydata_sio3_si_rt")
    private Date buoydataSio3SiRt;
    /**
     * PH
     **/
    @JsonProperty("BuoyData_pH")
    @TableField("buoydata_ph")
    private String buoydataPh;
    /**
     * PH要素时间
     **/
    @JsonProperty("BuoyData_pH_rt")
    @TableField("buoydata_ph_rt")
    private Date buoydataPhRt;
    /**
     * 名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
}



