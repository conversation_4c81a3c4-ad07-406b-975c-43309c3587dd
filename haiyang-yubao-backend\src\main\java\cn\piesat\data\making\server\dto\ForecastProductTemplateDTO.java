package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 预报产品模板表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastProductTemplateDTO implements Serializable {

    private static final long serialVersionUID = -48820726061797554L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    @NotBlank(message = "编码不能为空", groups = {Save.class})
    private String code;
    /**
     * 名称
     **/
    @NotBlank(message = "名称不能为空", groups = {Save.class})
    private String name;
    /**
     * 状态
     **/
    private Boolean status;
    /**
     * 排序
     **/
    @NotNull(message = "排序不能为空", groups = {Save.class})
    private Integer sort;
    /**
     * 文件类型
     **/
    @NotBlank(message = "文件类型不能为空", groups = {Save.class})
    private String fileType;
    /**
     * 关联的模板编码
     **/
    @NotBlank(message = "关联的模板编码不能为空", groups = {Save.class})
    private String relationTemplateCode;
    /**
     * 文件内容
     **/
    private String fileContent;
    /**
     * 文件地址
     **/
    @NotBlank(message = "文件地址不能为空", groups = {Save.class})
    private String fileUrl;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 数管-数据产品id
     **/
    private Long productId;
}



