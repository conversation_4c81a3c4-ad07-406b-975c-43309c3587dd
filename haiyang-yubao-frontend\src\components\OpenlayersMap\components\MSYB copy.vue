<template>
  <div class="query-item">
    <div class="query-title">数据源：</div>
    <n-select
      v-model:value="dataSource"
      class="query-info"
      :options="options"
      label-field="dataSource"
      value-field="dataSource"
    />
  </div>
  <div class="query-item">
    <div class="query-title">起报时间：</div>
    <n-date-picker
      v-model:formatted-value="timestamp"
      class="query-info"
      type="datetime"
      clearable
      default-time="08:00:00"
    />
  </div>
  <div class="query-item">
    <div class="query-title">要素选择：</div>
    <n-select v-model:value="element" :options="elementsOptions" />
  </div>
  <!-- <div class="query-item">
    <div class="query-title">数据形式：</div>
    <n-checkbox-group v-model:value="dataType" class="query-info">
      <n-checkbox value="1" label="格点值" />
      <n-checkbox value="2" label="等值线" />
      <n-checkbox value="3" label="填色" />
    </n-checkbox-group>
  </div> -->
  <div class="query-bottom">
    <qx-button
      v-show="element === 'wave' && haveHLLayer"
      @click="modifyLayer"
      >{{ isEdit ? '编辑' : '保存' }}</qx-button
    >
    <qx-button v-show="element === 'wave' && haveHLLayer" @click="cancleEdit">取消</qx-button>
    <qx-button class="my-btn" @click="getData">生成落区</qx-button>
    <qx-button @click="onClear">清空</qx-button>
  </div>

  <div class="alis-mouse-position">
    <div class="legend">
      <div class="text">海浪</div>
      <div class="color-ramp">
        <span v-for="item in colorRamp" :key="item">{{ item.toFixed(1) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="MSYB">
import { ref, onMounted, inject, onUnmounted } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/toolRequest'
import GeoJSON from 'ol/format/GeoJSON.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { Circle as CircleStyle, Fill, Stroke, Style, Text } from 'ol/style.js'
import GeoTIFFSource from 'ol/source/GeoTIFF.js'
import * as GeoTIFF from 'geotiff'
import TileLayer from 'ol/layer/WebGLTile.js'
import { Point } from 'ol/geom'
import Feature from 'ol/Feature'
import gisUtils from 'src/utils/gis'
import { useMessage } from 'naive-ui'
import moment from 'moment'

const message = useMessage()
const getMap = inject<(map: any) => void>('getMap')
const dataSource = ref<null | string>(null)
const timestamp = ref(null)
const dataType = ref<any>([])
let options = ref<any>([])
let layers: any = []
let lonLat = [128, 64, 32, 16, 8, 4, 2, 1, 1, 1]
const isEdit = ref(true) // 是否处于编辑状态
const haveHLLayer = ref(false) // 是否有海浪图层
function addDZX(url: string) {
  const countryStyle = new Style({
    stroke: new Stroke({
      color: 'green',
      width: 2
    }),
    text: new Text({
      font: '14px Calibri,sans-serif',
      // overflow: true,
      fill: new Fill({
        color: '#000'
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 3
      })
    })
  })
  const style = [countryStyle]

  const vectorLayer = new VectorLayer({
    source: new VectorSource({
      url: url,
      format: new GeoJSON()
    }),
    zIndex: 10,
    style: function (feature) {
      const label = feature.get('value') + ''
      countryStyle?.getText()?.setText(label)
      return style
    },
    declutter: true
  })
  if (getMap) {
    getMap((map: any) => {
      map.addLayer(vectorLayer)
      vectorLayer.setProperties({
        layerType: '模式预报'
      })
      layers.push(vectorLayer)
      // console.log(vectorLayer.getExtent())
      // map.getView().fit(vectorLayer.getExtent(), {
      //   pading: [10, 10, 10, 10]
      // })
    })
  }
}
let vectorMark: any = null // 格点图层
let fillMapLayer: any = null // 填色图层
let element = ref('wave')
const elementsOptions = [
  {
    label: '海风',
    value: 'wind'
  },
  {
    label: '海浪',
    value: 'wave'
  },
  {
    label: '海流',
    value: 'current'
  },
  {
    label: '海温',
    value: 'temp'
  },
  {
    label: '盐度',
    value: 'salt'
  },
  {
    label: '风暴潮',
    value: 'storm'
  }
]
const jet = [
  '#00007F',
  '#000091',
  '#0000A3',
  '#0000B6',
  '#0000C8',
  '#0000DA',
  '#0000EC',
  '#0000FE',
  '#0000FF',
  '#0010FF',
  '#0020FF',
  '#0030FF',
  '#0040FF',
  '#0050FF',
  '#0060FF',
  '#0070FF',
  '#0084FF',
  '#0094FF',
  '#00A4FF',
  '#00B4FF',
  '#00C4FF',
  '#00D4FF',
  '#00E4F7',
  '#0CF4EA',
  '#18FFDD',
  '#25FFD0',
  '#32FFC3',
  '#3FFFB7',
  '#4CFFAA',
  '#59FF9D',
  '#66FF90',
  '#73FF83',
  '#83FF73',
  '#90FF66',
  '#9DFF59',
  '#AAFF4C',
  '#B7FF3F',
  '#C3FF32',
  '#D0FF25',
  '#DDFF18',
  '#EAFF0C',
  '#F7F400',
  '#FFE500',
  '#FFD700',
  '#FFC800',
  '#FFB900',
  '#FFAA00',
  '#FF9B00',
  '#FF8900',
  '#FF7A00',
  '#FF6B00',
  '#FF5C00',
  '#FF4D00',
  '#FF3F00',
  '#FF3000',
  '#FF2100',
  '#FE1200',
  '#EC0300',
  '#DA0000',
  '#C80000',
  '#B60000',
  '#A30000',
  '#910000',
  '#7F0000'
]
/** 点击表格数据展示对应的图层 */

let colorRamp = ref<number[]>([])
function getData() {
  if (element.value === 'wave') {
    haveHLLayer.value = true
  } else {
    haveHLLayer.value = false
  }
  return
  onClear('create')
  layers = []

  let mapper: any = {
    ERA5: {
      tif: 'HN_ERA5_SWH_TIF',
      json: 'HN_ERA5_SWH_JSON'
    },
    ECthin: {
      tif: 'HN_ECTHIN_SWH_TIF',
      json: 'HN_ECTHIN_SWH_JSON'
    }
  }
  if (dataType.value.includes('1') || dataType.value.includes('3')) {
    Api.getReAnalysis(mapper[dataSource.value as string].tif, {
      producer: dataSource.value,
      beginTime: timestamp.value,
      endTime: timestamp.value
      // date: '2024-09-05 08:00:00'
    }).then(async (res: any) => {
      if (res && res.length) {
        let result = getResultByFileType(res, 'tif')
        let tifUrl = config.onlyOfficeServerUrl + result.file_path

        console.log(tifUrl, 'tifUrl')
        if (dataType.value.includes('1')) {
          setPointsList(tifUrl)
        }
        if (dataType.value.includes('3')) {
          // let result = getResultByFileType(res, 'tif')

          const tif = await GeoTIFF.fromUrl(tifUrl)

          const tifImage = await tif.getImage()
          const rasters = await tifImage.readRasters()
          // console.log(tif, tifImage, rasters)
          // @ts-ignore
          let max = findMaxMin(Array.from(rasters[0])).max
          // @ts-ignore
          let min = findMaxMin(Array.from(rasters[0])).min

          if (max == 65535) {
            // @ts-ignore
            max = secondMax(Array.from(rasters[0]))
          }

          if (min == '-9999') {
            // @ts-ignore
            min = getSecondSmallest(Array.from(rasters[0]))
          }
          const arr = gisUtils.divideRangeIntoParts(min, max, 64)
          colorRamp.value = gisUtils.divideRangeIntoParts(min, max, 5)
          const colorArr: any = []
          arr.forEach((item, index) => {
            colorArr.push({
              color: jet[index],
              label: item,
              opacity: '1.0',
              quantity: item
            })
          })
          const colorData = {
            colorAttr: colorArr,
            invalidValue: '65535',
            renderType: 'ramp'
          }

          createLayer(tifUrl, colorData)
        }
      }
    })
  }

  if (dataType.value.includes('2')) {
    Api.getReAnalysis(mapper[dataSource.value as string].json, {
      producer: dataSource.value,
      beginTime: timestamp.value,
      endTime: timestamp.value
    })
      .then(async (res: any) => {
        if (res && res.length) {
          let result = getResultByFileType(res, 'json')
          let tifUrl = config.onlyOfficeServerUrl + result.file_path
          addDZX(tifUrl)
        } else {
          message.success('暂无数据')
        }
      })
      .catch(e => {
        console.error(e, 'eeeee')
      })
  }
}

function onClear(type = 'all') {
  if (getMap) {
    getMap((map: any) => {
      layers.forEach((item: any) => {
        map.removeLayer(item)
      })
      map.removeLayer(vectorMark)
      map.removeLayer(fillMapLayer)
    })
  }
  if (type == 'all') {
    dataType.value = []
    dataSource.value = null
    timestamp.value = null
  }
}

// 获取第二小
function getSecondSmallest(arr: any) {
  // 使用 Set 去除重复的值，然后转换回数组
  const uniqueArr = [...new Set(arr)]

  // 确保数组至少有两个不同的元素
  if (uniqueArr.length < 2) {
    throw new Error('数组中没有第二小的元素')
  }

  // 对数组进行排序
  uniqueArr.sort((a: any, b: any) => a - b)

  // 返回排序后的第二个元素
  return uniqueArr[1]
}

function findMaxMin(arr: any) {
  // 检查数组是否为空
  if (arr.length === 0) {
    throw new Error('数组不能为空')
  }

  // 初始化最大值和最小值为数组的第一个元素
  let maxVal = arr[0]
  let minVal = arr[0]

  // 遍历数组，更新最大值和最小值
  for (let i = 1; i < arr.length; i++) {
    if (arr[i] > maxVal) {
      maxVal = arr[i]
    } else if (arr[i] < minVal) {
      minVal = arr[i]
    }
  }

  // 返回一个对象，包含最大值和最小值
  return { max: maxVal, min: minVal }
}

function getResultByFileType(data: any, type: string) {
  let result = data.find((item: any) => {
    const fileType = item.file_path.substring(
      item.file_path.lastIndexOf('.') + 1
    )
    return fileType.includes(type)
  })
  return result
}

function secondMax(arr: any) {
  if (arr.length < 2) {
    return null // 数组长度小于2时，无法找到第二大的数
  }

  let largest = -Infinity
  let secondLargest = -Infinity

  for (let i = 0; i < arr.length; i++) {
    if (arr[i] > largest) {
      secondLargest = largest
      largest = arr[i]
    } else if (arr[i] > secondLargest && arr[i] !== largest) {
      secondLargest = arr[i]
    }
  }

  return secondLargest === -Infinity ? null : secondLargest
}

function createLayer(url: string, colorData: any) {
  const sourceOptions: any = {
    url: url,
    max: null,
    min: null,
    bands: ['1'],
    nodata: colorData.invalidValue
  }
  const source = new GeoTIFFSource({
    sources: [sourceOptions],
    normalize: false, // 归一化
    interpolate: false, // 插值
    wrapX: false,
    // @ts-ignore
    crossOrigin: 'anonymous'
  })
  fillMapLayer = new TileLayer({
    source: source
  })
  const styleOptions = gisUtils.getTifStyle(colorData)
  fillMapLayer.setStyle({
    color: styleOptions
  })
  getMap &&
    getMap((map: any) => {
      map.addLayer(fillMapLayer)
    })
}

// 栅格点
function setPointsList(url: string) {
  if (getMap) {
    getMap((map: any) => {
      let resultSources: any = new VectorSource({ wrapX: false })
      vectorMark = new VectorLayer({
        source: resultSources,
        zIndex: 10,
        style: new Style({
          fill: new Fill({
            color: 'rgba(0, 0, 0, 0)'
          }),
          stroke: new Stroke({ color: '#0000cd', width: 2 })
        })
      })
      map.addLayer(vectorMark)
      const pointStyle = function (feature: any) {
        return new Style({
          zIndex: 1010,
          text: new Text({
            fill: new Fill({
              color: '#fff'
            }),
            stroke: new Stroke({
              color: '#000',
              width: 2
            }),
            text: feature.get('val'),
            font: '15px Calibri,sans-serif blod'
          })
        })
      }

      const pointSource: any = new GeoTIFFSource({
        sources: [
          {
            // visible red, band 1 in the style expression above
            nodata: 65535,
            // url: 'src/components/OpenlayersMap/components/swh_spa.tif',
            url,
            min: 0,
            max: 255,
            bands: [1]
          }
        ],
        normalize: true,
        interpolate: false,
        wrapX: false
      })
      let geoPointtifLayer = new TileLayer({
        source: pointSource,
        zIndex: 101,
        style: {
          color: [
            'case',
            ['between', ['band', 1], 0, 255],
            ['color', 0, 0, 0, 0],
            ['color', 0, 0, 0, 0]
          ]
        }
      })
      map.addLayer(geoPointtifLayer)

      let extent0: any = null
      let extent1: any = null
      const zoom = 2
      let isFinally = true
      let value = lonLat[zoom]
      const extent = map.getView().calculateExtent(map.getSize())
      extent0 = Math.floor(extent[0])
      extent1 = Math.floor(extent[1])
      map.on('moveend', () => {
        const zoom = map.getView().getZoom().toFixed(0)
        value = lonLat[zoom]
        const extent = map.getView().calculateExtent(map.getSize())
        extent0 = Math.floor(extent[0] - (extent[0] % value))
        extent1 = Math.floor(extent[1] - (extent[1] % value))
        drawPonit()
      })
      geoPointtifLayer.on('postrender', (evt: any) => {
        // this.map.on("rendercomplete", (evt) => {
        if (isFinally) {
          const save_to_center = map.getView().getCenter()
          const save_to_zoom = map.getView().getZoom().toFixed(0)
          const center = [save_to_center[0] + 0.01, save_to_center[1] + 0.01]
          map.getView().animate({ center: center }, { zoom: save_to_zoom })
          const zoom = map.getView().getZoom().toFixed(0)
          value = lonLat[zoom]
          const extent = map.getView().calculateExtent(map.getSize())
          extent0 = Math.floor(extent[0] - (extent[0] % value))
          extent1 = Math.floor(extent[1] - (extent[1] % value))
          drawPonit()
          isFinally = false
        }
      })
      function drawPonit() {
        const xyArray = []
        for (let x = extent0; x < extent[2]; x += value) {
          for (let y = extent1; y < extent[3]; y += value) {
            xyArray.push([x, y])
          }
        }

        let drawPoint
        let feature

        const projection = map.getView().getProjection()
        // // 创建源投影和目标投影
        // const sourceProjection = new Projection({
        //   code: "EPSG:4326" // 源投影的代码
        // });

        // const targetProjection = new Projection({
        //   code: projection // 目标投影的代码
        // });

        const features = []
        for (const index in xyArray) {
          drawPoint = new Point(xyArray[index])
          feature = new Feature(drawPoint)
          feature.set('val', '')
          feature.setStyle(pointStyle)
          features.push(feature)
        }
        vectorMark.getSource().clear()
        vectorMark.getSource().addFeatures(features)

        let pixelLonlat
        let pointFeature
        if (xyArray.length <= 0) return
        for (const index in xyArray) {
          pixelLonlat = map.getPixelFromCoordinate(xyArray[index])
          const data: any = geoPointtifLayer.getData(pixelLonlat)
          if (data != null) {
            pointFeature = resultSources.getFeaturesAtCoordinate(xyArray[index])
            if (pointFeature.length !== 0) {
              if (data[0] !== 0) {
                //   // 设置点信息
                pointFeature[0].set('val', data[0] + '')
              }
            }
          }
        }
      }
    })
  }
}
function modifyLayer() {
  isEdit.value = !isEdit.value
}
function cancleEdit(){
  isEdit.value = true
}
onMounted(() => {
  Api.getDataSourceList()
    .then(res => {
      options.value = res
    })
    .catch(() => {})
})
onUnmounted(() => {
  if (getMap) {
    getMap((map: any) => {
      layers.forEach((item: any) => {
        map.removeLayer(item)
      })
    })
  }
})
</script>

<style lang="scss" scoped>
.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;
  .query-title {
    white-space: nowrap;
    width: 80px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }
  .query-info {
    width: 225px;
    flex: 1;
  }
}
.my-btn {
  width: 100px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}
.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.alis-mouse-position {
  position: fixed;
  z-index: 800;
  right: 20px;
  bottom: 18px;
  width: 370px;
  // padding: 15px;
  color: #fff;
  background: #eff4fc;
  border-radius: 4px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  .legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 15px;
    width: 100%;
    height: 100%;
    .text {
      white-space: nowrap;
      margin-right: 8px;
    }
    .color-ramp {
      flex: 1;
      position: relative;
      height: 15px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 12px;
      padding: 0px 6px;
      background: linear-gradient(
        90deg,
        #00007f,
        #000093,
        #0000a9,
        #0000c1,
        #0000d7,
        #0000ed,
        #0000ff,
        #0009ff,
        #001eff,
        #0032ff,
        #0045ff,
        #005aff,
        #006eff,
        #0083ff,
        #0096ff,
        #00aaff,
        #00bfff,
        #00d2ff,
        #01e7f4,
        #11fae4,
        #20ffd5,
        #31ffc3,
        #41ffb4,
        #52ffa3,
        #62ff93,
        #72ff83,
        #82ff73,
        #92ff63,
        #a1ff54,
        #b2ff42,
        #c2ff33,
        #d4ff21,
        #e3ff12,
        #f3f902,
        #ffe500,
        #ffd300,
        #ffbf00,
        #ffae00,
        #ff9b00,
        #ff8800,
        #ff7600,
        #ff6300,
        #ff5000,
        #ff3f00,
        #ff2b00,
        #ff1a00,
        #ef0600,
        #d90000,
        #c30000,
        #ab0000,
        #950000,
        #7f0000
      );
    }
  }
}
</style>
