package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.BuoyStationData;
import cn.piesat.data.making.server.vo.BuoyStationDataVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 浮标站数据表表数据库访问层
 *
 * <AUTHOR>
 */
public interface BuoyStationDataDao extends BaseMapper<BuoyStationData> {

    @Select({"<script>",
            "SELECT bsd.buoy_station_code buoyStationCode, bs.cn_name buoyStationName, bs.location_json buoyStationLocationJson, bsd.time, " +
                    "bsd.wind_speed windSpeed, bsd.wind_dir windDir, bsd.wind_wave_height windWaveHeight " +
                    "FROM fm_buoy_station_b bs " +
                    "inner join fm_buoy_station_data_b bsd on bs.code = bsd.buoy_station_code " +
                    "WHERE 1=1 " +
                    "<if test='startTime != null'>" +
                    "and bsd.time &gt;= #{startTime} " +
                    "</if>" +
                    "<if test='endTime != null'>" +
                    "and bsd.time &lt;= #{endTime} " +
                    "</if>" +
                    "<if test='geoRange != null'>" +
                    "and ST_Contains(ST_GeomFromText(#{geoRange},4326), bs.location_geo) " +
                    "</if>" +
                    "<if test='buoyStationCodeList != null'>" +
                    "and bs.code in " +
                    "<foreach collection='buoyStationCodeList' item='code' open='(' separator=',' close=')'> " +
                    "#{code}" +
                    "</foreach>" +
                    "</if> order by bsd.time",
            "</script>"})
    List<BuoyStationDataVO> getDataList(Date startTime, Date endTime, String geoRange, List<String> buoyStationCodeList);
}
