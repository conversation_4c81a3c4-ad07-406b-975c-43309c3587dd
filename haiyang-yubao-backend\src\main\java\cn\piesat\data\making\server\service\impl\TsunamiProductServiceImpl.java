package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.TsunamiProductDao;
import cn.piesat.data.making.server.dto.TsunamiProductDTO;
import cn.piesat.data.making.server.entity.TsunamiProduct;
import cn.piesat.data.making.server.mapper.TsunamiProductMapper;
import cn.piesat.data.making.server.service.TsunamiProductService;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.TsunamiProductVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 海啸产品表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TsunamiProductServiceImpl extends ServiceImpl<TsunamiProductDao, TsunamiProduct> implements TsunamiProductService {

    @Resource
    private TsunamiProductDao tsunamiProductDao;

    @Override
    public PageResult<TsunamiProductVO> getPage(TsunamiProductDTO dto, PageParam pageParam) {
        Page<TsunamiProduct> page = this.page(new Page<>(pageParam.getPageNum(), pageParam.getPageSize()), createQueryWrapper(dto));
        List<TsunamiProductVO> voList = TsunamiProductMapper.INSTANCE.entityListToVoList(page.getRecords());
        return new PageResult(voList, pageParam.getPageNum(), pageParam.getPageSize(), page.getTotal());
    }

    @Override
    public List<TsunamiProductVO> getList(TsunamiProductDTO dto) {
        List<TsunamiProduct> list = this.list(createQueryWrapper(dto));
        return TsunamiProductMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public void save(TsunamiProductDTO dto) {
        if (dto.getId() == null) {
            this.save(TsunamiProductMapper.INSTANCE.dtoToEntity(dto));
        } else {
            this.updateById(TsunamiProductMapper.INSTANCE.dtoToEntity(dto));
        }
    }

    @Override
    public void downloadFile(Long id, HttpServletResponse response) {
        TsunamiProduct product = this.getById(id);
        if (product == null) {
            throw new BusinessException("海啸产品不存在！");
        }
        FileUtil.downloadFile(product.getFileUrl(), product.getName(), response);
    }

    @Override
    public void batchDownload(TsunamiProductDTO dto, HttpServletResponse response) {
        List<TsunamiProduct> list = this.list(createQueryWrapper(dto));
        List<String> filePathList = list.stream().map(TsunamiProduct::getFileUrl).collect(Collectors.toList());
        //打包下载
        FileUtil.zipFiles(filePathList, "海啸产品.zip", response);
    }

    private LambdaQueryWrapper<TsunamiProduct> createQueryWrapper(TsunamiProductDTO dto) {
        LambdaQueryWrapper<TsunamiProduct> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            queryWrapper.like(TsunamiProduct::getName, dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getIds())) {
            List<String> stringList = Arrays.asList(dto.getIds().split(","));
            List<Long> longList = stringList.stream().map(Long::parseLong).collect(Collectors.toList());
            queryWrapper.in(TsunamiProduct::getId, longList);
        }
        if (dto.getStartTime() != null) {
            queryWrapper.ge(TsunamiProduct::getCreateTime, dto.getStartTime());
        }
        if (dto.getEndTime() != null) {
            queryWrapper.lt(TsunamiProduct::getCreateTime, dto.getEndTime());
        }
        return queryWrapper.orderByDesc(TsunamiProduct::getCreateTime);
    }
}





