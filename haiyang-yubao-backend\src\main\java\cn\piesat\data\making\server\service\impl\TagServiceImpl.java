package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.TagDao;
import cn.piesat.data.making.server.dto.TagDTO;
import cn.piesat.data.making.server.entity.Tag;
import cn.piesat.data.making.server.mapper.TagMapper;
import cn.piesat.data.making.server.service.TagService;
import cn.piesat.data.making.server.vo.TagVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TagServiceImpl extends ServiceImpl<TagDao, Tag>
        implements TagService {

    @Resource
    private TagDao tagDao;

    @Override
    public List<TagVO> getList(TagDTO dto) {
        List<Tag> list = this.list(createQueryWrapper(dto));
        return TagMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public void save(TagDTO dto) {
        if (dto.getId() == null) {
            this.save(TagMapper.INSTANCE.dtoToEntity(dto));
        } else {
            this.updateById(TagMapper.INSTANCE.dtoToEntity(dto));
        }
    }

    @Override
    public void deleteById(Long id) {
        baseMapper.deleteById(id);
    }

    private LambdaQueryWrapper<Tag> createQueryWrapper(TagDTO dto) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        if (dto.getId() != null) {
            queryWrapper.eq(Tag::getId, dto.getId());
        }
        return queryWrapper.orderByAsc(Tag::getCreateTime);
    }
}





