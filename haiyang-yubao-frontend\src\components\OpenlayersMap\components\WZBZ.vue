<template>
  <div class="tools-container">
    <div class="query-item">
      <div class="query-title">文字大小：</div>
      <n-select
        v-model:value="fontSize"
        class="query-info"
        :options="options"
      />
    </div>
    <div class="query-item">
      <div class="query-title">文字颜色：</div>
      <div class="color-picker">
        <n-color-picker
          v-model:value="fillColor"
          class="color-item"
          :actions="['confirm']"
          :swatches="[
            '#F90102',
            '#FE7B0E',
            '#FFF300',
            '#0083FD',
            'rgba(0, 0, 0, 0)',
            '#FFFFFF'
          ]"
        >
        </n-color-picker>
      </div>
    </div>
  </div>
  <div class="query-bottom">
    <qx-button class="my-btn" @click="addPlot">开始标注</qx-button>
    <qx-button class="my-btn" @click="deletePlot">删除</qx-button>
    <qx-button class="my-btn" @click="clearPlot">清空</qx-button>
  </div>
  <n-modal
    v-model:show="showModal"
    :close-on-esc="false"
    :mask-closable="false"
  >
    <n-card
      style="width: 300px"
      title="请输入文字"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <n-input v-model:value="text" type="textarea" placeholder="请输入文字" />
      <template #footer>
        <qx-button @click="cancle">取消</qx-button>
        <qx-button @click="mark">确定</qx-button>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts" name="WZBJ">
import { ref, onMounted, inject, onUnmounted, reactive } from 'vue'
import { QxButton } from 'src/components/QxButton'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import Draw from 'ol/interaction/Draw'
import Select from 'ol/interaction/Select.js'
import { Fill, Stroke, Style, Text, Icon } from 'ol/style.js'
import { useMessage } from 'naive-ui'
import { createDiscreteApi } from 'naive-ui'
import changeStore from './changeStore.js'
import { Modify } from 'ol/interaction.js'
const { dialog } = createDiscreteApi(['dialog'])
const message = useMessage()
const getMap = inject<(map: any) => void>('getMap')
const activeBtn = ref('')
const fontSize = ref(14)
const options = [
  {
    label: '14px',
    value: 14
  },
  {
    label: '16px',
    value: 16
  },
  {
    label: '18px',
    value: 18
  }
]
const fillColor = ref('#000000')
const showModal = ref(false)
const text = ref('')
let draw: any = null
let curDrawFeature: any = null

function addPlot() {
  if (draw) {
    if (getMap) {
      getMap((map: any) => {
        map.removeInteraction(draw)
      })
    }
  }
  draw = new Draw({
    source: source,
    type: 'Point'
  })
  if (getMap) {
    getMap((map: any) => {
      map.removeInteraction(select)
      map.addInteraction(draw)
      draw.on('drawstart', function () {
        text.value = ''
        showModal.value = true
      })
      draw.on('drawend', function (event: any) {
        if (event.feature) {
          curDrawFeature = event.feature
          if (props.isMove) {
            modify.setActive(true)
          }
        }
        map.removeInteraction(draw)
      })
    })
  }
}
function deletePlot() {
  const features = select.getFeatures().getArray()
  console.log(features, 'selectFeature')
  if (features.length > 0) {
    dialog.warning({
      title: '警告',
      content: '确定删除选中的标注？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        features.forEach((item: any) => {
          source.removeFeature(item)
        })
        select.getFeatures().clear()
        if (getMap) {
          getMap((map: any) => {
            changeStore(map)
          })
        }
      },
      onNegativeClick: () => {
        // message.error('不确定')
      }
    })
  } else {
    message.error('请选择要删除的标注')
  }
}
function clearPlot() {
  const features = source.getFeatures()
  modify.setActive(false)
  if (features.length > 0) {
    dialog.warning({
      title: '警告',
      content: '确定清空所有标注？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        // message.success('确定')
        if (getMap) {
          getMap((map: any) => {
            source.refresh()
            source.removeFeature(curDrawFeature)
            map.removeInteraction(draw)
            select.getFeatures().clear()
            changeStore(map)
          })
        }
      },
      onNegativeClick: () => {
        // message.error('不确定')
      }
    })
  } else {
    message.error('您还未进行标注')
  }
}
function cancle() {
  showModal.value = false
  if (getMap) {
    getMap((map: any) => {
      source.removeFeature(curDrawFeature)
      map.removeInteraction(draw)
    })
  }
}
function mark() {
  showModal.value = false
  curDrawFeature.setProperties({
    label: text.value,
    color: fillColor.value,
    fontSize: fontSize.value
  })
  curDrawFeature.setStyle(
    new Style({
      text: new Text({
        text: text.value,
        font: `${fontSize.value}px Microsoft YaHei`,
        fill: new Fill({
          color: fillColor.value
        })
      })
    })
  )
  const feature = curDrawFeature.clone()
  source.addFeature(feature)
  source.removeFeature(curDrawFeature)
  if (getMap) {
    getMap((map: any) => {
      map.addInteraction(select)
      changeStore(map)
    })
  }
}
const source = new VectorSource()
const textLayer = new VectorLayer({
  source: source,
  zIndex: 35
})
textLayer.setProperties({
  layerType: '文字标注',
  canRedo: true
})
const selected = new Style({
  text: new Text({
    font: `30px Microsoft YaHei`,
    fill: new Fill({
      color: fillColor.value
    })
  })
})
function selectStyle(feature: any) {
  const properties = feature.getProperties()
  const text = properties.label || ''
  selected.getText().setText(text)
  const fill = properties.color || '#000000'
  selected.getText().getFill().setColor(fill)
  const fontSize = properties.fontSize || 30
  selected.getText().setFont(`${fontSize + 10}px Microsoft YaHei`)
  return selected
}
const select = new Select({ style: selectStyle, layers: [textLayer] })

const props = defineProps({
  isMove: {
    type: Boolean,
    default: false
  }
})
const modify = new Modify({
  source
})

onMounted(() => {
  if (getMap) {
    getMap((map: any) => {
      map.addLayer(textLayer)
      map.addInteraction(modify)
    })
  }
})
onUnmounted(() => {
  if (getMap) {
    getMap((map: any) => {
      map.removeLayer(textLayer)
      map.removeInteraction(draw)
      map.removeInteraction(select)
      map.removeInteraction(modify)
    })
  }
})
</script>

<style lang="scss" scoped>
.tools-container {
  // width: 346px;
  height: 100px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 12px 0px;
  padding: 0px 12px;
  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 0px;
    .query-title {
      white-space: nowrap;
      width: 70px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
    .query-info {
      width: 346px;
    }
    .small-input {
      width: 63px;
      height: 32px;
      margin-left: 8px;
    }
    .my-slider {
      width: 178px;
    }
    .color-picker {
      display: flex;
      .color-item {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }
      .have-border {
        border-radius: 2px 2px 2px 2px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }
      .transparent-div {
        position: relative;
        &::after {
          content: '';
          position: absolute;
          top: 7px;
          left: 0;
          right: 0;
          height: 2px; /* 斜线的高度 */
          background-color: rgba(0, 0, 0, 0.1); /* 斜线的颜色 */
          transform: rotate(-45deg); /* 斜线的角度 */
        }
      }
    }
  }
}
.my-btn {
  width: 140px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}
.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
