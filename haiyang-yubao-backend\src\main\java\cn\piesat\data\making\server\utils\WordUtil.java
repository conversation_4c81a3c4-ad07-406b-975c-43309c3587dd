package cn.piesat.data.making.server.utils;

import cn.hutool.core.io.FileUtil;
import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import cn.piesat.webconfig.exception.BusinessException;
import com.aspose.words.HtmlSaveOptions;
import com.aspose.words.ImageSize;
import com.aspose.words.NodeType;
import com.aspose.words.Shape;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class WordUtil {

    public static final String defaultCode = "default";

    /**
     * 创建word
     *
     * @param map       word map
     * @param detailMap k:预报模板编码 v:预报记录明细列表
     * @param inputPath 模板文件路径
     * @param outPath   输出路径
     */
    public static void createWord(Map<String, Object> map, Map<String, List<ForecastRecordDetail>> detailMap, String inputPath, String outPath) {
        //填充列表
        fillList(map, detailMap);
        //获取word中所有表格的信息
        Map<String, List<String>> tableMap = getTable(inputPath);
        //填充表格
        fillTable(map, tableMap, detailMap);
        //生成word
        makeWord(map, tableMap, inputPath, outPath);
    }

    /**
     * 填充列表数据
     *
     * @param map       word map
     * @param detailMap k:预报模板编码 v:预报记录明细列表
     */
    public static void fillList(Map<String, Object> map, Map<String, List<ForecastRecordDetail>> detailMap) {
        detailMap.entrySet().stream().forEach(detailEntry -> {
            List<ForecastRecordDetail> detailList = detailEntry.getValue();
            if (CollectionUtils.isEmpty(detailList)) {
                log.info(detailEntry.getKey() + "List数据不存在！");
                return;
            }
            List<Map<String, Object>> dataList = getDataList(detailList);
            map.put(detailEntry.getKey() + "List", dataList);
        });
    }

    /**
     * 填充表格数据
     *
     * @param map       word map
     * @param tableMap  k:表格名称 v:数据行变量列表
     * @param detailMap k:预报模板编码 v:预报记录明细列表
     */
    public static void fillTable(Map<String, Object> map, Map<String, List<String>> tableMap, Map<String, List<ForecastRecordDetail>> detailMap) {
        tableMap.entrySet().stream().forEach(tableEntry -> {
            String code = tableEntry.getKey().substring(0, tableEntry.getKey().length() - 5);
            List<ForecastRecordDetail> detailList = detailMap.get(code);
            if (CollectionUtils.isEmpty(detailList)) {
                log.info(tableEntry.getKey() + "数据不存在！");
                return;
            }
            List<Map<String, Object>> dataList = getDataList(detailList);
            map.put(tableEntry.getKey(), dataList);
        });
    }

    /**
     * 获取指定预报类型下的数据列表
     *
     * @param detailList 预报记录明细列表
     */
    public static List<Map<String, Object>> getDataList(List<ForecastRecordDetail> detailList) {
        //将预报记录明细按照区域编码进行分组
        Map<String, List<ForecastRecordDetail>> areaCodeDetailMap = detailList.stream()
                .collect(Collectors.groupingBy(ForecastRecordDetail::getAreaCode));
        //定义指定预报类型下的数据列表
        List<Map<String, Object>> dataList = new ArrayList();
        areaCodeDetailMap.entrySet().stream().forEach(areaCodeDetailEntry -> {
            //处理数据行
            Map<String, Object> dataMap = new HashMap<>();
            areaCodeDetailEntry.getValue().stream().forEach(detail -> {
                String columnCode = detail.getColumnCode();
                String value = detail.getValue();
                //区域数据
                dataMap.put(defaultCode, detail.getAreaName());
                //要素数据
                dataMap.put(columnCode, value);
            });
            dataList.add(dataMap);
        });
        return dataList;
    }

    /**
     * 获取word中所有表格的信息 k:表格名称 v:数据行变量列表
     **/
    public static Map<String, List<String>> getTable(String inputPath) {
        try {
            XWPFDocument doc = new XWPFDocument(new FileInputStream(inputPath));
            Map<String, List<String>> map = new HashMap<>();
            //获取文档所有表格
            Iterator<XWPFTable> iterator = doc.getTablesIterator();
            //循环表格
            while (iterator.hasNext()) {
                XWPFTable next = iterator.next();
                List<XWPFTableRow> rows = next.getRows();
                if (CollectionUtils.isEmpty(rows) || rows.size() < 2) {
                    continue;
                }
                List<String> list = new ArrayList<>();
                //获取表格名称
                XWPFTableRow headRow = rows.get(0);
                List<XWPFTableCell> headList = headRow.getTableCells();
                if (CollectionUtils.isEmpty(headList)) {
                    continue;
                }
                headList.stream().forEach(head -> {
                    String text = head.getText();
                    if (StringUtils.isNotBlank(text) && text.startsWith("{{") && text.contains("Table}}")) {
                        String tableName = text.substring(text.indexOf("{{") + 2, text.indexOf("}}"));
                        map.put(tableName, list);
                    }
                });
                //获取表格数据行变量列表
                XWPFTableRow dataRow = rows.get(1);
                List<XWPFTableCell> dataList = dataRow.getTableCells();
                if (CollectionUtils.isEmpty(dataList)) {
                    continue;
                }
                dataList.stream().forEach(data -> {
                    String text = data.getText();
                    if (StringUtils.isNotBlank(text)) {
                        String cellName = text.replace("[", "").replace("]", "");
                        list.add(cellName);
                    }
                });
            }
            return map;
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("解析word失败！");
        }
    }

    /**
     * 生成word
     *
     * @param map       word map
     * @param tableMap  表格 k:表格名称 v:数据行变量列表
     * @param inputPath 模板文件路径
     * @param outPath   输出路径
     */
    public static void makeWord(Map<String, Object> map, Map<String, List<String>> tableMap, String inputPath, String outPath) {
        try {
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
            Configure configures = Configure.createDefault();
            tableMap.keySet().stream().forEach(tableName -> configures.customPolicy(tableName, policy));
            FileUtil.mkParentDirs(outPath);
            XWPFTemplate.compile(inputPath, configures).render(map).writeToFile(outPath);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("生成word失败！");
        }
    }

    /**
     * 创建word文件
     *
     * @param content 文本内容
     * @param outPath 文件路径
     */
    public static void createFile(String content, String outPath) {
        try {
            //创建一个新的空白docx文档
            XWPFDocument document = new XWPFDocument();

            //创建一个段落
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(content);

            //写入数据到文档
            FileOutputStream out = new FileOutputStream(outPath);
            document.write(out);
            out.close();
        } catch (IOException e) {
            log.error("创建文件失败：{},{}", outPath, e);
            throw new BusinessException("创建文件失败！");
        }
    }

    /**
     * word转txt
     *
     * @param inputPath word路径
     * @param outPath   txt路径
     */
    public static void convertToTxt(String inputPath, String outPath) {
        try {
            FileInputStream fis = new FileInputStream(inputPath);
            XWPFDocument document = new XWPFDocument(fis);

            FileWriter writer = new FileWriter(outPath);
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph para : paragraphs) {
                writer.write(para.getText());
                writer.write("\n");
            }
            writer.close();
            document.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("生成txt失败！");
        }
    }

    /**
     * word转html
     *
     * @param inputPath word路径
     * @param outPath   html路径
     */
    public static void convertToHtml(String inputPath, String outPath) {
        try {
            File file = new File(inputPath);
            FileInputStream inputStream = new FileInputStream(file);
            com.aspose.words.Document doc = new com.aspose.words.Document(inputStream);
            //设置图片
            for (Shape shape : (Iterable<Shape>) doc.getChildNodes(NodeType.SHAPE, true)) {
                if (shape.hasImage()) {
                    //获取图片的原始尺寸
                    ImageSize imageSize = shape.getImageData().getImageSize();
                    //设置图片的宽度和高度，以保持原始尺寸
                    shape.setWidth(350);
                    shape.setHeight(350);
                }
            }
            //创建HtmlSaveOptions并设置图像
            HtmlSaveOptions saveOptions = new HtmlSaveOptions();
//            saveOptions.setImageResolution(300);
            saveOptions.setExportImagesAsBase64(true);

            //将文件转换为HTML
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            doc.save(outputStream, saveOptions);
            log.debug("临时文件内容：{}", outputStream);
            //将ByteArrayOutputStream的内容写入到文件中
            FileOutputStream fos = new FileOutputStream(outPath);
            outputStream.writeTo(fos);
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("生成html失败！");
        }
    }

    public static void main(String[] args) {
        String filePath = "C:\\Users\\<USER>\\Desktop\\";
        String fileName = "F_FDRW_S4_STP_20241116143402_V1_FORECAST_1731738842103.docx";
        String htmlName = "test.html";
        convertToHtml(filePath + fileName, filePath + htmlName);
    }
}
