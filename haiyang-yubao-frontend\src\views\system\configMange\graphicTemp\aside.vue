<template>
  <div class="common-aside product-temp-aside">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>图形模板列表</h3>
        <qx-button class="primary" @click="onCreate">新建</qx-button>
      </div>
      <div class="aside-header-tab d-flex">
        <Tab
          :active="tabIndex"
          :tab-list="tabList"
          class="flex1"
          @change="changeTab"
        />
        <div class="qx-select d-flex flex-justify-end flex1">
          <n-select
            v-model:value="status"
            :options="statusList"
            :bordered="false"
            style="width: 45%; margin-right: 5%"
            @update:value="filterData"
          />
        </div>
      </div>
    </div>
    <qx-tree
      v-loading="loading"
      :tree-data="treeData"
      ref="treeRef"
      :default-props="defaultProps"
      :is-switch="isSwitch"
      :default-expand-all="true"
      :default-selected-keys="defaultSelectedKeys"
      :edit="true"
      :have-right-menu="true"
      @delete-node="deleteNode"
      @change-switch="changeSwitch"
      @selected="selectHandler"
      @re-name="reNameHandler"
    />
  </div>
  <qx-dialog
    v-model:visible="reNameVisible"
    title="重命名预报图模板"
    width="345px"
    class="create-forecast-temp-dialog"
    @update:visible="reNameVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="formRef"
          class="forecast-temp-form"
          :model="curRightData"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="模板名称" path="name">
            <n-input
              v-model:value="curRightData.name"
              placeholder="请输入"
              clearable
            />
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="reNameVisible = false">取消</qx-button>
        <qx-button class="primary" @click="reName">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
  <qx-dialog
    v-model:visible="dialogVisible"
    title="新建图形模板"
    width="345px"
    class="create-forecast-temp-dialog"
    @update:visible="dialogVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="formRef"
          class="forecast-temp-form"
          :model="form"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="模板名称" path="name">
            <n-input v-model:value="form.name" placeholder="请输入" clearable />
          </n-form-item>
          <n-form-item label="关联区域" path="templateType">
            <n-select
              v-model:value="form.templateType"
              placeholder="请选择"
              clearable
              :options="areaList"
              label-field="name"
              value-field="templateType"
            ></n-select>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="dialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="saveTemplate">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { Tab } from 'src/components/Tab'
import { ref, reactive, onMounted } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/forecast'
import { QxTree } from 'src/components/QxTree'
import { useMessage } from 'naive-ui'
import { QxDialog } from 'src/components/QxDialog'
import type { TreeDropInfo, TreeOption } from 'naive-ui'
import { createDiscreteApi } from 'naive-ui'
const { dialog } = createDiscreteApi(['dialog'])
const emits = defineEmits(['changeTab', 'selected'])
// 定义当前组件全局变量 start
const message = useMessage()
const status = ref(0)
const fileType = ref('')

const statusList = [
  {
    label: '全部',
    value: 0
  },
  {
    label: '启用',
    value: 1
  },
  {
    label: '禁用',
    value: 2
  }
]
const areaList = ref<any[]>([])
const dialogVisible = ref<boolean>(false)
const form = reactive({
  name: '',
  templateType: '',
  status: false
})
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入模板名称',
      trigger: 'blur'
    }
  ],
  forecastType: [
    {
      required: true,
      message: '请选择关联区域',
      trigger: 'change'
    }
  ]
})
// 定义当前组件全局变量 end

// tab切换 start
const tabIndex = ref<number>(1)
const tabList = [
  { label: '预报图', id: 1 },
  { label: '警报图', id: 2 }
]
function changeTab(val: number) {
  tabIndex.value = val
  getForecastPruductList()
  emits('changeTab', val)
}
// tab切换 end

// tree start
const isSwitch = ref<boolean>(true)
const treeData = ref<any[]>([])
const defaultSelectedKeys = ref<any[]>([])
const defaultProps = {
  key: 'id',
  label: 'name',
  children: 'children'
}
//重命名预报单模板
const reNameVisible = ref(false)
const curRightData = ref<any>({})
function reNameHandler(option: any) {
  curRightData.value = option
  reNameVisible.value = true
}
const treeRef = ref()
function reName() {
  Api.renameTemplate(curRightData.value)
    .then((res: any) => {
      console.log(res)
      getForecastPruductList()
    })
    .catch(() => {})
}
function deleteNode(option: any) {
  curRightData.value = option
  dialog.warning({
    title: '提示',
    content: `是否删除${option.name}模板`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      Api.deleteTemplate(option)
        .then((res: any) => {
          console.log(res)
          message.success('删除成功')
          getForecastPruductList()
          treeRef.value.hiddenRightMenu()
          changeTab(tabIndex.value)
        })
        .catch(() => {})
    },
    onNegativeClick: () => {
      treeRef.value.hiddenRightMenu()
    }
  })
}
// 格式化状态 接口参数 全部 = null,启用=true,禁用=false
function getStatus() {
  if (status.value === 0) {
    return { status: null }
  } else if (status.value === 1) {
    return { status: true }
  } else {
    return { status: false }
  }
}
function filterData(val: string) {
  console.log(typeof val, 'val')
  getForecastPruductList()
  emits('changeTab')
}

const loading = ref(false)
// 获取预报产品列表
async function getForecastPruductList() {
  defaultSelectedKeys.value = []
  Api.getGraphicTemplate({
    productType: tabIndex.value
    // status: getStatus().status
  })
    .then(async (res: any) => {
      const list = JSON.parse(JSON.stringify(res))
      areaList.value = list
      res.forEach((item: any) => {
        item.children = []
      })
      for (let i = 0; i < res.length; i++) {
        const result = await Api.getTemplateByParent({
          productType: tabIndex.value,
          status: getStatus().status,
          templateType: res[i]?.templateType
        })
        res[i].children = result
      }
      treeData.value = res
      loading.value = false
    })
    .catch(() => {
      treeData.value = []
      loading.value = false
    })
}
function selectHandler(val: any, node: any, meta: any) {
  if (!node.children) {
    Api.getTemplateById(node.id)
      .then(res => {
        emits('selected', res)
      })
      .catch((err: any) => {
        console.log(err)
        message.error('获取模板失败')
      })
  }
}

function changeSwitch(val: boolean, option: any) {
  console.log(val, 'val--', option)
  option.status = val
  Api.updateForecastStatus(option)
    .then((res: any) => {
      message.success('修改成功')
      getForecastPruductList()
    })
    .catch(() => {
      message.error('修改失败')
    })
}

// tree end

// 新建
function onCreate() {
  dialogVisible.value = true
  form.name = ''
  form.templateType = ''
  form.status = false
}
function saveTemplate() {
  const data = {
    productType: tabIndex.value,
    name: form.name,
    templateType: form.templateType,
    status: false,
    leftLon: '107.35',
    leftLat: '21.95',
    rightLon: '112.63',
    rightLat: '17.76'
  }
  Api.saveGraphicTemplate(data)
    .then((res: any) => {
      getForecastPruductList()
      emits('changeTab')
      message.success('保存成功')
      dialogVisible.value = false
    })
    .catch(() => {
      message.error('保存失败')
      dialogVisible.value = false
    })
}
onMounted(() => {
  getForecastPruductList()
})
</script>

<style scoped lang="scss">
.product-temp-aside {
  margin-right: 20px;
}
.create-forecast-temp-dialog {
  .qx-dialog {
    // background: url(src/assets/images/common/dialog-bg.png) no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
  }
  .form-container {
    box-sizing: border-box;
    padding: 14px 27px 14px 24px;
    background: #fff;
    .n-form-item-feedback-wrapper {
      min-height: 14px;
    }
  }
  .btns {
    box-sizing: border-box;
    padding: 10px 0;
    text-align: center;
    background: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
</style>
