package cn.piesat.data.making.server.entity;

import java.util.Date;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 台风位置预警完成数据实体类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:21
 */
@Data
@Accessors(chain = true)
@TableName("fm_typhoon_completion_b")
public class FmTyphoonCompletionB implements Serializable {

    private static final long serialVersionUID = -64800576299392415L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 台风编号
     */
    @TableField("tfbh")
    private String tfbh;
    /**
     * 时间
     */
    @TableField("time")
    private Date time;
    /**
     * 位置
     */
    @TableField("location")
    private String location;
    /**
     * 完成
     */
    @TableField("completion")
    private String completion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCompletion() {
        return completion;
    }

    public void setCompletion(String completion) {
        this.completion = completion;
    }
}
