package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.UserDTO;
import cn.piesat.data.making.server.entity.User;
import cn.piesat.data.making.server.vo.UserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserMapper {

    UserMapper INSTANCE = Mappers.getMapper(UserMapper.class);

    /**
     * entity-->vo
     */
    UserVO entityToVo(User entity);

    /**
     * dto-->entity
     */
    User dtoToEntity(UserDTO dto);

    /**
     * entityList-->voList
     */
    List<UserVO> entityListToVoList(List<User> list);

    /**
     * dtoList-->entityList
     */
    List<User> dtoListToEntityList(List<UserDTO> dtoList);
}
