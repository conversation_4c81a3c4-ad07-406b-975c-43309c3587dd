.public-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .top {
    height: 40px;
    width: 100%;
    display: flex;
    border-bottom: 1px solid #d7e0eb;
    justify-content: space-between;

    .top-left {
      display: flex;
      height: 100%;
      align-items: center;

      .top-tab {
        // width: 100px;
        padding: 0px 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #dfebfa;
        border-radius: 6px 6px 0px 0px;
        border: 1px solid #d7e0eb;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #4e4e4e;
        margin-left: 2px;
        cursor: default;

        &:first-child {
          margin-left: 0px;
        }

        &.active {
          background: #bed8ff;
          border-radius: 6px 6px 0px 0px;
          border: 1px solid #d7e0eb;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #3c83ed;
        }
      }
    }

    .top-right {
      display: flex;
      align-items: center;

      .top-right-item {
        display: flex;
        align-items: center;

        .title {
          width: 100px;
          display: flex;
          justify-content: flex-end;
        }
      }

      .date {
        margin-right: 20px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
      }
    }
  }

  .content {
    // flex: 1;
    display: flex;
    padding-top: 10px;
    height: calc(100% - 40px);

    .left {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-right: 20px;
      overflow-y: auto;
    }

    .card {
      flex: 1;
      background: #fff;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;

      &:nth-last-child(1){
        margin-bottom: 0;
      }
      .card-header {
        height: 58.92px;
        background: url('src/assets/images/common/card-header.png') no-repeat;
        background-size: 100% calc(100% - 10px);
        display: flex;
        align-items: center;
        border-bottom: 1px solid #dfdfdf;
        justify-content: space-between;
        padding: 0px 20px;

        .header-left {
          display: flex;

          .card-title {
            font-weight: bold;
            font-size: 18px;
            color: #222222;
            margin-left: 10px;
            position: relative;

            &::before {
              content: '';
              width: 4px;
              height: 15px;
              background: rgba(86, 123, 255, 1);
              position: absolute;
              left: -10px;
            }
          }

          span {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
          }
        }
      }

      .card-info {
        flex: 1;
      }

      .n-input.n-input--textarea {
        height: 100%;
      }

      .n-input.n-input--textarea.n-input--resizable .n-input-wrapper {
        resize: none;
      }
      .n-input .n-input__border, .n-input .n-input__state-border{
        display: none;
      }
    }

    .right {
      width: 278px;
      background: #ffffff;
      box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
      border-radius: 8px 8px 8px 8px;
      padding: 21px;
      display: flex;
      flex-direction: column;

      .query-item {
        width: 100%;
        margin-bottom: 10px;
      }

      .query-btn {
        width: 100%;
        margin-bottom: 10px;
        text-align: right;
      }

      .station-list {
        flex: 1;
        height: 0;
        overflow-y: auto;
        padding-top: 10px;
      }
    }
  }

  .query-item__tab{
    flex:1;
    text-align: center;
    padding: 4px 0;
    color:#222222;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid rgb(224, 224, 230);
    &:nth-child(1){
      border-right:none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &:nth-child(2){
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    &.active{
      color: #0073ce;
      border:1px solid #0073ce;
    }
  }
}
