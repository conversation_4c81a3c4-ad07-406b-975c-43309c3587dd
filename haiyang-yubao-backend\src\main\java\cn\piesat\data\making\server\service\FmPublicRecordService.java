package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.vo.FmPublicRecordVO;

public interface FmPublicRecordService {

    FmPublicRecordVO getByPk(Long id);

    FmPublicRecordVO save(FmPublicRecordVO fmPublicRecordVO);

    FmPublicRecordVO update(FmPublicRecordVO fmPublicRecordVO);

    FmPublicRecordVO getLastRecord(String publicType);

    String makeFile(FmPublicRecordVO fmPublicRecordVO);
}
