package cn.piesat.data.making.server.enums;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 潮位警戒登等级定义
 */
public enum TideWarnLevel {
    MESSAGE(0,"无"),
    B<PERSON>UE(1,"蓝色"),
    YELLOW(2,"黄色"),
    ORANGE(3,"橙色"),
    RED(4,"红色");

    private int value;

    private String desc;

    private TideWarnLevel(int value,String desc){
        this.value = value;
        this.desc = desc;
    }

    public int getValue(){
        return this.value;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<Integer, String> toMap() {
        return Stream.of(TideWarnLevel.values())
                .collect(Collectors.toMap(TideWarnLevel::getValue, TideWarnLevel::getDesc));
    }
}
