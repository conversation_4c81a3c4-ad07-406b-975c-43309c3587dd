<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-04-07 14:22:55
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-16 16:05:24
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/tools.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="map-tools">
    <div v-for="(item, index) in mapTools" :key="item.name" class="tool-item">
      <n-tooltip trigger="hover">
        <template #trigger>
          <!-- <i
            class="icon"
            :class="[item.icon, index === toolIndex ? 'active' : '']"
            @click="onToolClick(index, item)"
          >
          </i> -->
          <i
            class="icon"
            :class="[item.icon, toolIndex.indexOf(index)>-1 ? 'active' : '']"
            @click="onToolClick(index, item)"
          >
          </i>
        </template>
        <span>{{ item.name }}</span>
      </n-tooltip>
    </div>
  </div>
  <CreateText
    v-model:visible="createTextVisible"
    @update:visible="toolIndex.length===0"
  />
  <LayerChoose
    v-model:visible="layerVisible"
    @update:visible="toolIndex.length===0"
  />
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import { CreateText, LayerChoose } from './index'

// const toolIndex = ref<null | number>(null)
  const toolIndex = ref<number[]>([4])
type ToolType = {
  name: string
  icon: string
  type: 'text' | 'areaEdit' | 'screenshot' | 'layerControl' | 'scale' | 'graticule'
}
const mapTools: ToolType[] = [
  {
    name: '文本',
    icon: 'icon-text',
    type: 'text'
  },
  {
    name: '区域编辑',
    icon: 'icon-areaedit',
    type: 'areaEdit'
  },
  {
    name: '截图',
    icon: 'icon-screenshot',
    type: 'screenshot'
  },
  {
    name: '图层控制',
    icon: 'icon-layer',
    type: 'layerControl'
  },
  {
    name: '比例尺显隐',
    icon: 'icon-scale',
    type: 'scale'
  },
  {
    name: '经纬网显隐',
    icon: 'icon-graticule',
    type: 'graticule'
  }
]
const emit = defineEmits([
  'text',
  'areaEdit',
  'screenshot',
  'layerControl',
  'scale',
  'graticule'
])
function onToolClick(index: number, item: ToolType) {
  layerVisible.value = false
  createTextVisible.value = false
  if(toolIndex.value.indexOf(index)>-1){
    toolIndex.value.splice(toolIndex.value.indexOf(index),1)
  }else{
    toolIndex.value.push(index)
  }
  // toolIndex.value = index
  if (item.type === 'text') {
    createTextVisible.value = true
  } else if (item.type === 'layerControl') {
    layerVisible.value = true
  } else {
    emit(item.type, item)
  }
}
const createTextVisible = ref(false)
const layerVisible = ref(false)

onMounted(() => {
  nextTick(() => {
    onToolClick(2, mapTools[2])
  })
})
</script>

<style scoped lang="scss">
$icons: icon-text, icon-layer, icon-screenshot, icon-areaedit, icon-scale, icon-graticule;
$iconsImage: 4, 17, 22, 23, 24, 25;
.map-tools {
  position: absolute;
  top: 10px;
  left: 5px;
  z-index: 3;
  .tool-item {
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:nth-last-child(1) {
      border: none;
    }
  }
  @for $i from 1 through length($icons) {
    $icon: nth($icons, $i);
    $iconNum: nth($iconsImage, $i);

    .#{$icon} {
      width: 32px;
      height: 32px;
      background-image: url(src/assets/images/tools/#{$iconNum}.png);
      background-size: 100% 100%;
      &.active {
        background-image: url(src/assets/images/tools/active#{$iconNum}.png);
      }
    }
  }
  .icon-scale {
    width: 20px;
    height: 20px;
  }
}
</style>
