package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.ShipDataDTO;
import cn.piesat.data.making.server.entity.ShipData;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.ShipDataVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.IOException;
import java.util.List;

/**
 * 船舶表服务接口
 *
 * <AUTHOR>
 */
public interface ShipDataService extends IService<ShipData> {

    /**
     * 查询列表
     */
    List<String> getShipList();

    /**
     * 查询列表
     */
    List<ShipDataVO> getList(ShipDataDTO dto);

    /**
     * 查询列表
     */
    FillMapVO getRangeList(ShipDataDTO dto);

    /**
     * 保存
     */
    void readAndSaveData(String directoryPath) throws IOException;
}




