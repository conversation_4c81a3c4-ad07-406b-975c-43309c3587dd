package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.RegionDao;
import cn.piesat.data.making.server.dto.RegionDTO;
import cn.piesat.data.making.server.entity.Region;
import cn.piesat.data.making.server.mapper.RegionMapper;
import cn.piesat.data.making.server.service.RegionService;
import cn.piesat.data.making.server.vo.RegionVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 行政区表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RegionServiceImpl extends ServiceImpl<RegionDao, Region>
        implements RegionService {

    @Override
    public List<RegionVO> getList() {
        List<Region> list = this.list();
        return RegionMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public void save(RegionDTO dto) {
        if (dto.getId() == null) {
            this.save(RegionMapper.INSTANCE.dtoToEntity(dto));
        } else {
            this.updateById(RegionMapper.INSTANCE.dtoToEntity(dto));
        }
    }

    @Override
    public String getCodeByName(String name) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Region::getName, name);
        return this.getOne(queryWrapper).getCode();
    }
}





