package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmPublicProductTemplate;
import cn.piesat.data.making.server.vo.FmPublicProductTemplateVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmPublicProductTemplateMapper {

    FmPublicProductTemplateMapper INSTANCE = Mappers.getMapper(FmPublicProductTemplateMapper.class);

    FmPublicProductTemplate voToEntity(FmPublicProductTemplateVO vo);

    FmPublicProductTemplateVO entityToVo(FmPublicProductTemplate fmPublicProductTemplate);

    List<FmPublicProductTemplateVO> entityListToVoList(List<FmPublicProductTemplate> list);
}
