<template>
  <div class="typhoon-container">
    <div class="typhoon-filter d-flex flex-align-center">
      <n-date-picker
        v-model:formatted-value="curYear"
        type="year"
        :year-range="[2000, moment().year() + 1]"
        clearable
        style="width: 50%"
        @update:formatted-value="getTyphoonList"
      />
    </div>
    <div class="table-container">
      <n-data-table
        :loading="loading"
        :columns="columns"
        :data="typhoonOptions"
        :row-key="rowKey"
        :max-height="150"
        @update:checked-row-keys="handleCheck"
      />
    </div>
  </div>

  <div id="popup" class="ol-popup-typhoon">
    <div id="popup1-content">
      <div v-show="typhoonInfo.name && typhoonInfo.name !== ''" class="info">
        台风名称：{{ typhoonInfo.name }}
      </div>
      <div v-show="typhoonInfo.tm && typhoonInfo.tm !== ''" class="info">
        机构：{{ typhoonInfo.tm }}
      </div>
      <div v-show="typhoonInfo.time && typhoonInfo.time !== ''" class="info">
        过去时间：{{ typhoonInfo.time }}
      </div>
      <div class="info">
        中心位置：{{ typhoonInfo.lng }},{{ typhoonInfo.lat }}
      </div>
      <div v-show="typhoonInfo.speed && typhoonInfo.speed !== ''" class="info">
        最大风速：{{ typhoonInfo.speed }} 米/秒
        <span
          v-show="typhoonInfo.strong && typhoonInfo.strong !== ''"
          class="strong"
          :class="[
            typhoonInfo.strong === '热带低压(TD)' ? 'td' : '',
            typhoonInfo.strong === '热带风暴(TS)' ? 'ts' : '',
            typhoonInfo.strong === '强热带风暴(STS)' ? 'sts' : '',
            typhoonInfo.strong === '台风(TY)' ? 'ty' : '',
            typhoonInfo.strong === '强台风(STY)' ? 'sty' : '',
            typhoonInfo.strong === '超强台风(Super TY)' ? 'superTy' : ''
          ]"
          >{{ typhoonInfo.strong }}</span
        >
      </div>
      <div v-show="typhoonInfo.power && typhoonInfo.power !== ''" class="info">
        中心气压：{{ typhoonInfo.power }} 百帕
      </div>
      <div
        v-show="typhoonInfo.moveDir && typhoonInfo.moveDir !== ''"
        class="info"
      >
        移动方向：{{ typhoonInfo.moveDir }}
      </div>
      <div
        v-show="typhoonInfo.moveSpeed && typhoonInfo.moveSpeed !== ''"
        class="info"
      >
        移动速度：{{ typhoonInfo.moveSpeed }} 公里/小时
      </div>
      <div
        v-if="typhoonInfo.windRadius && typhoonInfo.windRadius.length > 0"
        class="wind-info"
      >
        <div class="wind-level">风圈半径</div>
        <div class="wind-level">东北</div>
        <div class="wind-level">东南</div>
        <div class="wind-level">西南</div>
        <div class="wind-level">西北</div>
        <div
          v-for="(item, index) in typhoonInfo.windRadius"
          :key="index + 'level'"
          class="wind-level"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  watch,
  inject,
  nextTick,
  toRaw
} from 'vue'
import Polygon from 'ol/geom/Polygon.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import ToolApi from 'src/requests/toolRequest'
import gisUtils from 'src/utils/gis'
import { Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import Feature from 'ol/Feature.js'
import LineString from 'ol/geom/LineString.js'
import chaoqiangtaifeng from 'src/assets/images/tools/typhoon/chaoqiangtaifeng.png'
import qiangredaifengbao from 'src/assets/images/tools/typhoon/qiangredaifengbao.png'
import qiangtaifeng from 'src/assets/images/tools/typhoon/qiangtaifeng.png'
import redaidiya from 'src/assets/images/tools/typhoon/redaidiya.png'
import redaifengbao from 'src/assets/images/tools/typhoon/redaifengbao.png'
import taifeng from 'src/assets/images/tools/typhoon/taifeng.png'
import Point from 'ol/geom/Point.js'
import Overlay from 'ol/Overlay.js'
import moment from 'moment'
import type { DataTableRowKey } from 'naive-ui'
import { unByKey } from 'ol/Observable'
import {
  ITyphoonForecastInfo,
  ITyphoonInfo
} from 'src/requests/toolRequest.type'
import Map from 'ol/Map'
import { EventsKey } from 'ol/events'
import { MapBrowserEvent } from 'ol'

const map = inject<any>('map')
const getMap = inject<(map: any) => void>('getMap')
console.log(map.value, 'map===')
const props = defineProps({
  visible: {
    type: Boolean,
    default() {
      return false
    }
  }
})

let curYear = ref<any>(moment().format('YYYY'))
let loading = ref(false)
const rowKey = (row: any) => row.id
const columns = ref<any[]>([
  {
    type: 'selection'
  },
  {
    title: '台风编号',
    key: 'tfbh'
  },
  {
    title: '台风名称',
    key: 'name'
  }
])
const windRadiusSource = new VectorSource()
const windRadiusLayer = new VectorLayer({
  zIndex: 10,
  source: windRadiusSource
})
windRadiusLayer.setProperties({ name: '预报分析-台风' })
const typhoonInfo = reactive<any>({})
let typhoonOptions = ref<any[]>([])
const typhoon = ref('')
let overlay: any = null
const showPopup = ref(true)

watch(
  () => props.visible,
  (val: boolean) => {
    if (val) {
      getTyphoonList()
    }
  },
  {
    immediate: true
  }
)

// 获取台风列表
function getTyphoonList() {
  loading.value = true
  ToolApi.getTyList({
    year: curYear.value
  })
    .then((res: any) => {
      typhoonOptions.value = res.map((item: any) => {
        item.bhName = `${item.tfbh}-${item.name}`
        return item
      })
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

function clickTyphoon(evt: any, map: any) {
  if (evt.dragging) {
    return
  }
  const pixel = map.value.getEventPixel(evt.originalEvent)
  const feature = map.value.forEachFeatureAtPixel(
    pixel,
    function (feature: any) {
      return feature
    }
  )
  if (feature) {
    const info: any = feature.getProperties()
    if (info) {
      if (info.id) {
        addWindRadius(info)
        // const coordinate = evt.coordinate
        // overlay.setPosition(coordinate)
      }
    }
  }
}

function addWindRadius(data: any) {
  windRadiusSource.refresh()
  //  ne:东北 se:东南 sw:西南 nw:西北
  const features: any = []
  if (data.radius7Quad) {
    // 七级风圈
    const info = JSON.parse(data.radius7Quad)
    const center = [data.lng, data.lat]
    const EN7: any = gisUtils.getSectorPoints(center, info.ne, 0, 90, 30)
    EN7.shift()
    EN7.pop()
    const ES7: any = gisUtils.getSectorPoints(center, info.se, 90, 180, 30)
    ES7.shift()
    ES7.pop()
    const WS7: any = gisUtils.getSectorPoints(center, info.sw, 180, 270, 30)
    WS7.shift()
    WS7.pop()
    const WN7: any = gisUtils.getSectorPoints(center, info.nw, 270, 360, 30)
    WN7.shift()
    WN7.pop()
    const arr = [...EN7, ...ES7, ...WS7, ...WN7]
    arr.push(EN7[0])
    const feature = new Feature({
      geometry: new Polygon([arr])
    })
    feature.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#F4D000',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(244, 208, 0,0.3)'
        })
      })
    )
    features.push(feature)
  }
  if (data.radius10Quad) {
    // 十级风圈
    const info = JSON.parse(data.radius10Quad)
    const center = [data.lng, data.lat]
    const EN10: any = gisUtils.getSectorPoints(center, info.ne, 0, 90, 30)
    EN10.shift()
    EN10.pop()
    const ES10: any = gisUtils.getSectorPoints(center, info.se, 90, 180, 30)
    ES10.shift()
    ES10.pop()
    const WS10: any = gisUtils.getSectorPoints(center, info.sw, 180, 270, 30)
    WS10.shift()
    WS10.pop()
    const WN10: any = gisUtils.getSectorPoints(center, info.nw, 270, 360, 30)
    WN10.shift()
    WN10.pop()
    const arr10 = [...EN10, ...ES10, ...WS10, ...WN10]
    arr10.push(EN10[0])
    const feature = new Feature({
      geometry: new Polygon([arr10])
    })
    feature.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#F4D000',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(244, 208, 0,0.3)'
        })
      })
    )
    features.push(feature)
  }
  if (data.radius12Quad) {
    // 十级风圈
    const info = JSON.parse(data.radius12Quad)
    const center = [data.lng, data.lat]
    const EN12: any = gisUtils.getSectorPoints(center, info.ne, 0, 90, 30)
    EN12.shift()
    EN12.pop()
    const ES12: any = gisUtils.getSectorPoints(center, info.se, 90, 180, 30)
    ES12.shift()
    ES12.pop()
    const WS12: any = gisUtils.getSectorPoints(center, info.sw, 180, 270, 30)
    WS12.shift()
    WS12.pop()
    const WN12: any = gisUtils.getSectorPoints(center, info.nw, 270, 360, 30)
    WN12.shift()
    WN12.pop()
    const arr12 = [...EN12, ...ES12, ...WS12, ...WN12]
    arr12.push(EN12[0])
    const feature = new Feature({
      geometry: new Polygon([arr12])
    })
    feature.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#F4D000',
          width: 2
        }),
        fill: new Fill({
          color: 'rgba(244, 208, 0,0.3)'
        })
      })
    )
    features.push(feature)
  }
  windRadiusSource.addFeatures(features)
}

const source = new VectorSource()
const layer = new VectorLayer({
  zIndex: 11,
  source: source
})
layer.setProperties({ name: '预报分析-台风' })

function handleCheck(val: DataTableRowKey[]) {
  source.refresh()
  windRadiusSource.refresh()
  let result = typhoonOptions.value.filter((item: any) => val.includes(item.id))
  result.forEach((item: any) => {
    getTyData(item.tfbh)
  })
}

function getTyData(code: string) {
  windRadiusSource.refresh()
  source.refresh()
  ToolApi.getTyphoonInfo({
    code
  })
    .then((res: any) => {
      addTyphoon(res)
    })
    .catch(() => {})
}

let typhoonClicked: EventsKey | null = null

function addTyphoon(data: ITyphoonInfo[]) {
  const features: any = []
  const points: any = []
  data.forEach((item: any) => {
    let img = redaidiya
    if (item.strong === '热带低压(TD)') {
      img = redaidiya
    } else if (item.strong === '热带风暴(TS)') {
      img = redaifengbao
    } else if (item.strong === '强热带风暴(STS)') {
      img = qiangredaifengbao
    } else if (item.strong === '台风(TY)') {
      img = taifeng
    } else if (item.strong === '强台风(STY)') {
      img = qiangtaifeng
    } else if (item.strong === '超强台风(Super TY)') {
      img = chaoqiangtaifeng
    }
    const iconStyle = new Style({
      image: new Icon({
        anchor: [0.5, 0.5],
        src: img
      })
    })
    points.push([item.lng, item.lat])
    const iconFeature = new Feature({
      geometry: new Point([item.lng, item.lat])
    })
    iconFeature.setProperties(item)
    iconFeature.set('$isReal', true)
    iconFeature.setStyle(iconStyle)
    features.push(iconFeature)
  })
  let lineFeature = new Feature({
    geometry: new LineString(points)
  })
  features.push(lineFeature)
  source.addFeatures(features)

  getMap &&
    getMap((_map: Map) => {
      if (typhoonClicked) {
        _map.removeEventListener(typhoonClicked.type, typhoonClicked.listener)
      }
      _map.on('click', e => {
        const pixel = _map.getEventPixel(e.originalEvent)
        const featureAtPixel = _map.forEachFeatureAtPixel(pixel, feature => {
          const properties = feature.getProperties()
          if (properties.$isReal) {
            return feature
          }
        })
        if (featureAtPixel instanceof Feature) {
          const _properties = featureAtPixel.getProperties()
          const timeStr = moment(_properties.time).format('YYYYMMDDHHmmss')
          ToolApi.getForecastTyphoonByTime({
            code: _properties.tfbh,
            timeStr: timeStr
          })
            .then(res => {
              addForecastTyphoon(res, data[data.length - 1])
            })
            .catch(err => {
              console.warn(err)
            })
        }
      })
    })

  const forecast = data[data.length - 1].forecast
  forecast && addForecastTyphoon(forecast, data[data.length - 1])
}

/**
 * 移除台风预报路径
 */
function removeForecastFeatures() {
  const removeFeatures: Feature[] = []
  source.forEachFeature(feature => {
    const properties = feature.getProperties()
    if (properties.$isForecast) {
      removeFeatures.push(feature)
    }
  })
  removeFeatures.forEach(feature => {
    source.removeFeature(feature)
  })
}

/**
 * 添加台风预报路径
 * @param forecastArr
 */
function addForecastTyphoon(forecastArr: ITyphoonForecastInfo[], lastPath: ITyphoonInfo) {
  removeForecastFeatures()
  forecastArr.forEach(item => {
    if (
      lastPath.lat !== item.forecastpoints[0].lat ||
      lastPath.lng !== item.forecastpoints[0].lng
    ) {
      // 兼容中台新型数据 (预报点和历史点不重合)
      item.forecastpoints.unshift(lastPath)
    }
  })
  const isPathOverlap = uniqueTyphoonPathPoint()
  const features: Feature[] = []
  // 添加五个机构的预报台风
  forecastArr.forEach((ele: any) => {
    let lineColor: any = 'rgb(255, 60, 78)'
    if (ele.tm === '中国') {
      lineColor = '#ff3838'
    } else if (ele.tm === '中国香港') {
      lineColor = '#fff944'
    } else if (ele.tm === '日本') {
      lineColor = '#44ff63'
    } else if (ele.tm === '中国台湾') {
      lineColor = '#ec44ff'
    } else if (ele.tm === '美国') {
      lineColor = 'rgb(4, 250, 247)'
    } else if (ele.tm === '海南省海洋厅') {
      lineColor = '#4a44ff'
    }
    const tmPoints: any = []
    ele.forecastpoints.forEach((item: any) => {
      item.tm = ele.tm
      let img = redaidiya
      if (item.strong === '热带低压') {
        img = redaidiya
      } else if (item.strong === '热带风暴') {
        img = redaifengbao
      } else if (item.strong === '强热带风暴') {
        img = qiangredaifengbao
      } else if (item.strong === '台风') {
        img = taifeng
      } else if (item.strong === '强台风') {
        img = qiangtaifeng
      } else if (item.strong === '超强台风') {
        img = chaoqiangtaifeng
      }
      const iconStyle = new Style({
        image: new Icon({
          anchor: [0.5, 0.5],
          src: img
        })
      })
      tmPoints.push([item.lng, item.lat])
      const iconFeature = new Feature({
        geometry: new Point([item.lng, item.lat])
      })
      iconFeature.setProperties(item)
      iconFeature.set('$isForecast', true)
      iconFeature.setStyle(iconStyle)
      if (!isPathOverlap([item.lng, item.lat])) {
        features.push(iconFeature)
      }
    })
    let tmLineFeature = new Feature({
      geometry: new LineString(tmPoints)
    })
    tmLineFeature.set('$isForecast', true)
    tmLineFeature.setStyle(
      new Style({
        stroke: new Stroke({
          color: lineColor,
          width: 2,
          lineDash: [5, 10] // 设置虚线样式
        })
      })
    )
    features.push(tmLineFeature)
  })
  source.addFeatures(features)
}

function uniqueTyphoonPathPoint() {
  const points: number[][] = []
  source.forEachFeature(feature => {
    const geometry = feature.getGeometry()
    if (!(geometry instanceof Point)) {
      return
    }
    const coordinates = geometry.getCoordinates()
    points.push(coordinates)
  })
  return (point: number[]) => {
    const find = points.find(i => {
      return i[0] === point[0] && i[1] === point[1]
    })
    return !!find
  }
}

function displayFeatureInfo(evt: any, map: any) {
  if (evt.dragging) {
    return
  }
  showPopup.value = true
  const pixel = map.value.getEventPixel(evt.originalEvent)
  const feature = map.value.forEachFeatureAtPixel(
    pixel,
    function (feature: any) {
      return feature
    }
  )
  if (feature) {
    const info: any = feature.getProperties()
    if (info) {
      if (info.lat) {
        const name: any = typhoonOptions.value.find(
          (item: any) => item.tfbh === info.tfbh
        )?.name
        typhoonInfo.name = name ? name : ''
        typhoonInfo.tm = info.tm
        typhoonInfo.time = info.time
        typhoonInfo.speed = info.speed
        typhoonInfo.pressure = info.pressure
        typhoonInfo.lng = info.lng
        typhoonInfo.lat = info.lat
        typhoonInfo.moveDir = info.moveDir
        typhoonInfo.moveSpeed = info.moveSpeed
        typhoonInfo.strong = info.strong
        typhoonInfo.windRadius = []
        if (info.radius7Quad) {
          typhoonInfo.windRadius.push('7级')
          const radius: any = JSON.parse(info.radius7Quad)
          typhoonInfo.windRadius.push(
            radius.ne,
            radius.se,
            radius.sw,
            radius.nw + '(KM)'
          )
          //  ne:东北 se:东南 sw:西南 nw:西北
        }
        if (info.radius10Quad) {
          typhoonInfo.windRadius.push('10级')
          const radius: any = JSON.parse(info.radius10Quad)
          typhoonInfo.windRadius.push(
            radius.ne,
            radius.se,
            radius.sw,
            radius.nw + '(KM)'
          )
        }
        if (info.radius12Quad) {
          typhoonInfo.windRadius.push('12级')
          const radius: any = JSON.parse(info.radius12Quad)
          typhoonInfo.windRadius.push(
            radius.ne,
            radius.se,
            radius.sw,
            radius.nw + '(KM)'
          )
        }
        const coordinate = evt.coordinate
        overlay.setPosition(coordinate)
      } else {
        overlay.setPosition(undefined)
      }
    } else {
      overlay.setPosition(undefined)
    }
  } else {
    overlay.setPosition(undefined)
  }
}

function ctrlPopup(flag: any) {
  showPopup.value = flag
}

defineExpose({
  clickTyphoon
})

let moveHandel: any = null

onMounted(() => {
  nextTick(() => {
    map.value.addLayer(layer)
    map.value.addLayer(windRadiusLayer)
    const container: any = document.getElementById('popup')
    overlay = new Overlay({
      element: container
    })
    map.value.addOverlay(overlay)

    moveHandel = map.value.on('pointermove', function (evt: any) {
      displayFeatureInfo(evt, map)
    })
  })
})
onUnmounted(() => {
  const rawMap = toRaw(map.value)
  rawMap.removeLayer(layer)
  rawMap.removeLayer(windRadiusLayer)

  unByKey(moveHandel)
})
</script>
<style lang="scss" scoped>
.typhoon-container {
  width: 300px;
  position: absolute;
  bottom: 200px;
  right: 20px;
  z-index: 10;
  box-sizing: border-box;
  padding: 12px;
  background: #fff;
  height: 300px;

  .table-container {
    padding-top: 12px;
  }

  .search-btn {
    margin-left: 12px;
  }
}

.ol-popup-typhoon {
  position: fixed;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #cccccc;
  bottom: 12px;
  left: -50px;
  min-width: 280px;
  min-width: 335px;
  font-size: 14px;
  line-height: 22px;

  #popup1-content {
    .info {
      height: 22px;
      /**
      typhoonInfo.strong === '热带低压(TD)' ? 'td' : '',
            typhoonInfo.strong === '热带风暴(TS)' ? 'ts' : '',
            typhoonInfo.strong === '强热带风暴(STS)' ? 'sts' : '',
            typhoonInfo.strong === '台风(TY)' ? 'ty' : '',
            typhoonInfo.strong === '强台风(STY)' ? 'sty' : '',
            typhoonInfo.strong === '超强台风(SuperTY)' ? 'superTy' : ''
      */
      .strong {
        padding: 3px;
        color: #fff;

        &.td {
          background-color: #0083fd;
        }

        &.ts {
          background-color: #6bb654;
        }

        &.sts {
          background-color: #fff300;
        }

        &.ty {
          background-color: #fe7b0e;
        }

        &.sty {
          background-color: #f90102;
        }

        &.superTy {
          background-color: #9a5bbc;
        }
      }
    }

    .wind-info {
      display: flex;
      flex-wrap: wrap;
      margin-top: 10px;

      .wind-level {
        width: 56px;
        text-align: center;
        height: 22px;
        line-height: 22px;
      }
    }
  }
}

.ol-popup-typhoon:after,
.ol-popup-typhoon:before {
  top: 100%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.ol-popup-typhoon:after {
  border-top-color: white;
  border-width: 10px;
  left: 48px;
  margin-left: -10px;
}

.ol-popup-typhoon:before {
  border-top-color: #cccccc;
  border-width: 11px;
  left: 48px;
  margin-left: -11px;
}

.ol-popup-typhoon-closer {
  text-decoration: none;
  position: absolute;
  top: 2px;
  right: 8px;
}

.ol-popup-typhoon-closer:after {
  content: '✖';
}
</style>
