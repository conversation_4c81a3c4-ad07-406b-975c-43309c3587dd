package cn.piesat.data.making.server.utils;

import cn.hutool.core.io.FileUtil;
import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class XmlUtil {

    public static final String defaultCode = "default";

    /**
     * 创建xml
     *
     * @param inputPath 模板文件路径
     * @param outPath   输出路径
     * @param detailMap k:预报模板编码 v:预报记录明细列表
     **/
    public static void createXml(String inputPath, String outPath, Map<String, List<ForecastRecordDetail>> detailMap) {
        try {
            Document document = XmlUtil.readXml(inputPath, detailMap);
            Writer osWrite = new OutputStreamWriter(new FileOutputStream(outPath));
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("UTF-8");
            FileUtil.mkParentDirs(outPath);
            XMLWriter writer = new XMLWriter(osWrite, format);
            writer.write(document);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("生成xml失败！");
        }
    }

    /**
     * 读取xml模板
     *
     * @param inputPath 模板文件路径
     * @param detailMap k:预报模板编码 v:预报记录明细列表
     **/
    public static Document readXml(String inputPath, Map<String, List<ForecastRecordDetail>> detailMap) {
        try {
            SAXReader reader = new SAXReader();
            Document document = reader.read(new File(inputPath));
            //一级标签
            Element firstElement = document.getRootElement();
            String firstElementName = firstElement.getName();

            //二级标签
            Iterator secondIter = firstElement.elementIterator();
            //二级标签名称
            String secondElementName = "";
            //二级标签中name属性的值
            String secondNameVal = "";

            //k:预报模板编码beach_00001 v:二级标签名称t_hsyc
            Map<String, String> secondElementMap = new HashMap<>();
            //k:要素编码langgaoMin v:三级标签名称wave_height_min
            Map<String, String> thirdElementMap = new LinkedHashMap();
            //k:预报模板编码beach_00001 v:thirdElementMap
            Map<String, Map<String, String>> secongThirdMap = new HashMap();

            //遍历二级标签
            while (secondIter.hasNext()) {
                Element secondElement = (Element) secondIter.next();
                secondElementName = secondElement.getName();
                if (secondElement.attribute("code") == null) {
                    continue;
                }
                secondNameVal = secondElement.attribute("code").getValue();
                if (StringUtils.isBlank(secondNameVal)) {
                    continue;
                }
                //遍历三级标签
                Iterator thirdIter = secondElement.elementIterator();
                while (thirdIter.hasNext()) {
                    Element thirdElement = (Element) thirdIter.next();
                    if (thirdElement.attribute("code") == null) {
                        continue;
                    }
                    String thirdNameVal = thirdElement.attribute("code").getValue();
                    if (StringUtils.isBlank(thirdNameVal)) {
                        continue;
                    }
                    String thirdElementName = thirdElement.getName();
                    thirdElementMap.put(thirdNameVal, thirdElementName);
                }
                secondElementMap.put(secondNameVal, secondElementName);
                secongThirdMap.put(secondNameVal, thirdElementMap);
            }
            return saveXml(firstElementName, secondElementMap, secongThirdMap, detailMap);
        } catch (DocumentException e) {
            e.printStackTrace();
            throw new RuntimeException("解析xml失败！");
        }
    }

    /**
     * 组装xml标签
     *
     * @param firstElementName 一级标签名称
     * @param secondElementMap k:预报模板编码beach_00001 v:二级标签名称t_hsyc
     * @param secongThirdMap   k:预报模板编码beach_00001 v:thirdElementMap
     * @param detailMap        k:预报模板编码 v:预报记录明细列表
     **/
    public static Document saveXml(String firstElementName, Map<String, String> secondElementMap, Map<String, Map<String, String>> secongThirdMap, Map<String
            , List<ForecastRecordDetail>> detailMap) {
        //生成xml的第一行 <?xml version="1.0" encoding="UTF-8"?>
        Document outDocument = DocumentHelper.createDocument();
        //添加一级标签
        Element firstDocument = outDocument.addElement(firstElementName);

        Set<String> detailSet = detailMap.keySet();
        for (String code : detailSet) {
            List<ForecastRecordDetail> detailList = detailMap.get(code);
            //根据区域分组
            Map<String, List<ForecastRecordDetail>> codeDetailMap = detailList.stream().collect(Collectors.groupingBy(ForecastRecordDetail::getAreaCode));
            Set<String> areaCodeSet = codeDetailMap.keySet();

            //thirdElementMap k:要素编码langgaoMin v:三级标签名称wave_height_min
            Map<String, String> thirdElementMap = secongThirdMap.get(code);
            if (CollectionUtils.isEmpty(thirdElementMap)) {
                continue;
            }
            for (String areaCode : areaCodeSet) {
                //secondElementName 二级标签名称t_hsyc
                String secondElementName = secondElementMap.get(code);
                if (StringUtils.isBlank(secondElementName)) {
                    continue;
                }
                //添加二级标签
                Element secondElement = firstDocument.addElement(secondElementName);
                secondElement.addAttribute("code", code);

                //遍历预报记录列表添加三级标签 thirdElementMap k:要素编码langgaoMin v:三级标签名称wave_height_min
                for (Map.Entry<String, String> entry : thirdElementMap.entrySet()) {
                    List<ForecastRecordDetail> details = codeDetailMap.get(areaCode);
                    String columnCode = entry.getKey();
                    String thirdElementName = entry.getValue();
                    if (StringUtils.isBlank(thirdElementName)) {
                        continue;
                    }
                    if (defaultCode.equals(columnCode)) {
                        Element element = secondElement.addElement(thirdElementName);
                        element.setText(details.get(0).getAreaName());
                    }
                    //添加name属性的值不包含/的三级标签
                    List<ForecastRecordDetail> recordDetails = details.stream().filter(k -> k.getColumnCode().equals(columnCode)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(recordDetails)) {
                        Element element = secondElement.addElement(thirdElementName);
                        String value = recordDetails.get(0).getValue();
                        element.setText(StringUtils.isBlank(value) ? "" : value);
                    }
                    //添加name属性的值包含/的三级标签
                    if (columnCode.contains("/")) {
                        String[] arr = columnCode.split("/");
                        StringBuilder detailVal = new StringBuilder();
                        for (int i = 0; i < arr.length; i++) {
                            String elementKey = arr[i];
                            //获取分割标签对应的detail
                            List<ForecastRecordDetail> recordDetailList =
                                    details.stream().filter(d -> d.getColumnCode().equals(elementKey)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(recordDetailList)) {
                                detailVal.append("/");
                                continue;
                            }
                            String value = recordDetailList.get(0).getValue();
                            detailVal.append(StringUtils.isBlank(value) ? "" : value).append("/");
                        }
                        if (detailVal.length() > 2) {
                            Element element = secondElement.addElement(thirdElementName);
                            element.setText(detailVal.substring(0, detailVal.length() - 1));
                        }
                    }
                }
            }
        }
        return outDocument;
    }
}
