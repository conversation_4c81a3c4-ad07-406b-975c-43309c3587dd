<template>
  <!-- 预报单 -->
  <div class="forecasts-list">
    <!-- 左侧列表 -->
    <Aside
      :is-edit="isEdit"
      @change-product="onChangeProduct"
      @change-tab="changeTab"
    />
    <!-- 右侧内容 -- graphic：图解 area:区域 -->
    <div class="content">
      <!-- graphic：图解 -->
      <template v-if="currentProductType === 'graphic'">
        <base-map :current-product="currentProduct">
          <preview-aside :current-product="currentProduct" />
        </base-map>
      </template>
      <!-- area:区域  -->
      <template v-else>
        <div
          class="content-header d-flex flex-justify-between flex-align-center"
        >
          <h3>{{ currentProduct.name }}</h3>
          <div v-if="tabIndex == 1" class="btns">
            <div class="line">
              <qx-button class="primary" @click="onSubmit">提交</qx-button>
            </div>
          </div>
        </div>
        <div>
          <!-- 顶部数据源： -->
          <div class="content-filter d-flex">
            <div class="left-wrap">
              <div v-if="showDataSourcePicker" class="content-filter-item">
                <label for="">数据源：</label>
                <n-radio-group
                  v-model:value="dataSource"
                  name="radiogroup"
                  @update:value="handleChange"
                >
                  <n-radio
                    v-for="item in dataSourceList"
                    :key="item.value"
                    :value="item.value"
                    :disabled="tabIndex != 1"
                  >
                    {{ item.label }}
                  </n-radio>
                </n-radio-group>
              </div>
              <div
                v-if="showBaseTable"
                class="content-filter-item d-flex flex-align-center"
              >
                <label for="">起报时间:</label>
                <div class="content-filter-item__content">{{ reportTime }}</div>
              </div>
            </div>

            <div class="right-btn-group">
              <qx-button class="primary" @click="loadPreButton"
                >加载上一期
              </qx-button>
              <qx-button @click="resetButton">重置</qx-button>
              <qx-button @click="showDrawer = true">实况观测数据</qx-button>
              <qx-button @click="onEdit">编辑</qx-button>
              <!-- <qx-button class="primary" @click="onSave">保存</qx-button> -->
              <qx-button @click="saveButton">保存</qx-button>
            </div>
          </div>

          <!-- 区域的数据 -->
          <div v-if="showBaseTable" class="table-container flex1">
            <n-data-table
              v-loading="loading"
              :row-key="rowKey"
              class="qx-table"
              :data="tableData"
              :columns="dynamicColumns"
              :single-line="false"
              :max-height="550"
            ></n-data-table>
          </div>
          <!--          <div class="btns text-center">-->
          <!--            <qx-button class="primary" @click="loadPreButton"-->
          <!--              >加载上一期</qx-button-->
          <!--            >-->
          <!--            <qx-button @click="resetButton">重置</qx-button>-->
          <!--            <template v-if="tabIndex == 1">-->
          <!--              <qx-button @click="saveButton">保存</qx-button>-->
          <!--            </template>-->
          <!--          </div>-->
        </div>
        <sea-area-forecast
          v-if="currentProduct.forecastType === seaForecastType"
          :table-data="tableData"
          ref="seaAreaForecastRef"
        />
        <city-forecast
          v-if="currentProduct.forecastType === cityForecastType"
          :table-data="tableData"
          ref="cityForecastRef"
        ></city-forecast>
      </template>
    </div>
  </div>
  <Drawer :show-drawer="showDrawer" @close="showDrawer = false" />
</template>

<script setup lang="ts">
import { Aside, Drawer, SeaAreaForecast } from './index'
import { BaseMap } from 'src/components/OpenlayersMap'
import { PreviewAside } from 'src/components/PreviewAside'
import { computed, h, provide, readonly, ref } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/forecast'
import { DataTableColumn } from 'naive-ui'
import { useMessage } from 'naive-ui'
import QxTableEdit from 'src/components/QxTableEdit/index.js'
import eventBus from 'src/utils/eventBus'
import CityForecast from 'src/views/forecast/formProduction/cityForecast/CityForecast.vue'
import {
  defaultColumnRenderer,
  swimMeanColumnRenderer,
  useTableParser, waveLevelColumnRenderer
} from 'src/views/forecast/formProduction/forecastProductHooks/useTableParser'
import { toShitData } from 'src/views/forecast/formProduction/forecastProductHooks/utils'
import {
  bridgeKey, cityForecastType,
  IForecastProductItem, seaForecastType
} from 'src/views/forecast/formProduction/forecastProductHooks/types'
import { useBridge } from 'src/utils/vue-hooks/useBridge/useBridge'
import { useParagraph } from 'src/views/forecast/formProduction/forecastProductHooks/useParagraph'

const bridge = useBridge(bridgeKey)
const message = useMessage()
const currentProductType = ref('')
const currentProduct: any = ref({})
provide('currentProduct', currentProduct)
const seaAreaForecastRef = ref()
const cityForecastRef = ref()
const tableData = ref<any[]>([])
let { getParagraph } = useParagraph(tableData)
let isEdit = ref(false) // 是否处于编辑状态,默认关闭
provide('isEdit', isEdit)
let showDrawer = ref(false) // 是否显示24小时观测数据
interface RowData {
  [x: string]: any

  key: string
  value: string
}

const dynamicColumns = ref<DataTableColumn<RowData>[]>([
  {
    title: '名称',
    key: 'areaName',
    align: 'center'
  }
])
const rowKey = (row: any) => row.id
const loading = ref(true)
provide('loading', loading)
const showBaseTable = computed(
  () => !['area', 'city'].includes(currentProductType.value)
)
const showDataSourcePicker = computed(
  () =>
    !['area', 'city'].includes(currentProductType.value) &&
    tabIndex.value === 1
)
let forecastRecordInfo = ref<any>({})
let tabIndex = ref(1)
provide('tabIndex', tabIndex)
let reportTime = ref('')
provide('reportTime', reportTime)
const dataSource = ref('grid')
provide('dataSource', dataSource)

const dataSourceList = [
  {
    label: '智能网格',
    value: 'grid'
  },
  {
    label: 'NMEFC',
    value: 'forecast'
  }
]

// 获取起报时间
async function getLastForecastTime() {
  const params = {
    dataSource: dataSource.value
  }
  return await Api.getLastForecastTime(params)
}

function changeTab(val: any) {
  tabIndex.value = val
}

// 数据源切换
async function handleChange(item: any) {
  dataSource.value = item
  loading.value = true
  let reportResult = (await getLastForecastTime()) as any
  reportTime.value = reportResult
  getForecastRecordInfo()
}

// 获取预报列表详细数据
async function getForecastRecordInfo() {
  tableData.value = []

  let parmas = {
    taskId: currentProduct.value.id,
    dataSource: dataSource.value,
    reportTime: reportTime.value
  }
  // Api.forecastRecordInfo(parmas)
  const api =
    tabIndex.value === 2
      ? Api.forecastRecordInfoV3(parmas as any)
      : Api.forecastRecordInfoV2(parmas as any)
  api
    .then(res => {
      const data = res.data.detailList
      const dataParser = useTableParser(data)
      tableData.value = dataParser.tableData
      const columns = dataParser.tableColumns
      defaultColumnRenderer(columns, isEdit)
      swimMeanColumnRenderer(columns, isEdit)
      waveLevelColumnRenderer(columns, isEdit)
      dynamicColumns.value = columns
      forecastRecordInfo.value = res.data // 提交需要参数
      loading.value = false
    })
    .finally(() => {
      loading.value = false
    })
    .catch(err => {
      console.error(err, 'errr---')
      loading.value = false
      message.error('获取数据失败')
    })
}

// 左侧预报列表切换事件
async function onChangeProduct(item: any, flag: boolean) {
  console.log(item, 'item====')
  loading.value = false
  isEdit.value = false
  let reportResult = (await getLastForecastTime()) as any
  reportTime.value = reportResult
  if (flag) {
    saveButton(item)
  } else {
    changeProductInfo(item)
  }
}

function changeProductInfo(item?: any) {
  if (item) {
    currentProductType.value = item.forecastType
    currentProduct.value = item
    loading.value = true
    if (currentProductType.value != 'graphic') getForecastRecordInfo()
  } else {
    tableData.value = []
    dynamicColumns.value = [
      {
        title: '名称',
        key: 'areaName',
        align: 'center'
      }
    ]
  }
}

// 加载上一期
async function loadPreButton() {
  loading.value = true
  try {
    const params = {
      dataSource: dataSource.value,
      reportTime: reportTime.value
    }
    let res = (await Api.forecastRecordLoadPre(
      currentProduct.value.id,
      params
    )) as any
    if (res !== null) {
      // tableData.value = res.detailList
      const data = res.detailList
      const dataParser = useTableParser(data)
      tableData.value = dataParser.tableData
      const columns = dataParser.tableColumns
      defaultColumnRenderer(columns, isEdit)
      swimMeanColumnRenderer(columns, isEdit)
      waveLevelColumnRenderer(columns, isEdit)
      dynamicColumns.value = columns
      loading.value = false
      message.success('操作成功')
    } else {
      tableData.value = []
      loading.value = false
      message.success('操作成功，但无数据')
    }
  } catch (error) {
    loading.value = false
  }
}

// 重置
async function resetButton() {
  loading.value = true
  // try {
  //   let res = (await Api.forecastRecordReset(currentProduct.value.id)) as any
  //   tableData.value = res.detailList
  //   loading.value = false
  //   message.success('操作成功')
  // } catch (error) {
  //   loading.value = false
  // }
  getForecastRecordInfo()
}

function formatParams(type: string) {
  if (tableData.value.length == 0) {
    return
  }

  // let detailList: any[] = []

  let data: any[]
  if (
    [seaForecastType, cityForecastType].includes(
      currentProduct.value.forecastType
    )
  ) {
    if (seaAreaForecastRef.value) {
      data = seaAreaForecastRef.value.getTableData()
    } else if (cityForecastRef.value) {
      data = cityForecastRef.value.getTableData()
    } else {
      data = []
    }
  } else {
    data = tableData.value
  }
  toShitData(data, forecastRecordInfo.value.detailList)
  if (type === 'submit') {
    const detailList: IForecastProductItem[] =
      forecastRecordInfo.value.detailList
    detailList.forEach(item => {
      item.id = null
    })
  }

  // data.forEach((item: any) => {
  // Object.keys(item).forEach((key: any) => {
  //   let obj: any = {
  //     id: type == 'submit' ? null : item[`id${key}`],
  //     areaCode: item['areaCode'],
  //     areaName: item['areaName'],
  //     elementCode: item[`eCode${key}`],
  //     elementName: item[`eName${key}`],
  //     elementValueHandle: item[`elementValue${key}`],
  //     elementDisplay: item.elementDisplay,
  //     columnCode: item[`cCode${key}`],
  //     columnName: key,
  //     value: item[key]
  //   }
  //   detailList.push(obj)
  // })
  // })

  let params = {
    forecastTaskId: currentProduct.value['id'],
    dataSource: dataSource.value,
    reportTime: reportTime.value,
    detailList: forecastRecordInfo.value.detailList
  }

  return params
}

// 保存按钮
async function saveButton(productInfo: any) {
  let params = formatParams('save')
  loading.value = true
  try {
    let res = await Api.forecastRecordSave(params)
    isEdit.value = false
    loading.value = false
    message.success('操作成功')
    productInfo && changeProductInfo(productInfo)
  } catch (e: any) {
    loading.value = false
    let { msg = null } = e?.response?.data || {}
    message.error(msg || '操作失败')
    console.error(e || '操作失败')
  }
}

function onEdit() {
  isEdit.value = true
  message.success('已进入编辑状态,点击表格可进行编辑')
}

// 提交
async function onSubmit() {
  let {
    forecastTemplateId = '',
    forecastType = '',
    forecastTemplateCode = ''
  } = forecastRecordInfo.value
  let params = Object.assign({}, formatParams('submit'), {
    forecastTemplateId,
    forecastType,
    forecastTemplateCode
  })
  if ([seaForecastType, cityForecastType].includes(currentProduct.value.forecastType)) {
    const seaParagraph = seaAreaForecastRef.value?.getParagraph()
    const cityParagraph = cityForecastRef.value?.getParagraph()
    const forecastContent = seaParagraph ? seaParagraph : cityParagraph
    if (forecastContent === '' || forecastContent === undefined) {
      message.warning('请先生成文字')
      return
    }
    Object.assign(params, { forecastContent })
  } else if (
    ['重要港湾海岛预报', '三沙环境预报'].includes(currentProduct.value.name)
  ) {
    Object.assign(params, { forecastContent: getParagraph() })
  }
  try {
    // console.log('假装提交:', params)
    let res = await Api.forecastRecordSubmit(params)
    isEdit.value = false
    message.success('操作成功')
    eventBus.emit('updateForecast')
  } catch (e) {
    console.error(e || '操作失败')
  }
}
</script>

<style lang="scss">
.forecasts-list {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  display: flex;

  .content {
    height: calc(100vh - 104px) !important;
    margin-left: 20px;
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;

    .content-header {
      box-sizing: border-box;
      padding: 9px 20px;
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 23px;
      }
    }
  }

  .content-filter {
    box-sizing: border-box;
    padding: 20px;
    background: rgba(217, 217, 217, 0.2);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;

    .left-wrap {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .right-btn-group {
    }

    label,
    .n-radio__label,
    .content-filter-item__content {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      margin-right: 10px;
    }

    // .n-radio__dot.n-radio__dot--checked {
    //   background-color: #1c81f8 !important;
    // }
  }

  // .common-aside {
  //   position: absolute;
  //   height: calc(100% - 40px);
  //   z-index: 1;
  //   top: 20px;
  //   left: 20px;
  // }
  .table-container {
    height: calc(100% - 160px);
    box-sizing: border-box;
    padding: 18px;

    .btns {
      // margin-top: 20px;
      text-align: center;
    }
  }

  .btns {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .line:last-child {
      margin-top: 5px;
    }
  }
}
</style>
