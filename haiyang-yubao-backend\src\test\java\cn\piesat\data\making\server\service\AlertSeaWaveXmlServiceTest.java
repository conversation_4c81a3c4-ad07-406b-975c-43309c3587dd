package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
public class AlertSeaWaveXmlServiceTest {

    @Autowired
    private SeaWaveAlarmService seaWaveAlarmServiceImpl;

    @Autowired
    private AlertSeaWaveXmlService alertSeaWaveXmlServiceImpl;

    @Test
    public void testSender(){
        SeaWaveAlarm seaWaveAlarm = seaWaveAlarmServiceImpl.getById(1959454835999322113L);

        alertSeaWaveXmlServiceImpl.sender(Thread.currentThread().getContextClassLoader().getResource("").getPath()+"alertxml/seawavetemplate.xml",seaWaveAlarm);
    }
}
