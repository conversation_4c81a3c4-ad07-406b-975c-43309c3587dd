<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-15 10:02:17
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-02-20 16:17:22
 * @FilePath: \hainan-jianzai-web\src\components\OpenlayersMap\components\LQGJ.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="btns">
    <qx-button
      class="edit-btn"
      :class="[activeBtn === 'draw' ? 'active' : '']"
      @click="trigger('draw')"
      >绘制</qx-button
    >
    <qx-button
      class="edit-btn"
      :class="[activeBtn === 'modify' ? 'active' : '']"
      @click="trigger('modify')"
      >修改</qx-button
    >
    <qx-button
      class="edit-btn"
      :class="[activeBtn === 'move' ? 'active' : '']"
      @click="trigger('move')"
      >移动</qx-button
    >
    <qx-button
      class="edit-btn"
      :class="[activeBtn === 'delete' ? 'active' : '']"
      @click="trigger('delete')"
      >删除</qx-button
    >
    <qx-button
      class="edit-btn"
      :class="[activeBtn === 'clear' ? 'active' : '']"
      @click="trigger('clear')"
      >清空</qx-button
    >
  </div>
  <div class="tools-container">
    <div class="query-item">
      <div class="query-title">不透明度：</div>
      <n-slider
        v-model:value="opacity"
        :default-value="100"
        :step="1"
        :format-tooltip="formatTooltip"
        class="my-slider"
      />
      <n-input-number
        v-model:value="opacity"
        class="small-input"
        :show-button="false"
      >
        <template #suffix> % </template>
      </n-input-number>
    </div>
    <div class="query-item">
      <div class="query-title">描边线宽：</div>
      <n-select
        v-model:value="borderWidth"
        class="query-info"
        :options="options"
      />
    </div>
    <div class="query-item">
      <div class="query-title">描边颜色：</div>
      <div class="color-picker">
        <div
          v-for="item in colors"
          :key="item.id"
          class="color-item"
          :style="{ background: item.value }"
          :class="[
            item.haveBorder ? 'have-border' : '',
            item.id === 'transparent' ? 'transparent-div' : '',
            borderColor === item.value ? 'active-color' : ''
          ]"
          @click="borderColor = item.value"
        ></div>
        <div
          class="color-item"
          :style="{ background: '#000000' }"
          :class="[borderColor === '#000000' ? 'active-color' : '']"
          @click="borderColor = '#000000'"
        ></div>
        <!-- <n-color-picker
          v-model:value="borderColorPick"
          class="color-item"
          :show-alpha="false"
          :actions="['confirm']"
          :modes="['hex']"
          @update:value="borderColor = borderColorPick"
        >
        </n-color-picker> -->
      </div>
    </div>
    <div class="query-item">
      <div class="query-title">填充颜色：</div>
      <div class="color-picker">
        <div
          v-for="item in colors"
          :key="item.id"
          class="color-item"
          :style="{ background: item.value }"
          :class="[
            item.haveBorder ? 'have-border' : '',
            item.id === 'transparent' ? 'transparent-div' : '',
            fillColor === item.value ? 'active-color' : ''
          ]"
          @click="fillColor = item.value"
        ></div>
        <!-- <n-color-picker
          v-model:value="fillColorPicker"
          :show-alpha="false"
          :actions="['confirm']"
          class="color-item"
          :modes="['hex']"
          @update:value="fillColor = fillColorPicker"
        >
        </n-color-picker> -->
      </div>
    </div>
    <div class="query-item">
      <div class="query-title">落区值：</div>
      <n-input
        v-model:value="text"
        placeholder="请输入落区值"
        class="query-info"
      />
    </div>
  </div>
  <div class="query-bottom">
    <qx-button
      v-show="activeBtn === 'modify'"
      class="my-btn"
      @click="saveModify"
      >保存修改</qx-button
    >
  </div>
</template>

<script setup name="LQGJ">
import { ref, inject, onMounted, onUnmounted, watch, computed } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { Stroke, Style, Fill, Text, Circle } from 'ol/style.js'
import Draw from 'ol/interaction/Draw.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import Polygon from 'ol/geom/Polygon.js'
//src\utils\plotting\plot\utils\plotutil.js
import * as PlotUtils from 'src/utils/plotting/plot/utils/plotutil.js'
import Point from 'ol/geom/Point.js'
import Feature from 'ol/Feature.js'
import Select from 'ol/interaction/Select.js'
import Translate from 'ol/interaction/Translate.js'
import { Modify, Snap } from 'ol/interaction.js'
import { click } from 'ol/events/condition.js'
import { useMessage } from 'naive-ui'
import changeStore from './changeStore.js'
const message = useMessage()
const getMap = inject('getMap')
const activeBtn = ref('')
const opacity = ref(0)
const formatTooltip = value => `${value}%`
const borderWidth = ref(1)
const options = [
  {
    label: '1px',
    value: 1
  },
  {
    label: '2px',
    value: 2
  },
  {
    label: '3px',
    value: 3
  }
]
const colors = ref([])
const stormSurgeColors = [
  {
    label: '红色',
    value: '#F90102',
    id: 'red',
    haveBorder: false
  },
  {
    label: '橘色',
    value: '#FE7B0E',
    id: 'orange',
    haveBorder: false
  },
  {
    label: '黄色',
    value: '#FFF300',
    id: 'yellow',
    haveBorder: false
  },
  {
    label: '蓝色',
    value: '#0083FD',
    id: 'blue',
    haveBorder: false
  },
  {
    label: '透明',
    value: 'rgba(0,0,0,0)',
    id: 'transparent',
    haveBorder: true
  },
  {
    label: '白色',
    value: '#ffffff',
    id: 'white',
    haveBorder: true
  }
]
const seaWaveColors = [
  {
    label: '红色',
    value: '#770804',
    id: 'red',
    haveBorder: false,
    min: 9,
    max: 14
  },
  {
    label: '橘色',
    value: '#f70501',
    id: 'orange',
    haveBorder: false,
    min: 6,
    max: 9
  },
  {
    label: '黄色',
    value: '#FD7D15',
    id: 'yellow',
    haveBorder: false,
    min: 4,
    max: 6
  },
  {
    label: '蓝色',
    value: '#FDF205',
    id: 'blue',
    haveBorder: false,
    min: 3,
    max: 4
  },
  {
    label: '透明',
    value: '#0102F7',
    id: 'transparent',
    haveBorder: true,
    min: 2.5,
    max: 3
  },
  {
    label: '白色',
    value: '#54DDC4',
    id: 'white',
    haveBorder: true,
    min: 0,
    max: 2.5
  }
]
let alarmType = computed(() => {
  return localStorage.getItem('alarmType')
})

const borderColor = ref('')
const fillColor = ref('')
const fillColorPicker = ref('')
const borderColorPick = ref('')
const text = ref('1')
function trigger(type) {
  activeBtn.value = type
  if (type === 'draw') {
    startDraw()
  }
  if (type === 'clear') {
    source.refresh()
    editPointSource.refresh()

    getMap(map => {
      changeStore(map)
      map.removeInteraction(modify)
      select.setActive(true)
      activeBtn.value = ''
    })
  }
  if (type === 'delete') {
    selectFeatures.forEach(feature => {
      source.removeFeature(feature)
    })
    selectFeatures = null
    getMap(map => {
      changeStore(map)
    })
  }
  if (type === 'modify') {
    translate.setActive(false)
    startModify()
  }
  if (type === 'move') {
    select.setActive(true)
    if (activeBtn.value === 'modify') {
      message.warning('请先结束修改')
    } else {
      editPointSource.refresh()
      startMove()
    }
  }
}

const source = new VectorSource()

const vector = new VectorLayer({
  source: source,
  zIndex: 11,
  properties: {
    name: 'lqgj',
    canRedo: true
  }
})
let cutDrawFeaturePoint = null
let geometryFunction = function (coordinates, geometry) {
  var count = coordinates.length
  cutDrawFeaturePoint = coordinates
  var pnts = JSON.parse(JSON.stringify(coordinates[0]))
  pnts.push(pnts[0], pnts[1])
  var normals = []
  for (var i = 0; i < pnts.length - 2; i++) {
    var normalPoints = PlotUtils.getBisectorNormals(
      0.3,
      pnts[i],
      pnts[i + 1],
      pnts[i + 2]
    )
    normals = normals.concat(normalPoints)
  }
  var length = normals.length
  normals = [normals[length - 1]].concat(normals.slice(0, length - 1))

  var pList = []
  for (i = 0; i < pnts.length - 2; i++) {
    var pnt1 = pnts[i]
    var pnt2 = pnts[i + 1]
    pList.push(pnt1)
    for (var t = 0; t <= 100; t++) {
      var pnt = PlotUtils.getCubicValue(
        t / 100,
        pnt1,
        normals[i * 2],
        normals[i * 2 + 1],
        pnt2
      )
      pList.push(pnt)
    }
    pList.push(pnt2)
  }
  if (!geometry) {
    geometry = new Polygon([pList])
  } else {
    geometry.setCoordinates([pList])
  }
  return geometry
}
let draw = new Draw({
  source: source,
  type: 'Polygon',
  style: {
    'circle-radius': 5,
    'circle-fill-color': fillColor.value,
    'stroke-color': 'rgba(255,0,0,0)',
    'stroke-width': 2,
    'fill-color': 'rgba(255,0,0,0.1)'
  },
  geometryFunction: geometryFunction
})
draw.on('drawstart', function (evt) {
  getMap(map => {
    // map.addInteraction(draw)
    map.removeInteraction(select)
  })
})
draw.on('drawend', function (evt) {
  let result = colors.value.find(item => {
    return (
      item.min !== undefined &&
      item.min <= text.value &&
      item.max !== undefined &&
      text.value < item.max
    )
  })
  evt.feature.setProperties({
    points: cutDrawFeaturePoint,
    value: text.value,
    strokeWith: borderWidth.value,
    strokeColor: borderColor.value ? borderColor.value : result?.value,
    fillColor: fillColor.value,
    opacity: opacity.value,
    category: '2',
    transparency: opacity.value / 100,
    color: fillColor.value
  })
  evt.feature.setStyle(
    new Style({
      stroke: new Stroke({
        color: borderColor.value ? borderColor.value : result?.value,
        width: borderWidth.value
      }),
      fill: new Fill({
        color: hexToRgb(fillColor.value, opacity.value)
      }),
      text: new Text({
        font: '16px Calibri,sans-serif',
        text: text.value,
        fill: new Fill({
          color: '#000'
        }),
        stroke: new Stroke({
          color: '#fff',
          width: 3
        }),
        placement: 'line'
      })
    })
  )

  activeBtn.value = ''
  // 这里需要异步解决source中没有feature的问题，异步解决绘制后直接选中的问题
  setTimeout(() => {
    getMap(map => {
      changeStore(map)
      map.removeInteraction(draw)
      map.addInteraction(select)
    })
  }, 0)
})
// 开始绘制落区
function startDraw() {
  getMap(map => {
    map.addInteraction(draw)
  })
}
function hexToRgb(hex, opacity) {
  let result = ''
  if (!hex.includes('#')) {
    result = hex
  } else {
    // 去除可能存在的"#"符号
    hex = hex.replace('#', '')

    // 将三位数的十六进制值转换为六位
    if (hex.length === 3) {
      hex = hex
        .split('')
        .map(hexDigit => hexDigit + hexDigit)
        .join('')
    }

    // 将十六进制值转换为RGB值
    const r = parseInt(hex.slice(0, 2), 16)
    const g = parseInt(hex.slice(2, 4), 16)
    const b = parseInt(hex.slice(4, 6), 16)

    result = `rgba(${r}, ${g}, ${b},${opacity / 100})`
  }
  return result
}
let select = null // ref to currently selected interaction

const selected = new Style({
  fill: new Fill({
    color: 'rgba(0, 0, 255, 0.2)'
  }),
  stroke: new Stroke({
    color: 'rgba(0, 0, 255, 0.7)',
    width: 2,
    lineDash: [5, 10] // 设置虚线样式
  })
})
function selectStyle(feature) {
  const color = feature.get('COLOR') || 'rgba(0, 255, 255, 0.2)'
  selected.getFill().setColor(color)
  return selected
}
const selectSingleClick = new Select({ style: selectStyle, layers: [vector] })

// select interaction working on "click"
const selectClick = new Select({
  condition: click,
  style: selectStyle
})
select = selectClick
let selectFeatures = null
select.on('select', function (e) {
  activeBtn.value = ''
  translate.setActive(false)
  selectFeatures = e.selected
  if (selectFeatures.length > 0) {
    let feature = selectFeatures[0]
    let properties = feature.getProperties()
    text.value = properties.value
    fillColor.value = properties.fillColor
    opacity.value = properties.opacity
    borderColor.value = properties.strokeColor
    borderWidth.value = properties.strokeWith
    cutDrawFeaturePoint = properties.points
  } else {
    source.forEachFeature(function (feature) {
      let properties = feature.getProperties()
      feature.setStyle(
        new Style({
          stroke: new Stroke({
            color: properties.strokeColor,
            width: properties.strokeWith
          }),
          fill: new Fill({
            color: hexToRgb(properties.fillColor, properties.opacity)
          }),
          text: new Text({
            font: '16px Calibri,sans-serif',
            text: properties.value,
            fill: new Fill({
              color: '#000'
            }),
            stroke: new Stroke({
              color: '#fff',
              width: 3
            }),
            placement: 'line'
          })
        })
      )
    })
  }
})
const translate = new Translate({
  features: select.getFeatures() //选中之后的要素
})
// todo: 移动落区后重新设置点的位置
translate.on('translateend', function (evt) {
  const coordinate = evt.coordinate
  const startCoordinate = evt.startCoordinate
  const deltaX = coordinate[0] - startCoordinate[0]
  const deltaY = coordinate[1] - startCoordinate[1]
  let resPoints = []

  if (evt.features.array_.length > 0) {
    evt.features.array_.forEach(feature => {
      if (feature.values_.points && feature.values_.points.length > 0) {
        feature.values_.points.forEach(points => {
          points.forEach(point => {
            resPoints.push([point[0] + deltaX, point[1] + deltaY])
          })
        })
        feature.setProperties({ points: [resPoints] })
      }
    })
    getMap(map => {
      changeStore(map)
    })
  }
})
const editPointSource = new VectorSource()
const editPointLayer = new VectorLayer({
  source: editPointSource,
  zIndex: 11
})
const modify = new Modify({
  source: editPointSource
})
let modifyPoints = function (coordinates) {
  cutDrawFeaturePoint = coordinates
  var pnts = JSON.parse(JSON.stringify(coordinates[0]))
  pnts.push(pnts[0], pnts[1])
  var normals = []
  for (var i = 0; i < pnts.length - 2; i++) {
    var normalPoints = PlotUtils.getBisectorNormals(
      0.3,
      pnts[i],
      pnts[i + 1],
      pnts[i + 2]
    )
    normals = normals.concat(normalPoints)
  }
  var length = normals.length
  normals = [normals[length - 1]].concat(normals.slice(0, length - 1))

  var pList = []
  for (i = 0; i < pnts.length - 2; i++) {
    var pnt1 = pnts[i]
    var pnt2 = pnts[i + 1]
    pList.push(pnt1)
    for (var t = 0; t <= 100; t++) {
      var pnt = PlotUtils.getCubicValue(
        t / 100,
        pnt1,
        normals[i * 2],
        normals[i * 2 + 1],
        pnt2
      )
      pList.push(pnt)
    }
    pList.push(pnt2)
  }
  return pList
}
modify.on('modifyend', function (evt) {
  let points = []
  editPointSource.forEachFeature(feature => {
    points.push(feature.getGeometry().getCoordinates())
  })
  const list = modifyPoints([points])
  selectFeatures.forEach(feature => {
    feature.setGeometry(new Polygon([list]))
    feature.setProperties({ points: [points] })
  })
  setTimeout(() => {
    getMap(map => {
      changeStore(map)
    })
  }, 0)
})
function saveModify() {
  getMap(map => {
    map.removeInteraction(modify)
    select.setActive(true)
    editPointSource.refresh()
    activeBtn.value = ''
    let result = colors.value.find(item => {
      return (
        item.min !== undefined &&
        item.min <= text.value &&
        item.max !== undefined &&
        text.value < item.max
      )
    })
    selectFeatures.forEach(feature => {
      source.forEachFeature(aa => {
        if (aa.ol_uid === feature.ol_uid) {
          aa.setStyle(
            new Style({
              stroke: new Stroke({
                color: borderColor.value ? borderColor.value : result?.value,
                width: borderWidth.value
              }),
              fill: new Fill({
                color: hexToRgb(fillColor.value, opacity.value)
              }),
              text: new Text({
                font: '16px Calibri,sans-serif',
                text: text.value,
                fill: new Fill({
                  color: '#000'
                }),
                stroke: new Stroke({
                  color: '#fff',
                  width: 3
                }),
                placement: 'line'
              })
            })
          )
          aa.set('value', text.value)
          aa.set('fillColor', fillColor.value)
          aa.set('strokeColor', borderColor.value)
          aa.set('borderWidth', borderWidth.value)
          aa.set('opacity', opacity.value)
          aa.set('category', '2')
          aa.set('transparency', opacity.value / 100)
          aa.set('color', fillColor.value)
        }
      })
    })
    selectFeatures = []
    select.setActive(true)
    // map.removeInteraction(select)
  })
}
function startModify() {
  editPointSource.refresh()
  getMap(map => {
    // 监听modifyend事件，获取要素修改后的坐标

    // map.addInteraction(select)
    map.removeInteraction(modify)
    const pointStyle = new Style({
      image: new Circle({
        radius: 7,
        fill: new Fill({
          color: 'black'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        })
      })
    })
    if (selectFeatures.length > 0) {
      selectFeatures.forEach(feature => {
        if (feature.values_.points && feature.values_.points.length > 0) {
          feature.values_.points.forEach(points => {
            points.forEach(point => {
              const iconFeature = new Feature({
                geometry: new Point(point)
              })

              iconFeature.setStyle(pointStyle)
              editPointSource.addFeature(iconFeature)
            })
          })
          map.addInteraction(modify)
          select.setActive(false)
        } else {
          message.error('请先选择要素')
          activeBtn.value = ''
        }
      })
    } else {
      message.error('请先选择要素')
      activeBtn.value = ''
    }
  })
}
function startMove() {
  const pointStyle = new Style({
    image: new Circle({
      radius: 7,
      fill: new Fill({
        color: 'black'
      }),
      stroke: new Stroke({
        color: 'white',
        width: 2
      })
    })
  })
  if (selectFeatures.length > 0) {
    selectFeatures.forEach(feature => {
      if (feature.values_.points && feature.values_.points.length > 0) {
        translate.setActive(true)
      } else {
        message.error('请先选择要素')
        activeBtn.value = ''
      }
    })
  } else {
    message.error('请先选择要素')
    activeBtn.value = ''
  }
}

onMounted(() => {
  if (alarmType.value == 1) {
    colors.value = seaWaveColors
    borderColor.value = seaWaveColors[0].value
    fillColor.value = seaWaveColors[0].value
  } else {
    colors.value = stormSurgeColors
    borderColor.value = stormSurgeColors[0].value
    fillColor.value = stormSurgeColors[0].value
  }
  selectFeatures = null
  getMap(map => {
    map.addLayer(vector)
    map.addLayer(editPointLayer)
    map.addInteraction(translate)
    translate.setActive(false)
    // map.addInteraction(modify)
  })
})
onUnmounted(() => {
  getMap(map => {
    map.removeLayer(vector)
    map.removeInteraction(select)
    map.removeInteraction(draw)
    map.removeInteraction(modify)
    map.removeLayer(editPointLayer)
    map.removeInteraction(translate)
  })
})
defineExpose({
  vector
})
</script>

<style lang="scss" scoped>
.btns {
  display: flex;
  justify-content: space-between;
  .edit-btn {
    width: 60px;
    height: 32px;
    background: #1c81f8;
    border-radius: 4px 4px 4px 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-right: 0; */
    padding: 0px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 16px;
    &:nth-last-child(1) {
      margin-right: 0;
    }
    &.active {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
  }
}

.tools-container {
  // width: 346px;
  height: 202px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 12px 0px;
  padding: 0px 12px;
  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 0px;
    .query-title {
      white-space: nowrap;
      width: 70px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
    .query-info {
      width: 346px;
    }
    .small-input {
      width: 63px;
      height: 32px;
      margin-left: 8px;
    }
    .my-slider {
      width: 178px;
    }
    .color-picker {
      display: flex;
      .color-item {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        &.active-color {
          border: 3px solid #333;
          padding: 1px;
        }
      }
      .have-border {
        border-radius: 2px 2px 2px 2px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }
      .transparent-div {
        position: relative;
        &::after {
          content: '';
          position: absolute;
          top: 7px;
          left: 0;
          right: 0;
          height: 2px; /* 斜线的高度 */
          background-color: rgba(0, 0, 0, 0.1); /* 斜线的颜色 */
          transform: rotate(-45deg); /* 斜线的角度 */
        }
      }
    }
  }
}
.my-btn {
  width: 140px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}
.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
