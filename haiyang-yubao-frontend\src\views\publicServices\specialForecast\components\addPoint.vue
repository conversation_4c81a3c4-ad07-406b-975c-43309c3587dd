<template>
  <qx-dialog
    title="点坐标"
    :visible="visible"
    width="400px"
    class="point-lonlat-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <n-form
        ref="formRef"
        :model="form"
        label-placement="left"
        label-width="auto"
        class="qx-form"
        :show-feedback="false"
      >
        <n-form-item label="经度" path="areaName">
          <n-grid>
            <n-form-item-gi :span="7">
              <n-input
                v-model:value="form.lonDegree"
                placeholder="请输入"
                clearable
              />
              °
            </n-form-item-gi>
            <n-form-item-gi :span="7" :offset="1">
              <n-input
                v-model:value="form.lonMin"
                placeholder="请输入"
                clearable
              />
              ′
            </n-form-item-gi>
            <n-form-item-gi :span="7" :offset="1">
              <n-input
                v-model:value="form.lonSec"
                placeholder="请输入"
                clearable
              />
              ″
            </n-form-item-gi>
          </n-grid>
        </n-form-item>
        <n-form-item label="纬度" path="areaName">
          <n-grid>
            <n-form-item-gi :span="7">
              <n-input
                v-model:value="form.latDegree"
                placeholder="请输入"
                clearable
              />
              °
            </n-form-item-gi>
            <n-form-item-gi :span="7" :offset="1">
              <n-input
                v-model:value="form.latMin"
                placeholder="请输入"
                clearable
              />
              ′
            </n-form-item-gi>
            <n-form-item-gi :span="7" :offset="1">
              <n-input
                v-model:value="form.latSec"
                placeholder="请输入"
                clearable
              />
              ″
            </n-form-item-gi>
          </n-grid>
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btn-group">
        <qx-button class="cancel" @click="onClose">取消</qx-button>
        <qx-button class="primary" @click="onConfirm">确定</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>
<script setup lang="ts">
import { QxDialog } from 'src/components/QxDialog'
import { QxButton } from 'src/components/QxButton'
import { reactive, watchEffect, PropType } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  point: {
    type: Array as PropType<number[]>,
    default: () => []
  }
})
const initForm = {
  lonDegree: '',
  lonMin: '',
  lonSec: '',
  latDegree: '',
  latMin: '',
  latSec: ''
}
type Form = {
  [key: string]: string
}
let form: Form = reactive({ ...initForm })

const emit = defineEmits(['update:visible', 'save'])
function onClose() {
  emit('update:visible', false)
}
function onConfirm() {
  let lon = convertToDecimal(
    Number(form.lonDegree),
    Number(form.lonMin),
    Number(form.lonSec)
  )
  let lat = convertToDecimal(
    Number(form.latDegree),
    Number(form.latMin),
    Number(form.latSec)
  )
  emit('save', { lon, lat })
  emit('update:visible', false)
}

function convertToDecimal(degrees = 0, minutes = 0, seconds = 0) {
  // 将度、分、秒转换为十进制
  const decimal = degrees + minutes / 60 + seconds / 3600
  return decimal
}

interface DegreeResult {
  [key: string]: string
}

/**
 * 将十进制度数转换为度分秒格式
 * @param val - 十进制度数的字符串表示
 * @param key - 结果对象的前缀键名
 * @returns 包含度、分、秒的对象
 */
function ToDegrees(val: string, key: string): DegreeResult {
  if (typeof val === 'undefined' || val === '') {
    return {
      [`${key}Degree`]: '',
      [`${key}Min`]: '',
      [`${key}Sec`]: ''
    }
  }

  const i = val.indexOf('.')
  const strDu = i < 0 ? val : val.substring(0, i) // 获取度

  let strFen = '0'
  let strMiao = '0'

  if (i > 0) {
    const decimalPart = '0' + val.substring(i)
    const minutesValue = parseFloat(decimalPart) * 60
    strFen = minutesValue.toString()

    const j = strFen.indexOf('.')
    if (j > 0) {
      const secondsDecimal = '0' + strFen.substring(j)
      const secondsValue = parseFloat(secondsDecimal) * 60
      strMiao = secondsValue.toString()

      const k = strMiao.indexOf('.')
      const truncatedSeconds = strMiao.substring(0, k + 4) // 取到小数点后三位
      strMiao = parseFloat(truncatedSeconds).toFixed(2) // 精确到小数点后两位
      strFen = strFen.substring(0, j) // 获取分
    }
  }

  return {
    [`${key}Degree`]: strDu,
    [`${key}Min`]: strFen,
    [`${key}Sec`]: strMiao
  }
}

watchEffect(() => {
  if (props.point && props.point.length) {
    let result = {
      ...ToDegrees(props.point[0].toString(), 'lon'),
      ...ToDegrees(props.point[1].toString(), 'lat')
    }
    Object.keys(result).forEach((key: string) => {
      form[key] = result[key]
    })
  } else {
    form = reactive({ ...initForm })
  }
})
</script>
<style lang="scss" scoped>
.point-lonlat-dialog {
  dialog-content {
    box-sizing: border-box;
    padding: 20px;
  }
  .qx-form {
    box-sizing: border-box;
    padding: 20px 20px 0;
  }
  .btn-group {
    text-align: right;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 20px;
  }
  :deep(.n-form-item.n-form-item--left-labelled) {
    margin-bottom: 10px;
  }
}
</style>
