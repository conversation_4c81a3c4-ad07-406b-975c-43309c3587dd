package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationHourRnO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-降水-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationHourRnOService extends IService<OceanStationHourRnO> {

    List<OceanStationHourRnO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




