package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmSchedulingTableDTO;
import cn.piesat.data.making.server.dto.SchedulingAddDTO;
import cn.piesat.data.making.server.entity.FmSchedulingTable;
import cn.piesat.data.making.server.vo.FmSchedulingTableVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:05
 */
public interface FmSchedulingTableService extends IService<FmSchedulingTable>{

        /**
         * 根据参数查询分页
         */
        PageResult<FmSchedulingTableVO> getPage(FmSchedulingTableDTO dto,PageParam pageParam);

        /**
         * 根据参数查询列表
         */
        List<FmSchedulingTable> getList(FmSchedulingTableDTO dto);

        List<FmSchedulingTable> update(List<FmSchedulingTable> list);

        int sign(FmSchedulingTable schedulingTable);

        /**
         * 根据id查询数据
         */
        FmSchedulingTableVO getById();

        /**
         * 保存数据
         */
        List<FmSchedulingTable> save(SchedulingAddDTO dto);

        /**
         * 批量保存数据
         */
        void saveList(List<FmSchedulingTableDTO> dtoList);

        /**
         * 根据id删除数据
         */
        void deleteById(Long id);

        /**
         * 根据idList批量删除数据
         */
        void deleteByIdList(List<Long> idList);

        void saveAll(List<FmSchedulingTable> dto);

        List<FmSchedulingTable> upload(MultipartFile multipartFile, Date date);

        void download(Date date);
        }
