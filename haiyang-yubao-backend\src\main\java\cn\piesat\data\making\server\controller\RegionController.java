package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.RegionDTO;
import cn.piesat.data.making.server.service.RegionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 行政区表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/region")
public class RegionController {

    @Resource
    private RegionService regionService;

    /**
     * 保存行政区表
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "行政区管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {RegionDTO.Save.class}) @RequestBody RegionDTO dto) {
        regionService.save(dto);
    }
}

