package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationHourWlO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-海浪特征值-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationHourWlOService extends IService<OceanStationHourWlO> {

    List<OceanStationHourWlO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




