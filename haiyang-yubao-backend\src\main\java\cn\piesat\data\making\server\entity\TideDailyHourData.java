package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("fm_tide_daily_hour")
public class TideDailyHourData {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String stationName;

    private Long stationId;
    /**
     * 潮时
     */
    private Date tideTime;
    /**
     * 潮高
     */
    private int height;
    private Date createTime;
    private Date updateTime;
    /**
     * 基准
     */
    private String datum;
}
