<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.piesat.data.making.server.dao.TideDailyDataDao">


<select id="selectGeoJsonByIds" resultType="java.lang.String">
SELECT
    row_to_json(fc) AS geojson
FROM (
         SELECT
             'FeatureCollection' AS type,
             array_to_json(array_agg(f)) AS features
         FROM (
                  SELECT
                      'Feature' AS type,
                      ST_AsGeoJSON(location_geo)::json AS geometry,
                      row_to_json((SELECT l FROM (SELECT id, name) AS l)) AS properties
                  FROM
                      fm_area_b
                  WHERE
                         id in (<foreach collection="areaIds" item="item" separator=",">#{item}</foreach>)
              ) AS f
     ) AS fc;
</select>


<select id="selectWarnLevel" resultType="cn.piesat.data.making.server.vo.TideDailyWarnLevelVO">
    select t.*,t2.name as regionName from fm_tide_daily t left join fm_station_b t1
    on t.station_id = t1.id
    left join fm_region_b t2 on t1.region_code = t2.code
    <where>
        station_id in (<foreach collection="stationIds" item="item" separator=",">#{item}</foreach>)
        <if test="startTime != null and endTime != null">
           and t.tide_time between #{startTime,jdbcType=TIMESTAMP} and  #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="datum != null and datum != ''">
            and t.datum = #{datum,jdbcType=VARCHAR}
        </if>
    </where>
    order by t.station_id,t.tide_time
</select>

</mapper>

