/*
 * @Author: panrong
 * @Date: 2022-02-16 15:29:34
 * @LastEditTime: 2025-05-12 13:49:50
 * @LastEditors: x<PERSON><PERSON><PERSON> xuli<PERSON><EMAIL>
 * @Description: 路由
 */
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    meta: {
      requiresAuth: true
    },
    redirect: '/analysis'
  },
  {
    path: '/404',
    name: '404',
    meta: {
      requiresAuth: true
    },
    component: () => import('src/views/404.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('src/views/login/LoginPage.vue')
  },
  {
    path: '/forecast',
    name: 'forecast',
    meta: {
      title: '预报警报制作分发系统'
    },
    redirect: '/forecastProduct',
    component: () => import('src/views/forecast/index.vue'),
    children: [
      {
        path: '/analysis',
        name: 'analysis',
        meta: {
          title: '预报分析',
          showMenu: true,
          activePath: '/analysis'
        },
        component: () => import('src/views/analysis/index.vue')
      },
      {
        path: '/forecastProduct',
        name: 'forecastProduct',
        meta: {
          title: '预报制作',
          showMenu: true,
          activePath: '/forecastProduct',
          dropmenu: true
        },
        component: () => import('src/views/common.vue'),
        children: [
          {
            path: '/forecastProduct/drawing',
            name: 'drawing',
            meta: {
              title: '绘图',
              icon: 'icon icon-forecast',
              activePath: '/forecastProduct'
            },
            component: () => import('src/views/forecast/drawing/index.vue')
          },
          {
            path: '/forecastProduct/formProduction',
            name: 'formProduction',
            meta: {
              title: '表单制作',
              icon: 'icon icon-sea-temp',
              activePath: '/forecastProduct'
            },
            component: () =>
              import('src/views/forecast/formProduction/forecastProduct.vue')
          }
        ]
      },
      {
        path: '/alarm',
        name: 'alarm',
        meta: {
          title: '警报制作',
          showMenu: true,
          dropmenu: true
        },
        component: () => import('src/views/common.vue'),
        children: [
          {
            path: '/alarm/alarmMaking',
            name: 'alarmMaking',
            meta: {
              title: '警报制作',
              showMenu: true,
              activePath: '/alarm'
            },
            component: () => import('src/views/alarm/alarmMaking.vue')
          },
          {
            path: '/alarm/messageMaking',
            name: 'messageMaking',
            meta: {
              title: '消息制作',
              showMenu: true,
              activePath: '/alarm'
            },
            component: () => import('src/views/alarm/messageMaking.vue')
          }
        ]
      },
      {
        path: '/releaseInfo',
        name: 'releaseInfo',
        meta: {
          title: '产品发布',
          showMenu: true,
          activePath: '/releaseInfo'
        },
        component: () => import('src/views/releaseInfo/index.vue')
      },
      {
        path: '/publicServices',
        name: 'publicServices',
        meta: {
          title: '公共服务',
          showMenu: true,
          dropmenu: true
        },
        component: () => import('src/views/common.vue'),
        children: [
          {
            path: '/specialForecast',
            name: 'specialForecast',
            meta: {
              title: '专项预报',
              showMenu: true,
              activePath: '/publicServices'
            },
            component: () =>
              import('src/views/publicServices/specialForecast/index.vue')
          },
          {
            path: '/tsunamiProducts',
            name: 'tsunamiProducts',
            meta: {
              title: '海啸警报产品',
              showMenu: true,
              activePath: '/publicServices'
            },
            component: () =>
              import('src/views/publicServices/tsunamiProducts/index.vue')
          },
          // {
          //   path: '/consultation',
          //   name: 'consultation',
          //   meta: {
          //     title: '会商PPT',
          //     showMenu: true
          //   },
          //   component: () => import('src/views/publicServices/building.vue')
          // },
          {
            path: '/analystReports',
            name: 'analystReports',
            meta: {
              title: '分析报告',
              showMenu: true
            },
            component: () => import('src/views/layout.vue'),
            redirect: '/analystReports/liveFlash',
            children: [
              {
                path: '/analystReports/liveFlash',
                name: 'liveFlash',
                meta: {
                  title: '信息快报',
                  showMenu: true,
                  icon: 'icon-shikuangsubao',
                  activePath: '/publicServices'
                },
                component: () =>
                  import(
                    'src/views/publicServices/analystReports/liveFlash.vue'
                  )
              },
              {
                path: '/analystReports/waveBriefing',
                name: 'waveBriefing',
                meta: {
                  title: '海浪简报',
                  showMenu: true,
                  icon: 'icon-hailangjianbao',
                  activePath: '/publicServices'
                },
                component: () =>
                  import(
                    'src/views/publicServices/analystReports/waveBriefing.vue'
                  )
              },
              {
                path: '/analystReports/astronomicalTideNews',
                name: 'astronomicalTideNews',
                meta: {
                  title: '天文大潮消息',
                  icon: 'icon-tianwenfadacaomessage',
                  showMenu: true,
                  activePath: '/publicServices'
                },
                component: () =>
                  import(
                    'src/views/publicServices/analystReports/astronomicalTideNews.vue'
                  )
              },
              {
                path: '/analystReports/collaborativeDuty',
                name: 'collaborativeDuty',
                meta: {
                  title: '协同值班信息',
                  showMenu: true,
                  icon: 'icon-xietongzhibanxinxi',
                  activePath: '/publicServices'
                },
                component: () =>
                  import(
                    'src/views/publicServices/analystReports/collaborativeDuty.vue'
                  )
              }
            ]
          },
          {
            path: '/library',
            name: 'library',
            meta: {
              title: '素材库',
              showMenu: true
            },
            component: () => import('src/views/layout.vue'),
            redirect: '/library/fileProduct',
            children: [
              {
                path: '/library/fileProduct',
                name: 'fileProduct',
                meta: {
                  title: '文档产品',
                  showMenu: true,
                  activePath: '/publicServices',
                  icon: 'icon icon-document'
                },
                component: () =>
                  import('src/views/publicServices/library/fileProduct.vue')
              },
              {
                path: '/library/imgProduct',
                name: 'imgProduct',
                meta: {
                  title: '图片产品',
                  showMenu: true,
                  icon: 'icon icon-img',
                  activePath: '/publicServices'
                },
                component: () =>
                  import('src/views/publicServices/library/imgProduct.vue')
              },
              {
                path: '/library/videoProduct1',
                name: 'videoProduct1',
                meta: {
                  title: '视频产品',
                  showMenu: true,
                  icon: 'icon icon-video',
                  activePath: '/publicServices'
                },
                component: () =>
                  import('src/views/publicServices/library/video.vue')
              }
            ]
          }
        ]
      },
      {
        path: '/forecastTest',
        name: 'forecastTest',
        meta: {
          title: '预报检验',
          showMenu: true,
          activePath: '/forecastTest'
        },
        component: () => import('src/views/forecastTest/index.vue')
      },
      {
        path: '/systemManage',
        name: 'systemManage',
        meta: {
          title: '系统管理',
          dropmenu: true
        },
        redirect: '/configManage',
        component: () => import('src/views/common.vue'),
        children: [
          {
            path: '/configManage',
            name: 'configManage',
            meta: {
              title: '配置管理',
              showMenu: true
            },
            redirect: '/configManage/tempManage/forecastTemp',
            component: () => import('src/views/layout.vue'),
            children: [
              {
                path: '/configManage/tempManage',
                name: 'tempManage',
                meta: {
                  title: '模板配置',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                redirect: '/configManage/tempManage/forecastTemp',
                component: () => import('src/views/common.vue'),
                children: [
                  {
                    path: '/configManage/tempManage/forecastTemp',
                    name: 'forecastTemp',
                    meta: {
                      title: '预报单模板',
                      icon: 'icon icon-forecast-temp',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () =>
                      import(
                        'src/views/system/configMange/forecastTemp/index.vue'
                      )
                  },
                  {
                    path: '/configManage/tempManage/productTemp',
                    name: 'productTemp',
                    meta: {
                      title: '产品模板',
                      icon: 'icon icon-product-temp',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () =>
                      import(
                        'src/views/system/configMange/productTemp/index.vue'
                      )
                  },
                  {
                    path: '/configManage/tempManage/imageTemp',
                    name: 'imageTemp',
                    meta: {
                      title: '图形模板',
                      icon: 'icon icon-image-temp',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () =>
                      import(
                        'src/views/system/configMange/graphicTemp/imageTemp.vue'
                      )
                  }
                ]
              },
              {
                path: '/configManage/channelManage',
                name: 'channelManage',
                meta: {
                  title: '推送渠道管理',
                  showMenu: true
                },
                redirect: '/configManage/channelManage/ftp',
                component: () => import('src/views/common.vue'),
                children: [
                  {
                    path: '/configManage/channelManage/ftp',
                    name: 'channelManageFtp',
                    meta: {
                      title: 'FTP连接池',
                      icon: 'icon icon-publish-protocol',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () => import('src/views/dataPush/index.vue')
                  },
                  {
                    path: '/configManage/channelManage/ftp/add',
                    name: 'ftpAdd',
                    component: () => import('src/views/dataPush/index.vue'),
                    meta: {
                      flag: 2,
                      title: '新增',
                      icon: '',
                      showMenu: false,
                      activeMenu: 'ftp',
                      activePath: '/systemManage'
                    }
                  },
                  {
                    path: '/configManage/channelManage/ftp/edit',
                    name: 'ftpEdit',
                    component: () => import('src/views/dataPush/index.vue'),
                    props: (route: any) => ({ id: route.query.id }),
                    meta: {
                      flag: 2,
                      title: '新增',
                      icon: '',
                      showMenu: false,
                      activeMenu: 'ftp',
                      activePath: '/systemManage'
                    }
                  },
                  {
                    path: '/configManage/channelManage/email',
                    name: 'email',
                    meta: {
                      title: 'Email邮箱',
                      icon: 'icon icon-channel',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () => import('src/views/dataPush/index.vue'),
                    children: [
                      {
                        path: '/configManage/channelManage/email/emailManage',
                        name: 'emailManage',
                        meta: {
                          title: '邮箱管理',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () => import('src/views/dataPush/index.vue')
                      },
                      {
                        path: '/configManage/channelManage/email/sendingGroup',
                        name: 'sendingGroup',
                        meta: {
                          title: '发送组管理',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () => import('src/views/dataPush/index.vue')
                      },
                      {
                        path: '/configManage/channelManage/email/relation',
                        name: 'relation',
                        component: () => import('src/views/dataPush/index.vue'),
                        props: (route: any) => ({ id: route.query.id }),
                        meta: {
                          title: '关联管理',
                          flag: 2,
                          icon: '',
                          showMenu: false,
                          activeMenu: '/channelManage/email/sendingGroup',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/configManage/channelManage/email/unitGroup',
                        name: 'unitGroup',
                        meta: {
                          title: '单位管理',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () => import('src/views/dataPush/index.vue')
                      }
                    ]
                  }
                ]
              },
              {
                path: '/configManage/pushManage/realTime',
                name: 'pushManage',
                meta: {
                  title: '实时数据推送',
                  showMenu: true
                },
                redirect: '/configManage/pushManage/realTime/pushTask',
                component: () => import('src/views/common.vue'),
                children: [
                  {
                    path: '/configManage/pushManage/realTime/pushTask',
                    name: 'pushTask',
                    meta: {
                      title: '推送单位管理',
                      icon: 'icon icon-data-maintenance',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () => import('src/views/dataPush/index.vue')
                  },
                  {
                    path: '/configManage/pushManage/realTime/add',
                    name: 'PushManageRealTimeAdd',
                    component: () => import('src/views/dataPush/index.vue'),
                    props: (route: any) => ({
                      taskType: route.query.taskType,
                      type: route.query.type
                    }),
                    meta: {
                      title: '添加任务',
                      flag: 2,
                      icon: '',
                      showMenu: false,
                      activeMenu: '/configManage/pushManage/realTime/pushTask',
                      activePath: '/systemManage'
                    }
                  },
                  {
                    path: '/configManage/pushManage/realTime/edit',
                    name: 'PushManageRealTimeEdit',
                    component: () => import('src/views/dataPush/index.vue'),
                    props: (route: any) => ({
                      id: route.query.id,
                      taskType: route.query.taskType,
                      type: route.query.type
                    }),
                    meta: {
                      title: '编辑任务',
                      flag: 2,
                      icon: '',
                      showMenu: false,
                      activeMenu: '/configManage/pushManage/realTime/pushTask',
                      activePath: '/systemManage'
                    }
                  },
                  {
                    path: '/configManage/pushManage/realTime/dataChannel',
                    name: 'realTimeDataChannel',
                    component: () => import('src/views/dataPush/index.vue'),
                    meta: {
                      title: '实时数据通道',
                      flag: 1,
                      icon: '',
                      showMenu: true,
                      activeMenu: '/pushManage/realTime/dataChannel',
                      activePath: '/systemManage'
                    }
                  },
                  {
                    path: '/configManage/pushManage/realTime/dataChannel/add',
                    name: 'realTimeDataChannelAdd',
                    component: () => import('src/views/dataPush/index.vue'),
                    props: (route: any) => ({ id: route.query.id }),
                    meta: {
                      title: '添加通道',
                      flag: 2,
                      icon: '',
                      showMenu: false,
                      activeMenu:
                        '/configManage/pushManage/realTime/dataChannel',
                      activePath: '/systemManage'
                    }
                  },
                  {
                    path: '/configManage/pushManage/realTime/dataChannel/edit',
                    name: 'realTimeDataChannelEdit',
                    component: () => import('src/views/dataPush/index.vue'),
                    props: (route: any) => ({ id: route.query.id }),
                    meta: {
                      title: '编辑通道',
                      flag: 2,
                      icon: '',
                      showMenu: false,
                      activeMenu:
                        '/configManage/pushManage/realTime/dataChannel',
                      activePath: '/systemManage'
                    }
                  }
                ]
              },
              {
                path: '/configManage/otherConfig',
                name: 'otherConfig',
                meta: {
                  title: '其他配置',
                  showMenu: true
                },
                redirect: '/configManage/otherConfig/dataMaintenance',
                component: () => import('src/views/common.vue'),
                children: [
                  {
                    path: '/configManage/otherConfig/dataMaintenance',
                    name: 'dataMaintenance',
                    meta: {
                      title: '数据维护',
                      icon: 'icon icon-data-maintenance',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () =>
                      import(
                        'src/views/system/otherConfig/dataMaintenance/index.vue'
                      )
                  },
                  {
                    path: '/configManage/otherConfig/defenseGuide',
                    name: 'defenseGuide',
                    meta: {
                      title: '防御指南',
                      icon: 'icon icon-defense-guide',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () =>
                      import(
                        'src/views/system/otherConfig/defenseGuide/index.vue'
                      )
                  },
                  {
                    path: '/configManage/otherConfig/labelMaintenance',
                    name: 'labelMaintenance',
                    meta: {
                      title: '标签维护',
                      icon: 'icon icon-defense-guide',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () =>
                      import(
                        'src/views/system/otherConfig/labelMaintenance/index.vue'
                      )
                  }
                ]
              }
            ]
          },
          {
            path: '/systemSetting',
            name: 'systemSetting',
            meta: {
              title: '系统设置'
            },
            redirect: '/systemSetting/userPermission/user',
            component: () => import('src/views/layout.vue'),
            children: [
              {
                path: '/systemSetting/userPermission/user',
                name: 'user',
                meta: {
                  title: '人员管理',
                  icon: 'icon icon-user-manage',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () =>
                  import('src/views/system/setting/user/index.vue')
              },
              {
                path: '/systemSetting/userPermission/role',
                name: 'role',
                meta: {
                  title: '角色管理',
                  icon: 'icon icon-role',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () =>
                  import('src/views/system/setting/user/index.vue')
              },
              {
                path: '/systemSetting/operatLogs',
                name: 'operatLogs',
                meta: {
                  title: '日志管理',
                  icon: 'icon icon-log',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () =>
                  import('src/views/system/setting/dictionary/index.vue')
              },
              {
                path: '/systemSetting/scheduling',
                name: 'scheduling',
                meta: {
                  title: '排班管理',
                  icon: 'icon icon-calender',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () =>
                  import('src/views/system/setting/scheduling/index.vue')
              },
              {
                path: '/systemSetting/accessStatistics',
                name: 'accessStatistics',
                meta: {
                  title: '用户访问统计',
                  icon: 'icon icon-statistics',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () =>
                  import('src/views/system/accessStatistics/index.vue')
              }
            ]
          },
          {
            path: '/pooledProcessing',
            name: 'pooledProcessing',
            meta: {
              title: '数据管理'
            },
            redirect: '/pooledProcessing/collection/connectivity/ftp',
            component: () => import('src/views/layout.vue'),
            children: [
              {
                path: '/pooledProcessing/collection',
                name: 'collection',
                meta: {
                  title: '数据采集',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () => import('src/views/common.vue'),
                redirect: '/pooledProcessing/collection/connectivity/ftp',
                children: [
                  {
                    path: '/pooledProcessing/collection/connectivity',
                    name: 'connectivity',
                    meta: {
                      title: '连接配置',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () => import('src/views/collection/index.vue'),
                    children: [
                      {
                        path: '/pooledProcessing/collection/connectivity/ftp',
                        name: 'ftp',
                        meta: {
                          title: 'FTP连接池',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () =>
                          import('src/views/collection/index.vue')
                      }
                    ]
                  },
                  {
                    path: '/pooledProcessing/collection/dispose',
                    name: 'dispose',
                    meta: {
                      title: '汇集管理',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    component: () => import('src/views/common.vue'),
                    redirect: '/pooledProcessing/collection/dispose/history',
                    children: [
                      {
                        path: '/pooledProcessing/collection/dispose/history',
                        name: 'history',
                        meta: {
                          title: '历史数据汇集',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () =>
                          import('src/views/collection/index.vue')
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/historyDataInfo',
                        name: 'historyDataInfo',
                        component: () =>
                          import('src/views/collection/index.vue'),
                        props: (route: any) => ({
                          id: route.query.id,
                          type: route.query.type,
                          taskType: route.query.taskType
                        }),
                        meta: {
                          title: '编辑',
                          activeMenu: 'history',
                          showMenu: false,
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/realTime',
                        name: 'realTime',
                        meta: {
                          title: '实时数据汇集',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () =>
                          import('src/views/collection/index.vue')
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/realTimeDataInfo',
                        name: 'realTimeDataInfo',
                        component: () =>
                          import('src/views/collection/index.vue'),
                        props: (route: any) => ({
                          id: route.query.id,
                          type: route.query.type,
                          taskType: route.query.taskType
                        }),
                        meta: {
                          flag: 2,
                          title: '编辑',
                          icon: '',
                          showMenu: false,
                          activeMenu: 'realTime',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/poolingPlan',
                        name: 'poolingPlan',
                        meta: {
                          title: '汇集计划管理',
                          showMenu: true,
                          activePath: '/systemManage'
                        },
                        component: () =>
                          import('src/views/collection/index.vue')
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/dataSourceClass',
                        name: 'dataSourceClass',
                        component: () =>
                          import('src/views/collection/index.vue'),
                        meta: {
                          title: '数据源分类',
                          showMenu: true,
                          activeMenu:
                            '/pooledProcessing/collection/dispose/dataSourceClass'
                        }
                      },
                      {
                        path: '/pooledProcessing/collection/routeDispose/passageway',
                        name: 'passageway',
                        component: () =>
                          import('src/views/collection/index.vue'),
                        meta: {
                          title: '通道管理',
                          showMenu: true,
                          activeMenu:
                            '/pooledProcessing/collection/routeDispose/passageway'
                        }
                      },
                      {
                        path: '/pooledProcessing/collection/routeDispose/edit',
                        name: 'edit',
                        component: () =>
                          import('src/views/collection/index.vue'),
                        props: route => ({
                          id: route.query.id,
                          channelCode: route.query.channelCode
                        }),
                        meta: {
                          title: '编辑',
                          showMenu: false,
                          activeMenu:
                            '/pooledProcessing/collection/routeDispose/passageway'
                        }
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/monitorLogs',
                        name: 'monitorLogs',
                        component: () =>
                          import('src/views/collection/index.vue'),
                        meta: {
                          title: '汇集记录查询',
                          showMenu: true,
                          activeMenu: '/collection/dispose/monitorLogs'
                        }
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/httpDownload',
                        name: 'httpDownload',
                        props: (route: any) => ({ id: route.query.id }),
                        meta: {
                          title: 'HTTP下载',
                          icon: '',
                          showMenu: false,
                          activePath: '/systemManage'
                        },
                        component: () =>
                          import('src/views/collection/index.vue')
                      },
                      {
                        path: '/pooledProcessing/collection/dispose/messageQueuing',
                        name: 'messageQueuing',
                        props: (route: any) => ({ id: route.query.id }),
                        meta: {
                          title: '消息队列',
                          icon: '',
                          showMenu: false,
                          activeMenu: '/dispose/realTime',
                          activePath: '/systemManage'
                        },
                        component: () =>
                          import('src/views/collection/index.vue')
                      }
                    ]
                  }
                ]
              },
              {
                path: '/pooledProcessing/taskProcessing',
                name: 'taskProcessing',
                meta: {
                  title: '任务处理',
                  showMenu: true,
                  activePath: '/systemManage'
                },
                component: () => import('src/views/common.vue'),
                redirect: '/pooledProcessing/taskProcessing/plugin/manage/list',
                children: [
                  {
                    path: '/pooledProcessing/taskProcessing/plugin',
                    name: 'DispatchPlugin',
                    // component: () => import("@/views/plugin/index.vue"),
                    meta: {
                      title: '插件管理',
                      icon: 'icon-icon_sbkzzl',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    children: [
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/manage/list',
                        name: 'DispatchPluginManageList',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: false,
                        meta: {
                          title: '插件配置',
                          flag: 1,
                          icon: '',
                          showMenu: true,
                          activeMenu: 'manage/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/manage/update',
                        name: 'DispatchPluginManageListUpdate',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => ({
                          id: route.query.id,
                          active: route.query.active
                        }),
                        meta: {
                          title: '插件修改',
                          flag: 2,
                          icon: '',
                          showMenu: false,
                          activeMenu: 'manage/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/manage/updateBaseInfo',
                        name: 'DispatchPluginManageUpdateBaseInfo',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => ({ id: route.query.id }),
                        meta: {
                          title: '添加',
                          flag: 2,
                          showMenu: false,
                          activeMenu: 'manage/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/manage/updateAlgorithm',
                        name: 'DispatchPluginManageUpdateAlgorithm',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => {
                          const {
                            query: { id, pluginName }
                          } = route
                          return { id, pluginName }
                        },
                        meta: {
                          title: '算法列表',
                          showMenu: false,
                          flag: 3,
                          activeMenu: 'manage/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/group/list',
                        name: 'DispatchPluginGroupList',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: true,
                        meta: {
                          title: '插件分类',
                          flag: 1,
                          icon: '',
                          showMenu: true,
                          activeMenu: 'group/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/agent/list',
                        name: 'PluginAgentList',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        meta: {
                          title: '代理程序',
                          flag: 1,
                          showMenu: true,
                          activeMenu: 'agent/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/plugin/expression',
                        name: 'expression',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        meta: {
                          title: '函数表达式管理',
                          flag: 1,
                          showMenu: true,
                          activeMenu: 'plugin/expression',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/expression/add',
                        name: 'expressionAdd',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        meta: {
                          title: '添加',
                          flag: 2,
                          showMenu: false,
                          activeMenu: 'plugin/expression',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/plugin/expression/edit',
                        name: 'expressionEdit',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => {
                          const {
                            query: { id }
                          } = route
                          return { id }
                        },
                        meta: {
                          title: '编辑',
                          flag: 2,
                          showMenu: false,
                          activeMenu: 'plugin/expression',
                          activePath: '/systemManage'
                        }
                      }
                    ]
                  },
                  {
                    path: '/pooledProcessing/taskProcessing/task',
                    name: 'DispatchTask',
                    // component: () => import("@/views/task/index.vue"),
                    meta: {
                      title: '任务管理',
                      icon: 'icon-icon_bell',
                      showMenu: true,
                      activePath: '/systemManage'
                    },
                    redirect:
                      '/pooledProcessing/taskProcessing/task/manages/list',
                    children: [
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/list',
                        name: 'DispatchTaskManageList',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: true,
                        meta: {
                          title: '任务配置',
                          flag: 1,
                          icon: '',
                          showMenu: true,
                          activeMenu: '/task/manages/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/updateBaseInfo',
                        name: 'DispatchTaskManageUpdateBaseInfo',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => ({ id: route.query.id }),
                        meta: {
                          title: '编辑任务',
                          flag: 2,
                          showMenu: false,
                          activeMenu: '/task/manages/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/detailRecord',
                        name: 'DispatchTaskManagedetailRecord',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => ({
                          id: route.query.id,
                          jobName: route.query.jobName
                        }),
                        meta: {
                          title: '执行记录',
                          flag: 2,
                          showMenu: false,
                          activeMenu: '/task/manages/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/addBaseInfo',
                        name: 'DispatchTaskManageAddBaseInfo',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        meta: {
                          title: '添加任务',
                          flag: 2,
                          showMenu: false,
                          activeMenu: '/task/manages/list',
                          activePath: '/systemManage'
                        }
                      },

                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/updateFlowChart',
                        name: 'DispatchTaskManageUpdateFlowChart',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => {
                          const {
                            query: { id, jobName }
                          } = route
                          return { id, jobName }
                        },
                        meta: {
                          title: '编辑流程图',
                          flag: 2,
                          showMenu: false,
                          breadcrumb: 'push',
                          activeMenu: '/task/manages/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/groups/list',
                        name: 'DispatchTaskGroupList',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: true,
                        meta: {
                          title: '任务类别',
                          icon: '',
                          flag: 1,
                          showMenu: true,
                          activeMenu: 'groups/list',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/timingTemplate',
                        name: 'DispatchTaskManageTimingTemplate',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        meta: {
                          title: '定时策略模板',
                          flag: 1,
                          icon: '',
                          showMenu: true,
                          activeMenu: 'manages/timingTemplate',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/timingTemplate/add',
                        name: 'DispatchTaskManageTimingTemplateAdd',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        meta: {
                          title: '添加',
                          flag: 2,
                          icon: '',
                          showMenu: false,
                          activeMenu: 'manages/timingTemplate',
                          activePath: '/systemManage'
                        }
                      },
                      {
                        path: '/pooledProcessing/taskProcessing/task/manages/timingTemplate/edit',
                        name: 'DispatchTaskManageTimingTemplateedit',
                        component: () =>
                          import('src/views/taskProcessing/index.vue'),
                        props: route => {
                          const {
                            query: { id }
                          } = route
                          return { id }
                        },
                        meta: {
                          title: '修改',
                          flag: 2,
                          icon: '',
                          showMenu: false,
                          activeMenu: 'manages/timingTemplate',
                          activePath: '/systemManage'
                        }
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]

const base_url = import.meta.env.BASE_URL //获取vite.config.js配置的base,默认是根目录/
const router = createRouter({
  history: createWebHistory(base_url),
  routes
})

router.beforeEach((to, from, next) => {
  // 需要授权才能访问
  // if (to.meta.requiresAuth) {
  //   location.href = config.loginUrl + encodeURIComponent(location.href)
  //   // next('/login?redirect_url=' + to.fullPath)
  // } else {
  //   // 不需要授权直接访问
  //   next()
  // }
  const sqlKeywords = /\b(UNION|SELECT|SLEEP|FROM|WHERE)\b/gi
  const xssPatterns = /<script|javascript:|onerror=|onload=/gi
  if (sqlKeywords.test(to.fullPath) || xssPatterns.test(to.fullPath)) {
    next('/404')
    throw new Error('非法请求参数')
  } else {
    next()
  }
})

export default router
