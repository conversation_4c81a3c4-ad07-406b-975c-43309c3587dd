package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.SeaWaveMessageDTO;
import cn.piesat.data.making.server.entity.SeaWaveMessage;
import cn.piesat.data.making.server.processor.WaveGenerateText;
import cn.piesat.data.making.server.service.SeaWaveMessageService;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 海浪消息
 *
 * <AUTHOR>
 * @since 2024-10-11 16:59:01
 */
@RestController
@RequestMapping("/seaWaveMessage")
public class SeaWaveMessageController {
    /**
     * 服务对象
     */
    @Autowired
    private SeaWaveMessageService seaWaveMessageService;

    /**
     * 分页查询所有数据
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @return 所有数据
     */
    @GetMapping("/pageList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.SELECT)
    public PageResult<SeaWaveMessage> pageList(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                               @RequestParam(value = "startTime", required = false) Date startTime,
                                               @RequestParam(value = "endTime", required = false) Date endTime) {
        return this.seaWaveMessageService.pageList(pageNum, pageSize, startTime, endTime);
    }

    /**
     * 查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.SELECT)
    public List<SeaWaveMessage> list() {
        return this.seaWaveMessageService.list(new LambdaQueryWrapper<SeaWaveMessage>().orderByAsc(SeaWaveMessage::getCreateTime));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.SELECT)
    public SeaWaveMessage selectOne(@PathVariable Long id) {
        return this.seaWaveMessageService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param seaWaveMessageDTO 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.INSERT)
    public SeaWaveMessage insert(@RequestBody @Validated SeaWaveMessageDTO seaWaveMessageDTO) {
        return this.seaWaveMessageService.saveInfo(seaWaveMessageDTO);
    }

    /**
     * 修改数据
     *
     * @param seaWaveMessageDTO 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.UPDATE)
    public void update(@RequestBody @Validated SeaWaveMessageDTO seaWaveMessageDTO) {
        this.seaWaveMessageService.updateInfo(seaWaveMessageDTO);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.DELETE)
    public void delete(@RequestBody List<Long> idList) {
        this.seaWaveMessageService.removeByIds(idList);
    }

    /**
     * 生成文字
     * @param waveAlarmGenerateText 参数
     * @return
     */
    @PostMapping("/generateText")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.UPDATE)
    public GenerateTextVO generateText(@RequestBody WaveGenerateText waveAlarmGenerateText){
        return seaWaveMessageService.generateText(waveAlarmGenerateText);
    }

    /**
     * 发布
     * @param seaWaveMessageDTO 参数
     */
    @PostMapping("/release")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪消息管理", operateType = OperateType.INSERT)
    public void release(@RequestBody @Validated SeaWaveMessageDTO seaWaveMessageDTO){
        seaWaveMessageService.release(seaWaveMessageDTO);
    }
}

