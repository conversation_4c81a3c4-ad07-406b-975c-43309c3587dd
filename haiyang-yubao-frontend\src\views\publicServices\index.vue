<template>
  <!-- 公共服务 -->
  <div class="public-services">
    <aside-menu :collapsed="collapsed" @collapsed="changeCollapsed" />
    <div class="content-container" :class="pathClass">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AsideMenu } from 'src/components/AsideMenu'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const pathClass = ref<string>('')

watch(
  () => route.path,
  val => {
    pathClass.value = val.split('/')[val.split('/').length - 1]
  },
  { immediate: true }
)

const collapsed = ref<boolean>(false)
function changeCollapsed(val: boolean) {
  collapsed.value = val
}
</script>

<style scoped>
</style>