<!--
 * @Author: x<PERSON><PERSON><PERSON> xuli<PERSON><EMAIL>
 * @Date: 2024-10-11 11:07:22
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-05-08 10:51:32
 * @FilePath: \hainan-jianzai-web\src\views\alarm\components\making.vue
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="alarm-open-icon" @click="changeCollapsed">
    <i class="icon icon-panel-unfold"></i>
    警报制作
  </div>

  <div
    class="alarm-panel"
    :class="isCollapsed ? 'fadeInRight' : 'fadeOutRight'"
  >
    <div class="panel-header d-flex flex-justify-between flex-align-center">
      <h3>
        <i class="icon icon-panel-collapse" @click="changeCollapsed"></i
        >警报制作
      </h3>
      <div class="btns d-flex flex-align-center">
        <div
          class="btn-item"
          :class="currentType === 1 ? 'active' : ''"
          @click="onChange(1)"
        >
          海浪
        </div>
        <div
          class="btn-item"
          :class="currentType === 2 ? 'active' : ''"
          @click="onChange(2)"
        >
          风暴潮
        </div>
      </div>
    </div>
    <div class="panel-content">
      <div class="column-header d-flex flex-justify-between flex-align-center">
        <h3>历史警报</h3>
        <div class="btns">
          <n-popconfirm @positive-click="deleteAlarm">
            <template #trigger>
              <qx-button>删除</qx-button>
            </template>
            是否删除该警报？
          </n-popconfirm>

          <qx-button class="primary" @click="onCreateAlarm">新建</qx-button>
        </div>
      </div>
      <div class="history-alarms">
        <div
          v-for="item in historyAlarms"
          :key="item.id"
          class="history-alarm-item"
          :class="[
            item.status === EAlarmCommitStatus.HAS_COMMIT ? 'submited' : '',
            curSelectWarning.id === item.id ? 'active' : ''
          ]"
          @click="onSelect(item)"
        >
          <div class="img-wrap">
            <div
              class="d-flex flex-align-center"
              :class="[
                item.alarmLevel == 1
                  ? 'red'
                  : item.alarmLevel == 2
                  ? 'orange'
                  : item.alarmLevel == 3
                  ? 'yellow'
                  : item.alarmLevel == 4
                  ? 'blue'
                  : item.alarmLevel == 5
                  ? 'grey'
                  : ''
              ]"
            >
              <img
                v-if="currentType === 1"
                :src="imagesMap[`sea${item.alarmLevel}`]"
                alt=""
              />
              <img
                v-if="currentType === 2"
                :src="imagesMap[`storm${item.alarmLevel}`]"
                alt=""
              />
              <div class="info">
                <h3>{{ currentType === 1 ? '海浪' : '风暴潮' }}</h3>
              </div>
            </div>
          </div>
          <div class="submit-time">
            <span>{{ item.releaseTime.slice(5) }}</span>
          </div>
          <div
            class="submit-status"
            :class="[
              item.alarmLevel == 1
                ? 'red'
                : item.alarmLevel == 2
                ? 'orange'
                : item.alarmLevel == 3
                ? 'yellow'
                : item.alarmLevel == 4
                ? 'blue'
                : item.alarmLevel == 5
                ? 'grey'
                : ''
            ]"
          >
            <span>{{
              item.status == EAlarmCommitStatus.HAS_COMMIT ? '已提交' : '未提交'
            }}</span>
            <i
              v-if="item.sourceType == EAffectType.TYPHOON"
              class="icon icon-ocean"
            ></i>
            <i
              v-if="item.sourceType == EAffectType.COLD_AIR"
              class="icon icon-cold"
            ></i>
          </div>
        </div>
      </div>
      <div class="column-header d-flex flex-justify-between flex-align-center">
        <h3>警报编辑</h3>
        <div class="btns">
          <qx-button v-if="isEdit" @click="previewWord">预览</qx-button>
          <qx-button @click="saveTemp">保存</qx-button>
          <qx-button class="primary" @click="submitAlarm">提交</qx-button>
        </div>
      </div>
      <div class="alarm-editor">
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-grid :cols="24" :x-gap="12">
            <n-form-item-gi :span="24" label="历史预警：">
              <div
                v-for="ele in historyAlarm"
                :key="ele.id + 'hhh'"
                class="status"
                :class="[
                  ele == 1
                    ? 'red'
                    : ele == 2
                    ? 'orange'
                    : ele == 3
                    ? 'yellow'
                    : ele == 4
                    ? 'blue'
                    : ele == 5
                    ? 'grey'
                    : ''
                ]"
              ></div>
            </n-form-item-gi>
            <n-form-item-gi
              :span="6"
              label="编号："
              label-width="50px"
              path="number"
            >
              <n-input
                v-model:value="model.number"
                placeholder="请输入编号"
              ></n-input>
            </n-form-item-gi>
            <n-form-item-gi
              :span="6"
              label="上期："
              label-width="50px"
              path="lastNumber"
            >
              {{ model.lastNumber }}
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="预警级别：" path="alarmLevel">
              <template v-for="(item, index) in levelList" :key="index">
                <div
                  v-if="
                    curSelectWarning.id || (!isEdit && item.id != 5) || isEdit
                  "
                  class="level"
                  :class="[
                    model.alarmLevel == item.id ? 'active' : '',
                    item.id == 1
                      ? 'red'
                      : item.id == 2
                      ? 'orange'
                      : item.id == 3
                      ? 'yellow'
                      : item.id == 4
                      ? 'blue'
                      : item.id == 5
                      ? 'grey'
                      : ''
                  ]"
                  @click="changeHistoryAlarm(item)"
                ></div>
              </template>
            </n-form-item-gi>

            <n-form-item-gi :span="12" label="发布时间：" path="releaseTime">
              <n-date-picker
                v-model:formatted-value="model.releaseTime"
                type="date"
                value-format="yyyy-MM-dd"
                @update:formatted-value="changeTime"
              />
              <n-select
                v-model:value="time"
                :options="timeOptions"
                @update:value="changeSelectTime"
                :consistent-menu-width="false"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="警报时段：">
              <n-select
                :value="model.alarmTime"
                @update:value="val => (model.alarmTime = val)"
                :options="alarmPeriods"
                label-field="content"
                value-field="content"
                :consistent-menu-width="false"
              />
            </n-form-item-gi>
            <n-form-item-gi
              v-show="false"
              :span="12"
              label="标题："
              label-width="50px"
              path="title"
            >
              <n-input
                v-model:value="model.title"
                placeholder="请输入标题"
              ></n-input>
            </n-form-item-gi>
            <n-form-item-gi
              :span="12"
              label="签发："
              label-width="50px"
              path="signUser"
            >
              <n-select
                v-model:value="model.signUser"
                :options="issuerList"
                label-field="name"
                value-field="id"
                @update:value="handleUpdateSign"
                :consistent-menu-width="false"
              />
            </n-form-item-gi>
            <n-form-item-gi
              :span="12"
              label="拟稿："
              label-width="50px"
              path="makeUser"
            >
              <n-select
                v-model:value="model.makeUser"
                :options="userList"
                label-field="name"
                value-field="id"
                @update:value="handleUpdateMake"
                :consistent-menu-width="false"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="影响要素：">
              <n-checkbox-group
                v-model:value="sourceTypeArray"
                @update:value="handleSourceTypeChange"
              >
                <n-checkbox label="冷空气" :value="1"></n-checkbox>
                <n-checkbox label="台风" :value="2"></n-checkbox>
              </n-checkbox-group>
            </n-form-item-gi>
            <!-- 台风编号 -->
            <n-form-item-gi
              :span="6"
              v-if="
                model.sourceType == EAffectType.TYPHOON ||
                model.sourceType == EAffectType.BOTH
              "
            >
              <n-select
                v-model:value="model.typhoonNo"
                placeholder="请选择"
                :options="typhoonList"
                label-field="bhName"
                value-field="tfbh"
                @update:value="getTyphoonInfo"
                :consistent-menu-width="false"
              ></n-select>
            </n-form-item-gi>
            <!-- 台风时间 -->
            <n-form-item-gi
              :span="6"
              v-if="
                model.sourceType == EAffectType.TYPHOON ||
                model.sourceType == EAffectType.BOTH
              "
            >
              <n-select
                v-model:value="model.typhoonTime"
                placeholder="请选择"
                :options="typhoonTimeList"
                label-field="time"
                value-field="time"
                :consistent-menu-width="false"
              ></n-select>
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <n-tabs
          v-model:value="tabActive"
          type="card"
          tab-style="min-width: 80px;"
          v-loading="textGenerateLoading"
        >
          <n-tab-pane
            v-for="panel in panels.filter(i => i.condition())"
            :key="panel.code"
            :name="panel.name"
          >
            <n-input
              v-model:value="panel.text"
              type="textarea"
              placeholder="请输入"
            />
          </n-tab-pane>
          <template #suffix>
            <div class="btns">
              <qx-button class="primary" @click="addText">{{
                currentType === 1 ? '文字生成' : '参考站'
              }}</qx-button>
            </div>
          </template>
        </n-tabs>
      </div>

      <div class="column-header d-flex flex-justify-between flex-align-center">
        <h3>警报图</h3>
        <div class="btns">
          <qx-button class="primary" @click="addModel">加载模板</qx-button>
        </div>
      </div>
      <div class="upload-container">
        <div v-for="(item, i) in fileImageList" :key="i + 'img'" class="images">
          <n-spin v-if="item.type === 'empty'" :show="loading">
            <div class="empty-img" @click="addImage">
              <div class="add-icon"><Add></Add></div>
              <div>添加警报图</div>
            </div>
          </n-spin>

          <div v-if="item.type === 'image'" style="max-height: 100%">
            <div class="delete-icon">
              <CloseCircle
                color="#f90102"
                @click="removeImage(item, i)"
              ></CloseCircle>
            </div>
            <n-image width="100" :src="imgUrl + item.url" />
          </div>
        </div>
      </div>
      <div class="forecast-preview-wrap">
        <div
          v-for="(imageInfo, i) in forecastInfoArr"
          :key="i"
          class="forecast-image-inner"
          @click="onForecastImageClicked(imageInfo)"
        >
          <n-tooltip>
            <template #trigger>
              <img width="100" :src="imageInfo.fileUrlColorful" />
            </template>
            <div>
              {{ imageInfo.name }}
            </div>
          </n-tooltip>
        </div>
      </div>
    </div>
  </div>

  <qx-dialog
    title="新建--警报"
    :visible="dialogVisible"
    width="365px"
    class="add-alarm-message"
    @update:visible="cancleNew"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="dialogFormRef"
          class="forecast-temp-form"
          :model="dialogForm"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="影响要素" path="alarmType">
            <n-radio-group
              v-model:value="dialogForm.sourceType"
              name="radiogroup"
            >
              <n-radio :value="EAffectType.COLD_AIR">冷空气</n-radio>
              <n-radio :value="EAffectType.TYPHOON">台风</n-radio>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            v-if="dialogForm.sourceType === EAffectType.TYPHOON"
            label="台风编号"
            path="typhoonNo"
          >
            <n-select
              v-model:value="dialogForm.typhoonNo"
              placeholder="请选择"
              :options="typhoonList"
              label-field="bhName"
              value-field="tfbh"
              @update:value="getTyphoonInfo"
            ></n-select>
          </n-form-item>
          <n-form-item
            v-if="dialogForm.sourceType === EAffectType.TYPHOON"
            label="台风时间"
            path="typhoonTime"
          >
            <n-select
              v-model:value="dialogForm.typhoonTime"
              placeholder="请选择"
              :options="typhoonTimeList"
              label-field="time"
              value-field="time"
            ></n-select>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="cancleNew">取消</qx-button>
        <qx-button class="primary" @click="onDialogSave">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
  <qx-dialog
    title="参考站"
    :visible="referenceStationVisible"
    width="1000px"
    class="add-alarm-message"
    @update:visible="referenceStationVisible = false"
  >
    <template #content>
      <div class="reference-container">
        <div class="reference-left">
          <!--          <n-input-->
          <!--            v-model:value="stationName"-->
          <!--            type="text"-->
          <!--            placeholder="请输入站点名称筛选"-->
          <!--            class="query-info"-->
          <!--            @change="getStationTreeData"-->
          <!--          />-->
          <div class="station-tree" v-loading="loadingRegionTreeData">
            <n-checkbox-group v-model:value="regionSelected">
              <n-space vertical>
                <div>区域选择</div>
                <n-checkbox
                  v-for="item in regionList"
                  :key="item.code"
                  :value="item.id"
                  >{{ item.name }}</n-checkbox
                >
              </n-space>
            </n-checkbox-group>
            <!--            <qx-tree-->
            <!--              :tree-data="stationTreeData"-->
            <!--              :default-props="defaultProps"-->
            <!--              :default-expand-all="true"-->
            <!--              :checkable="true"-->
            <!--              @checked="handleStationTreeSelected"-->
            <!--            />-->
          </div>
          <div class="query-info">
            <qx-button class="primary" @click="queryStationData"
              >查询</qx-button
            >
          </div>
        </div>
        <div class="reference-right">
          <div class="table-query-wrap">
            <n-space>
              <div class="datetime-wrap">
                <n-date-picker
                  v-model:formatted-value="stationTime"
                  type="daterange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  class="query-info"
                />
              </div>
              <div class="station-level-wrap">
                <n-radio-group v-model:value="regionLevel">
                  <n-radio
                    v-for="item in getReferenceStationLevelList()"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  ></n-radio>
                </n-radio-group>
              </div>
              <qx-button class="primary" @click="queryStationDataV2"
                >查询</qx-button
              >
            </n-space>
          </div>
          <div class="table-container">
            <n-data-table
              :row-key="rowKey"
              class="qx-table"
              :data="regionTableData"
              :columns="columns"
              :max-height="400"
              @update:checked-row-keys="handleCheck"
            ></n-data-table>
          </div>
          <div class="info-word">
            <p class="title">提示：</p>
            <p>通过勾选操作确定本次警报需要关联的影响地区及预警详细信息;</p>
            <p>
              该警戒潮位为按照《警戒潮位核定规范》（GB/T17839-2011）核定的四色警戒潮位，基面为85基准面。
            </p>
          </div>
        </div>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="referenceStationVisible = false">取消</qx-button>
        <qx-button class="primary" @click="generateStormText">生成</qx-button>
      </div>
    </template>
  </qx-dialog>
  <qx-dialog
    title="文字生成"
    :visible="textDialogVisible"
    width="365px"
    class="add-alarm-message"
    @update:visible="textDialogVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="dialogFormRef"
          class="forecast-temp-form"
          :model="textForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="区域范围" path="areaIds">
            <n-tree-select
              multiple
              cascade
              checkable
              check-strategy="child"
              :options="treeData"
              key-field="id"
              label-field="name"
              children-field="areaList"
              max-tag-count="responsive"
              :value="textForm.areaIds"
              @update:value="handleTextValue"
            />
          </n-form-item>
          <n-form-item label="起报时间">
            <n-select
              v-model:value="textForm.ncFileDate"
              placeholder="请选择"
              :options="ncFileList"
              label-field="data_time"
              value-field="data_time"
            ></n-select>
          </n-form-item>
          <n-form-item label="预报范围">
            <n-select
              v-model:value="textForm.forecastRange"
              placeholder="请选择"
              :options="rangeList"
            ></n-select>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="textDialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="generate">生成</qx-button>
      </div>
    </template>
  </qx-dialog>
  <qx-dialog
    v-if="wordVisiable"
    title="警报预览"
    :visible="wordVisiable"
    width="800px"
    class="add-alarm-message"
    @update:visible="wordVisiable = false"
  >
    <template #content>
      <div class="word-content">
        <!-- <iframe :src="fileUrl" width="100%" height="100%" frameborder="0"/> -->
        <OfficeEditor
          v-if="wordUrl"
          :url="wordUrl"
          :callback-url="callbackUrl"
        ></OfficeEditor>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  onMounted,
  getCurrentInstance,
  inject,
  watch
} from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/alarm'
import type { UploadFileInfo } from 'naive-ui'
import { useMessage } from 'naive-ui'
import PersApi from 'src/requests/pers'
import { QxDialog } from 'src/components/QxDialog'
import ToolApi from 'src/requests/toolRequest'
import { CloseCircle, Add } from '@vicons/ionicons5'
import ApiForecast from 'src/requests/forecast'
import type { MessageReactive, FormRules, FormInst } from 'naive-ui'
import { OfficeEditor } from 'src/components/OfficeEditor'
import GeoJSON from 'ol/format/GeoJSON.js'
import { QxTree } from 'src/components/QxTree'
import CryptoJS from 'crypto-js'
import tagApi from 'src/requests/tag'
import { DataTableColumn } from 'naive-ui'
import moment from 'moment'
import { useForecastList } from 'src/views/alarm/components/making-hooks/useForecastList'
import { useAlarmLevel } from './making-hooks/useAlarmLevel'
import { useAlarmPeriod } from 'src/views/alarm/components/making-hooks/useAlarmPeriod'
import {
  EAffectType,
  EAlarmCommitStatus
} from 'src/views/alarm/components/making-hooks/enums'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import { bridgeKey, IBridgeData } from '../alarmMakingHooks/bridge.type'
import { useAlarmContent } from 'src/views/alarm/components/making-hooks/useAlarmContent'
import type { IFormData } from 'src/views/alarm/components/making-hooks/form.type'
import { useForm } from 'src/views/alarm/components/making-hooks/useForm'
import { ReferenceTreeManager } from 'src/views/alarm/components/making-hooks/referenceTreeManager'
import { useReferenceStation } from 'src/views/alarm/components/making-hooks/useReferenceStation'
import { IRegionTree } from 'src/requests/alarm.type'

const bridge = inject<IBridge<IBridgeData>>(bridgeKey)
// 警报时段
const { alarmPeriods, currentAlarmPeriodID } = useAlarmPeriod()
// 警报图列表
const { forecastInfoArr, onForecastImageClicked } = useForecastList()
// 警报文字生成
const { getAlarmContent, getCityContent, getSMSContent } = useAlarmContent()
// 警报表单
const { getSummary } = useForm()
// 参考站
const {
  referenceStationVisible,
  regionList,
  regionSelected,
  updateRegionTreeData,
  regionLevel,
  getReferenceStationLevelList,
  loadingRegionTreeData,
  regionColumns: columns,
  regionTableData,
  stationTime,
  queryStationDataV2
} = useReferenceStation()

const req1 = import.meta.glob('src/assets/images/alarm/*.*', { eager: true })

const req: any = { ...req1 }
let imagesMap: any = {}
// 循环所有图片，将图片名设置成键，值为导入该图片的地址
for (const key in req) {
  // let name = key.replace(/(\.\/images\/|\..*)/g, '')
  let name = key.split('/').slice(-1)[0].split('.')[0]

  // 抛出图片大对象后，文件页面直接引入后将图片的具体名称作为属性就能导入该图片
  imagesMap[name] = req[key].default
}
const message = useMessage()
const wordVisiable = ref(false)
const wordUrl = ref('')
const callbackUrl = ref('')
let fileUrl = ref('')
function previewWord() {
  wordVisiable.value = true
  callbackUrl.value =
    config.onlyOfficeCallBack + 'record/' + curSelectWarning.value?.id
  wordUrl.value =
    config.onlyOfficeServerUrl + curSelectWarning.value.wordFilePath
  // fileUrl.value = config.kkFileUrl +decodeURIComponent(wordUrl.value)
}
let messageReactive: MessageReactive | null = null
const emits = defineEmits([
  'addForecast',
  'changeTime',
  'saveImage',
  'closeTool',
  'goMapFit'
])
const hasTool = ref(false)
const loading = ref(false)
const showAllLoading = ref(false)
const curSelectWarning = ref<any>({})
// 图片列表
const fileImageList = ref<any[]>([])
// form表单
const time = ref<any>('')
const timeOptions = [
  {
    label: '10时',
    value: '10:00:00'
  },
  {
    label: '16时',
    value: '16:00:00'
  },
  {
    label: '22时',
    value: '22:00:00'
  },
  {
    label: '04时',
    value: '04:00:00'
  }
]
function changeSelectTime(value: string) {}
let model: any = reactive<IFormData>({
  title: '', // 标题
  sourceType: 1, //冷空气或者台风
  releaseTime: null, // 发布时间
  signUser: '', // 签发
  signUserName: '', //签发人
  makeUser: '', // 制作
  makeUserName: '', //制作人
  alarmContent: '', //警报内容
  cityAlarmContent: '', // 城市警报内容
  smsContent: '', //短信
  typhoonNo: '', //台风编号
  typhoonTime: '', //台风时间
  lastNumber: '', //上期
  alarmLevel: '', //警报等级
  summarize: '', // 综述
  historyAlarmLevel: '', //历史警报等级
  alarmTime: alarmPeriods[0].content, // 警报时段
  number: '' //警报编号
})

// 多选数组，用于处理影响要素的多选
const sourceTypeArray = ref<number[]>([1])

// 处理影响要素多选变化
function handleSourceTypeChange(values: (string | number)[]) {
  // 将值转换为数字数组
  const numValues = values.map(v => Number(v)).filter(v => !isNaN(v))

  if (numValues.length === 0) {
    // 如果没有选择任何项，默认选择冷空气
    sourceTypeArray.value = [1]
    model.sourceType = EAffectType.COLD_AIR
  } else if (numValues.length === 1) {
    // 如果只选择了一项
    model.sourceType = numValues[0]
  } else if (numValues.length === 2) {
    // 如果选择了两项，设置为3（同时受冷空气和台风影响）
    model.sourceType = EAffectType.BOTH
  }
}

// 监听 model.sourceType 变化，同步更新 sourceTypeArray
watch(
  () => model.sourceType,
  newValue => {
    if (newValue === EAffectType.COLD_AIR) {
      sourceTypeArray.value = [1]
    } else if (newValue === EAffectType.TYPHOON) {
      sourceTypeArray.value = [2]
    } else if (newValue === EAffectType.BOTH) {
      sourceTypeArray.value = [1, 2]
    }
  }
)

bridge?.register('model', () => model)
/***新建风暴潮 ********************/
let dialogVisible = ref(false)
let typhoonList = ref([])
let typhoonTimeList = ref<{ time: string }[]>([])
const rules: FormRules = {
  typhoonNo: {
    required: true,
    message: '请选择台风编号',
    trigger: 'change'
  },
  typhoonTime: {
    required: true,
    message: '请选择台风时间',
    trigger: 'change'
  },
  title: {
    required: true,
    message: '请输入标题',
    trigger: 'blur'
  },
  releaseTime: {
    required: true,
    message: '请选择发布时间',
    trigger: ['blur', 'change']
  },
  signUser: {
    required: true,
    message: '请选择签发',
    trigger: 'change'
  }, // 签发
  makeUser: {
    required: true,
    message: '请选择预报员',
    trigger: 'change'
  },
  alarmContent: {
    required: true,
    message: '请输入警报内容',
    trigger: 'blur'
  },
  smsContent: {
    required: true,
    message: '请输入短信内容',
    trigger: 'blur'
  },
  number: {
    required: true,
    message: '请输入编号',
    trigger: 'blur'
  },
  alarmLevel: {
    type: 'number',
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择预警级别'
  }
}
// 新建弹窗警报消息 start
const dialogForm = reactive({
  typhoonNo: null, // 台风编号
  sourceType: 1, // 要素类型
  typhoonTime: null //台风时间
})
// 获取台风列表
function getTyList() {
  ToolApi.getTyList()
    .then((res: any) => {
      typhoonList.value = res.map((item: any) => {
        item.bhName = `${item.tfbh}-${item.name}`
        return item
      })
    })
    .catch(() => {
      message.error('获取数据失败')
    })
}
// 获取台风时间
function getTyphoonInfo(code: string) {
  ToolApi.getTyphoonInfo({ code })
    .then((res: any) => {
      typhoonTimeList.value = []
      if (Array.isArray(res)) {
        typhoonTimeList.value = res
      }
    })
    .catch(() => {
      message.error('获取数据失败')
    })
}
// 新增预报单
function createNew() {
  if (curSelectWarning.value.status === EAlarmCommitStatus.HAS_COMMIT) {
    dialogForm.typhoonNo = curSelectWarning.value.typhoonNo
    dialogForm.typhoonTime = curSelectWarning.value.typhoonTime
    dialogForm.sourceType = curSelectWarning.value.sourceType
    if (dialogForm.sourceType == EAffectType.TYPHOON) {
      getTyphoonInfo(curSelectWarning.value.typhoonNo)
    }
  } else if (curSelectWarning.value.status === EAlarmCommitStatus.NEED_COMMIT) {
    message.warning('不能基于未提交的警报新建')
    return
  } else {
    dialogForm.typhoonNo = null
    dialogForm.typhoonTime = null
    dialogForm.sourceType = 1
  }
  dialogVisible.value = true
  getTyList()
}

/**
 * 老版新建
 */
function onDialogSave() {
  model = reactive({
    title: '', // 标题
    releaseTime: moment().format('YYYY-MM-DD'), // 发布时间
    signUser: issuerList.value?.[0].id || '',
    makeUser: userList.value?.[0].id || '',
    signUserName: '', //签发人
    makeUserName: '', //制作人
    alarmContent: '', //警报内容
    smsContent: '', //短信
    typhoonNo: '', //台风编号
    typhoonTime: '', //台风时间
    lastNumber: '', //上期
    summarize: '', // 综述
    alarmLevel: '', //警报等级
    historyAlarmLevel: '', //历史警报等级
    number: '' //警报编号
  })
  changeHistoryAlarm(levelList.value[0])
  fileImageList.value = [
    {
      type: 'empty'
    }
  ]
  model.typhoonNo = dialogForm.typhoonNo
  model.typhoonTime = dialogForm.typhoonTime
  model.sourceType = dialogForm.sourceType
  if (curSelectWarning.value.number) {
    // 升降级
    const date = curSelectWarning.value.number.split('-')[0]
    const num = curSelectWarning.value.number.split('-')[1]
    const curNum = (Number(num) + 1).toString()
    model.number = `${date}-${curNum.padStart(2, '0')}`
  } else {
    // 新建
    const m = moment()
    m.subtract(1, 'days')
    const date = m.format('YYYYMMDD')
    model.number = `${date}-01`
  }
  model.lastNumber = curSelectWarning.value.number
  let his = ''
  if (
    curSelectWarning.value.historyAlarmLevel &&
    curSelectWarning.value.historyAlarmLevel !== '' &&
    curSelectWarning.value.alarmLevel != 5
  ) {
    his =
      curSelectWarning.value.historyAlarmLevel +
      ',' +
      curSelectWarning.value.alarmLevel
  } else {
    if (curSelectWarning.value.alarmLevel) {
      his = curSelectWarning.value.alarmLevel + ''
    }
  }
  historyAlarm.value = his.split(',')
  model.historyAlarmLevel = his
  dialogVisible.value = false
  isEdit.value = false
  hasTool.value = true
  emits('addForecast', currentType.value)
  // 设置时间
  model.releaseTime = moment().format('YYYY-MM-DD')
  const curTime = moment().format('YYYY-MM-DD HH:mm:ss')
  time.value = processTime(curTime)
}

/**
 * 默认表单
 */
function getDefaultModel(): IFormData {
  return {
    title: '', // 标题
    releaseTime: moment().format('YYYY-MM-DD'), // 发布时间
    signUser: issuerList.value?.[0].id || '',
    makeUser: userList.value?.[0].id || '',
    signUserName: '', //签发人
    makeUserName: '', //制作人
    alarmContent: '', //警报内容
    smsContent: '', //短信
    typhoonNo: '', //台风编号
    cityAlarmContent: '',
    typhoonTime: '', //台风时间
    lastNumber: '', //上期
    summarize: '', // 综述
    alarmLevel: '', //警报等级
    historyAlarmLevel: '', //历史警报等级
    number: '', //警报编号
    alarmTime: alarmPeriods[0].content,
    sourceType: EAffectType.COLD_AIR
  }
}

/**
 * 新建或升降级警报
 */
function onCreateAlarm() {
  if (curSelectWarning.value.status === EAlarmCommitStatus.HAS_COMMIT) {
    if (dialogForm.sourceType == EAffectType.TYPHOON) {
      getTyphoonInfo(curSelectWarning.value.typhoonNo)
    }
  } else if (curSelectWarning.value.status === EAlarmCommitStatus.NEED_COMMIT) {
    message.warning('不能基于未提交的警报新建')
    return
  } else {
    model.typhoonNo = null
    model.typhoonTime = null
    model.sourceType = 1
  }

  Object.assign(model, getDefaultModel())
  dialogVisible.value = true
  getTyList()
  changeHistoryAlarm(levelList.value[0])
  fileImageList.value = [
    {
      type: 'empty'
    }
  ]
  if (curSelectWarning.value.number) {
    // 升降级
    const date = curSelectWarning.value.number.split('-')[0]
    const num = curSelectWarning.value.number.split('-')[1]
    const curNum = (Number(num) + 1).toString()
    model.number = `${date}-${curNum.padStart(2, '0')}`
  } else {
    // 新建
    const m = moment()
    m.subtract(1, 'days')
    const date = m.format('YYYYMMDD')
    model.number = `${date}-01`
  }
  model.lastNumber = curSelectWarning.value.number
  let his = ''
  if (
    curSelectWarning.value.historyAlarmLevel &&
    curSelectWarning.value.historyAlarmLevel !== '' &&
    curSelectWarning.value.alarmLevel != 5
  ) {
    his =
      curSelectWarning.value.historyAlarmLevel +
      ',' +
      curSelectWarning.value.alarmLevel
  } else {
    if (curSelectWarning.value.alarmLevel) {
      his = curSelectWarning.value.alarmLevel + ''
    }
  }
  historyAlarm.value = his.split(',')
  model.historyAlarmLevel = his
  dialogVisible.value = false
  isEdit.value = false
  hasTool.value = true
  emits('addForecast', currentType.value)
  // 设置时间
  model.releaseTime = moment().format('YYYY-MM-DD')
  const curTime = moment().format('YYYY-MM-DD HH:mm:ss')
  time.value = processTime(curTime)
  // addText()
}

function onReset() {
  historyAlarm.value = []
  model = reactive({
    alarmLevel: '',
    title: '',
    releaseTime: null,
    number: '',
    lastNumber: null,
    signUser: issuerList.value?.[0].id || '',
    makeUser: userList.value?.[0].id || '',
    summarize: '',
    historyAlarmLevel: ''
  })
  time.value = ''
  curSelectWarning.value = {}
  fileImageList.value = [
    {
      type: 'empty'
    }
  ]
  panels.value.forEach((item: any) => {
    item.text = ''
  })
}
function cancleNew() {
  dialogVisible.value = false
  isEdit.value = true
}
function changeTime(value: any) {
  emits('changeTime', value)
}
// 添加警报图
function addImage() {
  // emits('addForecast', currentType.value)
  emits('goMapFit')
  if (hasTool.value) {
    loading.value = true
    setTimeout(() => {
      emits('saveImage')
    }, 1000)
  } else {
    message.warning('请先添加模板')
  }
}
function closeLoading() {
  loading.value = false
}
// 图片回显
function addWarningImage(url: any) {
  fileImageList.value.unshift({
    type: 'image',
    url: url
  })
}
function addModel() {
  hasTool.value = true
  emits('addForecast', currentType.value)
}
function removeImage(item: any, index: any) {
  fileImageList.value.splice(index, 1)
}
const isCollapsed = ref(true)
const currentType = ref<number>(1)
bridge?.register('currentType', () => currentType.value)
const guideLevelList = ref<any[]>([]) //防御指南等级内容
// 历史预警
const historyAlarms = ref<any[]>([])
// 预警级别
// const levelList = ref<any[]>([])
const { levelList, getTitleByLevel } = useAlarmLevel()
// 签发人拟稿人
let userList = ref<any[]>([])
// 签发人
let issuerList = ref<any[]>([])
// 获取用户列表
// function getUserList() {
//   const params = {
//     pageSize: 999,
//     pageNum: 1
//   }
//   PersApi.getUserList(params)
//     .then((res: any) => {
//       const { pageResult = [] } = res
//       userList.value = pageResult
//     })
//     .catch(() => {
//       message.error('获取数据失败')
//     })
// }
function getUserList() {
  // 获取签发人
  tagApi
    .getUserList({
      tagId: '1904727438635282434'
    })
    .then((res: any) => {
      issuerList.value = res
    })
    .catch(() => {
      issuerList.value = []
    })
  // 获取拟稿人
  tagApi
    .getUserList({
      tagId: '1904782290649661442'
    })
    .then((res: any) => {
      userList.value = res
    })
    .catch(() => {
      userList.value = []
    })
}
const textGenerateLoading = ref(false)
// 警报类型
const panels = ref<
  {
    name: string
    type: number
    text: string
    condition: () => boolean
    code: 'alarmContent' | 'defenseGuide' | 'smsContent' | 'cityAlarmContent'
  }[]
>([
  {
    name: '警报内容',
    type: 1,
    text: '',
    condition: () => true,
    code: 'alarmContent'
  },
  {
    name: '近岸内容',
    type: 4,
    text: '',
    condition: () => false,
    code: 'cityAlarmContent'
  },
  {
    name: '防御指南',
    type: 2,
    text: '',
    condition: () => true,
    code: 'defenseGuide'
  },
  {
    name: '短信',
    type: 3,
    text: '',
    condition: () => true,
    code: 'smsContent'
  }
])
const tabActive = ref('警报内容')

const tabContext = ref('')
//展开收起切换
function changeCollapsed() {
  isCollapsed.value = !isCollapsed.value
}
const isEdit = ref(true)

function onChange(type: number) {
  currentType.value = type
  localStorage.setItem('alarmType', type.toString())
  emits('closeTool')
  getRightList()
  getDefenseGuideList()
}
function getDefenseGuideList() {
  Api.getDefenseGuideList(currentType.value)
    .then((res: any) => {
      guideLevelList.value = res.guideLevelList
    })
    .catch(() => {
      guideLevelList.value = []
    })
}
function changeHistoryAlarm(item: any) {
  model.alarmLevel = item.id * 1
  const guide = guideLevelList.value.find((ele: any) => ele.levelId == item.id)
  if (guide) {
    const find = panels.value.find(i => i.code === 'defenseGuide')
    if (!find) {
      throw new Error('无法找到')
    }
    find.text = guide.guideContent
  }
  // formRef.value?.validate()

  // 根据选择等级自动生成标题
  const type = currentType.value === 1 ? '南海海浪' : '风暴潮'
  model.title = getTitleByLevel(type, model.alarmLevel)
}
const historyAlarm = ref<any[]>([]) //
// const historyAlarm
async function onSelect(item: any) {
  emits('closeTool')
  if (model.sourceType == EAffectType.TYPHOON) {
    // 台风需要加载台风列表和某个台风的时间列表
    // await getTyList()
    await getTyphoonInfo(curSelectWarning.value.typhoonNo)
  }
  if (curSelectWarning.value?.id === item.id) {
    curSelectWarning.value = null
    onReset()
  } else {
    if (!isEdit.value) {
      hasTool.value = false
      isEdit.value = true
    }
    curSelectWarning.value = item
    if (isEdit.value) {
      // 编辑状态
      historyAlarm.value = curSelectWarning.value.historyAlarmLevel?.split(',')
    } else {
      let his = ''
      if (curSelectWarning.value.historyAlarmLevel !== '') {
        his =
          curSelectWarning.value.historyAlarmLevel +
          ',' +
          curSelectWarning.value.alarmLevel
      } else {
        his = curSelectWarning.value.alarmLevel
      }
      historyAlarm.value = his.split(',')
    }
    Object.keys(model).forEach((key: string) => {
      if (key === 'releaseTime') {
        time.value = moment(curSelectWarning.value[key]).format('HH:00:00') //processTime(curSelectWarning.value[key])
        model[key] = moment(curSelectWarning.value[key]).format('YYYY-MM-DD')
      } else {
        model[key] = curSelectWarning.value[key]
      }
    })
    // time.value = processTime(model.releaseTime)
    panels.value.forEach((item: any) => {
      item.text = curSelectWarning.value[`${item.code}`]
    })
    fileImageList.value = [
      {
        type: 'empty'
      }
    ]
    const imgs = curSelectWarning.value.alarmImages.split(',')
    if (imgs.length > 0) {
      imgs.forEach((item: any) => {
        fileImageList.value.unshift({
          type: 'image',
          url: item
        })
      })
    }
  }
}

async function getRightList() {
  const funName =
    currentType.value === 1 ? 'getSeaWaveAlarmPageList' : 'getStormSurge'
  Api[funName]({
    pageNum: 1,
    pageSize: 4
  })
    .then((res: any) => {
      historyAlarms.value = res.pageResult
      if (res.pageResult.length > 0) {
        curSelectWarning.value = res.pageResult[0]
        historyAlarm.value = curSelectWarning.value.historyAlarmLevel.split(',')
        Object.keys(model).forEach((key: string) => {
          if (key === 'releaseTime') {
            time.value = moment(res.pageResult[0][key]).format('HH:00:00') //processTime(res.pageResult[0][key])
            model[key] = moment(res.pageResult[0][key]).format('YYYY-MM-DD')
          } else {
            model[key] = res.pageResult[0][key]
          }
        })
        console.log(model, time, '********')
        panels.value.forEach((item: any) => {
          item.text = res.pageResult[0][`${item.code}`]
        })
        fileImageList.value = [
          {
            type: 'empty'
          }
        ]
        if (res.pageResult[0].alarmImages) {
          const imgs = res.pageResult[0].alarmImages.split(',')
          if (imgs.length > 0) {
            imgs.forEach((item: any) => {
              fileImageList.value.unshift({
                type: 'image',
                url: item
              })
            })
          }
        }
      } else {
        onReset()
      }
    })
    .catch(() => {
      historyAlarms.value = []
      onReset()
    })
}

function processTime(inputTime: any) {
  // 解析输入的时间
  const date = new Date(inputTime)
  const hours = date.getHours()

  // 根据时间范围输出结果
  if (hours >= 0 && hours <= 11) {
    return '10:00:00'
  } else if (hours >= 12 && hours <= 17) {
    return '16:00:00'
  } else if (hours >= 18 && hours <= 23) {
    return '22:00:00'
  } else {
    return ''
  }
}
function handleUpdateSign(value: any, option: any) {
  model.signUserName = option.name
}
function handleUpdateMake(value: any, option: any) {
  model.makeUserName = option.name
}
const formRef = ref<FormInst | null>(null)
// 保存警报
const { proxy } = getCurrentInstance() as any
const getMap = inject<(map: any) => void>('getMap')
function saveTemp() {
  let alarmArea: any = ''
  if (getMap) {
    getMap((map: any) => {
      const layers: any = map.getLayers()
      layers.forEach((item: any) => {
        const prop: any = item.getProperties()
        if (currentType.value === 1) {
          if (prop.name === 'lqgj') {
            const features: any = item.getSource().getFeatures()
            features.forEach((feature: any) => {
              const properties: any = feature.getProperties()
              if (
                properties.color === '#770804' ||
                properties.color === '#f70501'
              ) {
                // 红色
                feature.set('name', '海浪红色警报区域')
                feature.set('level', '红色警报')
              }
              if (properties.color === '#FD7D15') {
                // 橙色
                feature.set('name', '海浪橙色警报区域')
                feature.set('level', '橙色警报')
              }
              if (properties.color === '#FDF205') {
                // 黄色
                feature.set('name', '海浪黄色警报区域')
                feature.set('level', '黄色警报')
              }
              if (
                properties.color === '#0102F7' ||
                properties.color === '#54DDC4'
              ) {
                // 蓝色
                feature.set('name', '海浪蓝色警报区域')
                feature.set('level', '蓝色警报')
              }
            })
            const format: any = new GeoJSON()
            alarmArea = format.writeFeatures(features)
          }
        } else {
          if (prop.layerType === '区域范围') {
            const features: any = item.getSource().getFeatures()
            const format: any = new GeoJSON()
            alarmArea = format.writeFeatures(features)
          }
        }
      })
    })
  }
  formRef.value?.validate((valid: any) => {
    console.log(model.alarmLevel, 'lve---')
    if (!valid) {
      const params = JSON.parse(JSON.stringify(curSelectWarning.value))
      Object.keys(model).forEach((key: string) => {
        params[key] = model[key]
      })
      panels.value.forEach((item: any) => {
        params[`${item.code}`] = item.text
      })
      let arr: any = []
      params.releaseTime = model.releaseTime + ' ' + time.value
      fileImageList.value.forEach((item: any) => {
        if (item.type === 'image') {
          arr.push(item.url)
        }
      })
      if (!messageReactive) {
        messageReactive = message.info('保存中请稍候...', {
          duration: 0
        })
      }
      params.alarmImages = arr.toString()
      params.alarmArea = alarmArea
      // params.stationWarning = checkedRows.value
      params.stationWarning = regionTableData.value.filter(i =>
        regionSelected.value.includes(i.id)
      )
      params.signUserName = issuerList.value.find(
        i => i.id == params.signUser
      ).name
      params.makeUserName = userList.value.find(
        i => i.id == params.makeUser
      ).name
      params.alarmContent =
        panels.value.find(i => i.code === 'alarmContent')?.text || ''
      params.cityAlarmContent =
        panels.value.find(i => i.code === 'cityAlarmContent')?.text || ''
      params.summarize = getSummary()

      if (!isEdit.value) {
        delete params.id
      }
      showAllLoading.value = true
      if (isEdit.value) {
        const funNameUpdate =
          currentType.value === 1 ? 'updateAlarm' : 'updateStormSurge'
        Api[funNameUpdate](params)
          .then((res: any) => {
            message.success('更新成功')

            emits('closeTool')
            hasTool.value = false
            getRightList()
          })
          .finally(() => {
            if (messageReactive) {
              messageReactive.destroy()
              messageReactive = null
            }
          })
          .catch((e: any) => {
            let { msg = null } = e?.response?.data || e?.data || {}
            message.error(msg || '更新失败')
          })
      } else {
        const funNameCreate =
          currentType.value === 1 ? 'createSeaWave' : 'createStormSurge'
        Api[funNameCreate](params)
          .then((res: any) => {
            message.success('创建成功')
            getRightList()
            emits('closeTool')
            hasTool.value = false
            if (messageReactive) {
              messageReactive.destroy()
              messageReactive = null
            }
            isEdit.value = true
          })
          .finally(() => {
            if (messageReactive) {
              messageReactive.destroy()
              messageReactive = null
            }
          })
          .catch((e: any) => {
            let { msg = null, data = {} } = e?.response?.data || e?.data || {}
            message.error(msg || '创建失败')
            message.error(Object.values(data).join(', '))
            // getRightList()
            // emits('closeTool')
            // hasTool.value = false
            if (messageReactive) {
              messageReactive.destroy()
              messageReactive = null
            }
            // isEdit.value = true
          })
      }
    }
  })
}
// 发布警报
function submitAlarm() {
  if (curSelectWarning.value.id && curSelectWarning.value.id !== '') {
    const pushFunName =
      currentType.value === 1 ? 'pushSeaWave' : 'pushStormSurge'
    Api[pushFunName](curSelectWarning.value.id)
      .then((res: any) => {
        message.success('推送成功')
        return res
      })
      .catch((e: any) => {
        message.error('推送失败')
      })

    const funName =
      currentType.value === 1 ? 'publishSeaWave' : 'releaseStormSurge'

    Api[funName](curSelectWarning.value.id)
      .then((res: any) => {
        message.success('发布成功')
        getRightList()
      })
      .catch(() => {
        message.error('发布失败')
        getRightList()
      })
  } else {
    message.warning('请先选择需要保存的警报')
  }
}
// 删除警报
function deleteAlarm() {
  const funName =
    currentType.value === 1 ? 'deleteSeaWaveAlarm' : 'deleteStormSurge'

  if (!curSelectWarning.value.id) {
    message.warning('请先选择需要删除的警报')
    return false
  }
  if (curSelectWarning.value.state == 2) {
    message.warning('已提交的警报不能删除')
    return false
  }
  Api[funName]([curSelectWarning.value.id])
    .then(() => {
      message.success('删除成功')
      getRightList()
    })
    .catch(() => {
      message.error('删除失败')
      getRightList()
    })
}
// 文字生成部分
const textDialogVisible = ref(false)
const treeData = ref<any[]>([])
function getTreeData() {
  const params = {
    name: '',
    codes: 'area,island,region,resort'
  }
  ApiForecast.getAreaList(params)
    .then((res: any) => {
      treeData.value = res
    })
    .catch(e => {
      message.error('获取数据失败')
    })
}
let textForm = reactive<{
  areaIds: string[]
  ncFilePath: string
  ncFileDate: string
  forecastRange: string
}>({
  areaIds: [], // 区域ID列表
  ncFilePath: '', // 数值NC文件
  ncFileDate: '', //数值NC文件
  forecastRange: '' //预报范围
})
function handleTextValue(value: any) {
  console.log(value)
  textForm.areaIds = value
}
function createText() {
  textDialogVisible.value = true
  textForm = reactive({
    areaIds: [], // 区域ID列表
    ncFilePath: '', // 数值NC文件
    ncFileDate: ncFileList.value?.[0].data_time || '', //数值NC文件时间
    forecastRange: rangeList?.[0].value || '' //预报范围
  })
  // 选中第一个选项
  textForm.areaIds = ['1840995378123055105']
  // const firstAreaId = treeData.value?.[0].areaList?.[0].id as string | undefined
  // console.group('areaId');
  // console.log(!!firstAreaId);
  // console.groupEnd();
  // textForm.areaIds = firstAreaId ? [firstAreaId] : []
}
async function addText() {
  if (currentType.value === 1) {
    // 老版逻辑
    // createText()
    textGenerateLoading.value = true
    try {
      const alarmContent = await getAlarmContent()
      const alarmFind = panels.value.find(i => i.code === 'alarmContent')
      alarmFind && (alarmFind.text = alarmContent)

      const cityContent = await getCityContent()
      const cityFind = panels.value.find(i => i.code === 'cityAlarmContent')
      cityFind && (cityFind.text = cityContent)

      const defenseGuide = panels.value.find(i => i.code === 'defenseGuide')
      const smsContent = getSMSContent(alarmContent, defenseGuide?.text || '')
      const smsFind = panels.value.find(i => i.code === 'smsContent')
      smsFind && (smsFind.text = smsContent)
    } finally {
      textGenerateLoading.value = false
    }
  } else {
    referenceStation()
  }
}
const stationName = ref<any>('')
const stationTreeData = ref<any[]>([
  {
    id: 1,
    name: '站点1',
    childList: [
      {
        id: 2,
        name: '站点2'
      }
    ]
  }
])
const defaultProps = reactive({
  key: 'id',
  label: 'name',
  children: 'childList'
})
const checkedTreeKeys = ref<any[]>([])
function getStationTreeData() {
  stationTreeData.value = []
  Api.getStationTree({
    name: stationName.value,
    flag: 'station',
    code: 'oceanStation'
  })
    .then((res: IRegionTree[]) => {
      stationTreeData.value = res
      const treeManager = new ReferenceTreeManager(res)
      const cityRootNode = treeManager.getCityRootNode()
    })
    .catch(() => {
      stationTreeData.value = []
    })
}
const rowKey = (row: any) => row.id
const tableData = ref<any[]>([])

function handleStationTreeSelected(keys: any, option: any, meta: any) {
  checkedTreeKeys.value = keys
}

/**
 * 查询参考站数据 (查询按钮点击)
 */
function queryStationData() {
  Api.getStationData({
    ids: checkedTreeKeys.value.toString(),
    startTime: stationTime.value[0],
    endTime: stationTime.value[1],
    datum: 'datum'
  })
    .then((res: any) => {
      tableData.value = res
    })
    .catch(() => {})
}
const checkedRows = ref<any[]>([])
function handleCheck(rowKeys: any[], rows: any[]) {
  checkedRows.value = rows
}
function isNumber(input: any) {
  return /^-?\d+(\.\d+)?$/.test(input)
}
function sortAndRemoveDuplicates(ranges: any) {
  // 验证字符串是否是数字
  const isNumeric = (str: any) => !isNaN(str) && !isNaN(parseFloat(str))

  // 验证并计算范围
  const isValidRange = (range: any) => {
    const parts = range.split('-')
    return parts.length === 2 && parts.every(isNumeric)
  }

  // 过滤掉无效的范围
  const validRanges = ranges.filter(isValidRange)

  // 将有效范围字符串拆分为数字并存储在一个数组中
  const numbers = validRanges.flatMap((range: any) =>
    range.split('-').map(Number)
  )

  // 计算最大值和最小值
  const max = Math.max(...numbers)
  const min = Math.min(...numbers)

  return [min, max]
}
function generateStormText() {
  const forecastSurgeArr: any = []
  if (getMap) {
    getMap((map: any) => {
      map.getAllLayers().forEach((layer: any) => {
        const property = layer.getProperties()
        if (property && property.layerType === '选取工具文字') {
          console.log(property)
          layer
            .getSource()
            .getFeatures()
            .forEach((feature: any) => {
              console.log(feature)
              const featureInfo = feature.getProperties()
              if (featureInfo) {
                forecastSurgeArr.push(featureInfo.text)
              }
            })
        }
      })
    })
  }
  const sortedArray = sortAndRemoveDuplicates(forecastSurgeArr)
  let forecastSurge = ''
  if (forecastSurgeArr.length === 0) {
    forecastSurge = 'xx-xx厘米'
  } else {
    forecastSurge =
      sortedArray[0] + '-' + sortedArray[sortedArray.length - 1] + '厘米'
  }
  const params = {
    typhoonTime: model.typhoonTime, //typhoonTime
    releaseTime: model.releaseTime, //releaseTime
    alarmLevelId: model.alarmLevel, //alarmLevelId
    sourceType: model.sourceType, //sourceType
    typhoonNo: model.typhoonNo, //typhoonNo
    // 老版本
    // stationWarning: checkedRows.value,
    stationWarning: regionTableData.value.filter(i =>
      regionSelected.value.includes(i.id)
    ),
    forecastSurge: forecastSurge
  }
  console.log(params, '*************')
  if (!messageReactive) {
    messageReactive = message.info('文字生成中请稍候...', {
      duration: 0
    })
  }
  const alarmContent = panels.value.find(i => i.code === 'alarmContent')
  const defenseGuide = panels.value.find(i => i.code === 'defenseGuide')
  const smsContent = panels.value.find(i => i.code === 'smsContent')
  const cityAlarmContent = panels.value.find(i => i.code === 'cityAlarmContent')
  // 获取防御指南
  Api.getAlarmDefenseGuide(currentType.value)
    .then((res: any) => {
      // console.log(res)
      if (res.guideLevelList && res.guideLevelList.length > 0) {
        res.guideLevelList.forEach((item: any) => {
          if (item.levelId == model.alarmLevel) {
            defenseGuide && (defenseGuide.text = item.guideContent)
          }
        })
      } else {
        defenseGuide && (defenseGuide.text = '')
      }
    })
    .catch(() => {
      defenseGuide && (defenseGuide.text = '')
    })
  Api.generateStormText(params)
    .then((res: any) => {
      alarmContent && (alarmContent.text = res.alarmContent)
      smsContent && (smsContent.text = res.smsContent)
      if (messageReactive) {
        messageReactive.destroy()
        messageReactive = null
      }
      referenceStationVisible.value = false
      // return getCityContent()
    })
    // .then((cityContent: string) => {
    //   const cityFind = panels.value.find(i => i.code === 'cityAlarmContent')
    //   cityFind && (cityFind.text = cityContent)
    // })
    .catch((err: any) => {
      console.warn(err)
      if (messageReactive) {
        messageReactive.destroy()
        messageReactive = null
      }
      message.error('生成失败')
      alarmContent && (alarmContent.text = '')
      smsContent && (smsContent.text = '')
      // panels.value.forEach((item: any) => {
      //   item.text = ''
      // })
      textDialogVisible.value = false
    })
}
function referenceStation() {
  referenceStationVisible.value = true
  getStationTreeData()
}
const ncFileList = ref<any[]>([])
const rangeList = [
  {
    label: '0-24',
    value: '0-24'
  },
  {
    label: '24-48',
    value: '24-48'
  }
]
let ncList: any = []
function getNcFileList() {
  ncFileList.value = []
  Api.getNcFileList({
    dataSource: 'forecast',
    productCode: 'regWave',
    num: 3
  })
    .then((res: any) => {
      console.log(res)
      res.forEach((item: any) => {
        ncFileList.value.push({
          data_time: item
        })
      })
      // ncFileList.value = res
      // ncList = res
    })
    .catch(() => {})
}
function generate() {
  const params = {
    typhoonTime: model.typhoonTime,
    releaseTime: model.releaseTime,
    areaIds: textForm.areaIds,
    // ncFilePath: ncInfo?.file_path,
    ncFileDate: textForm.ncFileDate,
    alarmLevelId: model.alarmLevel,
    sourceType: model.sourceType,
    typhoonNo: model.typhoonNo,
    typhoonStrong: curSelectWarning.value.typhoonStrong,
    forecastStartTime: textForm.forecastRange.split('-')[0],
    forecastEndTime: textForm.forecastRange.split('-')[1]
  }
  if (!messageReactive) {
    messageReactive = message.info('文字生成中请稍候...', {
      duration: 0
    })
  }
  const alarmContent = panels.value.find(i => i.code === 'alarmContent')
  const defenseGuide = panels.value.find(i => i.code === 'defenseGuide')
  const smsContent = panels.value.find(i => i.code === 'smsContent')
  const cityAlarmContent = panels.value.find(i => i.code === 'cityAlarmContent')
  // 获取防御指南
  Api.getAlarmDefenseGuide(currentType.value)
    .then((res: any) => {
      // console.log(res)
      if (res.guideLevelList && res.guideLevelList.length > 0) {
        res.guideLevelList.forEach((item: any) => {
          if (item.levelId == model.alarmLevel) {
            defenseGuide && (defenseGuide.text = item.guideContent)
          }
        })
      } else {
        defenseGuide && (defenseGuide.text = '')
      }
    })
    .catch(() => {
      defenseGuide && (defenseGuide.text = '')
    })
  Api.generateText(params)
    .then((res: any) => {
      alarmContent && (alarmContent.text = res.alarmContent)
      smsContent && (smsContent.text = res.smsContent)
      if (messageReactive) {
        messageReactive.destroy()
        messageReactive = null
      }
      textDialogVisible.value = false
    })
    .catch(() => {
      if (messageReactive) {
        messageReactive.destroy()
        messageReactive = null
      }
      message.error('生成失败')
      alarmContent && (alarmContent.text = '')
      smsContent && (smsContent.text = '')
      // panels.value.forEach((item: any) => {
      //   item.text = ''
      // })
      textDialogVisible.value = false
    })
}
let imgUrl = ref('')
onMounted(async () => {
  const originalStr = '{"page":{"pageNum":1,"pageSize":5683}}'
  const base64Str = btoa(originalStr) // 编码
  console.log(base64Str, 'base64Str')
  //appkey 密钥
  //b46c63d550525e8305a32e3abfdf3a7491fd9e65          cf2d3553fb3a413f9cf27a8bb0919ddf
  //appKey&秘钥&appParam
  const str =
    'b46c63d550525e8305a32e3abfdf3a7491fd9e65&cf2d3553fb3a413f9cf27a8bb0919ddf&' +
    base64Str
  const appSign = CryptoJS.MD5(str).toString()
  const params = 'b46c63d550525e8305a32e3abfdf3a7491fd9e65'
  // platformApi
  //   .getLayerInfo({
  //     appKey: 'b46c63d550525e8305a32e3abfdf3a7491fd9e65',
  //     appParam: base64Str,
  //     appSign: appSign
  //   })
  //   .then((res: any) => {
  //     console.log(res, '******')
  //   })
  //   .catch(() => {})
  localStorage.setItem('alarmType', currentType.value.toString())
  getTreeData()
  getNcFileList()
  imgUrl.value = config.onlyOfficeServerUrl
  await getRightList()
  await getTyList()
  getUserList()
  getDefenseGuideList()
})
defineExpose({
  addWarningImage,
  closeLoading
})
</script>

<style lang="scss">
.red {
  background: #f90102;
}
.orange {
  background: #fe7b0e;
}
.yellow {
  background: #fff300;
}
.blue {
  background: #0083fd;
}
.grey {
  background: #c4c4c4;
}
.alarm-panel {
  width: 650px;
  position: fixed;
  left: calc(100% - 650px);
  top: 70px;
  z-index: 99;
  background: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  height: 85%;
  .panel-header {
    box-sizing: border-box;
    padding: 10px 15px;
    background: linear-gradient(180deg, #ffffff 0%, rgba(0, 0, 0, 0) 100%);
    border-radius: 8px 0px 0px 0px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      line-height: 21px;
      display: flex;
      align-items: center;
      i {
        display: inline-block;
        width: 20px;
        height: 20px;
      }
    }
    .btn-item {
      width: 90px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: rgba(34, 34, 34, 1);
      line-height: 16px;
      text-align: center;
      padding: 7px 0 6px;

      position: relative;
      &::before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: url(src/assets/images/alarm/border.png) no-repeat;
        background-size: 100% 100%;
      }
      &:nth-child(1)::before {
        transform: rotate(180deg);
      }
      &:nth-child(2) {
        margin-left: -10px;
      }
      &.active {
        color: #0073ce;
        &::before {
          background: url(src/assets/images/alarm/active-border.png) no-repeat;
          background-size: 100% 100%;
        }
        &:nth-child(1)::before {
          transform: rotate(0);
        }
        &:nth-child(2)::before {
          transform: rotate(180deg);
        }
      }
    }
  }
  .panel-content {
    box-sizing: border-box;
    padding: 6px 15px 15px;
    overflow-y: auto;
    height: calc(100% - 60px);
  }
  .column-header {
    box-sizing: border-box;
    padding: 6px 0;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      padding-left: 10px;
      position: relative;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 3px;
        height: 19px;
        background: rgba(64, 124, 242, 1);
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
  .history-alarms {
    box-sizing: border-box;
    padding: 8px 8px 19px;
    display: flex;
    background: #fafcfe;
    border-radius: 4px;
    border: 1px solid #e7f0fb;
    margin-bottom: 28px;
    flex-direction: row-reverse;
    justify-content: start;
    .history-alarm-item {
      width: calc(25% - 7px);
      margin-right: 8px;
      background: #ffffff;
      box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      border: 1px solid rgba(1, 1, 1, 0);
      overflow: hidden;
      &.submited {
        position: relative;
        &::before {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          border: 12px solid #47bb52;
          border-bottom-color: transparent;
          border-left-color: transparent;
        }
        &::after {
          content: '';
          width: 5px;
          height: 2px;
          position: absolute;
          right: 2px;
          top: 4px;
          border: 2px solid #fff;
          border-bottom-color: transparent;
          border-left-color: transparent;
          transform: rotate(125deg);
        }
      }
      // &:nth-last-child(1) {
      //   margin-right: 0;
      // }
      &.active {
        background: #dae8ff;
        border-radius: 4px;
        border: 1px solid #357fff;
      }
      .img-wrap {
        display: flex;
        box-sizing: border-box;
        padding: 15px 16px 0 12px;
        flex: 1;
        margin-bottom: 10px;
        & > div {
          width: 100%;
          height: 40px;
          // background: #0073ce;
          border-radius: 4px;
          border-top-left-radius: 30px;
          border-bottom-left-radius: 30px;
        }
        img {
          width: 47px;
          height: 47px;
        }
        .info {
          flex: 1;
          h3 {
            font-family: Noto Sans SC, Noto Sans SC;
            font-weight: bold;
            font-size: 17px;
            color: #ffffff;
            line-height: 20px;
            // border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            padding: 3px 0;
          }
          span {
            font-family: Noto Sans SC, Noto Sans SC;
            font-weight: 400;
            font-size: 10px;
            color: #ffffff;
            line-height: 12px;
            padding: 3px 0;
            &:nth-of-type(1) {
              border-right: 1px solid rgba(255, 255, 255, 0.3);
              padding: 3px;
            }
          }
        }
      }
      .submit-time {
        font-family: Noto Sans SC, Noto Sans SC;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 14px;
        margin-bottom: 9px;
        text-align: center;
        span {
          color: #eb1d1d;
          margin-right: 5px;
        }
      }
      .submit-status {
        display: flex;
        align-items: center;
        span {
          flex: 1;
          // background: rgba(0, 115, 206, 1);
          font-family: Noto Sans SC, Noto Sans SC;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 14px;
          display: inline-block;
          height: 26px;
          line-height: 26px;
          text-align: center;
        }
      }
    }
  }

  .alarm-editor {
    box-sizing: border-box;
    padding: 15px 15px 6px;
    background: #fafcfe;
    border-radius: 4px;
    border: 1px solid #e7f0fb;
    margin-bottom: 28px;
    overflow: hidden;

    .status {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      margin-right: 10px;
      &.icon1 {
        background: rgba(231, 56, 80, 1);
      }
      &.icon2 {
        background: rgba(235, 133, 54, 1);
      }
      &.icon3 {
        background: rgba(245, 201, 71, 1);
      }
      &.active {
        border: 2px solid #000000;
      }
      &:not(:nth-last-child(1)) {
        position: relative;
        margin-right: 20px;
        &::after {
          content: '>';
          position: absolute;
          color: #666666;
          right: -13px;
        }
      }
    }
    .level {
      width: 20px;
      height: 20px;
      margin-right: 4px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      &.active {
        border: 2px solid #000000;
      }
    }
  }
  .n-tabs.n-tabs--top .n-tab-pane {
    padding-top: 0;
    .n-input__state-border,
    .n-input__border {
      border-top: none;
    }
  }
  .upload-container {
    box-sizing: border-box;
    padding: 9px 12px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    display: flex;
    .images {
      display: flex;
      flex-direction: column;
      justify-content: end;
      width: 103px;
      align-items: flex-end;
      position: relative;
      height: 105px;
      margin-right: 10px;
      .empty-img {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .add-icon {
          width: 24px;
          height: 24px;
          margin-bottom: 10px;
        }
      }
      .delete-icon {
        position: absolute;
        top: -6px;
        right: -12px;
        height: 24px;
        width: 24px;
      }
    }
  }
}
.alarm-open-icon {
  box-sizing: border-box;
  padding: 5px;
  position: absolute;
  right: 0;
  top: 23px;
  z-index: 98;
  background: #fff;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  display: flex;
  align-items: center;
  i {
    margin-right: 5px;
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
    -webkit-transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
  }
}

.fadeInRight {
  animation: fadeInRight 0.3s ease-in-out forwards;
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
    display: block;
  }

  to {
    opacity: 0;
    display: none;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

.fadeOutRight {
  animation: fadeOutRight 0.3s ease-in-out forwards;
}

// @keyframes fadeOutRight {
//   0% {
//     opacity: 1;
//   }
//   to {
//     opacity: 0;
//     -webkit-transform: translate3d(100%, 0, 0);
//     transform: translate3d(100%, 0, 0);
//   }
// }
.word-content {
  height: 700px;
}
.reference-container {
  padding: 10px;
  height: 600px;
  display: flex;
  .reference-left {
    width: 250px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .query-info {
      margin-bottom: 5px;
      display: flex;
      justify-content: center;
    }
    .station-tree {
      flex: 1;
      height: 0;
      overflow: auto;
    }
  }
  .reference-right {
    padding-left: 10px;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-query-wrap {
      flex: 0 0 50px;
    }

    .table-container {
      flex: 1;
      height: 456px;
    }
    p {
      line-height: 25px;
      font-size: 15px;
    }
    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #104cc0;
    }
  }
}
.forecast-preview-wrap {
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: 12px;
  overflow-x: auto;
}
</style>
