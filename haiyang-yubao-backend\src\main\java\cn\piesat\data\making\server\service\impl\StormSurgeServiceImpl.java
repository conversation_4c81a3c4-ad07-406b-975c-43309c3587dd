package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.StormSurgeDao;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.entity.StormSurge;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.service.StormSurgeService;
import cn.piesat.data.making.server.vo.TideDailyHourDataVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 区域表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class StormSurgeServiceImpl extends ServiceImpl<StormSurgeDao, StormSurge> implements StormSurgeService {

    @Resource
    private StationService stationService;

    @Override
    public List<TideDailyHourDataVO> getTideList(String stationNum, Date startTime, Date endTime) {
        List<TideDailyHourDataVO> resultList = new ArrayList<>();

        LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Station::getCode, stationNum);
        Station station = stationService.getOne(queryWrapper);
        if (station == null) {
            return resultList;
        }
        LambdaQueryWrapper<StormSurge> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StormSurge::getStationCode, stationNum);
        wrapper.between(StormSurge::getTime, startTime, endTime);
        List<StormSurge> list = this.list(wrapper);
        for (StormSurge stormSurge : list) {
            TideDailyHourDataVO tideDailyHourData = new TideDailyHourDataVO();
            tideDailyHourData.setStationId(station.getId().toString());
            tideDailyHourData.setStationNum(stationNum);
            tideDailyHourData.setStationName(stormSurge.getStationName());
            tideDailyHourData.setTideTime(stormSurge.getTime());
            tideDailyHourData.setHeight(stormSurge.getValue() + "");
            resultList.add(tideDailyHourData);
        }

        resultList.sort(Comparator.comparing(TideDailyHourDataVO::getTideTime));
        return resultList;
    }
}





