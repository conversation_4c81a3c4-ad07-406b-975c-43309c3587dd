!(function (e) {
  function t(e) {
    delete installedChunks[e]
  }
  function n(e) {
    var t = document.getElementsByTagName('head')[0],
      n = document.createElement('script')
    ;(n.type = 'text/javascript'),
      (n.charset = 'utf-8'),
      (n.src = l.p + '' + e + '.' + _ + '.hot-update.js'),
      t.appendChild(n)
  }
  function r(e) {
    return (
      (e = e || 1e4),
      new Promise(function (t, n) {
        if ('undefined' == typeof XMLHttpRequest)
          return n(new Error('No browser support'))
        try {
          var r = new XMLHttpRequest(),
            o = l.p + '' + _ + '.hot-update.json'
          r.open('GET', o, !0), (r.timeout = e), r.send(null)
        } catch (e) {
          return n(e)
        }
        r.onreadystatechange = function () {
          if (4 === r.readyState)
            if (0 === r.status)
              n(new Error('Manifest request to ' + o + ' timed out.'))
            else if (404 === r.status) t()
            else if (200 !== r.status && 304 !== r.status)
              n(new Error('Manifest request to ' + o + ' failed.'))
            else {
              try {
                var e = JSON.parse(r.responseText)
              } catch (e) {
                return void n(e)
              }
              t(e)
            }
        }
      })
    )
  }
  function o(e) {
    var t = P[e]
    if (!t) return l
    var n = function (n) {
      return (
        t.hot.active
          ? (P[n]
              ? P[n].parents.indexOf(e) < 0 && P[n].parents.push(e)
              : ((E = [e]), (v = n)),
            t.children.indexOf(n) < 0 && t.children.push(n))
          : (console.warn(
              '[HMR] unexpected require(' + n + ') from disposed module ' + e
            ),
            (E = [])),
        l(n)
      )
    }
    for (var r in l)
      Object.prototype.hasOwnProperty.call(l, r) &&
        'e' !== r &&
        Object.defineProperty(
          n,
          r,
          (function (e) {
            return {
              configurable: !0,
              enumerable: !0,
              get: function () {
                return l[e]
              },
              set: function (t) {
                l[e] = t
              }
            }
          })(r)
        )
    return (
      (n.e = function (e) {
        function t() {
          C--, 'prepare' === L && (D[e] || p(e), 0 === C && 0 === j && d())
        }
        return (
          'ready' === L && s('prepare'),
          C++,
          l.e(e).then(t, function (e) {
            throw (t(), e)
          })
        )
      }),
      n
    )
  }
  function i(e) {
    var t = {
      _acceptedDependencies: {},
      _declinedDependencies: {},
      _selfAccepted: !1,
      _selfDeclined: !1,
      _disposeHandlers: [],
      _main: v !== e,
      active: !0,
      accept: function (e, n) {
        if (void 0 === e) t._selfAccepted = !0
        else if ('function' == typeof e) t._selfAccepted = e
        else if ('object' == typeof e)
          for (var r = 0; r < e.length; r++)
            t._acceptedDependencies[e[r]] = n || function () {}
        else t._acceptedDependencies[e] = n || function () {}
      },
      decline: function (e) {
        if (void 0 === e) t._selfDeclined = !0
        else if ('object' == typeof e)
          for (var n = 0; n < e.length; n++) t._declinedDependencies[e[n]] = !0
        else t._declinedDependencies[e] = !0
      },
      dispose: function (e) {
        t._disposeHandlers.push(e)
      },
      addDisposeHandler: function (e) {
        t._disposeHandlers.push(e)
      },
      removeDisposeHandler: function (e) {
        var n = t._disposeHandlers.indexOf(e)
        n >= 0 && t._disposeHandlers.splice(n, 1)
      },
      check: a,
      apply: f,
      status: function (e) {
        if (!e) return L
        k.push(e)
      },
      addStatusHandler: function (e) {
        k.push(e)
      },
      removeStatusHandler: function (e) {
        var t = k.indexOf(e)
        t >= 0 && k.splice(t, 1)
      },
      data: O[e]
    }
    return (v = void 0), t
  }
  function s(e) {
    L = e
    for (var t = 0; t < k.length; t++) k[t].call(null, e)
  }
  function c(e) {
    return +e + '' === e ? +e : e
  }
  function a(e) {
    if ('idle' !== L) throw new Error('check() is only allowed in idle status')
    return (
      (w = e),
      s('check'),
      r(b).then(function (e) {
        if (!e) return s('idle'), null
        ;(I = {}), (D = {}), (T = e.c), (g = e.h), s('prepare')
        var t = new Promise(function (e, t) {
          y = { resolve: e, reject: t }
        })
        return (m = {}), p(0), 'prepare' === L && 0 === C && 0 === j && d(), t
      })
    )
  }
  function u(e, t) {
    if (T[e] && I[e]) {
      I[e] = !1
      for (var n in t)
        Object.prototype.hasOwnProperty.call(t, n) && (m[n] = t[n])
      0 == --j && 0 === C && d()
    }
  }
  function p(e) {
    T[e] ? ((I[e] = !0), j++, n(e)) : (D[e] = !0)
  }
  function d() {
    s('ready')
    var e = y
    if (((y = null), e))
      if (w)
        Promise.resolve()
          .then(function () {
            return f(w)
          })
          .then(
            function (t) {
              e.resolve(t)
            },
            function (t) {
              e.reject(t)
            }
          )
      else {
        var t = []
        for (var n in m)
          Object.prototype.hasOwnProperty.call(m, n) && t.push(c(n))
        e.resolve(t)
      }
  }
  function f(n) {
    function r(e, t) {
      for (var n = 0; n < t.length; n++) {
        var r = t[n]
        e.indexOf(r) < 0 && e.push(r)
      }
    }
    if ('ready' !== L)
      throw new Error('apply() is only allowed in ready status')
    n = n || {}
    var o,
      i,
      a,
      u,
      p,
      d = {},
      f = [],
      h = {},
      v = function () {
        console.warn(
          '[HMR] unexpected require(' + w.moduleId + ') to disposed module'
        )
      }
    for (var y in m)
      if (Object.prototype.hasOwnProperty.call(m, y)) {
        p = c(y)
        var w
        w = m[y]
          ? (function (e) {
              for (
                var t = [e],
                  n = {},
                  o = t.slice().map(function (e) {
                    return { chain: [e], id: e }
                  });
                o.length > 0;

              ) {
                var i = o.pop(),
                  s = i.id,
                  c = i.chain
                if ((u = P[s]) && !u.hot._selfAccepted) {
                  if (u.hot._selfDeclined)
                    return { type: 'self-declined', chain: c, moduleId: s }
                  if (u.hot._main)
                    return { type: 'unaccepted', chain: c, moduleId: s }
                  for (var a = 0; a < u.parents.length; a++) {
                    var p = u.parents[a],
                      d = P[p]
                    if (d) {
                      if (d.hot._declinedDependencies[s])
                        return {
                          type: 'declined',
                          chain: c.concat([p]),
                          moduleId: s,
                          parentId: p
                        }
                      t.indexOf(p) >= 0 ||
                        (d.hot._acceptedDependencies[s]
                          ? (n[p] || (n[p] = []), r(n[p], [s]))
                          : (delete n[p],
                            t.push(p),
                            o.push({ chain: c.concat([p]), id: p })))
                    }
                  }
                }
              }
              return {
                type: 'accepted',
                moduleId: e,
                outdatedModules: t,
                outdatedDependencies: n
              }
            })(p)
          : { type: 'disposed', moduleId: y }
        var b = !1,
          x = !1,
          k = !1,
          j = ''
        switch (
          (w.chain && (j = '\nUpdate propagation: ' + w.chain.join(' -> ')),
          w.type)
        ) {
          case 'self-declined':
            n.onDeclined && n.onDeclined(w),
              n.ignoreDeclined ||
                (b = new Error(
                  'Aborted because of self decline: ' + w.moduleId + j
                ))
            break
          case 'declined':
            n.onDeclined && n.onDeclined(w),
              n.ignoreDeclined ||
                (b = new Error(
                  'Aborted because of declined dependency: ' +
                    w.moduleId +
                    ' in ' +
                    w.parentId +
                    j
                ))
            break
          case 'unaccepted':
            n.onUnaccepted && n.onUnaccepted(w),
              n.ignoreUnaccepted ||
                (b = new Error('Aborted because ' + p + ' is not accepted' + j))
            break
          case 'accepted':
            n.onAccepted && n.onAccepted(w), (x = !0)
            break
          case 'disposed':
            n.onDisposed && n.onDisposed(w), (k = !0)
            break
          default:
            throw new Error('Unexception type ' + w.type)
        }
        if (b) return s('abort'), Promise.reject(b)
        if (x) {
          ;(h[p] = m[p]), r(f, w.outdatedModules)
          for (p in w.outdatedDependencies)
            Object.prototype.hasOwnProperty.call(w.outdatedDependencies, p) &&
              (d[p] || (d[p] = []), r(d[p], w.outdatedDependencies[p]))
        }
        k && (r(f, [w.moduleId]), (h[p] = v))
      }
    var C = []
    for (i = 0; i < f.length; i++)
      (p = f[i]),
        P[p] &&
          P[p].hot._selfAccepted &&
          C.push({ module: p, errorHandler: P[p].hot._selfAccepted })
    s('dispose'),
      Object.keys(T).forEach(function (e) {
        !1 === T[e] && t(e)
      })
    for (var D, I = f.slice(); I.length > 0; )
      if (((p = I.pop()), (u = P[p]))) {
        var M = {},
          S = u.hot._disposeHandlers
        for (a = 0; a < S.length; a++) (o = S[a])(M)
        for (
          O[p] = M, u.hot.active = !1, delete P[p], delete d[p], a = 0;
          a < u.children.length;
          a++
        ) {
          var H = P[u.children[a]]
          H && (D = H.parents.indexOf(p)) >= 0 && H.parents.splice(D, 1)
        }
      }
    var R, A
    for (p in d)
      if (Object.prototype.hasOwnProperty.call(d, p) && (u = P[p]))
        for (A = d[p], a = 0; a < A.length; a++)
          (R = A[a]),
            (D = u.children.indexOf(R)) >= 0 && u.children.splice(D, 1)
    s('apply'), (_ = g)
    for (p in h) Object.prototype.hasOwnProperty.call(h, p) && (e[p] = h[p])
    var N = null
    for (p in d)
      if (Object.prototype.hasOwnProperty.call(d, p) && (u = P[p])) {
        A = d[p]
        var U = []
        for (i = 0; i < A.length; i++)
          if (((R = A[i]), (o = u.hot._acceptedDependencies[R]))) {
            if (U.indexOf(o) >= 0) continue
            U.push(o)
          }
        for (i = 0; i < U.length; i++) {
          o = U[i]
          try {
            o(A)
          } catch (e) {
            n.onErrored &&
              n.onErrored({
                type: 'accept-errored',
                moduleId: p,
                dependencyId: A[i],
                error: e
              }),
              n.ignoreErrored || N || (N = e)
          }
        }
      }
    for (i = 0; i < C.length; i++) {
      var F = C[i]
      ;(p = F.module), (E = [p])
      try {
        l(p)
      } catch (e) {
        if ('function' == typeof F.errorHandler)
          try {
            F.errorHandler(e)
          } catch (t) {
            n.onErrored &&
              n.onErrored({
                type: 'self-accept-error-handler-errored',
                moduleId: p,
                error: t,
                orginalError: e,
                originalError: e
              }),
              n.ignoreErrored || N || (N = t),
              N || (N = e)
          }
        else
          n.onErrored &&
            n.onErrored({ type: 'self-accept-errored', moduleId: p, error: e }),
            n.ignoreErrored || N || (N = e)
      }
    }
    return N
      ? (s('fail'), Promise.reject(N))
      : (s('idle'),
        new Promise(function (e) {
          e(f)
        }))
  }
  function l(t) {
    if (P[t]) return P[t].exports
    var n = (P[t] = {
      i: t,
      l: !1,
      exports: {},
      hot: i(t),
      parents: ((x = E), (E = []), x),
      children: []
    })
    return e[t].call(n.exports, n, n.exports, o(t)), (n.l = !0), n.exports
  }
  var h = window.webpackHotUpdate
  window.webpackHotUpdate = function (e, t) {
    u(e, t), h && h(e, t)
  }
  var v,
    y,
    m,
    g,
    w = !0,
    _ = 'ec974ff3a8ec85543cc4',
    b = 1e4,
    O = {},
    E = [],
    x = [],
    k = [],
    L = 'idle',
    j = 0,
    C = 0,
    D = {},
    I = {},
    T = {},
    P = {}
  ;(l.m = e),
    (l.c = P),
    (l.d = function (e, t, n) {
      l.o(e, t) ||
        Object.defineProperty(e, t, {
          configurable: !1,
          enumerable: !0,
          get: n
        })
    }),
    (l.n = function (e) {
      var t =
        e && e.__esModule
          ? function () {
              return e.default
            }
          : function () {
              return e
            }
      return l.d(t, 'a', t), t
    }),
    (l.o = function (e, t) {
      return Object.prototype.hasOwnProperty.call(e, t)
    }),
    (l.p = '/'),
    (l.h = function () {
      return _
    }),
    o(0)((l.s = 0))
})([
  function (e, t, n) {
    var r = n(1)
    !(function (e) {
      var t = new r()
      e.ssoClient = t
    })(window)
  },
  function (e, t, n) {
    var r = (n(2), n(3)),
      o = {
        passport_baseURL: 'http://10.25.23.56',
        login_uri: '/oauth_sso/login',
        check_uri: '/api/checkLogin',
        check_options: { auto: !0, timer: 6e4 }
      },
      i = function () {
        this.config = o
        var e = n(4)
        this.emitter = new e.EventEmitter()
      }
    ;(i.prototype.init = function (e) {
      this.config = Object.assign({}, o, e)
      var t = this
      this.config.check_callback ||
        (this.config.check_callback = function (e) {
          if (!e.success) {
            var n = window.location.href
            console.log(n),
              window.location.replace(
                t.config.passport_baseURL +
                  t.config.login_uri +
                  '?redirect_url=' +
                  n
              )
          }
        }),
        (this.config.check_options = o.check_options),
        this.emitter.on('passportSuccess', function () {
          t.config.check_options.auto && t.startCheckLogin.call(t)
        }),
        this.loadPassport()
    }),
      (i.prototype.loadPassport = function () {
        var e = this.config.passport_baseURL,
          t = (window.location.href, this),
          n = this.getCookie('passport')
        ;(t._ssoDomain = n || e), t.emitter.emit('passportSuccess')
      }),
      (i.prototype.getCookie = function (e) {
        var t,
          n = new RegExp('(^| )' + e + '=([^;]*)(;|$)')
        return (t = document.cookie.match(n)) ? unescape(t[2]) : null
      }),
      (i.prototype.startCheckLogin = function () {
        var e = this,
          t = this.config.check_options,
          n = t.timer,
          r = this.config.check_callback
        this.intervalTask = setInterval(function () {
          e.checkLogin.call(e, r)
        }, n)
      }),
      (i.prototype.checkLogin = function (e) {
        var t = this,
          n = this.ssoDomain() || null
        if (!n) throw 'ssoDomain is null'
        var r = this.getCookie('sso-check-t'),
          o = new Date().getTime(),
          i = this.config.check_options.timer
        if (r && o - r < i)
          return void (
            this.getCookie('u-token') ||
            t.emitter.emit('checkComplete', {
              success: !1,
              resultCode: 1101,
              message: 'token is null'
            })
          )
        null != r &&
          (document.cookie =
            'sso-check-t=' + r + ';expires=' + new Date().toGMTString()),
          this.ssoDomain(),
          (document.cookie = 'sso-check-t=' + o + '; path=/'),
          this.ajax({
            crossDomain: !0,
            url: n + this.config.check_uri,
            dataType: 'jsonp',
            jsonp: 'callback',
            type: 'GET',
            data: {},
            success: function (r) {
              console.log('res' + JSON.stringify(r)),
                t.emitter.emit('checkComplete', r),
                e && 'function' == typeof e && e(r, n)
            },
            error: function (e) {}
          })
      }),
      (i.prototype.logout = function (e) {
        var t = this,
          n = this.ssoDomain()
        if (n) {
          var r = n + '/api/v1/logout'
          this.ajax({
            url: r,
            dataType: 'jsonp',
            jsonp: 'callback',
            type: 'GET',
            success: function (r) {
              t.emitter.emit('logoutComplete', r),
                e && 'function' == typeof e && e(r, n)
            }
          })
        }
      }),
      (i.prototype.addCheckCompleteListener = function (e) {
        e && 'function' == typeof e && this.emitter.on('checkComplete', e)
      }),
      (i.prototype.addLogoutCompleteListener = function (e) {
        e && 'function' == typeof e && this.emitter.on('logoutComplete', e)
      }),
      (i.prototype.ssoDomain = function () {
        return this._ssoDomain
      }),
      (i.prototype.ajax = function (e) {
        'Edge' == this.IEVersion() || '-1' == this.IEVersion()
          ? r(e)
          : jQuery.ajax(e)
      }),
      (i.prototype.browserType = function () {
        var e = navigator.userAgent,
          t = e.indexOf('Opera') > -1,
          n = e.indexOf('compatible') > -1 && e.indexOf('MSIE') > -1 && !t,
          r = e.indexOf('Windows NT 6.1; Trident/7.0;') > -1 && !n,
          o = e.indexOf('Firefox') > -1,
          i = e.indexOf('Safari') > -1 && -1 == e.indexOf('Chrome'),
          s = e.indexOf('Chrome') > -1 && e.indexOf('Safari') > -1
        if (n) {
          new RegExp('MSIE (\\d+\\.\\d+);').test(e)
          var c = parseFloat(RegExp.$1)
          return 7 == c
            ? 'IE7'
            : 8 == c
            ? 'IE8'
            : 9 == c
            ? 'IE9'
            : 10 == c
            ? 'IE10'
            : 11 == c
            ? 'IE11'
            : '0'
        }
        return o
          ? 'FF'
          : t
          ? 'Opera'
          : i
          ? 'Safari'
          : s
          ? 'Chrome'
          : r
          ? 'Edge'
          : void 0
      }),
      (i.prototype.isIE = function () {
        var e = navigator.userAgent,
          t = e.indexOf('Opera') > -1
        return e.indexOf('compatible') > -1 && e.indexOf('MSIE') > -1 && !t
          ? '1'
          : '-1'
      }),
      (i.prototype.IEVersion = function () {
        var e = navigator.userAgent,
          t = e.indexOf('Opera') > -1,
          n = e.indexOf('compatible') > -1 && e.indexOf('MSIE') > -1 && !t,
          r = e.indexOf('Windows NT 6.1; Trident/7.0;') > -1 && !n
        if (n) {
          new RegExp('MSIE (\\d+\\.\\d+);').test(e)
          var o = parseFloat(RegExp.$1)
          return 7 == o
            ? 'IE7'
            : 8 == o
            ? 'IE8'
            : 9 == o
            ? 'IE9'
            : 10 == o
            ? 'IE10'
            : 11 == o
            ? 'IE11'
            : '0'
        }
        return r ? 'Edge' : '-1'
      }),
      (e.exports = i)
  },
  function (e, t) {
    var n = {}
    ;(n.getCookie = function (e) {
      return e &&
        0 != e.length &&
        document.cookie.length > 0 &&
        ((c_start = document.cookie.indexOf(e + '=')), -1 != c_start)
        ? ((c_start = c_start + e.length + 1),
          (c_end = document.cookie.indexOf(';', c_start)),
          -1 == c_end && (c_end = document.cookie.length),
          unescape(document.cookie.substring(c_start, c_end)))
        : null
    }),
      (e.exports = n)
  },
  function (e, t) {
    function n() {
      var e = {
        type: arguments[0].type || 'GET',
        url: arguments[0].url || '',
        async: arguments[0].async || 'true',
        data: arguments[0].data || {},
        dataType: arguments[0].dataType || 'text',
        contentType:
          arguments[0].contentType || 'application/x-www-form-urlencoded',
        beforeSend: arguments[0].beforeSend || function () {},
        success: arguments[0].success || function () {},
        error: arguments[0].error || function () {},
        jsonp: arguments[0].jsonp || null
      }
      'jsonp' == e.dataType ? o(e) : r(e)
    }
    function r(e) {
      e.beforeSend()
      var t = i()
      ;(t.responseType = e.dataType),
        t.open(e.type, e.url, e.async),
        t.setRequestHeader('Content-Type', e.contentType),
        t.send(s(e.data)),
        (t.onreadystatechange = function () {
          4 == t.readyState &&
            (200 == t.status ? e.success(t.response) : e.error(t))
        })
    }
    function o(e) {
      var t = e.jsonp,
        n = document.getElementsByTagName('head')[0],
        r = 'callback' + new Date().getTime()
      ;(window[r] = function (t) {
        n.removeChild(i),
          clearTimeout(i.timer),
          (window[r] = null),
          e.success && e.success(t)
      }),
        (e.data[t] = r)
      var o = s(e.data),
        i = document.createElement('script')
      i.readyState
        ? (i.onreadystatechange = function () {
            console.log(1111),
              /loaded|complete/i.test(i.readyState)
                ? (i.onreadystatechange = null)
                : (n.removeChild(i), e.error && e.error({ message: '超时' }))
          })
        : ((i.onload = function () {}),
          (i.onerror = function (t) {
            n.removeChild(i),
              e.error && e.error({ message: '超时' }),
              (window[r] = null)
          })),
        n.appendChild(i),
        (i.src = e.url + '?' + o),
        e.time &&
          (i.timer = setTimeout(function () {
            ;(window[r] = null),
              n.removeChild(i),
              e.error && e.error({ message: '超时' })
          }, time))
    }
    function i() {
      return window.ActiveXObject
        ? new ActiveXObject('Microsoft.XMLHTTP')
        : window.XMLHttpRequest
        ? new XMLHttpRequest()
        : void 0
    }
    function s(e) {
      if ('object' == typeof e) {
        var t = ''
        for (var n in e) t += n + '=' + e[n] + '&'
        return (t = t.substring(0, t.length - 1))
      }
      return e
    }
    e.exports = n
  },
  function (e, t, n) {
    'use strict'
    function r(e) {
      console && console.warn && console.warn(e)
    }
    function o() {
      o.init.call(this)
    }
    function i(e) {
      if ('function' != typeof e)
        throw new TypeError(
          'The "listener" argument must be of type Function. Received type ' +
            typeof e
        )
    }
    function s(e) {
      return void 0 === e._maxListeners
        ? o.defaultMaxListeners
        : e._maxListeners
    }
    function c(e, t, n, o) {
      var c, a, u
      if (
        (i(n),
        (a = e._events),
        void 0 === a
          ? ((a = e._events = Object.create(null)), (e._eventsCount = 0))
          : (void 0 !== a.newListener &&
              (e.emit('newListener', t, n.listener ? n.listener : n),
              (a = e._events)),
            (u = a[t])),
        void 0 === u)
      )
        (u = a[t] = n), ++e._eventsCount
      else if (
        ('function' == typeof u
          ? (u = a[t] = o ? [n, u] : [u, n])
          : o
          ? u.unshift(n)
          : u.push(n),
        (c = s(e)) > 0 && u.length > c && !u.warned)
      ) {
        u.warned = !0
        var p = new Error(
          'Possible EventEmitter memory leak detected. ' +
            u.length +
            ' ' +
            String(t) +
            ' listeners added. Use emitter.setMaxListeners() to increase limit'
        )
        ;(p.name = 'MaxListenersExceededWarning'),
          (p.emitter = e),
          (p.type = t),
          (p.count = u.length),
          r(p)
      }
      return e
    }
    function a() {
      if (!this.fired)
        return (
          this.target.removeListener(this.type, this.wrapFn),
          (this.fired = !0),
          0 === arguments.length
            ? this.listener.call(this.target)
            : this.listener.apply(this.target, arguments)
        )
    }
    function u(e, t, n) {
      var r = { fired: !1, wrapFn: void 0, target: e, type: t, listener: n },
        o = a.bind(r)
      return (o.listener = n), (r.wrapFn = o), o
    }
    function p(e, t, n) {
      var r = e._events
      if (void 0 === r) return []
      var o = r[t]
      return void 0 === o
        ? []
        : 'function' == typeof o
        ? n
          ? [o.listener || o]
          : [o]
        : n
        ? h(o)
        : f(o, o.length)
    }
    function d(e) {
      var t = this._events
      if (void 0 !== t) {
        var n = t[e]
        if ('function' == typeof n) return 1
        if (void 0 !== n) return n.length
      }
      return 0
    }
    function f(e, t) {
      for (var n = new Array(t), r = 0; r < t; ++r) n[r] = e[r]
      return n
    }
    function l(e, t) {
      for (; t + 1 < e.length; t++) e[t] = e[t + 1]
      e.pop()
    }
    function h(e) {
      for (var t = new Array(e.length), n = 0; n < t.length; ++n)
        t[n] = e[n].listener || e[n]
      return t
    }
    function v(e, t) {
      return new Promise(function (n, r) {
        function o(n) {
          e.removeListener(t, i), r(n)
        }
        function i() {
          'function' == typeof e.removeListener && e.removeListener('error', o),
            n([].slice.call(arguments))
        }
        m(e, t, i, { once: !0 }), 'error' !== t && y(e, o, { once: !0 })
      })
    }
    function y(e, t, n) {
      'function' == typeof e.on && m(e, 'error', t, n)
    }
    function m(e, t, n, r) {
      if ('function' == typeof e.on) r.once ? e.once(t, n) : e.on(t, n)
      else {
        if ('function' != typeof e.addEventListener)
          throw new TypeError(
            'The "emitter" argument must be of type EventEmitter. Received type ' +
              typeof e
          )
        e.addEventListener(t, function o(i) {
          r.once && e.removeEventListener(t, o), n(i)
        })
      }
    }
    var g,
      w = 'object' == typeof Reflect ? Reflect : null,
      _ =
        w && 'function' == typeof w.apply
          ? w.apply
          : function (e, t, n) {
              return Function.prototype.apply.call(e, t, n)
            }
    g =
      w && 'function' == typeof w.ownKeys
        ? w.ownKeys
        : Object.getOwnPropertySymbols
        ? function (e) {
            return Object.getOwnPropertyNames(e).concat(
              Object.getOwnPropertySymbols(e)
            )
          }
        : function (e) {
            return Object.getOwnPropertyNames(e)
          }
    var b =
      Number.isNaN ||
      function (e) {
        return e !== e
      }
    ;(e.exports = o),
      (e.exports.once = v),
      (o.EventEmitter = o),
      (o.prototype._events = void 0),
      (o.prototype._eventsCount = 0),
      (o.prototype._maxListeners = void 0)
    var O = 10
    Object.defineProperty(o, 'defaultMaxListeners', {
      enumerable: !0,
      get: function () {
        return O
      },
      set: function (e) {
        if ('number' != typeof e || e < 0 || b(e))
          throw new RangeError(
            'The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' +
              e +
              '.'
          )
        O = e
      }
    }),
      (o.init = function () {
        ;(void 0 !== this._events &&
          this._events !== Object.getPrototypeOf(this)._events) ||
          ((this._events = Object.create(null)), (this._eventsCount = 0)),
          (this._maxListeners = this._maxListeners || void 0)
      }),
      (o.prototype.setMaxListeners = function (e) {
        if ('number' != typeof e || e < 0 || b(e))
          throw new RangeError(
            'The value of "n" is out of range. It must be a non-negative number. Received ' +
              e +
              '.'
          )
        return (this._maxListeners = e), this
      }),
      (o.prototype.getMaxListeners = function () {
        return s(this)
      }),
      (o.prototype.emit = function (e) {
        for (var t = [], n = 1; n < arguments.length; n++) t.push(arguments[n])
        var r = 'error' === e,
          o = this._events
        if (void 0 !== o) r = r && void 0 === o.error
        else if (!r) return !1
        if (r) {
          var i
          if ((t.length > 0 && (i = t[0]), i instanceof Error)) throw i
          var s = new Error(
            'Unhandled error.' + (i ? ' (' + i.message + ')' : '')
          )
          throw ((s.context = i), s)
        }
        var c = o[e]
        if (void 0 === c) return !1
        if ('function' == typeof c) _(c, this, t)
        else
          for (var a = c.length, u = f(c, a), n = 0; n < a; ++n)
            _(u[n], this, t)
        return !0
      }),
      (o.prototype.addListener = function (e, t) {
        return c(this, e, t, !1)
      }),
      (o.prototype.on = o.prototype.addListener),
      (o.prototype.prependListener = function (e, t) {
        return c(this, e, t, !0)
      }),
      (o.prototype.once = function (e, t) {
        return i(t), this.on(e, u(this, e, t)), this
      }),
      (o.prototype.prependOnceListener = function (e, t) {
        return i(t), this.prependListener(e, u(this, e, t)), this
      }),
      (o.prototype.removeListener = function (e, t) {
        var n, r, o, s, c
        if ((i(t), void 0 === (r = this._events))) return this
        if (void 0 === (n = r[e])) return this
        if (n === t || n.listener === t)
          0 == --this._eventsCount
            ? (this._events = Object.create(null))
            : (delete r[e],
              r.removeListener &&
                this.emit('removeListener', e, n.listener || t))
        else if ('function' != typeof n) {
          for (o = -1, s = n.length - 1; s >= 0; s--)
            if (n[s] === t || n[s].listener === t) {
              ;(c = n[s].listener), (o = s)
              break
            }
          if (o < 0) return this
          0 === o ? n.shift() : l(n, o),
            1 === n.length && (r[e] = n[0]),
            void 0 !== r.removeListener &&
              this.emit('removeListener', e, c || t)
        }
        return this
      }),
      (o.prototype.off = o.prototype.removeListener),
      (o.prototype.removeAllListeners = function (e) {
        var t, n, r
        if (void 0 === (n = this._events)) return this
        if (void 0 === n.removeListener)
          return (
            0 === arguments.length
              ? ((this._events = Object.create(null)), (this._eventsCount = 0))
              : void 0 !== n[e] &&
                (0 == --this._eventsCount
                  ? (this._events = Object.create(null))
                  : delete n[e]),
            this
          )
        if (0 === arguments.length) {
          var o,
            i = Object.keys(n)
          for (r = 0; r < i.length; ++r)
            'removeListener' !== (o = i[r]) && this.removeAllListeners(o)
          return (
            this.removeAllListeners('removeListener'),
            (this._events = Object.create(null)),
            (this._eventsCount = 0),
            this
          )
        }
        if ('function' == typeof (t = n[e])) this.removeListener(e, t)
        else if (void 0 !== t)
          for (r = t.length - 1; r >= 0; r--) this.removeListener(e, t[r])
        return this
      }),
      (o.prototype.listeners = function (e) {
        return p(this, e, !0)
      }),
      (o.prototype.rawListeners = function (e) {
        return p(this, e, !1)
      }),
      (o.listenerCount = function (e, t) {
        return 'function' == typeof e.listenerCount
          ? e.listenerCount(t)
          : d.call(e, t)
      }),
      (o.prototype.listenerCount = d),
      (o.prototype.eventNames = function () {
        return this._eventsCount > 0 ? g(this._events) : []
      })
  }
])
