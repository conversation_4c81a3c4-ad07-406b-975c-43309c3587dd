package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmPublicTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Calendar;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicTaskDaoTest {

    @Autowired
    private FmPublicTaskDao fmPublicTaskDaoImpl;

    @Test
    public void testSave(){
        FmPublicTask fmPublicTask = new FmPublicTask();

        fmPublicTask.setName("name");
        fmPublicTask.setPublicType("1");
        fmPublicTask.setSaveTime(Calendar.getInstance().getTime());

        fmPublicTaskDaoImpl.insert(fmPublicTask);
    }
}
