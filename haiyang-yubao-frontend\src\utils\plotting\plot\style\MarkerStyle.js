/*
 * @Author: xuli<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-17 17:55:05
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2024-10-19 11:35:10
 * @FilePath: \hainan-jianzai-web\src\utils\plotting\plot\style\MarkerStyle.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import FTStyle from "./Style";
import Style from "ol/style/Style";
import Icon from "ol/style/Icon";
import Text from "ol/style/Text";
import img from "src/assets/images/marker-begin.png"
import img1 from "src/assets/images/weather.png"
class MarkerStyle extends FTStyle {
  /**
   * @class MarkerStyle
   * @classdesc 点类样式
   * @extends {FTStyle}
   * <AUTHOR>
   * @constructs
   */
  constructor(type) {
    super();
    this.src = "";
    this.offset = [0, 0];
    if (type == "marker") {
      this.src = img
    } else {
      this.src = img1
      if (type == "mete_rising") {
        this.offset = [224, 496];
      }
      if (type == "mete_cooling") {
        this.offset = [768, 496];
      }
      if (type == "mete_typhon") {
        this.offset = [640, 496];
      }
    }
    this._style = {
      image: {
        // --ol.Image 的全部属性
        icon: {
          src: this.src,
          offset: this.offset,
          size: [32, 40],
          opacity: 1,
          scale: 1,
          anchor: [0.5, 0.3]
        }
      }
    };
  }
  parse() {
    let image = null;

    if (this._style.image) {
      if (this._style.image.icon) {
        image = new Icon(this._style.image.icon);
      }
    }

    return new Style({
      image: image
    });
  }
}
export default MarkerStyle;
