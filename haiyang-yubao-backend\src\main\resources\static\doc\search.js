let api = [];
const apiDocListSize = 1
api.push({
    name: 'default',
    order: '1',
    list: []
})
api[0].list.push({
    alias: 'AlarmController',
    order: '1',
    link: '外部接口-警报',
    desc: '外部接口-警报',
    list: []
})
api[0].list[0].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/outside/alarm/info/{id}/{category}',
    desc: '根据警报id、类别获取警报信息',
});
api[0].list[0].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/outside/alarm/download',
    desc: '下载文件',
});
api[0].list.push({
    alias: 'AlarmDefenseGuideController',
    order: '2',
    link: '警报防御指南',
    desc: '警报防御指南',
    list: []
})
api[0].list[1].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/list',
    desc: '查询所有数据',
});
api[0].list[1].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[1].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/save',
    desc: '新增数据',
});
api[0].list[1].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/update',
    desc: '修改数据',
});
api[0].list[1].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/delete/{id}',
    desc: '删除数据',
});
api[0].list[1].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/setOpen/{id}',
    desc: '修改防御指南状态',
});
api[0].list[1].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmDefenseGuide/selectByType/{type}',
    desc: '根据类型查询',
});
api[0].list.push({
    alias: 'AlarmLevelController',
    order: '3',
    link: '警报等级信息',
    desc: '警报等级信息',
    list: []
})
api[0].list[2].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmLevel/list',
    desc: '查询所有数据',
});
api[0].list[2].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmLevel/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[2].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmLevel/save',
    desc: '新增数据',
});
api[0].list[2].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmLevel/update',
    desc: '修改数据',
});
api[0].list.push({
    alias: 'AlarmProductTemplateController',
    order: '4',
    link: '警报产品模板',
    desc: '警报产品模板',
    list: []
})
api[0].list[3].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmProductTemplate/list',
    desc: '查询所有数据',
});
api[0].list[3].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmProductTemplate/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[3].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmProductTemplate/save',
    desc: '新增数据',
});
api[0].list[3].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmProductTemplate/update',
    desc: '修改数据',
});
api[0].list[3].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmProductTemplate/delete',
    desc: '删除数据',
});
api[0].list[3].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/alarmProductTemplate/setOpen/{id}',
    desc: '设置开启',
});
api[0].list.push({
    alias: 'AreaController',
    order: '5',
    link: '区域表',
    desc: '区域表',
    list: []
})
api[0].list[4].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/area/list',
    desc: '根据区域类型编码查询区域列表',
});
api[0].list[4].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/area/save',
    desc: '保存区域',
});
api[0].list[4].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/area/delete/{id}',
    desc: '根据区域id删除区域',
});
api[0].list[4].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/area/generateAreaJson',
    desc: '生成areaJsonFile',
});
api[0].list.push({
    alias: 'AreaTypeController',
    order: '6',
    link: '区域类型表',
    desc: '区域类型表',
    list: []
})
api[0].list[5].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/areaType/treeList',
    desc: '查询区域类型-区域树形列表',
});
api[0].list.push({
    alias: 'BuoyStationController',
    order: '7',
    link: '浮标站表',
    desc: '浮标站表',
    list: []
})
api[0].list[6].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/buoyStation/list',
    desc: '查询中台浮标站列表 数据维护新增站点时使用',
});
api[0].list[6].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/buoyStation/dataList',
    desc: '根据时间范围、站点查询观测结果',
});
api[0].list[6].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/buoyStation/geoRangeDataList',
    desc: '根据时间范围、空间范围查询观测结果',
});
api[0].list.push({
    alias: 'ElementController',
    order: '8',
    link: '要素字典表',
    desc: '要素字典表',
    list: []
})
api[0].list[7].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/element/page',
    desc: '查询要素字典分页',
});
api[0].list[7].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/element/list',
    desc: '查询要素字典列表',
});
api[0].list[7].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/element/info/{id}',
    desc: '根据要素字典id查询详情',
});
api[0].list[7].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/element/save',
    desc: '保存要素字典',
});
api[0].list[7].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/element/delete/{id}',
    desc: '根据要素字典id删除要素字典',
});
api[0].list.push({
    alias: 'FmCheckController',
    order: '9',
    link: '质检数据控制层',
    desc: '质检数据控制层',
    list: []
})
api[0].list[8].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/checkData/list',
    desc: '根据预报任务Id查询列表',
});
api[0].list.push({
    alias: 'FmForecastProductDataController',
    order: '10',
    link: '极值数据控制层',
    desc: '极值数据控制层',
    list: []
})
api[0].list[9].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmForecastProductData/page',
    desc: '查询分页',
});
api[0].list[9].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmForecastProductData/list',
    desc: '查询分页',
});
api[0].list[9].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmForecastProductData/waveStartTimeList',
    desc: '获取海浪警报起报时间列表',
});
api[0].list[9].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmForecastProductData/lastForecastStartTime',
    desc: '获取最近一次预报起报时间',
});
api[0].list.push({
    alias: 'FmGraphicDataSourceController',
    order: '11',
    link: '数据源控制层',
    desc: '数据源控制层',
    list: []
})
api[0].list[10].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicDataSource/list',
    desc: '查询列表',
});
api[0].list.push({
    alias: 'FmGraphicRecordBController',
    order: '12',
    link: '图形记录表控制层',
    desc: '图形记录表控制层',
    list: []
})
api[0].list[11].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/page',
    desc: '查询分页',
});
api[0].list[11].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/list',
    desc: '根据图形模板id、图形记录名称查询列表',
});
api[0].list[11].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/infoList',
    desc: '根据图形记录名称查询列表',
});
api[0].list[11].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[11].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/save',
    desc: '保存数据',
});
api[0].list[11].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/submit',
    desc: '提交数据',
});
api[0].list[11].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[11].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/delete',
    desc: '根据idList批量删除数据',
});
api[0].list[11].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/getTyphoonList',
    desc: '查询台风里列表',
});
api[0].list[11].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/getTyphoonByCode',
    desc: '根据台风编号查询台风具体信息',
});
api[0].list[11].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicRecordB/findTyphoonForecastByCode',
    desc: '根据台风编号查询台风具体信息',
});
api[0].list.push({
    alias: 'FmGraphicSignController',
    order: '13',
    link: '图形标签控制层',
    desc: '图形标签控制层',
    list: []
})
api[0].list[12].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicSign/list',
    desc: '查询列表',
});
api[0].list.push({
    alias: 'FmGraphicTemplateBController',
    order: '14',
    link: '图形模板表控制层',
    desc: '图形模板表控制层',
    list: []
})
api[0].list[13].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/page',
    desc: '查询分页',
});
api[0].list[13].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/list',
    desc: '查询列表',
});
api[0].list[13].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/getListParent',
    desc: '查询列表',
});
api[0].list[13].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[13].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/getByParentId/{parentId}',
    desc: '根据parentId查询数据',
});
api[0].list[13].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/save',
    desc: '保存数据',
});
api[0].list[13].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/update',
    desc: '更新数据',
});
api[0].list[13].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/updateStatus',
    desc: '更新图形模版状态',
});
api[0].list[13].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/updateName',
    desc: '更新图形模版名称',
});
api[0].list[13].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[13].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/delete',
    desc: '根据idList批量删除数据',
});
api[0].list[13].list.push({
    order: '12',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/uploadImageBase',
    desc: 'base64上传',
});
api[0].list[13].list.push({
    order: '13',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateB/uploadImage',
    desc: '图片上传',
});
api[0].list.push({
    alias: 'FmGraphicTemplateMainController',
    order: '15',
    link: '图形模版字典',
    desc: '图形模版字典',
    list: []
})
api[0].list[14].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTemplateMain/list',
    desc: '查询列表',
});
api[0].list.push({
    alias: 'FmGraphicToolsController',
    order: '16',
    link: '图形工具控制层',
    desc: '图形工具控制层',
    list: []
})
api[0].list[15].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmGraphicTools/list',
    desc: '查询列表',
});
api[0].list.push({
    alias: 'FmIsolineDataController',
    order: '17',
    link: '等值线控制层',
    desc: '等值线控制层',
    list: []
})
api[0].list[16].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmIsolineData/list',
    desc: '查询列表',
});
api[0].list.push({
    alias: 'FmPublicRecordController',
    order: '18',
    link: '',
    desc: '',
    list: []
})
api[0].list[17].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicRecord/getLastRecord',
    desc: '',
});
api[0].list[17].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicRecord/saveOrUpdate',
    desc: '',
});
api[0].list[17].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicRecord/submit',
    desc: '',
});
api[0].list[17].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicRecord/download/{id}',
    desc: '',
});
api[0].list[17].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicRecord/downloadDoc/{id}.docx',
    desc: '',
});
api[0].list.push({
    alias: 'FmPublicTaskController',
    order: '19',
    link: '',
    desc: '',
    list: []
})
api[0].list[18].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicTask/schedule',
    desc: '',
});
api[0].list[18].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPublicTask/getLastTaskId',
    desc: '',
});
api[0].list.push({
    alias: 'FmPushResultController',
    order: '20',
    link: '',
    desc: '',
    list: []
})
api[0].list[19].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmPushResult/testMail',
    desc: '',
});
api[0].list.push({
    alias: 'FmSchedulingMainTableController',
    order: '21',
    link: '排班祝表控制层',
    desc: '排班祝表控制层',
    list: []
})
api[0].list[20].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingMainTable/page',
    desc: '查询分页',
});
api[0].list[20].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingMainTable/list',
    desc: '查询列表',
});
api[0].list[20].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingMainTable/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[20].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingMainTable/save',
    desc: '保存数据',
});
api[0].list[20].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingMainTable/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[20].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingMainTable/delete',
    desc: '根据idList批量删除数据',
});
api[0].list.push({
    alias: 'FmSchedulingTableController',
    order: '22',
    link: '排班控制层',
    desc: '排班控制层',
    list: []
})
api[0].list[21].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/page',
    desc: '查询分页',
});
api[0].list[21].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/list',
    desc: '根据月份查询排班信息',
});
api[0].list[21].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[21].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/save',
    desc: '保存数据',
});
api[0].list[21].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/saveAll',
    desc: '保存全部数据',
});
api[0].list[21].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/update',
    desc: '排班管理：修改排班表',
});
api[0].list[21].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/sign',
    desc: '排班管理：签到',
});
api[0].list[21].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[21].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/delete',
    desc: '根据idList批量删除数据',
});
api[0].list[21].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/upload',
    desc: '上传',
});
api[0].list[21].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingTable/download',
    desc: '导出',
});
api[0].list.push({
    alias: 'FmSchedulingUserController',
    order: '23',
    link: '控制层',
    desc: '控制层',
    list: []
})
api[0].list[22].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingUser/page',
    desc: '查询分页',
});
api[0].list[22].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingUser/list',
    desc: '查询列表',
});
api[0].list[22].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmSchedulingUser/save',
    desc: '保存数据',
});
api[0].list.push({
    alias: 'FmTideDialyController',
    order: '24',
    link: '',
    desc: '',
    list: []
})
api[0].list[23].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTideDialy/queryListByParam',
    desc: '跟据条件查询',
});
api[0].list.push({
    alias: 'FmTyphoonBController',
    order: '25',
    link: '台风信息控制层',
    desc: '台风信息控制层',
    list: []
})
api[0].list[24].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/page',
    desc: '查询分页',
});
api[0].list[24].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/list',
    desc: '查询列表',
});
api[0].list[24].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[24].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/save',
    desc: '保存数据',
});
api[0].list[24].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[24].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/delete',
    desc: '根据idList批量删除数据',
});
api[0].list[24].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/similar',
    desc: '查询相似路径台风',
});
api[0].list[24].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonB/syncTyphoon/{startYear}/{endYear}',
    desc: '',
});
api[0].list.push({
    alias: 'FmTyphoonCompletionBController',
    order: '26',
    link: '台风位置预警完成数据控制层',
    desc: '台风位置预警完成数据控制层',
    list: []
})
api[0].list[25].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonCompletionB/page',
    desc: '查询分页',
});
api[0].list[25].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonCompletionB/list',
    desc: '查询列表',
});
api[0].list[25].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonCompletionB/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[25].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonCompletionB/save',
    desc: '保存数据',
});
api[0].list[25].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonCompletionB/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[25].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonCompletionB/delete',
    desc: '根据idList批量删除数据',
});
api[0].list.push({
    alias: 'FmTyphoonRealBController',
    order: '27',
    link: '台风实时数据信息控制层',
    desc: '台风实时数据信息控制层',
    list: []
})
api[0].list[26].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonRealB/page',
    desc: '查询分页',
});
api[0].list[26].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonRealB/list',
    desc: '查询列表',
});
api[0].list[26].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonRealB/info/{id}',
    desc: '根据id查询数据',
});
api[0].list[26].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonRealB/save',
    desc: '保存数据',
});
api[0].list[26].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonRealB/delete/{id}',
    desc: '根据id删除数据',
});
api[0].list[26].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/fmTyphoonRealB/delete',
    desc: '根据idList批量删除数据',
});
api[0].list.push({
    alias: 'FmZfxDictController',
    order: '28',
    link: '字典控制层',
    desc: '字典控制层',
    list: []
})
api[0].list[27].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/fmZfxDict/list',
    desc: '查询列表',
});
api[0].list.push({
    alias: 'ForecastProductRecordController',
    order: '29',
    link: '预报产品记录表',
    desc: '预报产品记录表',
    list: []
})
api[0].list[28].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/list',
    desc: '查询预报产品记录列表',
});
api[0].list[28].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/save',
    desc: '审核预览生成预报产品记录',
});
api[0].list[28].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/release',
    desc: '发布预报产品',
});
api[0].list[28].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/download',
    desc: '下载',
});
api[0].list[28].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/downloadDoc/{id}.docx',
    desc: '',
});
api[0].list[28].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/releaseResult/page',
    desc: '查询预报产品发布结果分页',
});
api[0].list[28].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/releaseResult/{pushTaskId}',
    desc: '根据推送任务id、产品名称、状态查询产品列表',
});
api[0].list[28].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductRecord/updateFile',
    desc: '更新产品文件内容',
});
api[0].list.push({
    alias: 'ForecastProductTemplateController',
    order: '30',
    link: '预报产品模板表',
    desc: '预报产品模板表',
    list: []
})
api[0].list[29].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/list',
    desc: '查询产品模板列表',
});
api[0].list[29].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/info/{id}',
    desc: '根据产品模板id查询详情',
});
api[0].list[29].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/save',
    desc: '保存产品模板',
});
api[0].list[29].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/update/{id}/{status}',
    desc: '根据产品模板id修改产品模板状态',
});
api[0].list[29].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/upload',
    desc: '模板上传',
});
api[0].list[29].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/download/{id}',
    desc: '模板下载',
});
api[0].list[29].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/delete/{id}',
    desc: '根据产品模板id删除产品模板',
});
api[0].list[29].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastProductTemplate/updateName',
    desc: '根据产品模板id修改产品模板名称',
});
api[0].list.push({
    alias: 'ForecastRecordController',
    order: '31',
    link: '预报记录表',
    desc: '预报记录表',
    list: []
})
api[0].list[30].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/save',
    desc: '保存预报记录',
});
api[0].list[30].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/submit',
    desc: '提交预报记录',
});
api[0].list[30].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/info/{taskId}',
    desc: '根据预报任务id、数据源查询预报记录',
});
api[0].list[30].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/info/new/{taskId}',
    desc: '根据预报任务id、数据源查询预报记录',
});
api[0].list[30].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/loadPre/{taskId}',
    desc: '加载上一期',
});
api[0].list[30].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/reset/{taskId}',
    desc: '重置',
});
api[0].list[30].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastRecord/syncDataToPublish/{taskId}',
    desc: '查询预报数据信息，同步到预报数据推送表中 提交预报',
});
api[0].list.push({
    alias: 'ForecastSpecialController',
    order: '32',
    link: '公共服务-专项预报',
    desc: '公共服务-专项预报',
    list: []
})
api[0].list[31].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/forecast/pageList',
    desc: '根据条件查询数据（分页）',
});
api[0].list[31].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/forecast/saveInfo',
    desc: '保存数据',
});
api[0].list[31].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/forecast/updateInfo',
    desc: '修改数据',
});
api[0].list[31].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/forecast/updateStatus/{id}/{status}',
    desc: '修改状态',
});
api[0].list[31].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/forecast/info/{id}',
    desc: '根据id查询详情',
});
api[0].list.push({
    alias: 'ForecastTaskController',
    order: '33',
    link: '预报任务表',
    desc: '预报任务表',
    list: []
})
api[0].list[32].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTask/list/{status}',
    desc: '根据状态查询预报任务列表',
});
api[0].list[32].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTask/info',
    desc: '查询预报任务列表的状态统计信息',
});
api[0].list[32].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTask/schedule',
    desc: '定时创建预报任务',
});
api[0].list.push({
    alias: 'ForecastTemplateController',
    order: '34',
    link: '预报模板表',
    desc: '预报模板表',
    list: []
})
api[0].list[33].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTemplate/treeList',
    desc: '查询预报模板列表',
});
api[0].list[33].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTemplate/info/{id}',
    desc: '根据预报模板id查询详情',
});
api[0].list[33].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTemplate/save',
    desc: '保存预报模板',
});
api[0].list[33].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTemplate/update/{id}/{status}',
    desc: '根据预报模板id修改预报模板状态',
});
api[0].list[33].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTemplate/delete/{id}',
    desc: '根据预报模板id删除预报模板',
});
api[0].list[33].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/forecastTemplate/updateName',
    desc: '根据预报模板id修改预报模板名称',
});
api[0].list.push({
    alias: 'GeoUtilController',
    order: '35',
    link: 'geo工具接口',
    desc: 'GEO工具接口',
    list: []
})
api[0].list[34].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/geo/globalNationalBoundary/intersection',
    desc: '获取全球国家边界交点',
});
api[0].list.push({
    alias: 'LiveObserveElementController',
    order: '36',
    link: '实况观测要素表',
    desc: '实况观测要素表',
    list: []
})
api[0].list[35].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/liveObserveElement/list',
    desc: '查询实况观测要素表列表',
});
api[0].list.push({
    alias: 'OceanFacilityController',
    order: '37',
    link: '海洋设施表',
    desc: '海洋设施表',
    list: []
})
api[0].list[36].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanFacility/typeList',
    desc: '查询海洋设施类型列表',
});
api[0].list[36].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanFacility/list',
    desc: '根据海洋设施类型查询海洋设施列表',
});
api[0].list.push({
    alias: 'OceanStationController',
    order: '38',
    link: '海洋站表',
    desc: '海洋站表',
    list: []
})
api[0].list[37].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/list',
    desc: '查询中台海洋站列表 数据维护新增站点时使用',
});
api[0].list[37].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/data/list',
    desc: '根据时间范围查询观测结果 24小时观测数据',
});
api[0].list[37].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/hourDataList',
    desc: '根据时间范围、站点查询观测结果-小时',
});
api[0].list[37].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/minuteDataList',
    desc: '根据时间范围、站点查询观测结果-分钟',
});
api[0].list[37].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/geoRangeDataList',
    desc: '根据时间范围、空间范围查询观测结果',
});
api[0].list[37].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/syncOceanAndSmallBuoyData',
    desc: '同步数据-海洋站、浅海小型浮标',
});
api[0].list[37].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/syncLargeBuoyData',
    desc: '定时同步数据-深海大型锚系浮标',
});
api[0].list[37].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/oceanStation/tideList',
    desc: '根据关联站点编码查询实况观测潮位数据列表',
});
api[0].list.push({
    alias: 'RegionController',
    order: '39',
    link: '行政区表',
    desc: '行政区表',
    list: []
})
api[0].list[38].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/region/save',
    desc: '保存行政区表',
});
api[0].list.push({
    alias: 'SeaWaveAlarmController',
    order: '40',
    link: '海浪警报制作',
    desc: '海浪警报制作',
    list: []
})
api[0].list[39].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/pageList',
    desc: '分页查询所有数据',
});
api[0].list[39].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/list',
    desc: '查询所有数据',
});
api[0].list[39].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[39].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/save',
    desc: '新增数据',
});
api[0].list[39].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/update',
    desc: '修改数据',
});
api[0].list[39].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/delete',
    desc: '删除数据',
});
api[0].list[39].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/generateText',
    desc: '生成文字',
});
api[0].list[39].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/release/{id}',
    desc: '发布',
});
api[0].list[39].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/statistic',
    desc: '统计',
});
api[0].list[39].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/pushSelectOne/{id}',
    desc: '通过主键查询单条数据,并推送发布到官网、公众号',
});
api[0].list[39].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveAlarm/downloadDoc/{id}.docx',
    desc: '',
});
api[0].list.push({
    alias: 'SeaWaveMessageController',
    order: '41',
    link: '海浪消息',
    desc: '海浪消息',
    list: []
})
api[0].list[40].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/pageList',
    desc: '分页查询所有数据',
});
api[0].list[40].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/list',
    desc: '查询所有数据',
});
api[0].list[40].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[40].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/save',
    desc: '新增数据',
});
api[0].list[40].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/update',
    desc: '修改数据',
});
api[0].list[40].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/delete',
    desc: '删除数据',
});
api[0].list[40].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/generateText',
    desc: '生成文字',
});
api[0].list[40].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/seaWaveMessage/release',
    desc: '发布',
});
api[0].list.push({
    alias: 'ShipDataController',
    order: '42',
    link: '船舶表',
    desc: '船舶表',
    list: []
})
api[0].list[41].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/shipData/shipList',
    desc: '查询船舶列表 数据维护使用',
});
api[0].list[41].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/shipData/list',
    desc: '根据船舶名称查询船舶轨迹列表',
});
api[0].list[41].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/shipData/rangeList',
    desc: '根据时间范围、空间范围查询船舶轨迹列表',
});
api[0].list.push({
    alias: 'StationController',
    order: '43',
    link: '站点表',
    desc: '站点表',
    list: []
})
api[0].list[42].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/station/page',
    desc: '查询站点分页',
});
api[0].list[42].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/station/list',
    desc: '根据名称查询站点列表',
});
api[0].list[42].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/station/listByStationTypeCode',
    desc: '根据站点类型查询站点列表',
});
api[0].list[42].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/station/save',
    desc: '保存站点',
});
api[0].list[42].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/station/delete/{id}',
    desc: '根据站点id删除站点',
});
api[0].list[42].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/station/upload',
    desc: '批量导入站点',
});
api[0].list[42].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/station/syncRelationStation/{stationTypeCode}',
    desc: '同步关联站点字段',
});
api[0].list.push({
    alias: 'StationTypeController',
    order: '44',
    link: '站点类型表',
    desc: '站点类型表',
    list: []
})
api[0].list[43].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/stationType/treeList',
    desc: '查询站点类型-行政区-站点树形列表',
});
api[0].list.push({
    alias: 'StormSurgeAlarmController',
    order: '45',
    link: '风暴潮警报制作',
    desc: '风暴潮警报制作',
    list: []
})
api[0].list[44].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/pageList',
    desc: '分页查询所有数据',
});
api[0].list[44].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/list',
    desc: '查询所有数据',
});
api[0].list[44].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[44].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/save',
    desc: '新增数据',
});
api[0].list[44].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/update',
    desc: '修改数据',
});
api[0].list[44].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/delete',
    desc: '删除数据',
});
api[0].list[44].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/generateText',
    desc: '生成文字',
});
api[0].list[44].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/release/{id}',
    desc: '发布',
});
api[0].list[44].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/statistic',
    desc: '统计',
});
api[0].list[44].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/pushSelectOne/{id}',
    desc: '通过主键查询单条数据,并同步到推送表',
});
api[0].list[44].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeAlarm/downloadDoc/{id}.docx',
    desc: '',
});
api[0].list.push({
    alias: 'StormSurgeController',
    order: '46',
    link: '预报分析-风暴潮',
    desc: '预报分析-风暴潮',
    list: []
})
api[0].list[45].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurge/tideList',
    desc: '根据站点编码查询风暴潮数据列表',
});
api[0].list.push({
    alias: 'StormSurgeMessageController',
    order: '47',
    link: '风暴潮消息制作',
    desc: '风暴潮消息制作',
    list: []
})
api[0].list[46].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/pageList',
    desc: '分页查询所有数据',
});
api[0].list[46].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/list',
    desc: '查询所有数据',
});
api[0].list[46].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/info/{id}',
    desc: '通过主键查询单条数据',
});
api[0].list[46].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/save',
    desc: '新增数据',
});
api[0].list[46].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/update',
    desc: '修改数据',
});
api[0].list[46].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/delete',
    desc: '删除数据',
});
api[0].list[46].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/stormSurgeMessage/release',
    desc: '发布',
});
api[0].list.push({
    alias: 'TagController',
    order: '48',
    link: '标签表',
    desc: '标签表',
    list: []
})
api[0].list[47].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/tag/list',
    desc: '查询标签表列表',
});
api[0].list[47].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/tag/save',
    desc: '保存标签表',
});
api[0].list[47].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/tag/delete/{id}',
    desc: '根据标签表id删除标签表',
});
api[0].list.push({
    alias: 'TideController',
    order: '49',
    link: '潮汐表',
    desc: '潮汐表',
    list: []
})
api[0].list[48].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/tide/daily_data',
    desc: '站点潮位数据',
});
api[0].list[48].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/tide/list',
    desc: '根据站点id查询潮汐数据列表',
});
api[0].list[48].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/tide/export',
    desc: '导出天文潮数据',
});
api[0].list.push({
    alias: 'TsunamiProductController',
    order: '50',
    link: '海啸产品表',
    desc: '海啸产品表',
    list: []
})
api[0].list[49].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/tsunamiProduct/page',
    desc: '查询海啸产品表分页',
});
api[0].list[49].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/tsunamiProduct/list',
    desc: '查询海啸产品表列表',
});
api[0].list[49].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/tsunamiProduct/download/{id}',
    desc: '下载',
});
api[0].list[49].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/tsunamiProduct/batch/download',
    desc: '批量下载',
});
api[0].list.push({
    alias: 'UploadFileController',
    order: '51',
    link: '上传文件',
    desc: '上传文件',
    list: []
})
api[0].list[50].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/upload/image',
    desc: '上传图片文件',
});
api[0].list[50].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/upload/imageBase64',
    desc: '上传图片base64',
});
api[0].list[50].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/upload/onlyOfficeCallback/{type}/{id}',
    desc: '保存onlyOffice文件回执',
});
api[0].list.push({
    alias: 'UserController',
    order: '52',
    link: '用户表',
    desc: '用户表',
    list: []
})
api[0].list[51].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/user/page',
    desc: '查询用户表分页',
});
api[0].list[51].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/user/list',
    desc: '查询用户表列表',
});
api[0].list[51].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/user/info/{id}',
    desc: '根据用户表id查询详情',
});
api[0].list[51].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/user/save',
    desc: '保存用户表',
});
api[0].list[51].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/user/delete/{id}',
    desc: '根据用户表id删除用户表',
});
document.onkeydown = keyDownSearch;
function keyDownSearch(e) {
    const theEvent = e;
    const code = theEvent.keyCode || theEvent.which || theEvent.charCode;
    if (code == 13) {
        const search = document.getElementById('search');
        const searchValue = search.value.toLocaleLowerCase();

        let searchGroup = [];
        for (let i = 0; i < api.length; i++) {

            let apiGroup = api[i];

            let searchArr = [];
            for (let i = 0; i < apiGroup.list.length; i++) {
                let apiData = apiGroup.list[i];
                const desc = apiData.desc;
                if (desc.toLocaleLowerCase().indexOf(searchValue) > -1) {
                    searchArr.push({
                        order: apiData.order,
                        desc: apiData.desc,
                        link: apiData.link,
                        list: apiData.list
                    });
                } else {
                    let methodList = apiData.list || [];
                    let methodListTemp = [];
                    for (let j = 0; j < methodList.length; j++) {
                        const methodData = methodList[j];
                        const methodDesc = methodData.desc;
                        if (methodDesc.toLocaleLowerCase().indexOf(searchValue) > -1) {
                            methodListTemp.push(methodData);
                            break;
                        }
                    }
                    if (methodListTemp.length > 0) {
                        const data = {
                            order: apiData.order,
                            desc: apiData.desc,
                            link: apiData.link,
                            list: methodListTemp
                        };
                        searchArr.push(data);
                    }
                }
            }
            if (apiGroup.name.toLocaleLowerCase().indexOf(searchValue) > -1) {
                searchGroup.push({
                    name: apiGroup.name,
                    order: apiGroup.order,
                    list: searchArr
                });
                continue;
            }
            if (searchArr.length === 0) {
                continue;
            }
            searchGroup.push({
                name: apiGroup.name,
                order: apiGroup.order,
                list: searchArr
            });
        }
        let html;
        if (searchValue == '') {
            const liClass = "";
            const display = "display: none";
            html = buildAccordion(api,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        } else {
            const liClass = "open";
            const display = "display: block";
            html = buildAccordion(searchGroup,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        }
        const Accordion = function (el, multiple) {
            this.el = el || {};
            this.multiple = multiple || false;
            const links = this.el.find('.dd');
            links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown);
        };
        Accordion.prototype.dropdown = function (e) {
            const $el = e.data.el;
            $this = $(this), $next = $this.next();
            $next.slideToggle();
            $this.parent().toggleClass('open');
            if (!e.data.multiple) {
                $el.find('.submenu').not($next).slideUp("20").parent().removeClass('open');
            }
        };
        new Accordion($('#accordion'), false);
    }
}

function buildAccordion(apiGroups, liClass, display) {
    let html = "";
    let doc;
    if (apiGroups.length > 0) {
         if (apiDocListSize == 1) {
            let apiData = apiGroups[0].list;
            for (let j = 0; j < apiData.length; j++) {
                html += '<li class="'+liClass+'">';
                html += '<a class="dd" href="#_' + apiData[j].link + '">' + apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
                html += '<ul class="sectlevel2" style="'+display+'">';
                doc = apiData[j].list;
                for (let m = 0; m < doc.length; m++) {
                    let spanString;
                    if (doc[m].deprecated == 'true') {
                        spanString='<span class="line-through">';
                    } else {
                        spanString='<span>';
                    }
                    html += '<li><a href="#_1_' + apiData[j].order + '_' + doc[m].order + '_' + doc[m].desc + '">' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + spanString + doc[m].desc + '<span></a> </li>';
                }
                html += '</ul>';
                html += '</li>';
            }
        } else {
            for (let i = 0; i < apiGroups.length; i++) {
                let apiGroup = apiGroups[i];
                html += '<li class="'+liClass+'">';
                html += '<a class="dd" href="#_' + apiGroup.name + '">' + apiGroup.order + '.&nbsp;' + apiGroup.name + '</a>';
                html += '<ul class="sectlevel1">';

                let apiData = apiGroup.list;
                for (let j = 0; j < apiData.length; j++) {
                    html += '<li class="'+liClass+'">';
                    html += '<a class="dd" href="#_'+apiGroup.order+'_'+ apiData[j].order + '_'+ apiData[j].link + '">' +apiGroup.order+'.'+ apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
                    html += '<ul class="sectlevel2" style="'+display+'">';
                    doc = apiData[j].list;
                    for (let m = 0; m < doc.length; m++) {
                       let spanString;
                       if (doc[m].deprecated == 'true') {
                           spanString='<span class="line-through">';
                       } else {
                           spanString='<span>';
                       }
                       html += '<li><a href="#_'+apiGroup.order+'_' + apiData[j].order + '_' + doc[m].order + '_' + doc[m].desc + '">'+apiGroup.order+'.' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + spanString + doc[m].desc + '<span></a> </li>';
                   }
                    html += '</ul>';
                    html += '</li>';
                }

                html += '</ul>';
                html += '</li>';
            }
        }
    }
    return html;
}