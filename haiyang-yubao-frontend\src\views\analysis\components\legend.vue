<template>
  <div class="legend-wrap">
    <template v-if="isTyphoonActive">
      <div class="typhoon-legend">
        <div class="typhoon-item-wrap">
          <img src="src/assets/images/forecast/typhoon-legend.png" alt="">
        </div>
      </div>
    </template>
    <template v-if="url">
      <img :src="url" alt="" />
    </template>
    <template v-if="type === 'stormSurge' && modeType === 'county'">
      <img :src="stormSurgeImage" alt="" />
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useTyphoonLegend } from 'src/views/analysis/components/legend-hook/typhoon'
import stormSurgeImage from 'src/assets/images/forecast/stormSurgeFloodplain.png'

const props = defineProps({
  isTyphoonActive: {
    type: Boolean,
    default: false
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 0
  },
  url: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  modeType: {
    type: String,
    default: ''
  }
})

const { typhoonLegend } = useTyphoonLegend()
</script>
<style lang="scss">
.legend-wrap {
  position: fixed;
  z-index: 3;
  right: 20px;
  bottom: 30px;
  //height: 300px;
  width: 540px;
  pointer-events: none;

  img {
    max-width: 100%;
    height: 100%;
  }

  .legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 15px;
    width: 100%;
    height: 100%;

    .text {
      white-space: nowrap;
      margin-right: 8px;
    }

    .color-ramp {
      flex: 1;
      position: relative;
      height: 15px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 12px;
      padding: 0px 6px;
      background: linear-gradient(
        90deg,
        #00007f,
        #000093,
        #0000a9,
        #0000c1,
        #0000d7,
        #0000ed,
        #0000ff,
        #0009ff,
        #001eff,
        #0032ff,
        #0045ff,
        #005aff,
        #006eff,
        #0083ff,
        #0096ff,
        #00aaff,
        #00bfff,
        #00d2ff,
        #01e7f4,
        #11fae4,
        #20ffd5,
        #31ffc3,
        #41ffb4,
        #52ffa3,
        #62ff93,
        #72ff83,
        #82ff73,
        #92ff63,
        #a1ff54,
        #b2ff42,
        #c2ff33,
        #d4ff21,
        #e3ff12,
        #f3f902,
        #ffe500,
        #ffd300,
        #ffbf00,
        #ffae00,
        #ff9b00,
        #ff8800,
        #ff7600,
        #ff6300,
        #ff5000,
        #ff3f00,
        #ff2b00,
        #ff1a00,
        #ef0600,
        #d90000,
        #c30000,
        #ab0000,
        #950000,
        #7f0000
      );
    }
  }

  .typhoon-legend {
    display: flex;
    flex-direction: column;
    width: 160px;
    padding: 10px;

    .typhoon-title {
      display: flex;
      justify-content: center;
      margin: 10px 0;
    }

    .typhoon-item-wrap {
      display: flex;
      flex-direction: column;
      height: 230px;
      width: 230px;

      .typhoon-item {
        display: flex;
        align-items: center;
        font-size: 20px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
