precision mediump float;
uniform sampler2D u_wind;
uniform vec2 u_wind_min;
uniform vec2 u_wind_max;
uniform sampler2D u_color_ramp;
varying vec2 v_particle_pos;
void main() {
    vec4 rgba=texture2D(u_wind, v_particle_pos).rgba;
    if(rgba.a==0.0){
        discard;
    }
    vec2 velocity = mix(u_wind_min, u_wind_max, rgba.rg);
    float speed_t = length(velocity) / length(u_wind_max);
    if(speed_t<0.0){
        discard;
    }
    // color ramp is encoded in a 16x16 texture
    vec2 ramp_pos = vec2(
        fract(20.0 * speed_t),
        floor(20.0 * speed_t) / 16.0
        );
    gl_FragColor = texture2D(u_color_ramp, ramp_pos);
}
