<template>
  <button
    class="qx-button"
    :class="getClass()"
    :disabled="disabled"
    @click="onClick"
  >
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default() {
      return 'medium'
    }
  },
  disabled: {
    type: Boolean,
    default() {
      return false
    }
  }
})

type mapperType = {
  [key: string]: string
}

function getClass(): string {
  const arr: string[] = []
  const { disabled, size } = props
  let mapper: mapperType = {
    small: 'qx-button_small',
    medium: 'qx-button_medium',
    large: 'qx-button_large',
    tiny: 'qx-button_tiny'
  }
  arr.push(mapper[size])

  if (disabled) {
    arr.push('qx-button--disabled')
  }
  return arr.join(' ')
}

const emit = defineEmits(['click'])
function onClick() {
  emit('click')
}
</script>

<style lang="scss">
.qx-button {
  padding: 0 18px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-family: <PERSON>, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  background: #fff;
  margin-right: 8px;
  border: 1px solid rgba(34, 34, 34, 0.1);
  border-radius: 4px;
  cursor: pointer;

  &:nth-last-child(1) {
    margin-right: 0;
  }
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #0000;
    color: rgb(51, 54, 57);
  }

  &.primary {
    background: #1c81f8;
    border: 1px solid #1c81f8;
    color: #fff;
    &:not(.n-button--disabled):hover {
      background: #1c81f8 !important;
      color: #fff !important;
    }
  }

  &.border {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    padding: 8px 18px;
    border-radius: 29px;
    border: 1px solid #ffffff;
    background: transparent;
    height: auto;
    line-height: 1;
  }
  &.warning {
    background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    border: none;
    color: #fff;
    &:not(.n-button--disabled):hover {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%) !important;
      color: #fff !important;
    }
  }

  &_tiny {
    height: 22px;
    line-height: 22px;
  }
}
</style>
