package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.ForecastSpecialDTO;
import cn.piesat.data.making.server.service.ForecastSpecialService;
import cn.piesat.data.making.server.utils.page.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 公共服务-专项预报
 */
@RestController
@RequestMapping("/forecast")
public class ForecastSpecialController {

    @Autowired
    private ForecastSpecialService forecastSpecialService;

    /**
     * 根据条件查询数据（分页）
     * @param dto
     * @return
     */
    @GetMapping("/pageList")
    public PageResult pageList(ForecastSpecialDTO dto){
        return forecastSpecialService.pageList(dto);
    }

    /**
     * 保存数据
     * @param dto
     */
    @PostMapping("/saveInfo")
    public void saveInfo(@RequestBody ForecastSpecialDTO dto){
        forecastSpecialService.saveInfo(dto);
    }

    /**
     * 修改数据
     * @param dto
     */
    @PostMapping("/updateInfo")
    public void updateInfo(@RequestBody ForecastSpecialDTO dto){
        forecastSpecialService.updateInfo(dto);
    }

    /**
     * 修改状态
     * @param id
     * @param status
     */
    @GetMapping("/updateStatus/{id}/{status}")
    public void updateStatus(@PathVariable Long id, @PathVariable Integer status){
        forecastSpecialService.updateStatus(id,status);
    }

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    public ForecastSpecialDTO info(@PathVariable Long id){
        return forecastSpecialService.info(id);
    }
}
