package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationOntimeDataODao;
import cn.piesat.data.making.server.entity.OceanStationOntimeDataO;
import cn.piesat.data.making.server.service.OceanStationOntimeDataOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点数据-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationOntimeDataOServiceImpl extends ServiceImpl<OceanStationOntimeDataODao, OceanStationOntimeDataO>
        implements OceanStationOntimeDataOService {

    @Resource
    private OceanStationOntimeDataODao oceanStationOntimeDataODao;

    @Override
    public List<OceanStationOntimeDataO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationOntimeDataODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationOntimeDataODao.getMaxCreateTime();
    }
}





