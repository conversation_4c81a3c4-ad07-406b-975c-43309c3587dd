package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.generate.ForecastSpecialWordDTO;
import cn.piesat.data.making.server.enums.ProductEnum;

import java.util.Date;

public interface GenerateProductService {

    Long sendPushServer(String taskName, String wordFilePath, String htmlFilePath, String smsFilePath, ProductEnum word, ProductEnum html, ProductEnum sms,
                        Date time);

    void generateSms(String smsFilePath,String param);

    void generateWord(String filePath,String template, Object param);

    void generateWord(String filePath,String template, Object param, String... tableNames);

    void generateHtml(String wordFilePath, String htmlFilePath);
}
