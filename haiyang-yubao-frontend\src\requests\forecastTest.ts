import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)
const productUrl = import.meta.env.VITE_PRODUCT_BASE_URL
const axiosProductInstance = getAxios(productUrl)

const ForecastTest = {
  /**
   * 获取检验结果
   * @param params
   * @returns
   */
  getTestResuls(productId: string, params?: any) {
    return axiosProductInstance({
      url: `/api/product/record/range/forecast/${productId}`,
      method: 'GET',
      params
    })
  },
  /**
   * 获取预报检验菜单
   * @param params
   * @returns
   */
  getMenuList(params: any) {
    return axiosInstance({
      url: `/fmZfxDict/list`,
      method: 'GET',
      params
    })
  },

  /**
   * 预览图片
   * @param filePath
   * @returns
   */
  previewImage(filePath: string) {
    console.log(filePath, 'filePath')
    return axiosProductInstance({
      url: `/image/preview?filePath=${filePath}`,
      method: 'GET',
      responseType: 'blob'
    })
  }
}

export default ForecastTest
