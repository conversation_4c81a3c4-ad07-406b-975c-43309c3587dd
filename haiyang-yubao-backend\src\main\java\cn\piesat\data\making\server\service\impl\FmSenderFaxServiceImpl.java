package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.codec.Base64;
import cn.piesat.data.making.server.entity.FmPushResult;
import cn.piesat.data.making.server.service.FmPushResultService;
import cn.piesat.data.making.server.service.FmSenderService;
import cn.piesat.data.making.server.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.URLEncoder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.eclipse.emf.ecore.xml.type.internal.DataValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class FmSenderFaxServiceImpl implements FmSenderService {

    @Value("${piesat.faxServerUrl}")
    private String faxServerUrl;

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

//    @Value("${piesat.base-output-path}")
//    private String baseOutputPath;

    @Value("${piesat.kkfileViewUrl}")
    private String kkfileViewUrl;

    @Value("${piesat.faxTmpPath}")
    private String faxTmpPath;

    @Value("${piesat.localUrl}")
    private String localUrl;

    private static final int TIMEOUT = 18000000;

    @Override
    public void sender(FmPushResult fmPushResult) {
        if(fmPushResult!=null){
            try {
                String tmpFilaPath = fmPushResult.getPushContent();
                String tmpFileName = tmpFilaPath.substring(tmpFilaPath.indexOf("/")+1);
                String fileSuffix = tmpFileName.substring(tmpFileName.lastIndexOf(".")+1);

                if(("DOCX").equalsIgnoreCase(fileSuffix)||("DOC").equalsIgnoreCase(fileSuffix)){
                    //需要DOC到PDF转换
                    tmpFilaPath = this.transformReportPdf(localUrl+tmpFileName,tmpFileName,System.currentTimeMillis() + ".docx");
                    tmpFilaPath = tmpFilaPath.replaceAll("//","/");
                    log.info(tmpFilaPath);
                }

                String paramJson = "{\"faxFilePath\":\""+tmpFilaPath+"\",\"phone\":\""+fmPushResult.getPushChannelId()+"\"}";

                log.info(paramJson);
                String retStr = HttpClientUtil.post(faxServerUrl,paramJson,"","","");
                log.info("返回值: "+retStr);

                fmPushResult.setPushFlag("1");
            } catch (IOException e) {
                e.printStackTrace();
                fmPushResult.setPushFlag("2");
            }
            fmPushResultServiceImpl.updateFmPushResult(fmPushResult);
        }
    }

    private String transformReportPdf(String sourceDocPath, String docx ,String docxName) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        HttpsURLConnection connection = null;

        HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
            @Override
            public boolean verify(String s, SSLSession sslSession) {
                return true;
            }
        });
        try {
            TrustManager[] trustAllCerts = new TrustManager[] {new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
            };
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            URL url = new URL(kkfileViewUrl + "onlinePreview?officePreviewType=pdf&url=" + Base64.encode(sourceDocPath));
            connection = (HttpsURLConnection)url.openConnection();
//            connection.setHostnameVerifier(new HostnameVerifier() {
//                @Override
//                public boolean verify(String s, SSLSession sslSession) {
//                    return true;
//                }
//            });
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.connect();

            InputStream is = connection.getInputStream();
            List<String> retStrList = IOUtils.readLines(is,"UTF-8");

//            for(String retStr : retStrList){
//                log.info(retStr);
//            }

            String retFilePath = null;

            if(CollectionUtils.isNotEmpty(retStrList)&&retStrList.size()>55){
                log.info(retStrList.get(55));
                String fileStr = retStrList.get(55);
                retFilePath = retStrList.get(55).substring(fileStr.indexOf("'")+1,fileStr.lastIndexOf("'"));
                log.info(retFilePath);
            }

            is.close();
            return downloadPDF(retFilePath, docxName.replace("docx", "pdf"));
        } catch (IOException | KeyManagementException | NoSuchAlgorithmException e) {
            e.printStackTrace();
        }finally{
            if(in!=null){
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private String downloadPDF(String pdfUrl,String pdfName){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = Calendar.getInstance().getTime();
        String year = sdf.format(date).substring(0,4);
        //String month =sdf.format(date).substring(4,6);
        String dateStr = sdf.format(date);
        String pdfPath = faxTmpPath+ File.separator+"PDF"+ File.separator + year + File.separator + dateStr + File.separator ;
        URL urlfile = null;
        HttpURLConnection httpUrl = null;
        //BufferedInputStream bis = null;
        //BufferedOutputStream bos = null;

        InputStream is = null;
        FileOutputStream fos = null;
        if (!new File(pdfPath).exists()){
            new File(pdfPath).mkdirs();
        }
        File f = new File( pdfPath+pdfName);
        try {
            urlfile = new URL(pdfUrl);
            httpUrl = (HttpURLConnection) urlfile.openConnection();
            httpUrl.setConnectTimeout(TIMEOUT);
            httpUrl.setReadTimeout(TIMEOUT);
            httpUrl.connect();

            is = httpUrl.getInputStream();
            fos = new FileOutputStream(f);
            //bis = new BufferedInputStream(httpUrl.getInputStream());
            //bos = new BufferedOutputStream(new FileOutputStream(f));

            int len = 2048;
            IOUtils.copy(is,fos,len);
            /*
            byte[] b = new byte[len];
            while ((len = bis.read(b)) != -1) {
                bos.write(b, 0, len);
            }
            bos.flush();
            bos.close();
            bis.close();
            */
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if(httpUrl!=null){
                httpUrl.disconnect();
            }
        }

        return pdfPath+File.separator+pdfName;
    }
}
