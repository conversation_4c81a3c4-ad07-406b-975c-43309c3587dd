package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationHourWsO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-从测量日界开始十分钟风速风向-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationHourWsOService extends IService<OceanStationHourWsO> {

    List<OceanStationHourWsO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




