package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 实况观测要素表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_live_observe_element_b")
public class LiveObserveElement implements Serializable {

    private static final long serialVersionUID = 696103184526913504L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     **/
    @TableField("code")
    private String code;
    /**
     * 名称
     **/
    @TableField("name")
    private String name;
    /**
     * 类型：海洋站oceanStation 浮标站buoyStation 船舶ship
     **/
    @TableField("type")
    private String type;
}



