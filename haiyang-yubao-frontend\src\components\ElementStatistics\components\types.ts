import { ComputedRef } from 'vue'

export type paramsType = {
  startTime?: string
  endTime?: string
  oceanStationCodeList?: string[]
  buoyStationCodeList?: string[]
  shipCodeList?: string[]
  element?: string // 气象要素
  type?: string
  relationStation?: string
}

export interface FormType {
  dataTime: [ComputedRef<string>, ComputedRef<string>]
  originalDatetime: [number, number]
  oceanStationCodeList: Array<string>
  method: number
  element: string
  type: number
  dimension: string
  threshold: string
}

export type DataItem = {
  [key: string]: any
}

export interface IResponseItem {
  airPressure: string | null
  airTemperature: string | null
  humidity: string | null
  id: string | null
  oceanStationCode: string | null
  oceanStationLocationJson: string | null
  oceanStationName: string | null
  rain: string | null
  salinity: string | null
  seaTemperature: string | null
  tide: string | null
  time: string | null
  visibility: string | null
  windDir: string | null
  windSpeed: string | null
  windWaveHeight: string | null
  windWavePeriod: string | null
}

export const showStationDialog = 'showStationDialog_47e1fa4a'
