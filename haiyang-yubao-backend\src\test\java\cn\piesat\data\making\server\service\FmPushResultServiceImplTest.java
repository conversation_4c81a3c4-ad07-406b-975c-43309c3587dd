package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPushResultServiceImplTest {

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

    @Test
    public void testAddFmPushResList(){
        fmPushResultServiceImpl.addFmPushResList(null,"SASFCR","123","456",null,null);
    }
}
