package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.ForecastTemplateColumn;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 预报模板-列定义服务接口
 *
 * <AUTHOR>
 */
public interface ForecastTemplateColumnService extends IService<ForecastTemplateColumn> {

    /**
     * 查询列表
     */
    List<ForecastTemplateColumn> getList(Long templateId);

    /**
     * 保存
     */
    void saveList(Long templateId, List<ForecastTemplateColumn> list);
}




