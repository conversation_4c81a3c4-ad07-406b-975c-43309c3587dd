package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmGraphicSignDTO;
import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.dto.FmGraphicTmplateSignDTO;
import cn.piesat.data.making.server.entity.FmGraphicSign;
import cn.piesat.data.making.server.vo.FmGraphicSignVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:37
 */
public interface FmGraphicSignService extends IService<FmGraphicSign>{
        List<FmGraphicSignVO> getList(String type);
        void saveByTemplateId(List<FmGraphicTmplateSignDTO> list, Long templateId);
}
