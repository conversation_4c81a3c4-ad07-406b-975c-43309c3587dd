package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.entity.FmPushResult;
import cn.piesat.data.making.server.service.FmPushResultService;
import cn.piesat.data.making.server.service.FmSenderService;
import cn.piesat.data.making.server.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@Slf4j
public class FmSenderEmailServiceImpl implements FmSenderService {

    @Value("${piesat.mailServerUrl}")
    private String mailServerUrl;

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

    @Override
    public void sender(FmPushResult fmPushResult) {

        if(fmPushResult!=null){
            try {
                EmailObj emailObj = new EmailObj();
                emailObj.setEmail(fmPushResult.getPushChannelId());
                emailObj.setContent(fmPushResult.getPushMsg());
                emailObj.setFilePath(fmPushResult.getPushContent());
                String fileName = fmPushResult.getPushContent().substring(fmPushResult.getPushContent().lastIndexOf("/")+1);
                emailObj.setFileName(fileName);

                String mailObjStr = JsonUtil.object2Json(emailObj);

                log.info(mailObjStr);
                log.info("mailServerUrl:"+mailServerUrl);

                //HttpClientUtil.post("http://127.0.0.1:19000/hainan_manager/mail/sendMail","{\"email\":\"<EMAIL>\",\"content\":\"123\",\"fileName\":\"测试附件\",\"filePath\":\"/data/server/hainan_manager/application-pre.yml\"}","","","");
                String retStr = HttpClientUtil.post(mailServerUrl,mailObjStr,"","","");

                log.info("返回值: "+retStr);

                fmPushResult.setPushFlag("1");
            } catch (IOException e) {
                e.printStackTrace();
                fmPushResult.setPushFlag("2");
            }
            fmPushResultServiceImpl.updateFmPushResult(fmPushResult);
        }
    }
}

class EmailObj{

    private String email;

    private String content;

    private String fileName;

    private String filePath;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
