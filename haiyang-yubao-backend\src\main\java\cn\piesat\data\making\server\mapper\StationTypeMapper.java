package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.StationTypeDTO;
import cn.piesat.data.making.server.entity.StationType;
import cn.piesat.data.making.server.vo.StationTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StationTypeMapper {

    StationTypeMapper INSTANCE = Mappers.getMapper(StationTypeMapper.class);

    /**
     * entity-->vo
     */
    StationTypeVO entityToVo(StationType entity);

    /**
     * dto-->entity
     */
    StationType dtoToEntity(StationTypeDTO dto);

    /**
     * entityList-->voList
     */
    List<StationTypeVO> entityListToVoList(List<StationType> list);

    /**
     * dtoList-->entityList
     */
    List<StationType> dtoListToEntityList(List<StationTypeDTO> dtoList);
}
