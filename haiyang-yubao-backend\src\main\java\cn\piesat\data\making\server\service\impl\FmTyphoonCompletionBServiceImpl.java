package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.dto.FmTyphoonCompletionBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonCompletionB;
import cn.piesat.data.making.server.dao.FmTyphoonCompletionBDao;
import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.service.FmTyphoonCompletionBService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.vo.FmTyphoonCompletionBVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 台风位置预警完成数据服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
@Service
@Slf4j
public class FmTyphoonCompletionBServiceImpl extends ServiceImpl<FmTyphoonCompletionBDao, FmTyphoonCompletionB> implements FmTyphoonCompletionBService {

    @Resource
    private FmTyphoonCompletionBDao fmTyphoonCompletionBDao;


    @Override
    public PageResult<FmTyphoonCompletionBVO> getPage(FmTyphoonCompletionBDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmTyphoonCompletionBVO> getList(FmTyphoonCompletionBDTO dto) {
        return null;
    }

    @Override
    public FmTyphoonCompletionBVO getById(Long id) {
        return null;
    }

    @Override
    public void save(FmTyphoonCompletionBDTO dto) {

    }

    @Override
    public void saveList(List<FmTyphoonCompletionBDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }

    @Override
    public FmTyphoonCompletionB getInfo(String typhoonNo, Date typhoonTime) {
        LambdaQueryWrapper<FmTyphoonCompletionB> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmTyphoonCompletionB::getTfbh,typhoonNo);
        wrapper.eq(FmTyphoonCompletionB::getTime,typhoonTime);
        return getOne(wrapper,false);
    }
}
