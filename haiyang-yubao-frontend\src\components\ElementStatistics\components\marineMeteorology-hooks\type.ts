export interface IOceanTableData {
  airPressure: string
  airTemperature: string
  humidity: string
  id: null
  oceanStationCode: string
  oceanStationLocationJson: string
  oceanStationName: string
  rain: string
  salinity: string
  seaTemperature: string
  tide: string
  time: string
  visibility: string
  windDir: string
  windSpeed: string
  windWaveHeight: null
  windWavePeriod: null
}

export interface IBuoyTableData {
  buoyStationCode: string
  buoyStationLocationJson: string
  buoyStationName: string
  buoydataAt: string
  buoydataBg: string
  buoydataBp: string
  buoydataBx: string
  buoydataCd: null
  buoydataCs: null
  buoydataHu: string
  buoydataNjd: null
  buoydataSl: string
  buoydataWd: string
  buoydataWda: null
  buoydataWdh: null
  buoydataWdm: null
  buoydataWs: string
  buoydataWsa: null
  buoydataWsh: null
  buoydataWsm: string
  buoydataWt: string
  buoydataYbg: string
  buoydataYzq: string
  buoydataZbg: string
  buoydataZq: string
  buoydataZzq: string
  id: null
  puPbg: null
  puPfzq: null
  time: string
  ylpuPbg: null
  ylpuPfzq: null
}

export interface IShipTableData {
  airPressure: number
  airTemperature: number
  dewPointTemp: number
  id: string
  latitude: number
  locationGeo: string
  locationJson: string
  longitude: number
  lowCloudAmount: number
  seaTemp: number
  signShip: string
  surgeDirection: number
  surgeHeight: number
  surgePeriod: number
  time: string
  totalCloudAmount: number
  visibility: number
  waveHeight: number
  wavePeriod: number
  windDirection: number
  windSpeed: number
}
