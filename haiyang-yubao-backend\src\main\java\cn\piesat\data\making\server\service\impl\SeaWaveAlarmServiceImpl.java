package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.common.JsonManipulation;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.SeaWaveAlarmDao;
import cn.piesat.data.making.server.dto.SeaWaveAlarmDTO;
import cn.piesat.data.making.server.dto.generate.WaveAlarmWordDTO;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.enums.ProductEnum;
import cn.piesat.data.making.server.enums.WarningMessageStatus;
import cn.piesat.data.making.server.mapper.SeaWaveAlarmMapper;
import cn.piesat.data.making.server.mapper.WaveAlarmWordMapper;
import cn.piesat.data.making.server.model.AlarmInfo;
import cn.piesat.data.making.server.processor.WaveGenerateText;
import cn.piesat.data.making.server.processor.WaveGenerateTextProcessor;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.utils.GenerateFileUtil;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.data.making.server.vo.UserVO;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import cn.piesat.security.ucenter.starter.utils.UserUtils;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海浪警报制作表信息(SeaWaveAlarm)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:19
 */
@Service("seaWaveAlarmService")
public class SeaWaveAlarmServiceImpl extends ServiceImpl<SeaWaveAlarmDao, SeaWaveAlarm> implements SeaWaveAlarmService {
    private  Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Value("${piesat.make.fax}")
    private String fax;
    @Value("${piesat.make.wave-alarm-template}")
    private String waveAlarmTemplate;
    @Autowired
    private AlarmLevelService alarmLevelService;
    @Autowired
    private FmTyphoonCompletionBService typhoonCompletionBService;
    @Autowired
    private FmTyphoonRealBService typhoonRealBService;
    @Autowired
    private FmTyphoonBService fmTyphoonBService;
    @Autowired
    private WaveGenerateTextProcessor waveGenerateTextProcessor;
    @Autowired
    private GenerateProductService generateProductService;
    @Autowired
    private KafkaTemplate kafkaTemplate;

    @Autowired
    private UserService userServiceImpl;

    @Override
    public PageResult pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime) {
        LambdaQueryWrapper<SeaWaveAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(SeaWaveAlarm::getCreateTime);
        wrapper.eq(SeaWaveAlarm::getDisplay,0);
        if (startTime != null) {
            wrapper.ge(SeaWaveAlarm::getReleaseTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(SeaWaveAlarm::getReleaseTime, endTime);
        }
        Page<SeaWaveAlarm> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }

    @Override
    public GenerateTextVO generateText(WaveGenerateText waveAlarmGenerateText) {
       return waveGenerateTextProcessor.generate(waveAlarmGenerateText,true);
    }

    @Override
    public SeaWaveAlarm saveInfo(SeaWaveAlarmDTO seaWaveAlarmDTO) {
        this.checkNumber(seaWaveAlarmDTO.getNumber());
        SeaWaveAlarm seaWaveAlarm = SeaWaveAlarmMapper.INSTANCE.toEntity(seaWaveAlarmDTO);
        //生成文件
        this.makeFile(seaWaveAlarm);
        seaWaveAlarm.setStatus(WarningMessageStatus.NOT_SUBMIT.getValue());
        if (!StringUtils.isEmpty(seaWaveAlarm.getAlarmArea())) {
            seaWaveAlarm.setAlarmArea(JsonManipulation.addGeoJsonProperties(seaWaveAlarm.getAlarmArea(), "2", seaWaveAlarm.getReleaseTime(),
                    seaWaveAlarm.getAlarmContent()));
        }
        this.save(seaWaveAlarm);

        if(StringUtils.hasText(seaWaveAlarm.getLastNumber()))
        this.updateDisplay(seaWaveAlarm.getLastNumber());

        return seaWaveAlarm;
    }

    public boolean updateByIdObj(SeaWaveAlarm seaWaveAlarm) {
        return this.updateById(seaWaveAlarm);
    }

    @Override
    public Boolean checkNumber(String number) {
        SeaWaveAlarm seaWaveAlarm = this.selectByNumber(number);
        if(Objects.nonNull(seaWaveAlarm)) throw new BusinessException("该编号已存在");
        return true;
    }

    @Override
    public SeaWaveAlarm selectByNumber(String number) {
        LambdaQueryWrapper<SeaWaveAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SeaWaveAlarm::getNumber,number);
        return this.getOne(wrapper, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void release(Long id) {

        SeaWaveAlarm alarm = this.getById(id);
        String wordFilePath = alarm.getWordFilePath();
        String htmlFilePath = alarm.getHtmlFilePath();
        String smsFilePath = alarm.getSmsFilePath();
        Date releaseTime = alarm.getReleaseTime();
        Long pushTaskId = generateProductService.sendPushServer("海浪警报产品推送", wordFilePath, htmlFilePath, smsFilePath, ProductEnum.WAVE_WORD, ProductEnum.WAVE_HTML,
                ProductEnum.WAVE_SMS, releaseTime);

        alarm.setStatus(WarningMessageStatus.SUBMIT.getValue());
        alarm.setPushTaskId(pushTaskId);
        this.updateById(alarm);

        AlarmInfo alarmInfo = new AlarmInfo();
        alarmInfo.setId(id.toString());
        alarmInfo.setCategory(2);
        //解除警报
        if (alarm.getAlarmLevel() == 5) {
            //追溯历史预警记录的id
            List<Long> longList = this.traceHistoryByCode(alarm.getNumber());
            String ids = longList.stream().map(String::valueOf).collect(Collectors.joining(","));
            alarmInfo.setIds(ids);
            alarmInfo.setReleaseState(0);
        } else {
            alarmInfo.setReleaseState(1);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        alarmInfo.setAlarmReleaseTime(sdf.format(alarm.getReleaseTime()));
        alarmInfo.setDescriptionInformation(alarm.getAlarmContent());
        logger.info("发送消息给信息中心，消息内容{}", JsonUtil.object2Json(alarmInfo));
        kafkaTemplate.send("piesat-sea-wave-alarm", JsonUtil.object2Json(alarmInfo));
        //发送xml给国突
    }

    private List<Long> traceHistoryByCode(String code) {
        List<SeaWaveAlarm> list = this.list();
        List<SeaWaveAlarm> history = new ArrayList<>();
        SeaWaveAlarm current = null;
        for (SeaWaveAlarm record : list) {
            if (record.getNumber().equals(code)) {
                current = record;
                break;
            }
        }
        while (current != null && current.getLastNumber() != null) {
            for (SeaWaveAlarm record : list) {
                if (record.getNumber().equals(current.getLastNumber())) {
                    history.add(record);
                    current = record;
                    break;
                }
            }
        }
        return history.stream().map(SeaWaveAlarm::getId).collect(Collectors.toList());
    }
    
    @Override
    public SeaWaveAlarm updateInfo(SeaWaveAlarmDTO seaWaveAlarmDTO) {
        SeaWaveAlarm seaWaveAlarm = Optional.ofNullable(this.getById(seaWaveAlarmDTO.getId())).orElseThrow(() -> new BusinessException("未找到对应警报制作单"));
        if(WarningMessageStatus.SUBMIT.getValue().equals(seaWaveAlarm.getStatus())) throw new BusinessException("已提交状态警报制作单不可修改");
        if(!seaWaveAlarmDTO.getNumber().equals(seaWaveAlarm.getNumber())){this.checkNumber(seaWaveAlarmDTO.getNumber());}
        seaWaveAlarm =  SeaWaveAlarmMapper.INSTANCE.toEntity(seaWaveAlarmDTO);
        //生成文件
        this.makeFile(seaWaveAlarm);
        if (!StringUtils.isEmpty(seaWaveAlarm.getAlarmArea())) {
            seaWaveAlarm.setAlarmArea(JsonManipulation.addGeoJsonProperties(seaWaveAlarm.getAlarmArea(), "2", seaWaveAlarm.getReleaseTime(),
                    seaWaveAlarm.getAlarmContent()));
        }
        this.updateById(seaWaveAlarm);
        return seaWaveAlarm;
    }

    @Override
    public void updateDisplay(String lastNumber) {
        LambdaUpdateWrapper<SeaWaveAlarm> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SeaWaveAlarm::getNumber,lastNumber);
        wrapper.set(SeaWaveAlarm::getDisplay,1);
        this.update(wrapper);
    }

    @Override
    public Map<Long, Long> statistic(Date startTime, Date endTime) {
        LambdaQueryWrapper<SeaWaveAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(SeaWaveAlarm::getCreateTime);
        wrapper.eq(SeaWaveAlarm::getDisplay,0);
        if (startTime != null) {
            wrapper.ge(SeaWaveAlarm::getReleaseTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(SeaWaveAlarm::getReleaseTime, endTime);
        }
        return this.list(wrapper).stream().collect(Collectors.groupingBy(SeaWaveAlarm::getAlarmLevel, Collectors.counting()));
    }


    private void makeFile(SeaWaveAlarm seaWaveAlarm){
        //查询警报级别信息
        AlarmLevel alarmLevel = alarmLevelService.getById(seaWaveAlarm.getAlarmLevel());
        //查询制作人信息
        //UserInfoDTO userInfo = UserUtils.getUserInfo(seaWaveAlarm.getMakeUser());

        UserVO userVO = userServiceImpl.getInfoById(seaWaveAlarm.getMakeUser());
        //转换dto
        WaveAlarmWordDTO waveAlarmWordDTO = WaveAlarmWordMapper.INSTANCE.toDTO(seaWaveAlarm,alarmLevel,userVO,fax);
        //判断是否是台风或冷空气和台风
        if(SeaWaveAlarmDTO.TYPHOON.equals(seaWaveAlarm.getSourceType()) || SeaWaveAlarmDTO.BOTH.equals(seaWaveAlarm.getSourceType())){
            Preconditions.checkArgument(StringUtils.hasText(seaWaveAlarm.getTyphoonNo()),"台风编码为空");
            Preconditions.checkArgument(seaWaveAlarm.getTyphoonTime()!=null,"台风数据时间为空");
            //查询台风信息
            FmTyphoonB typhoon = fmTyphoonBService.getInfo(seaWaveAlarm.getTyphoonNo());
            //查询台风路径信息
            FmTyphoonRealB typhoonReal = typhoonRealBService.getInfo(seaWaveAlarm.getTyphoonNo(), seaWaveAlarm.getTyphoonTime());
            //查询台风预计位置信息
            FmTyphoonCompletionB typhoonCompletionB = typhoonCompletionBService.getInfo(seaWaveAlarm.getTyphoonNo(), seaWaveAlarm.getTyphoonTime());
            WaveAlarmWordDTO.Typhoon typhoonDTO = WaveAlarmWordMapper.INSTANCE.toTyphoon(seaWaveAlarm, typhoon, typhoonReal, typhoonCompletionB);
            waveAlarmWordDTO.setTyphoon(typhoonDTO);
        }

        //处理上传图片转为数组
        waveAlarmWordDTO.setImages(GenerateFileUtil.generatePicture(seaWaveAlarm.getAlarmImages()));
        //拼接产出文件地址
        Date releaseTime = seaWaveAlarm.getReleaseTime();
        String wordFilePath = String.format(CommonConstant.ALERT_WAVE_WORD_FILE_NAME,baseOutputPath,DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateWord(wordFilePath,waveAlarmTemplate, waveAlarmWordDTO);
        //word转html
        String htmlFilePath = String.format(CommonConstant.ALERT_WAVE_HTML_FILE_NAME, baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime,
                "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateHtml(wordFilePath, htmlFilePath);
        //短信文件写入
        String smsFilePath = String.format(CommonConstant.ALERT_WAVE_TXT_FILE_NAME, baseOutputPath, DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        generateProductService.generateSms(smsFilePath,seaWaveAlarm.getSmsContent());


        seaWaveAlarm.setWordFilePath(wordFilePath);
        seaWaveAlarm.setSmsFilePath(smsFilePath);

    }

    public void downloadDoc(HttpServletResponse response, Long id){
        //stormSurgeAlarmS stormSurgeAlarmServiceImpl = stormSurgeAlarmServiceImpl.getById(id);
        SeaWaveAlarm seaWaveAlarm = this.getById(id);
        String filePath = seaWaveAlarm.getWordFilePath();
        String fileName = filePath.substring(filePath.lastIndexOf("/")+1);
        FileUtil.downloadFile(filePath,fileName,response);
    }


}

