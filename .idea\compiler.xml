<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for data-making-server" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <option name="mapstruct.suppressGeneratorTimestamp" value="true" />
        <option name="mapstruct.suppressGeneratorVersionInfoComment" value="true" />
        <option name="mapstruct.verbose" value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../Program Files (x86)/Maven/Repository/org/projectlombok/lombok/1.18.16/lombok-1.18.16.jar" />
          <entry name="$PROJECT_DIR$/../../../Program Files (x86)/Maven/Repository/org/mapstruct/mapstruct-processor/1.4.2.Final/mapstruct-processor-1.4.2.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../Program Files (x86)/Maven/Repository/org/mapstruct/mapstruct/1.4.2.Final/mapstruct-1.4.2.Final.jar" />
        </processorPath>
        <module name="data-making-server" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="data-making-server" options="-Amapstruct.suppressGeneratorTimestamp=true -Amapstruct.suppressGeneratorVersionInfoComment=true -Amapstruct.verbose=true -parameters" />
    </option>
  </component>
</project>