package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastTaskDTO;
import cn.piesat.data.making.server.entity.ForecastTask;
import cn.piesat.data.making.server.vo.ForecastTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ForecastTaskMapper {

    ForecastTaskMapper INSTANCE = Mappers.getMapper(ForecastTaskMapper.class);

    /**
     * entity-->vo
     */
    ForecastTaskVO entityToVo(ForecastTask entity);

    /**
     * dto-->entity
     */
    ForecastTask dtoToEntity(ForecastTaskDTO dto);

    /**
     * entityList-->voList
     */
    List<ForecastTaskVO> entityListToVoList(List<ForecastTask> list);

    /**
     * dtoList-->entityList
     */
    List<ForecastTask> dtoListToEntityList(List<ForecastTaskDTO> dtoList);
}
