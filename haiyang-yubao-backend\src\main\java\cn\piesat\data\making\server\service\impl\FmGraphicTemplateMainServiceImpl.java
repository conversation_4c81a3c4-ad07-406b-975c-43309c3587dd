package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dto.FmGraphicTemplateBDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import cn.piesat.data.making.server.entity.FmGraphicTemplateMain;
import cn.piesat.data.making.server.dto.FmGraphicTemplateMainDTO;
import cn.piesat.data.making.server.vo.FmGraphicTemplateMainVO;
import cn.piesat.data.making.server.dao.FmGraphicTemplateMainDao;
import cn.piesat.data.making.server.service.FmGraphicTemplateMainService;
import cn.piesat.data.making.server.mapper.FmGraphicTemplateMainMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-13 09:43:28
 */
@Service
@Slf4j
public class FmGraphicTemplateMainServiceImpl extends ServiceImpl<FmGraphicTemplateMainDao, FmGraphicTemplateMain> implements FmGraphicTemplateMainService {

    @Resource
    private FmGraphicTemplateMainDao fmGraphicTemplateMainDao;


    @Override
    public List<FmGraphicTemplateMainVO> getList(FmGraphicTemplateMainDTO dto) {
        LambdaQueryWrapper<FmGraphicTemplateMain> queryWrapper = createQueryWrapper(dto);
        List<FmGraphicTemplateMain> fmGraphicTemplateMains = fmGraphicTemplateMainDao.selectList(queryWrapper);
        return FmGraphicTemplateMainMapper.INSTANCE.entityListToVoList(fmGraphicTemplateMains);
    }

    private LambdaQueryWrapper<FmGraphicTemplateMain> createQueryWrapper(FmGraphicTemplateMainDTO dto) {
        LambdaQueryWrapper<FmGraphicTemplateMain> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getProductType())) {
            queryWrapper.eq(FmGraphicTemplateMain::getProductType, dto.getProductType());
        }
        return queryWrapper;
    }
}
