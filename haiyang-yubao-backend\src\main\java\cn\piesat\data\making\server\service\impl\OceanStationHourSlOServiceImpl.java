package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourSlODao;
import cn.piesat.data.making.server.entity.OceanStationHourSlO;
import cn.piesat.data.making.server.service.OceanStationHourSlOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-表层海水盐度-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourSlOServiceImpl extends ServiceImpl<OceanStationHourSlODao, OceanStationHourSlO>
        implements OceanStationHourSlOService {

    @Resource
    private OceanStationHourSlODao oceanStationHourSlODao;

    @Override
    public List<OceanStationHourSlO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourSlODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourSlODao.getMaxCreateTime();
    }
}





