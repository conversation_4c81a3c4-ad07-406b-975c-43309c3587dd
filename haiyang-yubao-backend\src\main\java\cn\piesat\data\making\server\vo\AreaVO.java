package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 区域表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AreaVO implements Serializable {

    private static final long serialVersionUID = 803774753399346188L;

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    private String code;
    /**
     * 名称
     **/
    private String name;
    /**
     * 区域类型编码
     **/
    private String areaTypeCode;
    /**
     * 位置
     **/
    private String locationGeo;
    /**
     * 位置
     **/
    private String locationJson;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer index;
}



