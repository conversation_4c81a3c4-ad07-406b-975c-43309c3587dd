package cn.piesat.data.making.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 警报消息状态
 **/
@AllArgsConstructor
@Getter
public enum WarningMessageStatus {
    /**
     * 未提交
     */
    NOT_SUBMIT(1, "未提交"),
    /**
     * 已提交
     */
    SUBMIT(2, "已提交");

    private Integer value;
    private String desc;

    public static Map<Integer, String> toMap() {
        return Stream.of(WarningMessageStatus.values())
                .collect(Collectors.toMap(enumItem -> enumItem.getValue(), enumItem -> enumItem.getDesc()));
    }
}
