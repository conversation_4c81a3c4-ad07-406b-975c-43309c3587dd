<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-07 14:11:18
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-03 09:52:33
 * @FilePath: \hainan-jianzai-web\src\views\forecast\drawing\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD
-->
<template>
  <div class="drawing-container">
    <base-map :current-product="currentProduct" v-if="haveTemplate">
      <preview-aside :current-product="currentProduct" />
    </base-map>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { BaseMap } from 'src/components/OpenlayersMap'
import { PreviewAside } from 'src/components/PreviewAside'
import Api from 'src/requests/forecast'
const currentProduct: any = ref({})
const haveTemplate = ref(false)
function addForecast() {
  Api.getTemplateByParent({
    productType: 1,
    status: true,
    templateType: 'YBTZZ'
  })
    .then((res: any) => {
      currentProduct.value = res[0]
      haveTemplate.value = true
    })
    .catch(() => {})
}
onMounted(() => {
  addForecast()
})
</script>

<style lang="scss">
.drawing-container{
  width: 100%;
  height: 100%;
  position: relative;
}
</style>