package cn.piesat.data.making.server;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.entity.Area;
import cn.piesat.data.making.server.entity.FmForecastProductData;
import cn.piesat.data.making.server.service.AreaService;
import cn.piesat.data.making.server.service.FmForecastProductDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class ForecastProductDataServiceTest {

    @Resource
    private AreaService areaService;
    @Resource
    private FmForecastProductDataService fmForecastProductDataService;

    @Test
    public void test() {
        List<FmForecastProductData> list = new ArrayList<>();

        List<String> sourceList = Arrays.asList("智能网格", "NMEFC");

        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Area::getAreaTypeCode, Arrays.asList("area", "region", "island", "resort", "beach"));
        List<Area> areaList = areaService.list(queryWrapper);

        List<String> elementList = Arrays.asList("Hs", "T01", "MaCOM", "Dir");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        sourceList.stream().forEach(source -> areaList.stream().forEach(area -> elementList.stream().forEach(element -> {
            FmForecastProductData data = new FmForecastProductData();
            data.setName(area.getName());
            /*try {
                data.setForecastStartTime(sdf.parse("2024-09-01 12:00:00"));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            data.setAreaId(area.getCode());*/
            data.setElementCode(element);
            data.setDataSource(source);
            Map<String, String> map = new HashMap();
            if (element.equals("Dir")) {
                map = this.randomDir();
            }
            if (element.equals("MaCOM")) {
                map = this.randomMaCOM();
            }
            if (element.equals("T01")) {
                map = this.randomT01();
            }
            data.setMinValue(map.get("min"));
            data.setAveValue(map.get("mean"));
            data.setMaxValue(map.get("max"));
            list.add(data);
        })));
        fmForecastProductDataService.saveBatch(list);
    }

    //Dir  浪高  0.5-3.1
    public Map randomDir() {
        Map<String, String> map = new HashMap<>();
        double min = 0.8;
        double max = 3.1;
        Random random = new Random();
        double nextDouble = random.nextDouble();
        double minValue = min + (max - min) * nextDouble - 0.3;
        double maxValue = min + (max - min) * nextDouble;
        map.put("min", minValue + "");
        map.put("mean", ((minValue + maxValue) / 2) + "");
        map.put("max", maxValue + "");
        return map;
    }

    //MaCOM  温度  24.8-32.0
    public static Map randomMaCOM() {
        Map<String, String> map = new HashMap<>();
        double min = 28.8;
        double max = 32;
        Random random = new Random();
        double nextDouble = random.nextDouble();
        double minValue = min + (max - min) * nextDouble - 4;
        double maxValue = min + (max - min) * nextDouble;
        map.put("min", minValue + "");
        map.put("mean", ((minValue + maxValue) / 2) + "");
        map.put("max", maxValue + "");
        return map;
    }

    //T01  浪周期  4.0~16.0 s
    public static Map randomT01() {
        Map<String, String> map = new HashMap<>();
        double min = 10.0;
        double max = 16.0;
        Random random = new Random();
        double nextDouble = random.nextDouble();
        double minValue = min + (max - min) * nextDouble - 6;
        double maxValue = min + (max - min) * nextDouble;
        map.put("min", minValue + "");
        map.put("mean", ((minValue + maxValue) / 2) + "");
        map.put("max", maxValue + "");
        return map;
    }

    public static void main(String[] args) {
        System.out.println(JsonUtil.object2Json(randomMaCOM()));
    }
}
