import { onMounted, onUnmounted, ref, Ref, toRefs, watch } from 'vue'
import { ISegmentOptions, Tool } from '../types'
import type { IPopup } from 'src/utils/olPopup/ol-popup.type'
import { Select } from 'ol/interaction'
import { SimpleGeometry } from 'ol/geom'
import VectorSource from 'ol/source/Vector'
import VectorLayer from 'ol/layer/Vector'
import Map from 'ol/Map'
import { useSegmentInject } from 'src/components/OpenlayersMap/components/CoastalSegmentsTools/hooks/useSegmentInject'
import { Feature, MapBrowserEvent } from 'ol'
import { EventsKey } from 'ol/events'
import { unByKey } from 'ol/Observable'
import Point from 'ol/geom/Point'
import { Icon, Style } from 'ol/style'

export function useLabel(opt: {
  segmentOptions: Ref<ISegmentOptions>
  vectorSource: VectorSource<SimpleGeometry>
  vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
  popupFactory: () => IPopup
}) {
  let labelManager: LabelManager | null = null
  const showDialog = ref(false)
  const formInDialog = ref({
    text: ''
  })
  const { text } = toRefs(formInDialog.value)

  const getMap = useSegmentInject('getMap').inject()

  if (!getMap) {
    throw new Error('注入失败')
  }

  onMounted(() => {
    getMap(map => {
      labelManager = new LabelManager({
        map,
        ...opt,
        showDialog,
        formInDialog: text
      })
    })
  })

  onUnmounted(() => {
    labelManager?.close()
  })

  watch(
    () => opt.segmentOptions.value.currentTool,
    (value, oldValue) => {
      if (oldValue === Tool.NONE && value === Tool.LABEL) {
        labelManager?.start()
      } else if (oldValue === Tool.LABEL && value === Tool.NONE) {
        labelManager?.close()
      }
    }
  )

  return {
    showDialog,
    formInDialog,
    onConfirm: () => {
      labelManager?.onConfirm()
    },
    clearLabel() {
      labelManager?.clear()
    }
  }
}

class LabelManager {
  get coordinate(): number[] {
    return this._coordinate
  }

  private _coordinate: number[] = []
  private _eventKey: EventsKey | null = null
  private _pointVecSource: VectorSource<Point>
  private _pointVecLayer: VectorLayer<VectorSource<Point>>


  constructor(
    protected opt: {
      popupFactory: () => IPopup
      segmentOptions: Ref<ISegmentOptions>
      vectorSource: VectorSource<SimpleGeometry>
      vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
      map: Map
      showDialog: Ref<boolean>
      formInDialog: Ref<string>
    }
  ) {
    this._pointVecSource = new VectorSource<Point>()
    this._pointVecLayer = new VectorLayer({
      zIndex: 100,
      source: this._pointVecSource
    })
    // 向后兼容
    this._pointVecSource.set('layerType', '选取工具文字')
    this.opt.map.addLayer(this._pointVecLayer)
  }

  start() {
    // this.opt.showDialog.value = true
    this._eventKey = this.opt.map.on('click', this.onMapClick.bind(this))
  }

  onMapClick(e: MapBrowserEvent<UIEvent>) {
    this._coordinate = e.coordinate
    const eventPixel = this.opt.map.getEventPixel(e.originalEvent)
    const feature = this.opt.map.forEachFeatureAtPixel<Feature<SimpleGeometry>>(
      eventPixel,
      fea => fea as Feature<SimpleGeometry>
    )
    if (!(feature && this.opt.vectorSource.hasFeature(feature))) {
      return
    }
    this.showDialog()
  }

  createPopup(coordinate: number[]) {
    this.showDialog()
    const canvas = drawCanvas(
      30,
      30,
      90,
      40,
      5,
      '#000000',
      '#fff',
      this.opt.formInDialog.value
    )
    const iconFeature = new Feature<Point>({
      geometry: new Point(coordinate)
    })
    const iconStyle = new Style({
      image: new Icon({
        src: canvas.toDataURL(),
        imgSize: [300, 200],
        offset: [0, 0]
      })
    })
    iconFeature.setStyle([iconStyle])
    iconFeature.set('text', this.opt.formInDialog.value)
    this._pointVecSource.addFeature(iconFeature)
    this.hideDialog()
  }

  showDialog() {
    this.opt.showDialog.value = true
  }

  hideDialog() {
    this.opt.showDialog.value = false
  }

  onConfirm(): void {
    // 显示 popup
    // this._currentPopup?.show(this._coordinate, '<div>hello world</div>')
    this.opt.showDialog.value = false
    this.createPopup(this._coordinate)
  }

  clear() {
    this._pointVecSource.clear()
  }

  close() {
    this._eventKey && unByKey(this._eventKey)
    this.opt.map.removeLayer(this._pointVecLayer)
  }
}

function drawCanvas(
  x: number,
  y: number,
  width: number,
  height: number,
  radius: number,
  strokeStyle: string,
  fillStyle: string,
  size: string
) {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    return canvas
  }

  ctx.strokeStyle = strokeStyle
  ctx.fillStyle = fillStyle
  ctx.beginPath()

  // 绘制弧长、直线
  ctx.arc(x + radius, y + radius, radius, Math.PI, (Math.PI * 3) / 2, false)
  ctx.lineTo(x + width - radius, y)

  ctx.arc(
    x + width - radius,
    y + radius,
    radius,
    (Math.PI * 3) / 2,
    Math.PI * 2,
    false
  ) // 115 45 5
  ctx.lineTo(x + width, y + height - radius) // 120 65

  ctx.arc(
    x + width - radius,
    y + height - radius,
    radius,
    0,
    Math.PI / 2,
    false
  ) //115 65 5
  ctx.lineTo(x + width - 40, y + height) // 50 70
  ctx.lineTo(x + width - radius, y + height + 15) // 30 85
  ctx.lineTo(x + width - 10, y + height) // 35 70

  ctx.arc(x + radius, y + height - radius, radius, Math.PI / 2, Math.PI, false)
  ctx.lineTo(x, y + radius)

  ctx.closePath()
  ctx.fill()

  // 绘制文字
  const textX = 45
  const textY = 55
  ctx.font = 'bold 14px Arial'
  ctx.fillStyle = '#000'
  ctx.fillText(size + '厘米', textX, textY)

  // 绘制图片
  // var img = document.createElement("img");
  // ctx.drawImage(img, 0, 80);

  // 返回
  return canvas
}
