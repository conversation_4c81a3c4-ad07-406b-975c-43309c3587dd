package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import cn.piesat.common.utils.Constant;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:24
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_data_source")
public class FmGraphicDataSource implements Serializable {

    private static final long serialVersionUID = 771319850590819067L;


    @TableField("id")
    private Long id;
    @TableField("data_source")
    private String dataSource;
    @TableField("name")
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
