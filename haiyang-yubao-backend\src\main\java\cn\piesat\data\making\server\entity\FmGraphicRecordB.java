package cn.piesat.data.making.server.entity;


import java.io.Serializable;
import java.util.Date;

import cn.piesat.data.making.server.config.PgGeometryTypeHandler;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 图形记录表实体类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:33
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_record_b")
public class FmGraphicRecordB implements Serializable {

    private static final long serialVersionUID = 359932240163603197L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;
    /**
     * 预报任务id
     */
    @TableField("forecast_task_id")
    private Long forecastTaskId;
    /**
     * 图形模板id
     */
    @TableField("graphic_template_id")
    private Long graphicTemplateId;
    /**
     * 文件路径
     */
    @TableField("file_url")
    private String fileUrl;
    /**
     * 文件路径彩色
     */
    @TableField("file_url_colorful")
    private String fileUrlColorful;
    /**
     * 是否选中
     */
    @TableField("checked")
    private Boolean checked;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private Long createUserId;
    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private Long updateUserId;
    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 数据源
     */
    @TableField("data_source")
    private String dataSource;
    /**
     * 起报时间
     */
    @TableField("forecast_start_time")
    private Date forecastStartTime;
    /**
     * 图形json
     */
    @TableField(value = "graphic_json")
    private String graphicJson;

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Date getForecastStartTime() {
        return forecastStartTime;
    }

    public void setForecastStartTime(Date forecastStartTime) {
        this.forecastStartTime = forecastStartTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getForecastTaskId() {
        return forecastTaskId;
    }

    public void setForecastTaskId(Long forecastTaskId) {
        this.forecastTaskId = forecastTaskId;
    }

    public Long getGraphicTemplateId() {
        return graphicTemplateId;
    }

    public void setGraphicTemplateId(Long graphicTemplateId) {
        this.graphicTemplateId = graphicTemplateId;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getFileUrlColorful() {
        return fileUrlColorful;
    }

    public void setFileUrlColorful(String fileUrlColorful) {
        this.fileUrlColorful = fileUrlColorful;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getGraphicJson() {
        return graphicJson;
    }

    public void setGraphicJson(String graphicJson) {
        this.graphicJson = graphicJson;
    }
}
