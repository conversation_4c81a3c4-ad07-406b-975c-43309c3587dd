<template>
  <div class="defense-guide">
    <Aside @selected="selectHandler" />
    <div class="content">
      <div class="content-header d-flex flex-justify-between flex-align-center">
        <h3>指南详情</h3>
        <div class="btns">
          <qx-button @click="onEdit">编辑</qx-button>
          <qx-button class="primary" @click="onSave">保存</qx-button>
        </div>
      </div>

      <div class="main-container">
        <div class="content-filter">
          <n-form
            ref="formRef"
            class="forecast-temp-form"
            :model="form"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="left"
          >
            <n-form-item label="指南名称" path="guideName">
              <n-input
                v-model:value="form.guideName"
                placeholder="请输入"
                clearable
                style="width: 70%"
                :readonly="!isEdit"
              />
            </n-form-item>
            <n-form-item label="指南类型" path="alarmType">
              <n-radio-group
                v-model:value="form.alarmType"
                name="radiogroup"
                :disabled="!isEdit"
                @update:value="onAlarmTypeChange"
              >
                <n-radio :value="1">海浪</n-radio>
                <n-radio :value="2">风暴潮</n-radio>
              </n-radio-group>
            </n-form-item>
          </n-form>
        </div>
        <n-data-table
          :single-line="false"
          :columns="columns"
          :data="tableData"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Aside } from './index'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/forecast'
import { ref, onMounted, h, reactive } from 'vue'
import { useMessage } from 'naive-ui'
import QxTableEdit from 'src/components/QxTableEdit/index.js'

const formRef = ref()
const message = useMessage()
const columns = ref<any[]>([
  {
    title: '警告级别',
    key: 'level',
    width: 120,
    align: 'center'
  },
  {
    title: '防御指南',
    key: 'guideContent',
    render(row: any, index: number) {
      return h(QxTableEdit, {
        value: row.guideContent,
        isEdit: isEdit.value,
        onUpdateValue(v: any) {
          tableData.value[index].guideContent = v
        }
      })
    }
  }
])

const tableData = ref<any[]>([])

type FormType = {
  [key: string]: any
}

const form: FormType = reactive({
  alarmType: 1,
  guideName: '',
  status: 0
})

const rules = {
  guideName: { required: true, message: '请输入指南名称', trigger: 'blur' }
}

function selectHandler(val: string, option: any) {
  getAlarmInfo(option?.id)
}

function getAlarmInfo(val: string) {
  Api.getAlarmDefenseGuideInfoById(val)
    .then((res: any) => {
      if (res) {
        Object.keys(res).forEach((item: string) => {
          if (form.hasOwnProperty(item)) {
            form[item] = res[item]
          }
        })
        form.id = res.id
        let { guideLevelList } = res
        getAlarmLevel(guideLevelList)
      }
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
}

function getAlarmLevel(data: any) {
  tableData.value = []
  Api.getAlarmLevel()
    .then((res: any) => {
      let result: any[] = []

      res.forEach((item: any) => {
        let obj = {
          level: item.levelColor,
          levelId: item.id,
          guideContent: '',
          id: ''
        }
        data.forEach((d: any) => {
          if (item.id == d.levelId) {
            obj.level = item.levelColor
            ;(obj.guideContent = d.guideContent), (obj.id = d.id)
          }
        })
        result.push(obj)
      })
      console.log(result, 'result')
      tableData.value = result
    })
    .catch(e => {
      let { msg = null } = e?.response?.data
      message.error(msg || '获取数据失败')
      console.error(e, '获取警报级别')
    })
}
function onAlarmTypeChange(){
  formRef.value.restoreValidation()
}

let isEdit = ref(false)
function onEdit() {
  isEdit.value = true
  message.success('已进入编辑状态,点击表格可进行编辑')
}

function onSave() {
  let params = JSON.parse(JSON.stringify(form))
  let guideLevelList = tableData.value.map(item => {
    return {
      levelId: item.levelId,
      guideContent: item.guideContent
    }
  })
  params.guideLevelList = guideLevelList
  Api.editAlarmDefenseGuide(params)
    .then((res: any) => {
      message.success('操作成功')
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '操作失败')
    })
}
</script>

<style lang="scss">
.defense-guide {
  display: flex;
  .content {
    flex: 1;
    margin-left: 20px;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    .content-header {
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      box-sizing: border-box;
      padding: 9px 20px;
      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 21px;
      }
    }
    .main-container {
      box-sizing: border-box;
      padding: 85px 68px;
      .n-data-table {
        width: 80%;
      }
    }
  }
  .n-radio__dot.n-radio__dot--checked {
    box-shadow: var(--n-box-shadow-active) !important;
    &::before {
      background: var(--n-dot-color-active) !important;
    }
  }
  .n-radio__label {
    font-size: 14px;
    color: var(--n-text-color) !important;
  }
}
</style>
