package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.generate.WaveAlarmWordDTO;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.vo.UserVO;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import com.deepoove.poi.data.FilePictureRenderData;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface WaveAlarmWordMapper {
    WaveAlarmWordMapper INSTANCE = Mappers.getMapper(WaveAlarmWordMapper.class);

    @Mapping(source = "typhoon.tfbh",target = "tfbh")
    @Mapping(source = "typhoon.name",target = "name")
    @Mapping(source = "saveInfo.typhoonTime",target = "time",dateFormat = "dd日HH时")
    WaveAlarmWordDTO.Typhoon toTyphoon(SeaWaveAlarm saveInfo, FmTyphoonB typhoon, FmTyphoonRealB typhoonReal, FmTyphoonCompletionB typhoonCompletionB);

    @Mapping(target = "time", source = "saveInfo.releaseTime", dateFormat = "yyyy年MM月dd日HH时")
    WaveAlarmWordDTO toDTO(SeaWaveAlarm saveInfo, AlarmLevel alarmLevel, UserInfoDTO userInfo, String fax);

    @Mapping(target = "time", source = "saveInfo.releaseTime", dateFormat = "yyyy年MM月dd日HH时")
    @Mapping(target = "eMail", source = "userVO.email")
    WaveAlarmWordDTO toDTO(SeaWaveAlarm saveInfo, AlarmLevel alarmLevel, UserVO userVO, String fax);
}
