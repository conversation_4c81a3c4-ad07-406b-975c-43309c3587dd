package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.ForecastTask;
import cn.piesat.data.making.server.vo.ForecastTaskVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 预报任务表服务接口
 *
 * <AUTHOR>
 */
public interface ForecastTaskService extends IService<ForecastTask> {

    /**
     * 查询列表
     */
    List<ForecastTaskVO> getList(Integer status);
    /**
     * 查询列表
     */
    List<ForecastTask> getList();
    /**
     * 查询状态统计信息
     */
    Map<String, Long> getInfo();

    /**
     * 保存
     */
    void createForecastTask();
}




