package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.StationType;
import cn.piesat.data.making.server.model.StationInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 站点类型表服务接口
 *
 * <AUTHOR>
 */
public interface StationTypeService extends IService<StationType> {

    /**
     * 查询列表
     */
    List<StationInfo> getTreeList(String name, String flag, String code);
}




