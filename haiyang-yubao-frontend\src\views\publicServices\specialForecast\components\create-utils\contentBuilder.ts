import type { IFeatureCategory } from 'src/views/publicServices/specialForecast/components/create-hooks/types'

interface IContent<T = Record<string, any>> {
  value: string
  key: string
  info?: T
}

interface IContentGenerator {
  findByKey(key: string): IContent | undefined

  get contents(): IContent[]

  get contentsMap(): Record<string, string>
}

/**
 * 内容管理 & 生成 抽象类
 */
export abstract class AbstractContent implements IContentGenerator {
  protected _contents: IContent[] = []

  /**
   * 获取内容对象数组
   */
  get contents(): IContent[] {
    return this._contents
  }

  /**
   * 根据 key 查找内容对象
   * @param key
   */
  findByKey(key: string): IContent | undefined {
    return this._contents.find(i => i.key === key)
  }

  /**
   * 对象数组 -> 对象
   */
  get contentsMap(): Record<string, string> {
    const obj: Record<string, string> = {}
    this._contents.forEach(content => {
      obj[content.key] = content.value
    })
    return obj
  }
}

/**
 * 内容管理
 */
export class ContentManager
  extends AbstractContent
  implements IContentGenerator
{
  constructor(protected _options: { generator?: IContentGenerator[] } = {}) {
    super()
    _options.generator?.forEach(generator => {
      this._contents.push(...generator.contents)
    })
  }

  override get contentsMap(): Record<string, string> {
    const obj = {}
    this._options.generator?.map(g => {
      const map = g.contentsMap
      Object.assign(obj, map)
    })
    return obj
  }
}

/**
 * 内容生成器: 有效波高
 */
export class WaveHeightContent
  extends AbstractContent
  implements IContentGenerator
{
  static key = 'waveAnnotation'

  constructor() {
    super()
    this._contents.push({
      key: WaveHeightContent.key,
      value: '注: 预报波高为有效波高'
    })
  }
}

/**
 * 内容生成器: 网箱/海上气田/风电等防御措施
 */
export class NetCageContent
  extends AbstractContent
  implements IContentGenerator
{
  static key = 'suggestion'
  static categoryMap: Record<IFeatureCategory, IFeatureCategory> = {
    WX: 'WX',
    HSQT: 'HSQT',
    FD: 'FD'
  }
  protected actionMap = new window.Map([
    [
      NetCageContent.categoryMap.WX,
      () =>
        this._contents.push({
          key: NetCageContent.key,
          value: `防御措施建议:密切关注预警信息，检查与加固网箱框架，检查与加固锚泊系统，检查与加固网衣系统。`
        })
    ],
    [
      NetCageContent.categoryMap.HSQT,
      () =>
        this._contents.push({
          key: NetCageContent.key,
          value: `防御措施建议:密切关注预警信息，检查与加固锚泊系统，做好放浪避浪措施，作业人员及时撤离上岸。`
        })
    ],
    [
      NetCageContent.categoryMap.FD,
      () =>
        this._contents.push({
          key: NetCageContent.key,
          value: `防御措施建议:密切关注预警信息，检查与加固网箱框架，检查与加固锚泊系统，检查与加固网衣系统。`
        })
    ]
  ])

  constructor(options: { categoryNames: string[] }) {
    super()
    if (options.categoryNames.length === 0) {
      return
    }
    const arr = [...new Set(options.categoryNames)]
    arr.forEach(categoryName => {
      this.actionMap.get(categoryName as IFeatureCategory)?.()
    })
  }

  override get contentsMap(): Record<string, string> {
    return {
      [NetCageContent.key]: `${this._contents.map(i => i.value).join('')}`
    }
  }
}

/**
 * 内容生成器: 周边海洋设施抗台风信息
 */
export class SeaRanchContent
  extends AbstractContent
  implements IContentGenerator
{
  static key = 'ranch'
  static mapping: Record<string, { windLevel: string }> = {
    风电CZ1: { windLevel: '14' },
    风电CZ2: { windLevel: '17' },
    风电CZ3: { windLevel: '14-15' },
    海油平台: { windLevel: '17' },
    乐东海洋牧场: { windLevel: '17' },
    东方海洋牧场: { windLevel: '17' }
  }

  _contents: IContent<{ name: string; windLevel: string; type: string }>[] = []

  constructor(options: {
    seaRanchStationInfo: { name: string; type: string }[]
    driftingImagePath: string
  }) {
    super()
    if (
      options.seaRanchStationInfo.length === 0 ||
      !options.driftingImagePath
    ) {
      return
    }
    options.seaRanchStationInfo.forEach(info => {
      const name = info.name
      const type = info.type
      const _level = SeaRanchContent.mapping[type].windLevel
      const level = _level ? _level : '-'
      this._contents.push({
        key: SeaRanchContent.key,
        value: `【${name}】设计抗风能力等级为【${level}级】，结合本次预报信息，存在走锚/位移风险。`,
        info: {
          name: name,
          windLevel: level,
          type: type
        }
      })
    })
  }

  get contentsMap(): Record<string, string> {
    const names = this._contents.map(i => i.info!.name)
    const values = this._contents.map(i => {
      return `${i.info!.windLevel}级`
    })

    if (this._contents.length === 0) {
      return {
        [`${SeaRanchContent.key}Name`]: '',
        [`${SeaRanchContent.key}Value`]: ''
      }
    }

    return {
      [`${SeaRanchContent.key}Name`]: `【${names.join('、')}】`,
      [`${SeaRanchContent.key}Value`]: `抗风能力等级分别为【${values.join(
        '、'
      )}】，结合本次预报信息，存在走锚/位移风险。`
    }
  }
}

/**
 * 内容生成器: 海上气田防御措施
 */
// export class SeaGasFieldContent
//   extends AbstractContent
//   implements IContentGenerator
// {
//   static key = 'seaGasField'
//
//   constructor(options: { seaGasFieldNames: string[] }) {
//     super()
//     if (options.seaGasFieldNames.length === 0) {
//       return
//     }
//     this._contents.push({
//       key: SeaGasFieldContent.key,
//       value: `防御措施建议:密切关注预警信息，检查与加固锚泊系统，做好放浪避浪措施，作业人员及时撤离上岸。`
//     })
//   }
// }

/**
 * 内容生成器: 漂移路径
 */
export class DriftingContent
  extends AbstractContent
  implements IContentGenerator
{
  static key = 'drifting'

  constructor(options: { driftingImagePath: string }) {
    super()
    if (!options.driftingImagePath) {
      return
    }
    this._contents.push({
      key: DriftingContent.key,
      value: `图：漂移路径`
    })
  }
}
