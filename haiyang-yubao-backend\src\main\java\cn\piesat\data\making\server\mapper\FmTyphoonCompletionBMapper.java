package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmTyphoonCompletionBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonCompletionB;
import cn.piesat.data.making.server.vo.FmTyphoonCompletionBVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 台风位置预警完成数据Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
@Mapper(componentModel = "spring")
public interface FmTyphoonCompletionBMapper {

    FmTyphoonCompletionBMapper INSTANCE = Mappers.getMapper(FmTyphoonCompletionBMapper.class);

    /**
     * entity-->vo
     */
    FmTyphoonCompletionBVO entityToVo(FmTyphoonCompletionB entity);

    /**
     * dto-->entity
     */
    FmTyphoonCompletionB dtoToEntity(FmTyphoonCompletionBDTO dto);

    /**
     * entityList-->voList
     */
    List<FmTyphoonCompletionBVO> entityListToVoList(List<FmTyphoonCompletionB> list);

    /**
     * dtoList-->entityList
     */
    List<FmTyphoonCompletionB> dtoListToEntityList(List<FmTyphoonCompletionBDTO> dtoList);
}
