<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-20 13:27:16
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-03 10:57:58
 * @FilePath: /hainan-jianzai-web/src/views/analysis/components/webGLFlowField.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-if="isShow" ref="flowField" class="flowField">
    <canvas id="flowDistribution"></canvas>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { colorObj } from 'src/utils/hexToRgb.js'
import WindGL from 'src/utils/webglwind/index.js'
import eventBus from 'src/utils/eventBus'

let isShow = ref(false)
let flowField = ref(null)

const props = defineProps({
  map: {
    type: Object as () => any,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 100
  },
  height: { type: Number, default: 100 }
})

let flowFiledLayer: any = ref(null)
let jsonUrl = ref<string>('')
let imgUrl = ref<string>('/mapData/flowFieldImg.png')
let colorRamp = ref([])

type RenderFunction = (canvas: any, color: string, type: string) => WindGL

function createWebGLFlowFieLayer() {
  const styleColor = colorObj(colorRamp.value)
  class WindObj {
    map: any
    canvas: any
    render: RenderFunction
    wind: any
    gl: any
    color: any
    isFirst: boolean
    isRemove: boolean
    constructor(obj: any) {
      this.map = obj.map
      this.canvas = obj.canvas
      this.render = obj.render
      this.wind = null
      this.gl = null
      this.color = obj.color
      this._init()
      this.isFirst = true
      this.isRemove = false
    }
    _init = () => {
      nextTick(() => {
        const id = '#flowDistribution'
        this.canvas = document.querySelector(id)
        this.canvas.width = this.canvas.clientWidth
        this.canvas.height = this.canvas.clientHeight
        this.wind = this.render(this.canvas, this.color, '流场')
      })
      // this.rendercomplete =
      // this.changeSize = this.map.on("change:size", (ext) => {
      //   this.wind.resize(ext.target.getSize());
      // });
    }
    remove = () => {
      this.wind.remove()
      const id = '#flowDistribution'
      const canvas = document.querySelector(id) // 获取Canvas元素\
      canvas && canvas.remove() // 删除Canvas元素

      // 创建Canvas元素
      const canvasEl = document.createElement('canvas')
      canvasEl.width = props.width
      canvasEl.height = props.height

      canvasEl.id = id.slice(1)
      canvasEl.style.display = 'block'
      canvasEl.style.width = '100%'
      canvasEl.style.height = '100%'
      canvasEl.style.pointerEvents = 'none'

      // @ts-ignore
      flowField.value?.appendChild(canvasEl)
      this.isRemove = true
    }
    changeColor = (color: any) => {
      // 更改风流场颜色
      this.wind.setColorRamp(color)
      this.wind.resetWindTexture()
      this.wind.resize()
      // this.change(1);
    }
    change = (jsonUrl: string, pngUrl: string) => {
      // 更改风流场数据，用于播放或者更换风流场数据
      const _this = this
      // 读取网络
      // if (jsonUrl) {
      //   fetch(jsonUrl)
      //     .then(rlt => rlt.json())
      //     .then(function (windData1) {
      //       const windImage = new Image();
      //       windImage.crossOrigin = "Anonymous";
      //       windImage.src = pngUrl;
      //       // windImage.src=require("./alt20230111_100.png")
      //       windImage.onload = function () {
      //         _this.wind.setWind(windData1, windImage);
      //       };
      //     });
      // }
      // 读取本地
      const windData1 = {
        width: 661,
        height: 463,
        uMin: -30,
        uMax: 30,
        vMin: -30,
        vMax: 30,
        Lonmin: 105,
        Latmin: 8,
        Lonmax: 125,
        Latmax: 22,
        numParticles: 15000,
        speedFactor: 0.25
      }
      const windImage = new Image()
      windImage.crossOrigin = 'Anonymous'
      windImage.src = pngUrl
      windImage.src = '/mapData/flowFieldImg.png'
      windImage.onload = function () {
        _this.wind.setWind(windData1, windImage)
      }
    }
  }
  let windObj = null
  windObj = new WindObj({
    map: props.map,
    render: render,
    color: styleColor,
    type: 'OWV_model_eastward_wind'
  })
  flowFiledLayer.value = windObj
}

let frameID = ref<any>(null)
function render(canvas: any, color: string, type: string) {
  const gl = canvas.getContext('webgl', {
    antialiasing: false,
    preserveDrawingBuffer: false
  })
  const wind = new WindGL(gl, null, null, canvas, props.map, color, type, { fadeOpacity: 0.998 })
  function frame() {
    if (wind.windData) {
      wind.draw()
    }
    frameID.value = window.requestAnimationFrame(frame)
  }
  frame()
  // @ts-ignore
  props.map.on('postrender', () => {
    if (wind != null && wind.windData) {
      wind.resetWindTexture()
    }
  })

  fetch(jsonUrl.value)
    .then(rlt => rlt.json())
    .then(function (windData1) {
      const windImage = new Image()
      windImage.crossOrigin = 'Anonymous'
      windImage.src = imgUrl.value
      wind.numParticles = windData1.numParticles
        ? windData1.numParticles
        : 10000
      windImage.onload = function () {
        wind.setWind(windData1, windImage)
      }
    })
    .catch(e => {
      console.error(e, 'fetch-json')
    })

  return wind
}

onMounted(() => {
  eventBus.on('renderFlowField', (params: any) => {
    if (jsonUrl.value !== params?.jsonUrl) {
      jsonUrl.value = params?.jsonUrl ? params.jsonUrl : ''
      imgUrl.value = params?.imgUrl
        ? params.imgUrl
        : '/mapData/flowFieldImg.png'
      colorRamp.value = params?.colorList
      isShow.value = true
      nextTick(() => {
        createWebGLFlowFieLayer()
      })
    }
  })
  eventBus.on('clearFlowField', () => {
    jsonUrl.value = ''
    if (flowFiledLayer.value) {
      flowFiledLayer.value?.remove()
      flowFiledLayer.value = null
      isShow.value = false
    }
  })
})

onBeforeUnmount(() => {
  eventBus.off('clearFlowField')
  eventBus.off('renderFlowField')
})
</script>
<style lang="scss">
.flowField {
  // position: relative;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
  #flowDistribution {
    display: block;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-color: transparent;
  }
  .color {
    position: fixed;
    bottom: 40px;
    width: 500px;
    display: flex;
    justify-content: space-between;
    right: 20px;
    height: 20px;
    border-radius: 10px;
    div {
      // position: absolute;
      flex: 1;
      text-align: center;
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 20px;
    }
  }
}
</style>
