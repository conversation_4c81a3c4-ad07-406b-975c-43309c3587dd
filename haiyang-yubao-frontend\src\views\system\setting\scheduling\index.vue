<template>
  <!-- 排班管理 -->
  <div class="scheduling">
    <div class="content">
      <div class="content-header d-flex flex-justify-between">
        <div class="d-flex flex-align-center">
          <h3>排班表</h3>
          <n-date-picker
            v-model:value="schedulingDate"
            type="month"
            format="y年 M月"
            year-format="y年"
            month-format="M月"
            @update:value="onConfirm"
          />
        </div>
        <div class="operate-btns d-flex flex-align-center">
          <qx-button @click="onEdit(true)">编辑</qx-button>
          <qx-button @click="onSave">保存</qx-button>
          <n-upload
            action=""
            :show-file-list="false"
            :custom-request="onImport"
          >
            <qx-button>导入</qx-button>
          </n-upload>

          <qx-button class="primary" @click="onExport">导出</qx-button>
        </div>
      </div>

      <div class="table-container">
        <!-- <div class="table-legend text-right">
          <span><i></i>主班</span>
          <span><i></i>辅班</span>
        </div> -->

        <n-data-table
          v-loading="loading"
          :columns="columns"
          :data="data"
          :bordered="false"
          :single-line="false"
          single-column
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { h, ref, onMounted } from 'vue'
import moment from 'moment'
import Api from 'src/requests/scheduling'
import QxTableEdit from 'src/components/QxTableEdit/index.js'
import { useMessage } from 'naive-ui'
import { groupBy } from 'lodash'

const schedulingDate = ref(new Date().getTime())
const message = useMessage()
const defaultColumns = [
  {
    key: 'index',
    title: '序号',
    width: 60,
    align: 'center',
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    key: 'userName',
    title: '人员',
    align: 'center'
  }
]
const columns = ref<any[]>([])
const data = ref<any[]>([])
let loading = ref(false)

interface RowData {
  [propName: string]: any
}

function initColumns() {
  const dayLen = moment().daysInMonth()
  for (let i = 1; i <= dayLen; i++) {
    columns.value.push({
      title: i + '',
      key: 'day' + i,
      align: 'center',
      render(row: RowData, rowIndex: number) {
        return h(QxTableEdit, {
          value: row[`day${i}`],
          className: row[`day${i}`] ? 'main-class' : '',
          isEdit: false,
          onUpdateValue(v: string) {
            data.value[rowIndex][`day${i}`] = v
          }
        })
      }
    })
  }
}

function onConfirm() {
  getList()
}

function getList() {
  loading.value = true
  let date = moment(schedulingDate.value).format('YYYY-MM-DD')
  Api.getSchedulingList({date})
    .then((res: any) => {
      data.value = formatData(res)
      loading.value = false
    })
    .catch(e => {
      console.error(e, 'scheduling--getList')
      message.error('获取数据失败')
      loading.value = false
    })
}

// 编辑
function onEdit(status: boolean) {
  for (let i = 2; i < columns.value.length; i++) {
    let item = columns.value[i]

    item.render = (row: RowData, rowIndex: number) => {
      return h(QxTableEdit, {
        value: row[item.key],
        className: row[`day${i - 1}`] && !status ? 'main-class' : '',
        isEdit: status,
        onUpdateValue(v: string) {
          data.value[rowIndex][`day${i - 1}`] = v
        }
      })
    }
  }
}

// 保存
function onSave() {
  let reg = new RegExp('day\\d+')
  let result: any = []
  const resultList = data.value
  const dayLen = moment().daysInMonth()
  for (let i = 1; i <= dayLen; i++) {
    let schedulingDate = moment().date(i).format('YYYY-MM-DD')
    let obj: any = {}

    for (const item of resultList) {
      Object.keys(item).forEach(key => {
        if (reg.test(key)) {
          let day: any = key.match(reg)?.[0].slice(3)
          if (moment(schedulingDate).date() == day) {
            obj.userName = item.userName
            obj.userId = item.userId
            obj.schedulingDate = schedulingDate
          }
        }
      })
    }
    if (JSON.stringify(obj) !== '{}' && obj.userName) {
      result.push(obj)
    }
  }
  console.log(result, 'result')
  Api.saveScheduling(result)
    .then((res: any) => {
      message.success('保存成功')
      onEdit(false)
    })
    .catch(e => {
      console.error(e, 'scheduling--saveScheduling')
      message.error('保存失败')
    })
}

// 导入
function onImport({ file }: any) {
  loading.value = true
  const form = new FormData()
  form.append('multipartFile', file.file)
  form.append('date', moment().format('YYYY-MM-DD'))
  Api.uploadScheduling(form)
    .then((res: any) => {
      data.value = formatData(res)
      loading.value = false
    })
    .catch(e => {
      console.error(e, 'scheduling--onImport')
      message.error('导入失败')
      loading.value = false
    })
}

function formatData(data: any) {
  const arr: any = groupBy(data, (item: any) => {
    if (item['userName']) {
      return item['userName']
    }
  })
  let result: any[] = []
  Object.keys(arr).forEach((key: any) => {
    let obj: any = {
      userName: key
    }
    arr[key].forEach((item: any) => {
      obj[`day${item.day}`] = 1
      obj.userId = item.userId
    })
    result.push(obj)
  })
  return result
}

// 导出
function onExport() {
  const params = {
    date: moment(schedulingDate.value).format('YYYY-MM-DD')
  }
  Api.downloadScheduling(params).then((res: any) => {
    console.log(res, 'res====')
  })
}

onMounted(() => {
  columns.value = defaultColumns
  initColumns()
  getList()
})
</script>

<style lang="scss">
.scheduling {
  background: #f0f5fb;
  .content {
    background: #fff;
    height: 100%;
  }
  .operate-btns {
    .qx-button {
      flex-shrink: 0;
    }
    .n-upload {
      margin-right: 8px;
    }
  }
  .content-header {
    box-sizing: border-box;
    padding: 18px 23px 8px 26px;
    width: 100%;
    background: url(src/assets/images/common/content-header.png) no-repeat;
    background-size: 100% 100%;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 23px;
      margin-right: 12px;
    }
  }
  .table-legend {
    box-sizing: border-box;
    padding-top: 15px;
    padding-right: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    span {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #000000;
      display: flex;
      align-items: center;
      margin-right: 18px;
      i {
        margin-right: 3px;
        display: inline-block;
        width: 22px;
        height: 14px;
        background: #cce6fb;
        border-radius: 2px;
      }
      &:nth-child(2) {
        margin-right: 0;
        i {
          background: #d7f0db;
        }
      }
    }
  }
  .n-data-table-th {
    background: #fff !important;
    padding: 16px 0;
    border: none !important;
    background: #ffffff;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
    border-radius: 0px 0px 0px 0px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .n-data-table-th__title,
  .n-data-table-td {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 600;
    font-size: 14px;
    color: #222222;
    width: 50px;
  }
  .n-data-table-td {
    padding: 10px 0;
  }

  .main-class {
    background: #cce6fb;
    border-radius: 2px;
    border-left: 3px solid #1b7aeb;
    color: transparent;
  }
  .auxiliary-class {
    background: #d7f0db;
    border-radius: 2px;
    border-left: 3px solid #26c43f;
  }
  .n-data-table-td .main-class ~ .n-data-table-td .main-class {
    border-left: none;
  }
}
</style>
