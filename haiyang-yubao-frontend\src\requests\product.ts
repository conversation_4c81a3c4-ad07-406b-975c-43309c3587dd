import getAxios from 'src/utils/axios'
const productUrl = import.meta.env.VITE_PRODUCT_BASE_URL
const axiosInstance = getAxios(productUrl)

function getProductById(productId: string, params: any) {
  return axiosInstance({
    url: `/api/product/record/page/forecast/${productId}`,
    method: 'GET',
    params
  })
}

// 获取起报时间列表
function getForecastTime(productId: string, params: any) {
  return axiosInstance({
    url: `/api/product/record/top/forecast/${productId}`,
    method: 'GET',
    params
  })
}

export default {
  getProductById,
  getForecastTime
}
