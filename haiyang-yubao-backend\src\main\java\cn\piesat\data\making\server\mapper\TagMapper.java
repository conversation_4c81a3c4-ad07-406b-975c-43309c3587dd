package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.TagDTO;
import cn.piesat.data.making.server.entity.Tag;
import cn.piesat.data.making.server.vo.TagVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TagMapper {

    TagMapper INSTANCE = Mappers.getMapper(TagMapper.class);

    /**
     * entity-->vo
     */
    TagVO entityToVo(Tag entity);

    /**
     * dto-->entity
     */
    Tag dtoToEntity(TagDTO dto);

    /**
     * entityList-->voList
     */
    List<TagVO> entityListToVoList(List<Tag> list);

    /**
     * dtoList-->entityList
     */
    List<Tag> dtoListToEntityList(List<TagDTO> dtoList);
}
