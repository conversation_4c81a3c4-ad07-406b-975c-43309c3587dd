import FTStyle from "./Style";
import Style from "ol/style/Style";
import Stroke from "ol/style/Stroke";

class PolyLineStyle extends FTStyle {
  /**
   * @classdesc 折线类样式
   * <AUTHOR>
   * @extends {FTStyle}
   * @constructs
   */
  constructor() {
    super();

    this._style = {
      // --ol.style.Stroke所有选项
      stroke: {
        color: "#0000FF",
        width: 3,
        lineDash: [10, 10, 10]
      }
    };
  }

  parse() {
    let stroke = null;
    if (this._style.stroke) {
      stroke = new Stroke(this._style.stroke);
    }
    return new Style({
      stroke: stroke
    });
  }
}

export default PolyLineStyle;
