package cn.piesat.data.making.server.processor;

import cn.piesat.data.making.server.dto.GenerateWordDTO;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.webconfig.exception.BusinessException;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.Map;

public abstract class DefaultGenerateTextProcessor {
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    public GenerateTextVO templateReplace(Map<String,Object> param, String contentTemplate, String smsTemplate) throws IOException, TemplateException {
        //生成内容文字
        StringTemplateLoader stringTemplateLoader = new StringTemplateLoader();
        stringTemplateLoader.putTemplate("alarm_content.ftl",contentTemplate);
        stringTemplateLoader.putTemplate("alarm_sms.ftl",smsTemplate);

        Configuration configuration = new Configuration(Configuration.getVersion());
        configuration.setTemplateLoader(stringTemplateLoader);
        Template alarmContentTemplate = configuration.getTemplate("alarm_content.ftl");
        Template alarmSmsTemplate = configuration.getTemplate("alarm_sms.ftl");
        String alarmContent = FreeMarkerTemplateUtils.processTemplateIntoString(alarmContentTemplate, param);
        String alarmSms = FreeMarkerTemplateUtils.processTemplateIntoString(alarmSmsTemplate, param);

        return new GenerateTextVO(alarmContent,alarmSms);
    }

    protected abstract Map<String,Object> getParam(GenerateWordDTO generateWordDTO) throws Exception;


    public abstract GenerateTextVO generate(GenerateWordDTO generateWordDTO,boolean isAlarm);
}
