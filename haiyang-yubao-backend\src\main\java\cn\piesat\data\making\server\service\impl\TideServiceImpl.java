package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.DateUtil;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.entity.TideDailyData;
import cn.piesat.data.making.server.entity.TideDailyHourData;
import cn.piesat.data.making.server.enums.DatumEnum;
import cn.piesat.data.making.server.enums.TideWarnLevel;
import cn.piesat.data.making.server.mapper.TideDailyDataMapper;
import cn.piesat.data.making.server.model.TideDataParseResult;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.service.TideService;
import cn.piesat.data.making.server.utils.LambadaTools;
import cn.piesat.data.making.server.vo.StationVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class TideServiceImpl implements TideService {
    @Autowired
    private TideDailyServiceImpl tideDailyService;
    @Autowired
    private TideDailyHourServiceImpl tideDailyHourService;

    @Autowired
    private StationService stationService;

    @Override
    public TideDataParseResult parseTideData(String filePath) throws IOException, ParseException {
        String datum = DatumEnum.DATUM.getValue();

        File file = new File(filePath);
        Preconditions.checkArgument(file.exists() || file.isFile(), "文件不存在或不是文件类型");

        TideDataParseResult dataParseResult = new TideDataParseResult();
        List<String> tideLines = FileUtils.readLines(file, Charset.forName("GBK"));
        AtomicReference<String> stationName = new AtomicReference<>();
        AtomicInteger curYear = new AtomicInteger(Calendar.getInstance().get(Calendar.YEAR));
        AtomicInteger curMonth = new AtomicInteger(Calendar.getInstance().get(Calendar.MONTH));
        tideLines.forEach(LambadaTools.forEachWithIndex((row, index) -> {
            if (index == 0) {
                stationName.set(row.replaceAll("\\s*", ""));
                return;
            }
            StationVO stationVO = this.getStationByName(stationName.get());
            if (row.contains("潮汐表")) {
                row = row.replaceAll("[' ']+", " ");
                String[] rowArr = row.split(" ");
                curYear.set(Integer.parseInt(rowArr[0].replaceAll("\\s*", "").substring(0, 4)));
                curMonth.set(Integer.parseInt(rowArr[rowArr.length - 1].replaceAll("\\s*", "").substring(0, rowArr[rowArr.length - 1].length() - 1)));
                return;
            }
            if (row.contains("日") || row.contains("时区") || row.replaceAll("\\s*", "").equals(stationName.get())) return;

//            String[] dataArr = row.trim().split(" ");
//            System.out.println(row);
            String dailyHourHeights = row.substring(2, 2 + 4 * 24);
            int day = Integer.parseInt(row.substring(0, 2).trim());
            for (int i = 0; i < 24; i++) {
                int hour = i;
//                System.out.println(i);
                String heightStr = dailyHourHeights.substring(i * 4, i * 4 + 4);
//                System.out.println(heightStr);
                int tideHeight = Integer.parseInt(heightStr.replaceAll("\\s*", ""));
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.YEAR, curYear.get());
                calendar.set(Calendar.MONTH, curMonth.get() - 1);
                calendar.set(Calendar.DAY_OF_MONTH, day);
                calendar.set(Calendar.HOUR_OF_DAY, hour);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                Date date = calendar.getTime();
                TideDailyHourData tideDailyDataEntity = new TideDailyHourData();
                tideDailyDataEntity.setTideTime(date);
                tideDailyDataEntity.setHeight(tideHeight);
                tideDailyDataEntity.setStationName(stationName.get());
                tideDailyDataEntity.setStationId(stationVO == null ? null : stationVO.getId());
                Date curDate = new Date();
                tideDailyDataEntity.setCreateTime(curDate);
                tideDailyDataEntity.setUpdateTime(curDate);
                //测算基准
                tideDailyDataEntity.setDatum(datum);
                dataParseResult.getTideDailyHourDataListList().add(tideDailyDataEntity);

            }

            String dailyHeights = row.substring(2 + 4 * 24).replaceAll("[' ']+", " ").trim();
            System.out.println(dailyHeights);
            String[] dailyHeightArr = dailyHeights.split(" ");
            String dailyTimestr = null;
            for (int j = 0; j < dailyHeightArr.length; j++) {
                if (j % 2 == 0) {
                    dailyTimestr = dailyHeightArr[j].trim();
                    continue;
                } else {
                    if (dailyTimestr == null) continue;
                    int tideHeight = Integer.parseInt(dailyHeightArr[j].trim());
                    String formatDateStr = curYear.get() + "-" + curMonth.get() + "-" + day + " " + dailyTimestr;
                    System.out.println(dailyTimestr + ":" + dailyHeightArr[j]);
                    Date tideTime = DateUtil.stringToDate(formatDateStr, "yyyy-M-d HHmm");
                    TideDailyData tideDailyData = new TideDailyData();
                    tideDailyData.setTideTime(tideTime);
                    tideDailyData.setHeight(tideHeight);
                    tideDailyData.setType(tideHeight > 0 ? 1 : 0);
                    tideDailyData.setStationName(stationName.get());
                    tideDailyData.setStationId(stationVO == null ? null : stationVO.getId());
                    Date curDate = new Date();
                    tideDailyData.setCreateTime(curDate);
                    tideDailyData.setUpdateTime(curDate);
                    //测算基准
                    tideDailyData.setDatum(datum);
                    dataParseResult.getTideDailyDataList().add(tideDailyData);
                    continue;
                }
            }

        }));
        return dataParseResult;
    }

    @Override
    public void parseAndSaveTideData(String filePath) throws Exception {
        TideDataParseResult result = this.parseTideData(filePath);
        List<TideDailyData> tideDailyDataList = result.getTideDailyDataList();
        tideDailyService.saveBatch(tideDailyDataList);
        tideDailyHourService.saveBatch(result.getTideDailyHourDataListList());
    }

    @Override
    public void updateWarnHeight() {
        //查询所有未匹配海洋站
        LambdaQueryWrapper<Station> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Station::getStationTypeCode,"oceanStation");
        wrapper.isNotNull(Station::getDatumBlueWarn);
        List<Station> list = stationService.list(wrapper);
        list.forEach(station -> {
            List<TideDailyData> dailyDataList = tideDailyService.list(new LambdaQueryWrapper<TideDailyData>().eq(TideDailyData::getStationId, station.getId()).eq(TideDailyData::getDatum,"datum"));
            dailyDataList.forEach(tideDailyData -> {
                int height = tideDailyData.getHeight();
                if(height >= station.getDatumRedWarn()){
                    tideDailyData.setWarnHeight(station.getDatumRedWarn());
                    tideDailyData.setLevel(TideWarnLevel.RED.getValue());
                    return;
                }
                if(height >= station.getDatumOrangeWarn()){
                    tideDailyData.setWarnHeight(station.getDatumOrangeWarn());
                    tideDailyData.setLevel(TideWarnLevel.ORANGE.getValue());
                    return;
                }
                if(height >= station.getDatumYellowWarn()){
                    tideDailyData.setWarnHeight(station.getDatumYellowWarn());
                    tideDailyData.setLevel(TideWarnLevel.YELLOW.getValue());
                    return;
                }
                if(height >= station.getDatumBlueWarn()){
                    tideDailyData.setWarnHeight(station.getDatumBlueWarn());
                    tideDailyData.setLevel(TideWarnLevel.BLUE.getValue());
                }

            });
            tideDailyService.updateBatchById(dailyDataList,500);


        });
    }

    @Override
    public void count(Long stationId,String datum,Integer difference) {
        List<TideDailyData> list = tideDailyService.list(new LambdaQueryWrapper<TideDailyData>().eq(TideDailyData::getStationId, stationId).eq(TideDailyData::getDatum,datum));
        List<TideDailyData> tideDailyData =  TideDailyDataMapper.INSTANCE.handle85(list,difference);
        tideDailyService.saveBatch(tideDailyData);

    }


    private StationVO getStationByName(String name) {
        StationDTO dto = new StationDTO();
        dto.setName(name);
        List<StationVO> stationVOList = stationService.getList(dto);
        if (stationVOList == null || stationVOList.isEmpty()) return null;

        return stationVOList.get(0);

    }

    public static void main(String[] args) throws IOException, ParseException {
        TideServiceImpl tideService = new TideServiceImpl();
        String filePath = "D:\\项目文档\\hzxm\\2024\\2024\\海南12站（85）-2024\\白马井-2024.dat";
        TideDataParseResult result = tideService.parseTideData(filePath);
        System.out.println(result.getTideDailyDataList().size());
    }
}
