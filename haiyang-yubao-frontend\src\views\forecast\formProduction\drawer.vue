<template>
  <n-drawer
    v-model:show="visible"
    :width="1500"
    @after-leave="onClose"
    :resizable="true"
    :default-height="200"
    placement="bottom"
    :show-mask="false"
    :mask-closable="false"
  >
    <n-drawer-content title="实况观测数据" closable class="qx-drawer">
      <div class="table-wrap">
        <div v-loading="loading"  ref="tableInner" class="table-inner">
          <n-data-table
            class="qx-table"
            :data="tableData"
            :columns="columns"
            :single-line="false"
            :max-height="tableHeight - 20"
          ></n-data-table>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import Api from 'src/requests/forecast'
import { useMessage } from 'naive-ui'
import moment from 'moment'
import { mapper } from './data'
import { Drawer } from 'src/views/forecast/formProduction/index'
import { useElementSize } from '@vueuse/core'

const message = useMessage()
const props = defineProps({
  showDrawer: {
    type: Boolean,
    default: false
  }
})
const tableInner = ref<HTMLDivElement | undefined>()
let { height: tableHeight } = useElementSize(tableInner)
let visible = ref(false)
let loading = ref(false)
const columns = ref<any[]>()
const tableData = ref<any[]>([])

const emit = defineEmits(['close'])

watch(
  () => props.showDrawer,
  (val: any) => {
    visible.value = val
    if (val) {
      getOceanObservationData()
    }
  }
)

function onClose() {
  console.log(123)
  emit('close')
}

function swapElements(arr: any[], element1: string, element2: string) {
  const index1 = arr.indexOf(element1)
  const index2 = arr.indexOf(element2)

  if (index1 !== -1 && index2 !== -1) {
    ;[arr[index1], arr[index2]] = [arr[index2], arr[index1]]
  }
  return arr
}

// 获取24小时观测数据
function getOceanObservationData() {
  const params = {
    startTime: moment()
      .subtract(1, 'days')
      .startOf('days')
      .format('YYYY-MM-DD HH:mm:ss'),
    endTime: moment().format('YYYY-MM-DD HH:mm:ss')
    // startTime: '2025-05-17 00:00:00',
    // endTime: '2025-06-21 00:00:00'
  }

  loading.value = true
  Api.getOceanObservationData(params)
    .then((res: any) => {
      if (res?.length) {
        tableData.value = res

        let result = Object.keys(tableData.value[0]).filter(key => {
          return ![
            'id',
            'oceanStationCode',
            'time',
            'oceanStationLocationJson'
          ].includes(key)
        })
        let newArr = swapElements(result, 'oceanStationName', result[0])
        columns.value = newArr.map(item => {
          return {
            title: mapper[item],
            key: item,
            align: 'center'
          }
        })
        console.log(result, 'columns.value')
      } else {
        if (res.length === 0) {
          message.warning('暂无数据')
        }
      }
    })
    .finally(() => {
      loading.value = false
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
      console.error(e)
    })
}

onMounted(() => {
  // getOceanObservationData()
})
</script>

<style scoped lang="scss">
.custom-table {
  border: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;

  .table-col {
    flex: 1;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-size: 12px;
    color: #222222;
    line-height: 14px;
    text-align: center;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    height: 100%;
    line-height: 40px;

    &:nth-child(1) {
      width: 130px;
      flex: 1 0 auto;
    }

    &:nth-last-child(1) {
      border: none;
    }
  }

  .table-header {
    position: sticky;
    top: 0;
    background: #fafafa;
    height: 40px;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .table-col {
      font-weight: bold;
    }
  }

  .table-row {
    height: 40px;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    &:nth-last-child(1) {
      border-bottom: none;
    }
  }
}

.table-wrap {
  height: 100%;
  position: relative;

  .table-inner {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;

  }
}
</style>
