package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmGraphicTmplateSignDao;
import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.dto.FmGraphicTmplateSignDTO;
import cn.piesat.data.making.server.entity.FmGraphicSign;
import cn.piesat.data.making.server.dto.FmGraphicSignDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateTools;
import cn.piesat.data.making.server.entity.FmGraphicTmplateSign;
import cn.piesat.data.making.server.mapper.FmGraphicTemplateToolsMapper;
import cn.piesat.data.making.server.mapper.FmGraphicTmplateSignMapper;
import cn.piesat.data.making.server.vo.FmGraphicSignVO;
import cn.piesat.data.making.server.dao.FmGraphicSignDao;
import cn.piesat.data.making.server.service.FmGraphicSignService;
import cn.piesat.data.making.server.mapper.FmGraphicSignMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:37
 */
@Service
@Slf4j
public class FmGraphicSignServiceImpl extends ServiceImpl<FmGraphicSignDao, FmGraphicSign> implements FmGraphicSignService {

    @Resource
    private FmGraphicSignDao fmGraphicSignDao;
    @Resource
    private FmGraphicTmplateSignDao fmGraphicTmplateSignDao;

    @Override
    public List<FmGraphicSignVO> getList(String type) {
        return FmGraphicSignMapper.INSTANCE.entityListToVoList(fmGraphicSignDao.selectAll());
    }

    @Override
    public void saveByTemplateId(List<FmGraphicTmplateSignDTO> list, Long templateId) {
        if(fmGraphicTmplateSignDao.countByTemplateId(templateId) > 0){
            fmGraphicTmplateSignDao.deleteByTemplateId(templateId);
        }
        for (FmGraphicTmplateSign fmGraphicTemplateTools : FmGraphicTmplateSignMapper.INSTANCE.dtoListToEntityList(list)) {
            fmGraphicTemplateTools.setTemplateId(templateId);
            fmGraphicTmplateSignDao.insert(fmGraphicTemplateTools);
        }
    }
}
