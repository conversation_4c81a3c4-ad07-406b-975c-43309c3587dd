import axios, { AxiosResponse } from 'axios'
import type {
  AxiosInstance,
  CreateAxiosDefaults,
  InternalAxiosRequestConfig
} from 'axios'
import { get } from 'src/utils/ioc'
import { AuthService } from 'src/service/authService'

export class AxiosHandlerBuilder {
  private _instance: AxiosInstance | null = null
  private _createConfig: CreateAxiosDefaults = {}

  constructor() {
    // this._instance = axios.create()
  }

  setBaseURL(baseURL: string) {
    this._createConfig.baseURL = baseURL
    return this
  }

  setTimeout(second = 60) {
    this._createConfig.timeout = second * 1000
    return this
  }

  addReqToken() {
    const authInfo = get<AuthService>('auth').getAuth()
    if (authInfo?.token) {
      ;(
        this._createConfig.headers as any
      ).Authorization = `Bearer ${authInfo.token}`
    }
    return this
  }

  addResDestruct() {
    this.setResInterceptors(res => {
      const type = res.headers['content-type'] as string | undefined
      console.log(type)
      if (type?.includes('application/json')) {
        return res.data
      }
      return res
    })
    return this
  }

  setReqInterceptors(
    cb: (arg0: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
  ) {
    this._instance?.interceptors.request.use(cb)
  }

  setResInterceptors(cb: (arg0: AxiosResponse) => any) {
    this._instance?.interceptors.response.use(cb)
  }

  create() {
    this._instance = axios.create(this._createConfig)
    return this
  }

  getInstance() {
    if (!this._instance) {
      throw new Error('尚未实例化 axios 实例')
    }
    return this._instance
  }
}
