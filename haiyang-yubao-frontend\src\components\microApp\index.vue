<template>
  <micro-app
    :name="props.appName"
    :url="props.url"
    :data="microAppData"
    :baseroute="props.replacePath"
    iframe
  >
  </micro-app>
</template>

<script setup lang="ts">
import microApp from '@micro-zoe/micro-app'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
let microAppData = ref({})
const route = useRoute()

const props = defineProps({
  appName: String,
  url: {
    type: String,
    required: true
  },
  appData: {
    type: Object
  },
  replacePath: {
    type: String,
    default: '/'
  }
})

watch(
  () => route.path,
  val => {
    let routePath = route.path
    const path = routePath.replace(props.replacePath, '')
    microAppData.value = { path }
    microAppData.value = Object.assign({}, microAppData.value, props.appData)
    microApp.forceSetData(props.appName as string, microAppData.value)
  },
  { deep: true }
)
</script>

<style scoped></style>
