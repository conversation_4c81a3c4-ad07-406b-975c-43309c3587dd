<template>
  <div class="common-aside product-temp-aside">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>产品模板列表</h3>
        <qx-button class="primary" @click="onCreate">新建</qx-button>
      </div>
      <div class="aside-header-tab d-flex">
        <Tab
          :active="tabIndex"
          :tab-list="tabList"
          @change="changeTab"
          class="flex1"
        />
        <div class="qx-select d-flex flex-justify-end flex1">
          <n-select
            v-model:value="status"
            :options="statusList"
            :bordered="false"
            style="width: 45%; margin-right: 5%"
            @update:value="filterData"
          />

          <n-select
            v-if="tabIndex === 1"
            v-model:value="fileType"
            :options="fileTypeList"
            :bordered="false"
            style="width: 45%"
            @update:value="filterData"
          />
        </div>
      </div>
    </div>
    <qx-tree
      v-loading="loading"
      :tree-data="treeData"
      :default-props="defaultProps"
      :is-switch="isSwitch"
      :default-expand-all="true"
      :defaultSelectedKeys="defaultSelectedKeys"
      :edit="true"
      :have-right-menu="true"
      @change-switch="changeSwitch"
      @selected="selectHandler"
      @re-name="reNameHandler"
      :switchDisabled="tabIndex===1?false:true"
      ref='treeRef'
      @delete-node="deleteNode"
    />
  </div>
   <qx-dialog
    v-model:visible="reNameVisible"
    title="重命名预报单模板"
    width="345px"
    class="create-forecast-temp-dialog"
    @update:visible="reNameVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="formRef"
          class="forecast-temp-form"
          :model="curRightData"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="模板名称" path="name">
            <n-input
              v-model:value="curRightData.name"
              placeholder="请输入"
              clearable
            />
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="reNameVisible = false">取消</qx-button>
        <qx-button class="primary" @click="reName">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
  <qx-dialog
    title="新建模板"
    v-model:visible="dialogVisible"
    class="add-product-template"
    width="450px"
    @update:visible="dialogVisible = false"
  >
    <template #content>
      <Form ref="formRef" v-if="tabIndex === 1" />
      <AlarmForm v-else :typeList="treeData" :isEdit="true" ref="alarmRef" />
    </template>
    <template #suffix>
      <div class="btns">
        <qx-button @click="dialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onCreateSave">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { Tab } from 'src/components/Tab'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/forecast'
import { QxTree } from 'src/components/QxTree'
import { useMessage } from 'naive-ui'
import { QxDialog } from 'src/components/QxDialog'
import { Form, AlarmForm } from './index'
import { getFirstLeafNode } from 'src/utils/util'
import eventBus from 'src/utils/eventBus'
import { createDiscreteApi } from 'naive-ui'
const { dialog } = createDiscreteApi(['dialog'])
// 定义当前组件全局变量 start
const message = useMessage()
const status = ref(2)
const fileType = ref('')
const emits = defineEmits(['select', 'tab'])

const statusList = [
  {
    label: '全部',
    value: 2
  },
  {
    label: '启用',
    value: 1
  },
  {
    label: '禁用',
    value: 0
  }
]

const fileTypeList = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '.docx',
    value: '.docx'
  },
  {
    label: '.txt',
    value: '.txt'
  },
  {
    label: '.xml',
    value: '.xml'
  },
  {
    label: '.tga',
    value: '.tga'
  }
]
// 定义当前组件全局变量 end

// tab切换 start
const tabIndex = ref<number>(1)
const tabList = [
  { label: '预报产品', id: 1 },
  { label: '警报产品', id: 2 }
]
function changeTab(val: number) {
  tabIndex.value = val
  emits('tab', val)
  if (val === 1) {
    getForecastPruductList()
  } else {
    getAlarmProductList()
  }
}
// tab切换 end

// tree start
const isSwitch = ref<boolean>(true)
const treeData = ref([])
const defaultSelectedKeys = ref<any[]>([])
let editId = ref('')
let defaultProps = {
  key: 'id',
  label: 'name',
  children: 'children'
}
// 格式化状态 接口参数 全部 = null,启用=true,禁用=false
function getStatus() {
  if (status.value === 2) {
    return { status: null }
  } else if (status.value === 1) {
    return { status: true }
  } else {
    return { status: false }
  }
}
function filterData(val: string) {
  if (tabIndex.value === 1) {
    getForecastPruductList()
  } else {
    getAlarmProductList()
  }
}

const loading = ref(false)
// 获取预报产品列表
function getForecastPruductList() {
  loading.value = true
  let result = getStatus()
  let params = Object.assign(result, {
    fileType: fileType.value
  })

  Api.getForecastProductTemplate(params)
    .then((res: any) => {
      defaultProps = {
        key: 'id',
        label: 'name',
        children: 'children'
      }
      treeData.value = res
      if(editId.value){
        defaultSelectedKeys.value = [editId.value]
      }else{
        let id = res?.[0].id
        selectHandler(id, res?.[0])
        defaultSelectedKeys.value.push(res?.[0].id)
      }
      
    })
    .catch(e => {
      treeData.value = []
      defaultSelectedKeys.value = []
      let { msg = '' } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 获取警报产品列表
function getAlarmProductList() {
  let state: any = status.value
  if (state === 2) {
    state = ''
  }

  const params = { status: state }
  Api.getAlarmProductList(params)
    .then((res: any) => {
      res.forEach((item: any, index: number) => {
        item.id = index
        item.templateName = item.templateTypeName
      })
      treeData.value = res
      defaultProps = {
        key: 'id',
        label: 'templateName',
        children: 'templateList'
      }
      if(editId.value){
        defaultSelectedKeys.value = [editId.value]
        selectHandler('', {id: editId.value})
      }else{
        const result = getFirstLeafNode(res, 'templateList')
        selectHandler('', result)
        defaultSelectedKeys.value.push(result?.id as string)
      }

      eventBus.emit('getTemplateTypeList', res)
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '获取数据失败')
      treeData.value = []
    })
}

function selectHandler(val: string, option: any) {
  emits('select', val, option)
}

function changeSwitch(val: boolean, option: any) {
  if (tabIndex.value === 1) {
    changeForecastStatus(val, option)
  } else {
    changeAlarmStatus(val, option)
  }
}

// 修改预报产品状态
function changeForecastStatus(val: boolean, option: any) {
  Api.updateProductTempStatus(option?.id, val)
    .then((res: any) => {
      message.success('操作成功')
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// 修改警报产品状态
function changeAlarmStatus(val: boolean, option: any) {
  Api.editAlamProductStatus(option?.id)
    .then(res => {
      message.success('操作成功')
      getAlarmProductList()
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// tree end

// 新建弹窗 start
let dialogVisible = ref(false)
const formRef = ref<any>(null)
const alarmRef = ref<any>(null)
function onCreate() {
  dialogVisible.value = true
}

function onCreateSave() {
  if (tabIndex.value === 1) {
    saveForecastProduct()
  } else {
    saveAlarmProduct()
  }
}
//重命名预报单模板
const reNameVisible = ref(false)
const curRightData = ref<any>({})
function reNameHandler(option: any) {
  curRightData.value = option
  reNameVisible.value = true
}
const treeRef = ref()
function reName() {
  Api.reNameProductTemp(curRightData.value)
    .then((res: any) => {
      message.success('重命名成功')
      reNameVisible.value = false
      treeRef.value.hiddenRightMenu()
      getForecastPruductList()
    })
    .catch(() => {})
}
function deleteNode(option: any) {
  curRightData.value = option
  dialog.warning({
    title: '提示',
    content: `是否删除${option.name}模板`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      Api.deleteProductTemp(option)
        .then((res: any) => {
          console.log(res)
          message.success('删除成功')
          getForecastPruductList()
          treeRef.value.hiddenRightMenu()
        })
        .catch(() => {})
    },
    onNegativeClick: () => {
      treeRef.value.hiddenRightMenu()
    }
  })
}
// 保存预报产品模板
function saveForecastProduct() {
  formRef.value?.form.validate((errors: boolean) => {
    if (!errors) {
      const { dialogForm = {} } = formRef.value
      const params = JSON.parse(JSON.stringify(dialogForm))
      params.relationTemplateCode = params.relationTemplateCode.join(',')
      Api.saveProductTemp(params)
        .then((res: any) => {
          message.success('保存成功')
          dialogVisible.value = false
          getForecastPruductList()
        })
        .catch(e => {
          let { msg } = e?.response?.data
          message.error(msg || '操作失败')
        })
    }
  })
}

// 保存警报产品模板
function saveAlarmProduct() {
  alarmRef.value?.form.validate((errors: boolean) => {
    if (!errors) {
      let { alarmForm = {} } = alarmRef.value

      Api.saveAlarmProduct(alarmForm)
        .then(res => {
          message.success('操作成功')
          dialogVisible.value = false
          getAlarmProductList()
        })
        .catch(e => {
          let { msg } = e?.response?.data
          message.error(msg || '操作失败')
        })
    }
  })
}

// 新建弹窗 end

onMounted(() => {
  getForecastPruductList()
  eventBus.on('refresh', () => {
    if (tabIndex.value === 1) {
      getForecastPruductList()
    } else {
      getAlarmProductList()
    }
  })
  eventBus.on('saveTemp',(params:string)=>{
    editId.value = params
    if (tabIndex.value === 1) {
      getForecastPruductList()
    } else {
      getAlarmProductList()
    }
  })
})

onBeforeUnmount(()=>{
  eventBus.off('refresh')
  eventBus.off('saveTemp')
})
</script>

<style lang="scss">
.product-temp-aside {
  margin-right: 20px;
}
.add-product-template {
  .qx-dialog {
    // background: url(src/assets/images/common/dialog-bg.png) no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
  }
  .form-container {
    box-sizing: border-box;
    padding: 14px 27px 14px 24px;
    background: #fff;
  }
  .btns {
    box-sizing: border-box;
    padding: 10px 0;
    text-align: center;
    background: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
</style>
