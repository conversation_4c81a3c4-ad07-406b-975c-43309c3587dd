package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.StationVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 站点表服务接口
 *
 * <AUTHOR>
 */
public interface StationService extends IService<Station> {

    /**
     * 查询分页
     */
    PageResult<StationVO> getPage(StationDTO dto, PageParam pageParam);

    /**
     * 查询列表
     */
    List<StationVO> getList(StationDTO dto);

    /**
     * 保存
     */
    Long save(StationDTO dto);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 批量导入站点
     */
    void upload(MultipartFile file);

    /**
     * 范围查询
     */
    List<Station> getListByRange(String geoRange);

    void syncRelationStation(String stationTypeCode);

    List<StationVO> getListByStationTypeCode(StationDTO dto);
}




