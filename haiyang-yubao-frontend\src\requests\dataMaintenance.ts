/*
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-29 14:35:47
 * @LastEditors: xuli<PERSON>e <EMAIL>
 * @LastEditTime: 2024-10-31 16:22:00
 * @FilePath: \hainan-jianzai-web\src\requests\dataMaintenance.ts
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)

/**
 * @abstract 获取站点信息
 * @param params
 * @returns
 */
function getStationList(params?: any) {
  return axiosInstance({
    url: `/stationType/treeList`,
    method: 'GET',
    params
  })
}
/**
 * @abstract 保存站点信息
 * @param data 站点信息
 * @returns
 */
function saveStation(data: any) {
  return axiosInstance({
    url: `/station/save`,
    method: 'POST',
    data
  })
}
function getStationType(params?: any){
  return axiosInstance({
    url: `/stationType/treeList`,
    method: 'GET',
    params
  })
}
export default {
  getStationList,
  saveStation,
  getStationType
}
