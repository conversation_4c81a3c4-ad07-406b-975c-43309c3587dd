<template>
  <!-- 产品模板 -->
  <div class="product-temp d-flex">
    <Aside @select="selectHandler" @tab="onChangeTab" />
    <template v-if="tabIndex === 1">
      <Edit :info="templateInfo" :callbackUrl="callbackUrl" />
    </template>
    <template v-else>
      <alarm-edit :info="templateInfo" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { Aside, Edit, AlarmEdit } from './index'
import { QxButton } from 'src/components/QxButton'
import { reactive, ref } from 'vue'
import { useMessage } from 'naive-ui'
import Api from 'src/requests/forecast'

const message = useMessage()
let templateInfo = ref<any>({})
let callbackUrl = ref(config.onlyOfficeCallBack + 'record/')
let tabIndex = ref(1)
function selectHandler(val: string, option: any) {
  if (tabIndex.value === 1) {
    getProductTemplateInfoById(val)
  } else {
    getAlarmProductInfoById(option?.id)
  }
}

function onChangeTab(val: any) {
  tabIndex.value = val
}

// 获取模板信息
function getProductTemplateInfoById(val: string) {
  Api.getProductTemplateInfoById(val)
    .then((res: any) => {
      templateInfo.value = res
      callbackUrl.value = config.onlyOfficeCallBack + 'template/' + res?.id
    })
    .catch(e => {
      console.error(e, 'eeeeee')
      let { msg } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
}

function getAlarmProductInfoById(val: string) {
  Api.getAlarmProductInfoById(val).then((res: any) => {
    templateInfo.value = res
  })
}
</script>

<style lang="scss">
.product-temp {
  height: 100%;
  .content {
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;
    .content-header {
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-radius: 8px 8px 0px 0px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      box-sizing: border-box;
      padding: 9px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 21px;
        margin-bottom: 2px;
      }
    }
  }
  .operate-wrap {
    box-sizing: border-box;
    padding: 18px 0 19px 28px;
    background: rgba(217, 217, 217, 0.2);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }
    .n-checkbox__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      letter-spacing: 1px;
    }
    .n-checkbox {
      margin-right: 25px;
    }
  }
  .qx-select-wrap {
    margin-right: 56px;
    width: 170px;
    label {
      display: inline-block;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
      line-height: 32px;
      width: 70px;
      flex-shrink: 0;
    }
    .qx-select {
      .n-base-selection-label,
      .n-base-selection {
        height: 32px;
        min-height: 32px;
      }
    }
  }
}
</style>
