package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastRecordDTO;
import cn.piesat.data.making.server.entity.ForecastRecord;
import cn.piesat.data.making.server.vo.ForecastRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ForecastRecordMapper {

    ForecastRecordMapper INSTANCE = Mappers.getMapper(ForecastRecordMapper.class);

    /**
     * entity-->vo
     */
    ForecastRecordVO entityToVo(ForecastRecord entity);

    /**
     * dto-->entity
     */
    ForecastRecord dtoToEntity(ForecastRecordDTO dto);

    /**
     * entityList-->voList
     */
    List<ForecastRecordVO> entityListToVoList(List<ForecastRecord> list);

    /**
     * dtoList-->entityList
     */
    List<ForecastRecord> dtoListToEntityList(List<ForecastRecordDTO> dtoList);
}
