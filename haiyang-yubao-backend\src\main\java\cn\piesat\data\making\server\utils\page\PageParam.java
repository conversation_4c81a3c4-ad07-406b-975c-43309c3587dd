package cn.piesat.data.making.server.utils.page;

import cn.piesat.data.making.server.utils.result.Result;

import java.util.Objects;

/**
 * 分页参数
 *
 * <AUTHOR> peihe.che
 * @version 1.0
 * @date 2022/02/28 09:30
 */
public class PageParam {

    /**
     * 当前页码
     *
     * @required
     * @mock 1
     */
    private Integer pageNum;

    /**
     * 页长
     *
     * @required
     * @mock 10
     */
    private Integer pageSize;


    /**
     * 校验分页必填参数
     *
     * @return
     */
    public Result verify() {
        if (Objects.isNull(pageNum)) {
            return Result.fail("pageNum不能为空！");
        }
        if (Objects.isNull(pageSize)) {
            return Result.fail("pageSize不能为空！");
        }
        return Result.success();
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
