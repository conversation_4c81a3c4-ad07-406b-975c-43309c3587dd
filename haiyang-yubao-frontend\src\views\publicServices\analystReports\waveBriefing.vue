<template>
  <div class="public-container">
    <div class="top">
      <div class="top-left">
        <div
          :class="['top-tab', activeTab === 0 ? 'active' : '']"
          @click="activeTab = 0"
        >
          海浪日报
        </div>
        <div
          :class="['top-tab', activeTab === 1 ? 'active' : '']"
          @click="activeTab = 1"
        >
          海浪周报
        </div>
      </div>
      <div class="top-right">
        <span class="date">保存：{{ saveDate }}</span>
        <span class="date">提交：{{ commitDate }}</span>
        <qx-button class="primary">保存</qx-button>
        <qx-button class="warning">提交</qx-button>
      </div>
    </div>
    <div class="content flex-column">
      <template v-if="activeTab == 0">
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">20250123海况分析</div>
              <span>（上一期：2025-01-22）</span>
            </div>
            <div class="header-right">
              <qx-button class="primary">更新</qx-button>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              :rows="6"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">20250123海浪预报</div>
              <span>（上一期：2025-01-22）</span>
            </div>
            <div class="header-right">
              <qx-button class="primary">更新</qx-button>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="waveForecasting"
              type="textarea"
              :rows="6"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">建议措施</div>
              <span>（上一期：2025-01-22）</span>
            </div>
            <div class="header-right correspondence">
              <qx-button class="primary" @click="isShowCopyTxt = true"
                >对应关系</qx-button
              >
              <CopyTxt
                :visible="isShowCopyTxt"
                @close="isShowCopyTxt = false"
                :txtList="adviceList"
                title="对应关系"
              />
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="suggestion"
              type="textarea"
              :rows="6"
              placeholder="请输入"
            />
          </div>
        </div>
      </template>
      <template v-else>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">2024-1-17日至2025-1-23日实况分析</div>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="seaConditionAnalysis"
              type="textarea"
              :rows="6"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">2025-1-24日至2025-1-30日海浪预报</div>
              <span>（上一期：2025-01-17 至2025-01-23）</span>
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="waveForecasting"
              type="textarea"
              :rows="6"
              placeholder="请输入"
            />
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <div class="header-left">
              <div class="card-title">防御建议</div>
              <span>（上一期：2025-01-17 至2025-01-23）</span>
            </div>
            <div class="header-right correspondence">
              <qx-button class="primary" @click="isShowCopyTxt = true"
                >对应关系</qx-button
              >
              <CopyTxt
                :visible="isShowCopyTxt"
                @close="isShowCopyTxt = false"
                :txtList="adviceList"
                title="对应关系"
              />
            </div>
          </div>
          <div class="card-info">
            <n-input
              v-model:value="waveForecasting"
              type="textarea"
              :rows="6"
              placeholder="请输入"
            />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { adviceList } from './constantTxt.js'
import { CopyTxt } from './components'

const activeTab = ref(0)
const saveDate = ref('2025-01-23 12:00')
const commitDate = ref('2025-01-23 12:00')
const seaConditionAnalysis = ref(
  '1月22日08时～1月23日08时，南海大部分海域出现了大于3.0米的大浪区，其中中部和南部海域出现了大于4.0米的巨浪区。海南岛北部近岸海域出现了1.0-2.0米的轻到中浪，东部近岸海域出现了1.0-2.0米的轻到中浪，南部近岸海域出现了1.0-2.0米的轻到中浪，西部近岸海域出现了1.0-2.0米的轻到中浪。'
)
const waveForecasting = ref('111112')
const suggestion = ref('hifiuhiufheruifyeuriguhfdvudfudhff')

const isShowCopyTxt = ref(false)
const relationalList = ref<any[]>([])
adviceList.forEach((item: string) => {
  relationalList.value.push({
    type: 'render',
    render: () => {
      return renderHandler(item)
    }
  })
})

function renderHandler(item: string) {
  return h('div', { class: 'dropdown-item' }, [
    h('p', null, item),
    h('QxButton', { class: 'primary' }, '复制文本')
  ])
}
function handleUpdateShow(show: boolean) {}
</script>
<style lang="scss" scoped>
@import url('./style.scss');
.correspondence{
  position:relative;
}
</style>
