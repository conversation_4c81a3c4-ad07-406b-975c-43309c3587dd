package cn.piesat.data.making.server.dto.generate;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown=true)
public class SeaWaveAlarmResultDTO {
    private Long id;
    private String name;
    private String type;
    private Double ave;
    private Double max;
    private Double min;
    private String var;
    private Integer dtime;
    @JsonIgnore
    private DateTime forecastTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getAve() {
        return ave;
    }

    public void setAve(Double ave) {
        this.ave = ave;
    }

    public Double getMax() {
        return max;
    }

    public void setMax(Double max) {
        this.max = max;
    }

    public Double getMin() {
        return min;
    }

    public void setMin(Double min) {
        this.min = min;
    }

    public String getVar() {
        return var;
    }

    public void setVar(String var) {
        this.var = var;
    }

    public Integer getDtime() {
        return dtime;
    }

    public void setDtime(Integer dtime) {
        this.dtime = dtime;
    }

    public DateTime getForecastTime() {
        return forecastTime;
    }

    public void setForecastTime(DateTime forecastTime) {
        this.forecastTime = forecastTime;
    }
}
