<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-24 18:06:46
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-26 10:08:17
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/create.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <qx-dialog
    title="新建专项报告"
    :visible="visible"
    width="1297px"
    class="create-special-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <div class="dialog-content d-flex">
        <div class="dialog-content-left">
          <n-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            required-mark-placement="left"
            class="qx-form"
          >
            <n-form-item label="产品名称" path="user.productName">
              <n-input
                v-model:value="form.productName"
                placeholder="请输入"
                clearable
              />
            </n-form-item>
            <n-form-item label="发往单位名称" path="unitName">
              <n-input
                v-model:value="form.unitName"
                placeholder="请输入"
                clearable
              />
            </n-form-item>
            <n-form-item label="数据源" path="dataSource">
              <n-radio-group v-model:value="form.dataSource" name="radiogroup">
                <n-space>
                  <n-radio value="grid">智能网格</n-radio>
                  <n-radio value="nmefc">NMEFC</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="要素" path="element">
              <n-select
                v-model:value="form.element"
                :options="elementList"
                label-field="name"
                value-field="code"
                @update:value="onSelectElement"
              />
              <!-- <n-checkbox-group v-model:value="form.element">
                <n-space>
                  <n-checkbox
                    v-for="item in elementList"
                    :key="item"
                    :value="item.code"
                    :label="item.name"
                  />
                </n-space>
              </n-checkbox-group> -->
            </n-form-item>
            <n-form-item label="起报时间" path="startTime">
              <n-select
                v-model:formatted-value="form.startTime"
                :options="startTimeList"
                label-field="name"
                value-field="code"
              />
            </n-form-item>
            <n-form-item
              label="预报范围"
              path="forecastTime"
              :show-feedback="false"
            >
              <n-grid :cols="24">
                <n-form-item-gi :span="24" label="" path="forecastStartTime">
                  <n-date-picker
                    v-model:formatted-value="form.forecastStartTime"
                    type="datetime"
                    clearable
                  />
                  至
                </n-form-item-gi>
                <n-form-item-gi :span="24" label="" path="forecastEndTime">
                  <n-date-picker
                    v-model:formatted-value="form.forecastEndTime"
                    type="datetime"
                    clearable
                  />
                </n-form-item-gi>
              </n-grid>
            </n-form-item>
            <n-form-item label="预报区域" path="forecastArea">
              <n-grid>
                <n-form-item-gi :span="24" :show-feedback="false">
                  <i class="icon icon-add1" @click="areaVisible = true"></i>
                </n-form-item-gi>
                <n-form-item-gi
                  v-if="areaList.length > 0"
                  :span="24"
                  :show-feedback="false"
                >
                  <div class="area-list">
                    <div
                      v-for="(item, index) in areaList"
                      :key="item"
                      class="area-item d-flex"
                    >
                      <div class="area-name">{{ item.name }}</div>
                      <i
                        class="icon icon-edit1"
                        @click="drawPolygon(item, index)"
                      ></i>
                      <i class="icon icon-del1" @click="onDel(index)"></i>
                    </div>
                  </div>
                </n-form-item-gi>
              </n-grid>
            </n-form-item>
          </n-form>
          <div class="btn-group text-right">
            <qx-button class="cancel" @click="onClose">取消</qx-button>
            <qx-button class="primary" @click="onCreate">确定</qx-button>
          </div>
        </div>
        <base-map :show-tool="false" @ready="onMapReady"></base-map>
      </div>
    </template>
  </qx-dialog>

  <qx-dialog
    title="新建区域"
    width="332px"
    :visible="areaVisible"
    class="add-area-dialog"
  >
    <template #content>
      <n-form
        :model="areaForm"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        required-mark-placement="left"
        class="qx-form"
      >
        <n-form-item label="区域名称" path="areaName">
          <n-input
            v-model:value="areaForm.areaName"
            placeholder="请输入"
            clearable
          />
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btn-group">
        <qx-button class="cancel" @click="areaVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onDialogConfirm">确定</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>
<script lang="ts" setup>
import { QxDialog } from 'src/components/QxDialog'
import { BaseMap } from 'src/components/OpenlayersMap'
import { reactive, ref, onMounted } from 'vue'
import Api from 'src/requests/forecast'
import { useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import 'ol-ext/render/Cspline.js'
import Draw from 'ol/interaction/Draw.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import GeoJSON from 'ol/format/GeoJSON.js'

const message = useMessage()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:visible'])

function onClose() {
  emit('update:visible', false)
}
function onCreate() {}

let map = ref()
function onMapReady(val: any) {
  if (val) {
    map.value = val
    map.value.addLayer(vectorLayer) // 添加绘制图层
    map.value.addInteraction(draw) // 添加绘制交互
  }
}

const form = reactive({
  productName: '',
  unitName: '',
  dataSource: 'grid',
  element: null,
  forecastStartTime: null,
  forecastEndTime: null,
  startTime: '',
  forecastArea: []
})

const rules = {
  productName: {
    required: true,
    message: '请输入产品名称',
    trigger: 'blur'
  },
  unitName: {
    required: true,
    message: '请输入发往单位名称',
    trigger: 'blur'
  },
  element: {
    required: true,
    trigger: ['change'],
    message: '请选择要素'
  },
  areaName: {
    required: true,
    message: '请输入区域名称',
    trigger: 'blur'
  },
  startTime: {
    required: true,
    message: '请选择起报时间',
    trigger: 'change'
  },
  forecastStartTime: {
    required: true,
    message: '请选择预报范围开始时间',
    trigger: 'change'
  },
  forecastEndTime: {
    required: true,
    message: '请选择预报范围结束时间',
    trigger: 'change'
  }
}

// let elementList = ref<any[]>([
//   {
//     name: '浪高',
//     code: 'WaveHeight'
//   },
//   {
//     name: '浪向',
//     code: 'WaveDirection'
//   },
//   {
//     name: '风速',
//     code: 'WindSpeed'
//   },
//   {
//     name: '风向',
//     code: 'WindDirection'
//   },
//   {
//     name: '海温',
//     code: 'SeaTemperature'
//   },
//   {
//     name: '海流',
//     code: 'SeaCurrent'
//   },
//   {
//     name: '盐度',
//     code: 'Salinity'
//   }
// ]) //要素列表

let elementList = ref<any[]>([]) //要素列表
function onSelectElement(val: any) {
  // 修改要素
  console.log(val)
}
// 获取要素列表
function getELementList() {
  Api.getElementList()
    .then((res: any) => {
      elementList.value = res
    })
    .catch(e => {
      let { msg = null } = e?.data || {}
      message.error(msg || '获取要素列表失败')
    })
}

let startTimeList = ref<any[]>([]) // 起报时间列表
startTimeList.value = [
  {
    name: '2025-03-24 12:00',
    code: '2025-03-24 12:00'
  },
  {
    name: '2025-03-24 18:00',
    code: '2025-03-24 18:00'
  }
]

let areaList = ref<any[]>([]) // 区域列表
let areaVisible = ref(false)

let editIndex = ref(-1)
function onDel(index: number) {
  areaList.value.splice(index, 1)
  vectorSource.refresh()
}

const areaForm = reactive({
  areaName: ''
})
function onDialogConfirm() {
  areaList.value.push({
    name: areaForm.areaName
  })
  areaVisible.value = false
  areaForm.areaName = ''
}

// 绘制多边形
const vectorSource = new VectorSource()
const vectorLayer = new VectorLayer({
  source: vectorSource
})

const draw = new Draw({
  source: vectorSource,
  type: 'Polygon',
  trace: true,
  style: {
    'stroke-color': 'rgba(255, 255, 100, 0.25)',
    'stroke-width': 1.5,
    'fill-color': 'rgba(255, 255, 100, 0.25)'
  }
})

function drawPolygon(item: any, index: number) {
  editIndex.value = index
  vectorSource.refresh()
  draw.setActive(true)
}
draw.on('drawend', drawend)
function drawend() {
  draw.setActive(false)
  setTimeout(() => {
    const features: any = []
    vectorSource.forEachFeature(item => {
      features.push(item)
    })
    const geojsonFormat = new GeoJSON()
    const geojsonObject = geojsonFormat.writeFeatures(features)

    // 现在 geojsonObject 是一个 GeoJSON 对象，你可以将其转换为字符串或者直接使用
    const graphicJson = JSON.stringify(geojsonObject)
    areaList.value[editIndex.value].graphicJson = graphicJson
  }, 0)
}

onMounted(() => {
  getELementList()
})
</script>
<style lang="scss" scoped>
.create-special-dialog {
  .dialog-content {
    box-sizing: border-box;
    padding: 20px;
  }
  .dialog-content-left {
    margin-right: 10px;
  }
  .n-form {
    width: 308px;
    height: 489px;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 10px;
  }
  .btn-group {
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  #openlayers-map {
    width: calc(100% - 308px);
    height: 541px;
  }
  i.icon-add1 {
    width: 20px;
    height: 20px;
    background-image: url(src/assets/images/icons/icon-add1.png);
  }
  i.icon-edit1 {
    width: 32px;
    height: 32px;
    background-image: url(src/assets/images/icons/icon-edit1.png);
    margin-right: 5px;
  }
  i.icon-del1 {
    width: 32px;
    height: 32px;
    background-image: url(src/assets/images/icons/icon-del1.png);
  }

  .area-name {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    color: #5a94db;
    background: #fafcfe;
    border-radius: 4px;
    border: 1px solid #e7f0fb;
    padding: 0 5px;
    margin-right: 5px;
    min-width: 126px;
  }
  .area-item {
    margin-bottom: 5px;
  }
}
.add-area-dialog {
  .qx-form {
    box-sizing: border-box;
    padding: 20px 20px 0;
  }
  .btn-group {
    text-align: right;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 20px;
  }
}
</style>
