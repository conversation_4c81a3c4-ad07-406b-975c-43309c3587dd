package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmPublicTask;
import cn.piesat.data.making.server.vo.FmPublicTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmPublicTaskMapper {

    FmPublicTaskMapper INSTANCE = Mappers.getMapper(FmPublicTaskMapper.class);

    List<FmPublicTaskVO> entityListToVoList(List<FmPublicTask> list);
}
