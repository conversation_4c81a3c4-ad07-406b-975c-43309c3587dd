package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmPublicTemplateDao;
import cn.piesat.data.making.server.entity.FmPublicTemplate;
import cn.piesat.data.making.server.mapper.FmPublicTemplateMapper;
import cn.piesat.data.making.server.service.FmPublicTemplateService;
import cn.piesat.data.making.server.vo.FmPublicTemplateVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Service
public class FmPublicTemplateServiceImpl extends ServiceImpl<FmPublicTemplateDao, FmPublicTemplate> implements FmPublicTemplateService {

    @Autowired
    private FmPublicTemplateDao fmPublicTemplateDaoImpl;

    @Autowired
    private FmPublicTemplateMapper fmPublicTemplateMapperImpl;

    @Override
    public List<FmPublicTemplateVO> queryList(Boolean status) {
        List<FmPublicTemplate> resList = this.list(createQueryWrapper(status));
        return fmPublicTemplateMapperImpl.entityListToVoList(resList);
    }
    private LambdaQueryWrapper<FmPublicTemplate> createQueryWrapper(Boolean status) {
        LambdaQueryWrapper<FmPublicTemplate> queryWrapper = new LambdaQueryWrapper<>();
        if (status != null) {
            queryWrapper.eq(FmPublicTemplate::getStatus, status);
        }

        return queryWrapper.orderByAsc(FmPublicTemplate::getCreateTime);
    }

}
