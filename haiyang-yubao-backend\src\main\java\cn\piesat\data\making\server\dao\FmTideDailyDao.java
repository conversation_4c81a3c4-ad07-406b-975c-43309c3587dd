package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmTideDialy;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 潮位岸段潮汐预报表数据库访问层
 *
 * <AUTHOR>
 * @date 20250713
 */
public interface FmTideDailyDao extends BaseMapper<FmTideDialy> {

    @Select("select (a.height-b.datum_blue_warn)::int4 as datum_blue_dif,a.*,b.datum_blue_warn from fm_tide_daily a left join fm_station_b b on a.station_id = b.id where 1=1  and a.tide_time between to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') and a.datum = 'datum' and (a.height-b.datum_blue_warn)>-30 and b.id in (select station_id from fm_tide_daily group by station_id,station_name)")
    List<FmTideDialy> queryListByParam(@Param("startTime") String startTime, @Param("endTime")String endTime);
}
