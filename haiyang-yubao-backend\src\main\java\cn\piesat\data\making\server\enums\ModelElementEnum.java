package cn.piesat.data.making.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ModelElementEnum {
    /**
     * 海风
     */
    WIND("WIND"),
    /**
     * 海浪
     */
    WAVE("WAVE"),
    /**
     * 海温
     */
    TEMP("TEMP"),
    /**
     * 海流
     */
    CURRENT("CURRENT"),
    /**
     * 盐度
     */
    SALINITY("SALINITY"),
    /**
     * 风暴潮
     */
    TIDE("TIDE");

    private String value;
}
