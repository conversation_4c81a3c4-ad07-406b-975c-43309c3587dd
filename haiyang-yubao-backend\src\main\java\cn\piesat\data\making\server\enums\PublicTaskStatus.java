package cn.piesat.data.making.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 预报任务状态
 **/
@AllArgsConstructor
@Getter
public enum PublicTaskStatus {
    /**
     * 待制作
     */
    WAIT_MAKE(1, "待制作"),
    /**
     * 已制作
     */
    MAKED(2, "已制作"),
    /**
     * 已发布
     */
    RELEASED(3, "已发布");

    private Integer value;
    private String desc;

    public static Map<Integer, String> toMap() {
        return Stream.of(PublicTaskStatus.values())
                .collect(Collectors.toMap(enumItem -> enumItem.getValue(), enumItem -> enumItem.getDesc()));
    }
}
