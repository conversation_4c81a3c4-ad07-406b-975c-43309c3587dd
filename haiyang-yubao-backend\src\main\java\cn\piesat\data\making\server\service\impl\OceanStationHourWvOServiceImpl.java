package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourWvODao;
import cn.piesat.data.making.server.entity.OceanStationHourWvO;
import cn.piesat.data.making.server.service.OceanStationHourWvOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-海浪特征值-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourWvOServiceImpl extends ServiceImpl<OceanStationHourWvODao, OceanStationHourWvO>
        implements OceanStationHourWvOService {

    @Resource
    private OceanStationHourWvODao oceanStationHourWvODao;

    @Override
    public List<OceanStationHourWvO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourWvODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourWvODao.getMaxCreateTime();
    }
}





