import { Stroke, Style, Text } from 'ol/style'
import { FeatureLike } from 'ol/Feature'

/**
 * 获取等值线默认样式
 * @param keyCallback
 */
export function isolineStyleFactory(keyCallback: () => string) {
  return (feature: FeatureLike) => {
    const properties = feature.getProperties()
    const value = properties[keyCallback()]
    return new Style({
      stroke: new Stroke({
        width: 0.7,
        color: 'red'
      }),
      text: new Text({
        placement: 'line',
        font: 'bold 10px Calibri,sans-serif',
        stroke: new Stroke({
          width: 1,
          color: 'white'
        }),
        text: value.toString()
      })
    })
  }
}
