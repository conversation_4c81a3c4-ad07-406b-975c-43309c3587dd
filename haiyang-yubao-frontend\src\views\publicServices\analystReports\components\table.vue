<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-25 15:46:01
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-26 10:44:25
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/analystReports/components/infoBulletin/table.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="table-container">
    <n-data-table
      :columns="columns"
      :data="data"
    />
  </div>
</template>

<script setup lang="ts">
import {ref,watch,PropType} from 'vue'
const props = defineProps({
  tableHead:{
    type:Array,
    default:()=>[]
  },
  data:{
    type:Array as PropType<any[]>,
    default:()=>[]
  }
})

let columns = ref<any>([])

watch(()=>props.tableHead,(val:any)=>{
  columns.value = val
},{
  immediate:true
})
</script>

<style scoped>
.table-container{
  box-sizing: border-box;
  padding:20px;
}
</style>