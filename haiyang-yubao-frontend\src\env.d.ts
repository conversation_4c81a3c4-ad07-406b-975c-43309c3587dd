/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-11 10:52:17
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-23 14:32:20
 * @Description: 声名文件扩充
 * Copyright (c) 2022 by piesat, All Rights Reserved.
 */
/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv extends Readonly<Record<string, string>> {
  readonly VITE_Base_Url: string
  readonly VITE_Busi_Url: string

  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module 'crypto-js'

declare module 'html2canvas'
declare module 'proj4'
declare interface Window {
  DocsAPI: any
}
declare module 'shapefile'

// 声明一个命名空间mircoConfig
declare namespace config {
  const assemble_sub_web: string
  const dispatch_sub_web: string
  const data_manage_sub_web: string
  const auth_sub_web: string
  const data_push_web: string //数据推送
  const dictionary_sub_web: string //字典服务
  const loginExpireCode: string //登录失效code
  const permissionSwitch: boolean //权限开关
  const userMultipleType: boolean //用户多类型
  const loginUrl: string //登录地址
  const checklogin: boolean //是否需要登录
  const passportUrl: string
  const mapService: string
  const onlyOfficeCallBack: string
  const onlyOfficeServerUrl: string
  const kkFileUrl: string
  const hnMapService: string
  const homeUrl: string // 总集成页面
  const navMenuUrl: string
  const fileService: string // 文件服务地址
  const ettpService: string // ettp服务地址
  const mapParams: string // 地图参数
  const vecMapLayer: string // vect图层
  const cvaMapLayer: string // cva图层
  const landLayer: string //陆地图层
}

declare namespace menuJson {
  const navigationMenu: any
}

declare const ssoClient: any