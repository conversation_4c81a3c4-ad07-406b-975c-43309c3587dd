package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.generate.PublicProductWordDTO;
import cn.piesat.data.making.server.dto.generate.PublicRecordDateWordDTO;
import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;
import cn.piesat.data.making.server.vo.FmPublicRecordVO;
import cn.piesat.data.making.server.vo.UserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PublicProductWordMapper {
    PublicProductWordMapper INSTANCE = Mappers.getMapper(PublicProductWordMapper.class);

    @Mapping(target = "month", source = "vo.msgTime", dateFormat = "M")
    @Mapping(target = "time", source = "vo.msgTime", dateFormat = "yyyy年MM月dd日HH时")
    @Mapping(target = "number", source = "vo.msgNo")
    @Mapping(target = "eMail", source = "userVO.email")
    @Mapping(target = "makeUserName", source = "vo.signMakerName")
    @Mapping(target = "signUserName", source = "vo.signUserName")
    PublicProductWordDTO toDTO(FmPublicRecordVO vo, UserVO userVO, String fax);


    List<PublicRecordDateWordDTO> toList(List<FmPublicRecordDataVO> list);

    @Mapping(target = "tideTimeDate", source = "tideTime", dateFormat = "yyyy-MM-dd")
    @Mapping(target = "tideTimeTime", source = "tideTime", dateFormat = "HH:mm")
    PublicRecordDateWordDTO voToDTO(FmPublicRecordDataVO vo);
}
