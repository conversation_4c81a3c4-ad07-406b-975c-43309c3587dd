package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 预报模板-列定义实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_forecast_template_column_b")
public class ForecastTemplateColumn implements Serializable {

    private static final long serialVersionUID = 828209511416901198L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 预报模板id
     **/
    @TableField("forecast_template_id")
    private Long forecastTemplateId;
    /**
     * 要素编码
     **/
    @TableField("element_code")
    private String elementCode;
    /**
     * 列编码
     **/
    @TableField("column_code")
    private String columnCode;
    /**
     * 列名
     **/
    @TableField("column_name")
    private String columnName;
    /**
     * 数值处理
     **/
    @TableField("value_handle")
    private String valueHandle;
    /**
     * 是否显示
     **/
    @TableField("display")
    private Boolean display;
    /**
     * 创建人id
     **/
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建人
     **/
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人id
     **/
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;
    /**
     * 更新人
     **/
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ForecastTemplateColumn column = (ForecastTemplateColumn) o;
        return Objects.equals(elementCode, column.elementCode) && Objects.equals(valueHandle, column.valueHandle);
    }

    @Override
    public int hashCode() {
        return Objects.hash(elementCode, valueHandle);
    }
}



