<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-28 11:22:22
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-08 09:35:22
 * @FilePath: /hainan-jianzai-web/src/components/HeaderBar/NavMenu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%
-->
<template>
  <div
    v-if="props.visible"
    class="nav-menu"
    @mouseover="onMouseEnter()"
    @mouseleave="onMouseLeave()"
  >
    <iframe
      :src="iframeUrl"
      width="100%"
      height="100%"
      frameborder="0"
      style="border: none"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useCookies } from 'vue3-cookies'
const { cookies } = useCookies()
const { navMenuUrl } = config || {}
let iframeUrl = ref('')
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['change'])
function onMouseEnter() {
  emits('change', true)
}

function onMouseLeave() {
  emits('change', false)
}

onMounted(() => {
  let token = cookies.get('hn-token')
  iframeUrl.value = navMenuUrl + token
})
</script>

<style scoped lang="scss">
.nav-menu {
  width: 100%;
  height: 300px;
  // box-sizing: border-box;
  // padding: 30px;
  position: fixed;
  top: 64px;
  left: 0;
  z-index: 120;
  background: #fff;
  display: flex;
  .aside {
    width: 196px;
    height: 100%;
    border-right: 1px solid #dadada;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 22px;
      color: #222222;
      line-height: 26px;
      margin-bottom: 12px;
    }
    span {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: rgba(102, 102, 102, 0.8);
      line-height: 16px;
    }
  }
  .nav-menu-list {
    .nav-menu-item {
      margin-left: 107px;
      &:nth-child(1) {
        width: 531px;
        .nav-menu-item-list {
          display: flex;
          flex-wrap: wrap;
          .list-item {
            width: 50%;
          }
        }
      }
    }
    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 22px;
      color: #222222;
      line-height: 26px;
      margin-bottom: 27px;
    }
    .list-item {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
      margin-bottom: 25px;
      display: flex;
      align-items: center;
      cursor: pointer;
      i.icon {
        margin-right: 5px;
      }
    }
    .grid-wrapper {
      display: grid;
    }
  }
}
</style>
