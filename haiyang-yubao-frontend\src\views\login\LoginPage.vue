<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-21 14:17:20
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-18 15:29:05
 * @Description:
 * Copyright (c) 2023 by piesat, All Rights Reserved.
-->
<template>
  <n-config-provider :theme-overrides="themeOverrides">
    <div class="wrapper login">
      <div class="header">
        <h2>海南海洋业务一体化平台</h2>
      </div>
      <video
        src="src/assets/images/login/login.mp4"
        autoplay="true"
        loop="true"
        muted="true"
      ></video>

      <div class="login-form">
        <div class="title">登 录</div>
        <n-input-group>
          <i class="icon icon-username"></i>
          <n-input
            v-model:value="username"
            placeholder="请输入账号"
            clearable
          />
        </n-input-group>

        <n-input-group>
          <i class="icon icon-password"></i>
          <n-input
            v-model:value="pwd"
            clearable
            placeholder="请输入密码"
            :type="pwdType"
          />
          <i
            class="icon password-icon"
            :class="pwdType === 'password' ? 'icon-close' : 'icon-open'"
            @click="changePwdType"
          ></i>
        </n-input-group>

        <n-button
          type="primary"
          :block="true"
          class="login-btn"
          @click="onLogin"
          >登 录</n-button
        >
      </div>
    </div>
  </n-config-provider>
</template>

<script setup lang="ts">
import { useMessage } from 'naive-ui'
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Api from 'src/requests/authRequest'

const username = ref('')
const pwd = ref('')
const message = useMessage()
const router = useRouter()
const route = useRoute()

type pwdT = 'password' | 'text'
const pwdType = ref('password' as pwdT)

const themeOverrides = {
  Input: {
    placeholderColor: '#fff'
  }
}

onMounted(() => {
  window.addEventListener('keydown', keyDown)
})
const keyDown = (e:any) => {
  //如果是回车则执行登录方法
  if (e.keyCode == 13) {
    onLogin()
  }
}

onUnmounted(() => {
  window.removeEventListener('keydown', keyDown, false)
})

function onLogin() {
  const params = {
    loginName: username.value,
    loginPass: pwd.value
  }
  Api.login(params)
    .then(res => {
      const { fullPath } = route
      message.success('登录成功')

      localStorage.setItem('username', username.value)
      const decodeFullPath = decodeURIComponent(fullPath).replace(
        /^\/login\?/,
        ''
      )
      // 若无跳转来源路径，则跳转到当前项目登录后默认路径
      if (
        decodeFullPath.length <= 0 ||
        !decodeFullPath.includes('redirect_url')
      ) {
        router.push('/')
        return
      }
      const redirectPath = decodeFullPath.replace(/^redirect_url=/gi, '') // 重定向地址路径
      // 若来源地址是以`http`开头的完整路径，则跳转到来源路径
      if (/^http/gi.test(redirectPath)) {
        window.location.href = redirectPath
        return
      }
      // 若来源地址不是以`http`开头的路径，则认为是当前项目的路径，跳转到来源路径
      router.push(redirectPath)
    })
    .catch(e => {
      let { msg } = e?.data || {}
      message.error(msg || '登录失败')
    })
}

function changePwdType() {
  pwdType.value = pwdType.value == 'password' ? 'text' : 'password'
}
</script>

<style lang="scss">
.wrapper.login {
  width: 100%;
  height: 100%;
  position: relative;
  .login-form {
    position: absolute;
    top: 228px;
    right: 109px;
    background: url(src/assets/images/login/login-form.png) no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 82px 56px 80px;
    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 40px;
      color: #c7ddff;
      line-height: 47px;
      letter-spacing: 12px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 55px;
    }
  }
  .icon:not(.password-icon) {
    margin-right: 15px;
  }
  .password-icon {
    margin-left: 15px !important;
  }

  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
  .header {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 79px;
    background: url(src/assets/images/login/bg.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    h2 {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 40px;
      line-height: 47px;
      letter-spacing: 6px;
      // text-shadow: 0px 4px 4px rgba(0,0,0,0.25);
      text-align: center;
      font-style: normal;
      text-transform: none;
      background-image: linear-gradient(
        269.99999999996083deg,
        #9dd0ff 0%,
        #ffffff 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .n-input-group {
    width: 405px;
    background: rgba(26, 30, 38, 0.5);
    box-sizing: border-box;
    padding: 12px 15px 12px 17px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
  }
  .n-input {
    background: transparent;
  }
  .n-input__border,
  .n-input__state-border {
    border: 0;
  }
  .login-btn {
    height: 48px;
    margin-top: 74px;
    background: #1e75ff;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    line-height: 23px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    border: none;
    .n-button__border,
    .n-button__state-border {
      border: 0;
    }
    span {
      letter-spacing: 20px;
    }
  }
  .n-input .n-input-wrapper {
    padding: 0 10px;
  }
  .n-input__input-el {
    caret-color: transparent;
    color: #ffffff;
  }
}
</style>
