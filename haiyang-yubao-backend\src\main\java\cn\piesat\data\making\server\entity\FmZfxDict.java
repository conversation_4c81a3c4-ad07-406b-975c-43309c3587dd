package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import cn.piesat.common.utils.Constant;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-11-08 16:14:12
 */
@Data
@Accessors(chain = true)
@TableName("fm_zfx_dict")
public class FmZfxDict implements Serializable {

    private static final long serialVersionUID = 852644376363350854L;


    @TableField("id")
    private Long id;
    @TableField("product_id")
    private String productId;
    @TableField("name")
    private String name;
    @TableField("type")
    private String type;
    @TableField("docx_product_id")
    private String docxProductId;

    public String getDocxProductId() {
        return docxProductId;
    }

    public void setDocxProductId(String docxProductId) {
        this.docxProductId = docxProductId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
