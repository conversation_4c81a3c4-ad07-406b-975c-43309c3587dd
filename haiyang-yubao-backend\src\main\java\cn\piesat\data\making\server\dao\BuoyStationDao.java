package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.BuoyStation;
import cn.piesat.data.making.server.model.StationInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 浮标站表表数据库访问层
 *
 * <AUTHOR>
 */
public interface BuoyStationDao extends BaseMapper<BuoyStation> {
    @Select({"<script>",
            "SELECT DISTINCT code,name FROM (SELECT buoyinfo_id as code,buoyinfo_name as name FROM ocean_large_deepsea_mooringbuoy_o where buoyinfo_name is not null " +
                    "and buoyinfo_id in ('MF20000','MF20001','MF20002','MF20003','MF20004','MF20005','MF20006','MF20007','MF20008')" +
                    " UNION ALL " +
                    " SELECT buoyinfo_id as code,buoyinfo_name as name FROM ocean_small_shallowsea_buoy_o where buoyinfo_name is not null and buoyinfo_id is not null " +
                    ") AS temp " +
                    "where 1=1 " +
                    "<if test='stationName != null'> " +
                    " and temp.name like '%${stationName}%'" +
                    "</if>",
            "</script>"})
    List<StationInfo> getList(String stationName);
}
