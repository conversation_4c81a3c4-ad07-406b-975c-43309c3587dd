package cn.piesat.data.making.server.vo;

import lombok.Data;

import java.util.Date;

@Data
public class TideDailyWarnLevelVO {

    private Long id;
    /**
     * 站点名称
     */
    private String stationName;
    /**
     * 站点id
     */
    private Long stationId;
    /**
     * 时间
     */
    private Date tideTime;
    /**
     * 高潮值
     */
    private int height;
    /**
     * 警戒潮位
     */
    private Integer warnHeight;
    /**
     * 预警级别
     */
    private Integer level;
    /**
     * 预警级别描述
     */
    private String levelDesc;
    /**
     * 影响地区
     */
    private String regionName;
    /**
     * 日期
     */
    private String date;
    /**
     * 高潮时
     */
    private String tide;
}
