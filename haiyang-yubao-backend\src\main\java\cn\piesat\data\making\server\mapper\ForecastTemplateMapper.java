package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastTemplateDTO;
import cn.piesat.data.making.server.entity.ForecastTemplate;
import cn.piesat.data.making.server.vo.ForecastTemplateVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ForecastTemplateMapper {

    ForecastTemplateMapper INSTANCE = Mappers.getMapper(ForecastTemplateMapper.class);

    /**
     * entity-->vo
     */
    ForecastTemplateVO entityToVo(ForecastTemplate entity);

    /**
     * dto-->entity
     */
    ForecastTemplate dtoToEntity(ForecastTemplateDTO dto);

    /**
     * entityList-->voList
     */
    List<ForecastTemplateVO> entityListToVoList(List<ForecastTemplate> list);

    /**
     * dtoList-->entityList
     */
    List<ForecastTemplate> dtoListToEntityList(List<ForecastTemplateDTO> dtoList);
}
