package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.StormSurge;
import cn.piesat.data.making.server.vo.TideDailyHourDataVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * 海洋站表服务接口
 *
 * <AUTHOR>
 */
public interface StormSurgeService extends IService<StormSurge> {

    List<TideDailyHourDataVO> getTideList(String stationNum, Date startTime, Date endTime);
}




