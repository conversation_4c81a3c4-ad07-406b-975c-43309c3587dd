/*
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-07 16:59:29
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-02-08 10:11:45
 * @FilePath: \hainan-jianzai-web\src\components\OpenlayersMap\components\changeStore.js
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { Stroke, Style, Fill, Text, Circle } from 'ol/style.js'
import { useStackStore } from 'src/stores/snapshot.js'
const stackStore = useStackStore()
function changeStore(map) {
  const layers = map.getAllLayers()
  const objs = []
  layers.forEach(layer => {
    const properties = layer.getProperties()
    if (properties && properties.canRedo) {
      const features = layer.getSource().getFeatures()

      const fs = []
      features.forEach(feature => {
        const f1 = feature.clone()
        if (properties.name === 'lqgj') {
          let propertiesF = f1.getProperties()
          f1.setStyle(
            new Style({
              stroke: new Stroke({
                color: propertiesF.strokeColor,
                width: propertiesF.strokeWith
              }),
              fill: new Fill({
                color: hexToRgb(propertiesF.fillColor, propertiesF.opacity)
              }),
              text: new Text({
                font: '16px Calibri,sans-serif',
                text: propertiesF.value,
                fill: new Fill({
                  color: '#000'
                }),
                stroke: new Stroke({
                  color: '#fff',
                  width: 3
                }),
                placement: 'line'
              })
            })
          )
        }
        fs.push(f1)
      })
      objs.push({
        layer: layer,
        features: fs
      })
    }
  })
  stackStore.operate(objs)
  console.log(stackStore.operateStack)
}
function hexToRgb(hex, opacity) {
  let result = ''
  if (!hex.includes('#')) {
    result = hex
  } else {
    // 去除可能存在的"#"符号
    hex = hex.replace('#', '')

    // 将三位数的十六进制值转换为六位
    if (hex.length === 3) {
      hex = hex
        .split('')
        .map(hexDigit => hexDigit + hexDigit)
        .join('')
    }

    // 将十六进制值转换为RGB值
    const r = parseInt(hex.slice(0, 2), 16)
    const g = parseInt(hex.slice(2, 4), 16)
    const b = parseInt(hex.slice(4, 6), 16)

    result = `rgba(${r}, ${g}, ${b},${opacity / 100})`
  }
  return result
}
export default changeStore