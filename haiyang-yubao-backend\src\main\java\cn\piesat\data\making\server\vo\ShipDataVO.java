package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 船舶数据表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ShipDataVO implements Serializable {

    private static final long serialVersionUID = 992578722777680950L;

    /**
     * id
     **/
    private Long id;
    /**
     * 名称
     **/
    private String signShip;
    /**
     * 纬度
     **/
    private Double latitude;
    /**
     * 经度
     **/
    private Double longitude;
    /**
     * 可见度
     **/
    private Double visibility;
    /**
     * 气温
     **/
    private Double airTemperature;
    /**
     * 露点温度
     **/
    private Double dewPointTemp;
    /**
     * 风向
     **/
    private Double windDirection;
    /**
     * 风速
     **/
    private Double windSpeed;
    /**
     * 气压
     **/
    private Double airPressure;
    /**
     * 云总量
     **/
    private Double totalCloudAmount;
    /**
     * 云量低
     **/
    private Double lowCloudAmount;
    /**
     * 海温
     **/
    private Double seaTemp;
    /**
     * 波期
     **/
    private Double wavePeriod;
    /**
     * 波高
     **/
    private Double waveHeight;
    /**
     * 浪涌方向
     **/
    private Double surgeDirection;
    /**
     * 浪涌周期
     **/
    private Double surgePeriod;
    /**
     * 浪涌高度
     **/
    private Double surgeHeight;
    /**
     * 位置
     **/
    private String locationGeo;
    /**
     * 位置
     **/
    private String locationJson;
    /**
     * 时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
}



