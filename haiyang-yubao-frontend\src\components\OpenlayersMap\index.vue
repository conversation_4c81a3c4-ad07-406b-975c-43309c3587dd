<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2023-11-26 14:19:00
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-08 14:10:45
 * @FilePath: \hainan-jianzai-web\src\components\OpenlayersMap\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="openlayers-map" class="openlayers-map">
    <Tool v-if="showTool" ref="tool"/>
    <ElementStatistics v-if="showStatics"></ElementStatistics>
    <slot></slot>
    <div id="mouse-position" class="brp"></div>
  </div>
</template>

<script lang="ts" setup>
import 'ol/ol.css'
import { init } from './openlayersInit.js'
import { Tool } from './index'
import { ElementStatistics } from 'src/components/ElementStatistics/index'
import { ref, provide, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const props = defineProps({
  showTool: {
    type: Boolean,
    default: true
  },
  currentProduct: {
    type: Object,
    default: () => {}
  }
})
const showStatics = ref(false)
const showTool = ref(props.showTool)
const tool = ref()
let currentProduct = ref(props.currentProduct)
watch(
  () => props.currentProduct,
  newVal => {
    currentProduct.value = newVal
  },
  { immediate: true }
)
watch(
  () => props.showTool,
  newVal => {
    showTool.value = newVal
  },
  { immediate: true }
)
const emits = defineEmits(['ready'])
let map: any = null
const getMap = (callback: any) => {
  function sendMap() {
    if (map !== null) {
      callback(map)
    } else {
      setTimeout(sendMap, 50)
    }
  }
  sendMap()
}
const getMapV2 = () => {
  return map
}
function changeTime(time: any) {
  if (tool.value) {
    tool.value.updateLabel(time)
  }
}
provide('getMap', getMap)
provide('getMapV2', getMapV2)
onMounted(() => {
  map = init()
  emits('ready', map)
  if (route.name === 'analysis') {
    showStatics.value = true
  }
})
defineExpose({
  map,
  currentProduct,
  changeTime,
  tool
})
</script>

<style lang="scss" scoped>
.openlayers-map {
  width: 100%;
  height: 100%;
}
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
#mouse-div{
  position: relative;
  margin: 0 auto;
  top: 10px;
  width: 200px;
  height: 40px;
  line-height: 40px;
  background: #060505ba;
  text-align: center;
  color: #fff;
  border-radius: 5px;
}
.brp {
  display: block;
  position: absolute;
  background: #ffffffb3;
  bottom: 8px;
  right: 8px;
  color: #000000bf;
  padding: 6px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 20px;
  z-index: 5;
}
.spinner:after {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  border-radius: 50%;
  border: 5px solid rgba(180, 180, 180, 0.6);
  border-top-color: rgba(0, 0, 0, 0.6);
  animation: spinner 0.6s linear infinite;
}
</style>
