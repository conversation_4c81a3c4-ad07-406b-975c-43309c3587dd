import type { Point } from 'geojson'

export type Units =
  | 'meters'
  | 'metres'
  | 'millimeters'
  | 'millimetres'
  | 'centimeters'
  | 'centimetres'
  | 'kilometers'
  | 'kilometres'
  | 'miles'
  | 'nauticalmiles'
  | 'inches'
  | 'yards'
  | 'feet'
  | 'radians'
  | 'degrees'

export interface IOptions {
  extent: number[]
  points: number[][]
  width: number
  unit: Units
}

export type SetValuesCallback = (point: Point) => number
