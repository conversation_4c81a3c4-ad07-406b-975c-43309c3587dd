package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationMinuteSqODao;
import cn.piesat.data.making.server.entity.OceanStationMinuteSqO;
import cn.piesat.data.making.server.service.OceanStationMinuteSqOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站分钟数据-每分钟实时资料-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationMinuteSqOServiceImpl extends ServiceImpl<OceanStationMinuteSqODao, OceanStationMinuteSqO>
        implements OceanStationMinuteSqOService {

    @Resource
    private OceanStationMinuteSqODao oceanStationMinuteSqODao;

    @Override
    public List<OceanStationMinuteSqO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationMinuteSqODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationMinuteSqODao.getMaxCreateTime();
    }
}





