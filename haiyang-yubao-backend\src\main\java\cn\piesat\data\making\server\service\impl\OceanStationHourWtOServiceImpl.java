package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourWtODao;
import cn.piesat.data.making.server.entity.OceanStationHourWtO;
import cn.piesat.data.making.server.service.OceanStationHourWtOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-表层海水温度-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourWtOServiceImpl extends ServiceImpl<OceanStationHourWtODao, OceanStationHourWtO>
        implements OceanStationHourWtOService {

    @Resource
    private OceanStationHourWtODao oceanStationHourWtODao;

    @Override
    public List<OceanStationHourWtO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourWtODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourWtODao.getMaxCreateTime();
    }
}





