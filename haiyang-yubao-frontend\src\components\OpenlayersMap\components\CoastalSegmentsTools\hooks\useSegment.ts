import { ref, Ref } from 'vue'
import { ISegmentOptions, Tool } from '../types'
import { ISegmentColorItem } from './useColor'

export function useSegment(opt: { colors: ISegmentColorItem[] }) {
  const segmentOptions: Ref<ISegmentOptions> = ref({
    colorItem: opt.colors[0],
    currentTool: Tool.NONE
  })

  return {
    segmentOptions,
    changeTool: (tool: Tool) => changeTool(segmentOptions, tool),
    isToolSelected: (tool: Tool) => isToolSelected(segmentOptions, tool)
  }
}

/**
 * 改变工具
 * @param segmentOptions
 * @param tool
 */
function changeTool(segmentOptions: Ref<ISegmentOptions>, tool: Tool) {
  if (segmentOptions.value.currentTool === tool) {
    segmentOptions.value.currentTool = Tool.NONE
  } else {
    segmentOptions.value.currentTool = tool
  }
}

function isToolSelected(segmentOptions: Ref<ISegmentOptions>, tool: Tool) {
  return segmentOptions.value.currentTool === tool
}
