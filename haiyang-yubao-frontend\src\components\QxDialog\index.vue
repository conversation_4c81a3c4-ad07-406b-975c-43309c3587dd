<template>
  <div v-if="visible" class="qx-dialog__wrapper">
    <div
      class="qx-dialog"
      :style="{ width: props.width, height: props.height }"
    >
      <div
        class="qx-dialog-header d-flex flex-justify-between flex-align-center"
      >
        <h3>{{ props.title }}</h3>
        <Close class="icon-close" @click="onClose" />
      </div>
      <slot name="content"></slot>
      <slot name="suffix"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Close } from '@vicons/ionicons5'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '50%'
  },
  height: {
    type: String,
    default: 'auto'
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

const visible = computed({
  get: () => props.visible,
  set: () => emit('update:visible')
})

function onClose() {
  emit('update:visible', false)
}
</script>

<style lang="scss">
.qx-dialog__wrapper {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  .qx-dialog {
    background: #ffffff;

    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25);
    border-radius: 8px 8px 8px 8px;
  }
  .qx-dialog-header {
    height: 60px;
    padding-top: 0;
    padding-bottom: 0;
    border-bottom: none;
    box-sizing: border-box;
    padding: 0 21px 0 34px;
    // background: linear-gradient(180deg, #f1f9ff 0%, #ffffff 100%);
    background: url(src/assets/images/common/dialog-header-bg1.png) no-repeat;
    background-size: 100% 100%;
    border-radius: 8px 8px 0px 0px;
    // border-bottom: 1px solid rgba(0, 0, 0, .1);

    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #104cc0;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-top: -12px;
      position: relative;
      &::before {
        content: '';
        display: inline-block;
        width: 15.4px;
        height: 10.4px;
        background: url(src/assets/images/common/dialog-header-bg-icon.png)
          no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: -23px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .icon-close {
      cursor: pointer;
      width: 18px;
      height: 18px;
      margin-bottom: 15px;
    }
  }
  .form-container {
    box-sizing: border-box;
    padding: 14px 27px 0;
  }
  .btns {
    box-sizing: border-box;
    padding: 10px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
</style>
