package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmIsolineDataDTO;
import cn.piesat.data.making.server.entity.FmIsolineData;
import cn.piesat.data.making.server.vo.FmIsolineDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:31
 */
@Mapper(componentModel = "spring")
public interface FmIsolineDataMapper {

    FmIsolineDataMapper INSTANCE = Mappers.getMapper(FmIsolineDataMapper.class);

    /**
     * entity-->vo
     */
    FmIsolineDataVO entityToVo(FmIsolineData entity);

    /**
     * dto-->entity
     */
    FmIsolineData dtoToEntity(FmIsolineDataDTO dto);

    /**
     * entityList-->voList
     */
    List<FmIsolineDataVO> entityListToVoList(List<FmIsolineData> list);

    /**
     * dtoList-->entityList
     */
    List<FmIsolineData> dtoListToEntityList(List<FmIsolineDataDTO> dtoList);
}
