<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-24 17:00:21
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-17 15:07:33
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/index.vue
 * @Description: 专项预报
-->
<template>
  <div class="special-forecast">
    <div class="column-header">
      <h3>专项预报</h3>
    </div>
    <div class="search-filter d-flex flex-justify-between">
      <n-form
        ref="formRef"
        :model="form"
        label-placement="left"
        label-width="auto"
        require-mark-placement="left"
        inline
      >
        <n-form-item label="产品名称">
          <n-input
            v-model:value="form.productName"
            placeholder="请输入"
            clearable
          />
        </n-form-item>
        <n-form-item label="发布时间">
          <n-date-picker
            v-model:formatted-value="form.dataTime"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            clearable
          />
        </n-form-item>
      </n-form>
      <div class="btn-group d-flex">
        <qx-button class="primary" @click="onSearch">查询</qx-button>
        <qx-button class="warning" @click="onCreate"> 新建 </qx-button>
      </div>
    </div>
    <div class="table-container">
      <n-data-table
        :bordered="false"
        :single-line="false"
        :columns="columns"
        :data="tableData"
      />
      <n-pagination
        v-model:page="pagination.page"
        class="qx-pagination flex-justify-end"
        :page-count="pagination.pageCount"
        show-size-picker
        :page-sizes="[10, 20, 30, 40]"
        @update:page="onChangePage"
        @update:page-size="onChangePageSize"
      />
    </div>
  </div>
  <create-dialog
    v-if="createVisible"
    :title="dialogTitle"
    v-model:visible="createVisible"
    :info="rowData"
    @update:visible="getTableData"
  />
  <preview-dialog v-model:visible="previewVisible" :file-path="filePath" />
</template>

<script setup lang="ts">
import { ref, h, reactive, onMounted } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { CreateDialog, PreviewDialog } from './components'
import PublicService from 'src/requests/publicService'
import { useMessage } from 'naive-ui'

const message = useMessage()
let createVisible = ref(false)
const dialogTitle = ref('新建专项预报')
const formRef = ref(null)
const form = reactive({
  productName: '',
  dataTime: ''
})

const tableData = ref<any[]>([])
const columns = ref<any>([
  {
    title: '序号',
    render(row: any, index: number) {
      return index + 1
    }
  },
  {
    title: '产品名称',
    key: 'productName'
  },
  {
    title: '发布单位',
    key: 'sendUnitName'
  },
  {
    title: '发布时间',
    key: 'createTime'
  },
  {
    title: '状态',
    render(row: any) {
      let mapper = {
        1: h(
          'div',
          { class: 'status-wrap mading' },
          {
            default: () => [h('span', '正在生成')]
          }
        ),
        2: h(
          'div',
          { class: 'status-wrap finished' },
          {
            default: () => [h('span', '已生成')]
          }
        ),
        0: h(
          'div',
          { class: 'status-wrap not-start' },
          {
            default: () => [h('span', '未开始')]
          }
        )
      }
      return mapper[row.status as keyof typeof mapper]
    }
  },
  {
    title: '操作',
    render(row: any) {
      return [
        h(
          'span',
          {
            size: 'small',
            class: ['operate-btn', row.status != 0 ? 'disabled' : ''],
            style: { cursor: 'pointer' },
            onClick: row.status == 0 ? () => onChangeStatus(row) : null
          },
          { default: () => '开始生成' }
        ),
        h(
          'span',
          {
            size: 'small',
            class: ['operate-btn', row.status != 2 ? 'disabled' : ''],
            style: { cursor: 'pointer' },
            onClick: () => (row.status == 2 ? onPreview(row) : null)
          },
          { default: () => '预览' }
        ),
        h(
          'span',
          {
            size: 'small',
            class: ['operate-btn', row.status == 1 ? 'disabled' : ''],
            style: { cursor: 'pointer' },
            onClick: () => (row.status == 1 ? null : onEdit(row))
          },
          { default: () => '编辑' }
        ),
        h(
          'span',
          {
            size: 'small',
            class: ['operate-btn del', row.status != 2 ? 'disabled' : ''],
            style: { cursor: 'pointer' },
            onClick: () => (row.status == 2 ? onDownLoad(row) : null)
          },
          { default: () => '下载' }
        )
      ]
    }
  }
])

const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 0
})

type Params = {
  pageSize: number
  pageNum: number
  productName: string
  startTime?: string
  endTime?: string
}
function getTableData() {
  const params: Params = {
    pageSize: pagination.pageSize,
    pageNum: pagination.page,
    productName: form.productName
  }

  if (form.dataTime?.length) {
    params.startTime = form.dataTime[0]
    params.endTime = form.dataTime[1]
  }
  PublicService.getForecastPage(params)
    .then((res: any) => {
      if (res) {
        const { pageResult, pages } = res
        tableData.value = pageResult
        pagination.pageCount = pages
      }
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg || '获取数据失败')
    })
}

function onChangePage(page: number) {
  pagination.page = page
  getTableData()
}
function onChangePageSize(pageSize: number) {
  pagination.pageSize = pageSize
  pagination.page = 1
  getTableData()
}

let previewVisible = ref(false)
let previewTitle = ref('')
let filePath = ref('')
const rowData = ref<any>(null)

function onChangeStatus(row: any) {
  const loading = message.loading('正在生成', {
    duration: 0
  })
  PublicService.updateStatusById(row.id, 1)
    .then(() => {
      message.success('操作成功')
      getTableData()
    })
    .finally(() => {
      loading.destroy()
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg?.message || '操作失败')
    })
}

function onPreview(row: any) {
  previewTitle.value = row.name
  previewVisible.value = true
  filePath.value =
    config.kkFileUrl + btoa(config.fileService + row.reportPath)
}

function onEdit(row: any) {
  console.log(row)
  rowData.value = row
  createVisible.value = true
  dialogTitle.value = '编辑专项预报'
}

function onDownLoad(row: any) {
  const link = document.createElement('a')
  link.href = `${config.fileService}${row.reportPath}`
  let arr = row.reportPath.split('/')
  let fileName = arr[arr.length - 1].split('.')[0]
  link.download = fileName //下载的文件名称
  link.click()
}

function onSearch() {
  pagination.page = 1
  getTableData()
}
function onCreate() {
  rowData.value = null
  createVisible.value = true
  dialogTitle.value = '新建专项预报'
}

onMounted(() => {
  getTableData()
})
</script>

<style lang="scss">
.special-forecast {
  box-sizing: border-box;
  padding: 20px;
  background: #fff;
  .qx-pagination {
    margin-top: 20px;
  }
  .column-header {
    box-sizing: border-box;
    padding: 20px;
    background: url(src/assets/images/common/content-header.png) no-repeat;
    background-size: 100% 100%;
    border-bottom: 1px solid #dfdfdf;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      position: relative;
      padding-left: 20px;
      &::before {
        content: '';
        position: absolute;
        width: 4px;
        height: 18px;
        background: #0091ff;
        left: 0px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .operate-btn {
    border-right: none;
    &.disabled {
      color: #999;
      cursor: not-allowed;
    }
  }
  .btn-group {
    flex-shrink: 0;
  }
  .search-filter {
    box-sizing: border-box;
    padding: 20px 20px 0;
  }
  .table-container {
    box-sizing: border-box;
    padding: 20px;
    padding-top: 0;
  }
  .status-wrap {
    box-sizing: border-box;
    padding: 3px 7px;
    width: 74px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
    }
    &.mading {
      background: rgba(253, 149, 14, 0.32);
      span {
        color: #fd950e;
      }
    }
    &.finished {
      background: rgba(231, 250, 228, 1);
      span {
        color: #219417;
      }
    }
    &.not-start {
      background: rgba(223, 226, 223, 1);
      span {
        color: #999;
      }
    }
  }
  .n-data-table-base-table-body {
    max-height: 620px !important;
  }
  .n-data-table-base-table-header .n-data-table-table {
    table-layout: fixed !important;
  }
}
</style>
