<template>
  <div class="ht-tabs">
    <div
      class="ht-tab-item d-flex flex-align-center flex-justify-center"
      v-for="item in tabList"
      :key="item.id"
      @click="changeTab(item)"
      :class="tabIndex === item.id ? 'active' : ''"
    >
      <i v-if="item.iconColor !== undefined" class="status" :style="{'backgroundColor':item.iconColor}"></i>
      <span>{{ item.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
const tabIndex = ref<number>(0)
const props = defineProps({
  tabList: {
    type: Array<any>,
    default() {
      return []
    }
  },
  active: {
    type: Number,
    default() {
      return 0
    }
  }
})

watch(
  () => props.active,
  val => {
    tabIndex.value = val
  },
  { immediate: true }
)
const emit = defineEmits(['change'])
function changeTab(item: any) {
  emit('change', item.id)
}

type mapperType = {
  [key:number]:string
}

</script>

<style scoped lang="scss">
.ht-tabs {
  display: flex;
  align-items: center;
  .ht-tab-item {
    flex: 1;
    cursor: pointer;
    box-sizing: border-box;
    padding: 12px 0;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    text-align: center;
    border-bottom: 3px solid transparent;
    &.active {
      border-color: #3c83ed;
      font-weight: 600;
    }
  }
  .status{
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }
}
</style>