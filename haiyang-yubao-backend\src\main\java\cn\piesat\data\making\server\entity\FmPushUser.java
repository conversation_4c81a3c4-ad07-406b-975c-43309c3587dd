package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

@TableName("fm_push_user_b")
public class FmPushUser implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("rec_dept_id")
    private Long recDeptId;

    @TableField("rec_dept_name")
    private String recDeptName;

    @TableField("rec_id")
    private Long recId;

    @TableField("rec_name")
    private String recName;

    @TableField("rec_role_id")
    private Long recRoleId;

    @TableField("rec_dept_name")
    private String recRoleName;

    @TableField("email")
    private String email;

    @TableField("mobile")
    private String mobile;

    @TableField("fax_no")
    private String faxNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecDeptId() {
        return recDeptId;
    }

    public void setRecDeptId(Long recDeptId) {
        this.recDeptId = recDeptId;
    }

    public String getRecDeptName() {
        return recDeptName;
    }

    public void setRecDeptName(String recDeptName) {
        this.recDeptName = recDeptName;
    }

    public Long getRecId() {
        return recId;
    }

    public void setRecId(Long recId) {
        this.recId = recId;
    }

    public String getRecName() {
        return recName;
    }

    public void setRecName(String recName) {
        this.recName = recName;
    }

    public Long getRecRoleId() {
        return recRoleId;
    }

    public void setRecRoleId(Long recRoleId) {
        this.recRoleId = recRoleId;
    }

    public String getRecRoleName() {
        return recRoleName;
    }

    public void setRecRoleName(String recRoleName) {
        this.recRoleName = recRoleName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFaxNo() {
        return faxNo;
    }

    public void setFaxNo(String faxNo) {
        this.faxNo = faxNo;
    }
}
