/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-22 09:36:19
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-07 16:10:59
 * @FilePath: /hainan-jianzai-web/src/components/QxTableEdit/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref, nextTick, h, defineComponent } from 'vue'
import { NInput } from 'naive-ui'

const QxTableEdit = defineComponent({
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    onUpdateValue: [Function, Array],
    className: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: true
    },
    isFocus: {
      type: <PERSON>olean,
      default: false
    }
  },
  setup(props) {
    const isEdit = ref(false)
    const inputRef = ref(null)
    const inputValue = ref(props.value)
    function handleOnClick() {
      isEdit.value = true
      nextTick(() => {
        inputRef.value.focus()
      })
    }
    function handleChange() {
      if (typeof props.onUpdateValue === 'function') {
        props.onUpdateValue(inputValue.value)
      }
      isEdit.value = false
    }
    return () =>
      h(
        'div',
        {
          class: `qx-table-edit-wrapper ${props.className}`,
          style: 'min-height: 30px;line-height: 30px;',
          onClick: props.isEdit ? handleOnClick : null
        },
        isEdit.value || props.isFocus
          ? h(NInput, {
              ref: inputRef,
              style: { height: '90%' },
              value: inputValue.value,
              autosize: true,
              focus: props.isFocus ? true : false,
              onUpdateValue: v => {
                inputValue.value = v
              },
              onChange: handleChange,
              onBlur: handleChange
            })
          : props.value
      )
  }
})

export default QxTableEdit
