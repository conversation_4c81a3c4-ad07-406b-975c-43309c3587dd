package cn.piesat.data.making.server.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * 潮汐-小时信息
 *
 * <AUTHOR>
 */
@Data
public class TideHourInfo {

    @ExcelProperty("站点")
    private String name;

    @ColumnWidth(20)
    @ExcelProperty("时间")
    private Date time;

    @ExcelProperty("潮位")
    private Integer tide;

    @ExcelProperty("蓝色警戒值")
    private Integer blueWarnValue;

    @ExcelProperty("差值")
    private Integer diffValue;
}
