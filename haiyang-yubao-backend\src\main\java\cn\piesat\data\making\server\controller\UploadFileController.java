package cn.piesat.data.making.server.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dto.ImageUploadDTO;
import cn.piesat.data.making.server.dto.OnlyOfficeCallBackDTO;
import cn.piesat.data.making.server.event.OnlyOfficeFile;
import cn.piesat.webconfig.exception.BusinessException;
import cn.piesat.webconfig.response.IgnoreResponseAdvice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * 上传文件
 */
@RestController
@RequestMapping("/upload")
public class UploadFileController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 上传图片文件
     * @param file 图片文件
     * @return 文件地址
     */
    @PostMapping("/image")
    public String image(MultipartFile file){
        Optional.ofNullable(file).orElseThrow(()->new BusinessException("上传文件不能为空"));
        Date date = new Date();
        int year = DateUtil.year(date);
        String s = DateUtil.format(date, "yyyyMMdd");
        String originalFilename = file.getOriginalFilename();

        String filePath = String.format(CommonConstant.UPLOAD_IMAGE_PATH, baseOutputPath, year, s, originalFilename);

        try {
            FileUtil.writeFromStream(file.getInputStream(),filePath);
        } catch (IOException e) {
            logger.error("文件上传异常",e);
            throw new BusinessException("文件上传异常");
        }
        return filePath;
    }

    /**
     * 上传图片base64
     * @param imageUploadDTO 图片文件
     * @return 文件地址
     */
    @PostMapping("/imageBase64")
    public String imageBase64(@RequestBody @Validated ImageUploadDTO imageUploadDTO){

        String base64Image = imageUploadDTO.getBase64Image();

        Date date = new Date();
        int year = DateUtil.year(date);
        String s = DateUtil.format(date, "yyyyMMdd");
        String fileName = RandomUtil.randomString(32);
        String filePath = String.format(CommonConstant.UPLOAD_IMAGE_PATH, baseOutputPath, year, s, fileName);
        // 将Base64编码的字符串转换为字节数组
        // 从base64字符串中提取实际的base64编码内容
        base64Image = StrUtil.subAfter(base64Image, "base64,",false);
        byte[] imageBytes = Base64.getDecoder().decode(base64Image);
        FileUtil.writeBytes(imageBytes,filePath);
        return filePath;
    }

    /**
     * 保存onlyOffice文件回执
     * @param id
     * @param onlyOfficeCallBackDTO
     * @return
     */
    @PostMapping("/onlyOfficeCallback/{type}/{id}")
    @IgnoreResponseAdvice
    public Map<String,Object> onlyOfficeCallback(@PathVariable String type,@PathVariable Long id, @RequestBody OnlyOfficeCallBackDTO onlyOfficeCallBackDTO){
        if(Arrays.asList(2, 3, 6, 7).contains(onlyOfficeCallBackDTO.getStatus())){
            logger.debug("收到onlyOffice保存文件回执:{}",JsonUtil.object2Json(onlyOfficeCallBackDTO));
            //发送事件
            if(StringUtils.hasText(onlyOfficeCallBackDTO.getUrl())) {
                OnlyOfficeFile onlyOfficeFile = new OnlyOfficeFile(type, id, onlyOfficeCallBackDTO.getUrl());
                applicationEventPublisher.publishEvent(onlyOfficeFile);
            }
        }
        return new HashMap<String,Object>(){{put("error",0);}};
    }
}
