package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.piesat.data.making.server.common.CodeGenerator;
import cn.piesat.data.making.server.dao.AreaDao;
import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.entity.Area;
import cn.piesat.data.making.server.mapper.AreaMapper;
import cn.piesat.data.making.server.model.AreaStationInfo;
import cn.piesat.data.making.server.service.AreaService;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.vo.AreaVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;

/**
 * 区域表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class AreaServiceImpl extends ServiceImpl<AreaDao, Area> implements AreaService {

    @Resource
    private AreaDao areaDao;
    @Value("${piesat.make.areaJsonFilePath}")
    private String areaJsonFilePath;

    @Override
    public List<AreaVO> getList(AreaDTO dto) {
        List<Area> list = this.list(createQueryWrapper(dto));
        return AreaMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public Long save(AreaDTO dto) {
        dto.setCode(CodeGenerator.getPinYinHeadChar(dto.getName()));
        dto.setLocationGeo(dto.getLocationJson());
        Area entity = AreaMapper.INSTANCE.dtoToEntity(dto);
        if (dto.getId() == null) {
            this.save(entity);
        } else {
            this.updateById(entity);
        }
        return entity.getId();
    }

    @Override
    public void deleteById(Long id) {
        areaDao.deleteById(id);
    }

    /**
     * 根据名称更新位置
     */
    private void updateLocationByName(Area entity) {
        areaDao.updateLocationByName(entity.getCode(), entity.getName(), entity.getLocationGeo(), entity.getLocationJson());
    }

    /**
     * 初始化区域数据
     */
    private void initAreaData() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            URL resource = classLoader.getResource("initData/area.json");
            List<AreaStationInfo> data = mapper.readValue(new File(resource.getFile()), mapper.getTypeFactory().constructCollectionType(List.class,
                    AreaStationInfo.class));
            data.forEach(o -> {
                Area obj = new Area();
                obj.setName(o.getName());
                obj.setCode(CodeGenerator.getPinYinHeadChar(o.getName()));
                try {
                    //locationJson {"type":"Feature","geometry":{"type":"Point","coordinates":[110.4,20.2]},"properties":{},
                    // "id":"fid-4780e600_1920422c0bb_-8000"}
                    String locationJson = GeoToolsUtil.arrToGeojson(o.getRing());
                    obj.setLocationJson(locationJson);
                    //locationGeo {"type":"Point","coordinates":[110.4,20.2]}
                    JSONObject jsonObject = new JSONObject(locationJson);
                    obj.setLocationGeo(jsonObject.get("geometry").toString());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                this.updateLocationByName(obj);
            });
        } catch (Exception e) {
            log.error("区域位置信息初始化失败！");
        }
    }

    private LambdaQueryWrapper<Area> createQueryWrapper(AreaDTO dto) {
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getAreaTypeCode())) {
            queryWrapper.eq(Area::getAreaTypeCode, dto.getAreaTypeCode());
        }
        if (StringUtils.isNotBlank(dto.getName())) {
            queryWrapper.like(Area::getName, dto.getName());
        }
        return queryWrapper.orderByAsc(Area::getCreateTime);
    }


    @Override
    public void generateAreaJson(List<Long> areaIds) {
        String geoJsonByIds = this.areaDao.selectGeoJsonByIds(areaIds);
        FileUtil.writeUtf8String(geoJsonByIds,areaJsonFilePath);
    }
}





