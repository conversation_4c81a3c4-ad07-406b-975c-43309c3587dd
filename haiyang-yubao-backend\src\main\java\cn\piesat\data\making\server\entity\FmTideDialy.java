package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("fm_tide_daily")
public class FmTideDialy implements Serializable {

    private Long id;

    private Date tideTime;

    private Integer height;

    private Integer type;

    private String stationName;

    private Long stationId;

    private Date createTime;

    private Date updateTime;

    private Integer warnHeight;

    private Integer level;

    private String datum;

    private Integer datumBlueDif;

    private Integer datumBlueWarn;
}
