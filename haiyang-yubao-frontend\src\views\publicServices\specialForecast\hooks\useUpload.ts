import type { UploadFileInfo } from 'naive-ui'
import { createDiscreteApi } from 'naive-ui'
import * as shapefile from 'shapefile'
import GeoJSON from 'ol/format/GeoJSON.js'
import { Feature } from 'ol'
import eventBus from 'src/utils/eventBus'

const { message } = createDiscreteApi(['message'])

interface ShpObj {
  id: number
  name: string
  isPoint: boolean
  graphicJson: string
}

export function useUpload() {
  const shpObj: ShpObj[] = []

  //文件上传前回调
  const beforeUpload = ({
    file
  }: {
    file: UploadFileInfo
    fileList: UploadFileInfo[]
  }): Promise<boolean | void> | boolean | void => {
    if (file.name.indexOf('shp') === -1) {
      message.error('请上传shp文件')
      return false
    }
    return true
  }
  // 上传文件
  const uploadFile = ({
    file
  }: {
    file: UploadFileInfo
  }): Promise<boolean | void> | boolean | void => {
    if (!['error', 'finshed'].includes(file.status)) {
      return false
    }
    const uploadFile = file.file
    const reader = new FileReader()
    reader.readAsArrayBuffer(uploadFile as File)
    reader.onload = e => {
      openShapefile(e.target?.result)
    }
  }

  // 读取shp文件
  async function openShapefile(
    content: string | ArrayBuffer | null | undefined
  ) {
    // GeoJSON 对象
    const featureCollection = await shapefile.read(content)
    const geoJson = new GeoJSON()
    const features = geoJson.readFeatures(featureCollection)
    console.log(features, 'featuresfeatures')
    features.forEach((feature, index: number) => {
      const geoType = feature.getGeometry()?.getType()

      if (['MultiPolygon', 'MultiPoint'].includes(geoType as string)) {
        //@ts-ignore
        const coordinates = feature.getGeometry()?.getCoordinates()
        coordinates?.forEach((item: number[][], index: number) => {
          const featureCollection = {
            type: 'Feature',
            properties: {},
            geometry: {
              type: geoType?.replace('Multi', ''),
              coordinates: item
            }
          }
          const isPoint = geoType === 'MultiPoint' ? true : false
          const obj = formatObj(
            feature,
            isPoint,
            index,
            JSON.stringify(featureCollection)
          )
          shpObj.push(obj)
        })
      } else if (geoType === 'Point' || geoType === 'Polygon') {
        const obj = formatObj(
          feature,
          geoType === 'Point' ? true : false,
          index,
          geoJson.writeFeature(feature)
        )
        shpObj.push(obj)
      }
    })

    console.log(shpObj, 'shpObj')
    eventBus.emit('uploadShp', shpObj)
  }
  /**
   * 格式化对象
   * @param feature 要素
   * @param isPoint 是否是点
   * @param index 要素下标
   * @param graphicJson 要素的GeoJSON格式
   * @returns ShpObj
   */
  function formatObj(
    feature: Feature,
    isPoint: boolean,
    index: number,
    graphicJson: string
  ): ShpObj {
    const obj: ShpObj = {
      id: new Date().getTime(),
      name: feature.getProperties().name || `shp文件上传区域${index + 1}`,
      isPoint,
      graphicJson
    }
    return obj
  }

  return {
    uploadFile,
    beforeUpload,
    shpObj
  }
}
