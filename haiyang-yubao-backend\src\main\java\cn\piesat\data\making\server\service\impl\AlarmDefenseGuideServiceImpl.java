package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dto.AlarmDefenseGuideDTO;
import cn.piesat.data.making.server.entity.AlarmGuideLevel;
import cn.piesat.data.making.server.enums.StatusEnum;
import cn.piesat.data.making.server.mapper.AlarmDefenseGuideMapper;
import cn.piesat.data.making.server.mapper.AlarmGuideLevelMapper;
import cn.piesat.data.making.server.service.AlarmGuideLevelService;
import cn.piesat.data.making.server.vo.AlarmDefenseGuideVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.piesat.data.making.server.dao.AlarmDefenseGuideDao;
import cn.piesat.data.making.server.entity.AlarmDefenseGuide;
import cn.piesat.data.making.server.service.AlarmDefenseGuideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 警报防御指南(AlarmDefenseGuide)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:14
 */
@Service("alarmDefenseGuideService")
public class AlarmDefenseGuideServiceImpl extends ServiceImpl<AlarmDefenseGuideDao, AlarmDefenseGuide> implements AlarmDefenseGuideService {

    private static Map<Integer,String> templateType= new HashMap<Integer,String>()
    {{put(1,"海浪警报制作");put(2,"风暴潮警报制作");}};

    @Autowired
    private AlarmGuideLevelService alarmGuideLevelService;

    @Override
    public PageResult pageList(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<AlarmDefenseGuide> wrapper = new LambdaQueryWrapper<>();
        Page<AlarmDefenseGuide> page = this.page(new Page<>(pageNum, pageSize), wrapper);
        return new PageResult<>(page.getRecords(), pageNum, pageSize, page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlarmDefenseGuideDTO saveInfo(AlarmDefenseGuideDTO guideDTO) {
        AlarmDefenseGuide guide = AlarmDefenseGuideMapper.INSTANCE.toEntity(guideDTO);
        this.save(guide);
        List<AlarmDefenseGuideDTO.GuideLevelDTO> guideLevelList = guideDTO.getGuideLevelList();
        List<AlarmGuideLevel> guideLevels = AlarmGuideLevelMapper.INSTANCE.toEntity(guideLevelList,guide);
        alarmGuideLevelService.saveBatch(guideLevels);

        return AlarmDefenseGuideMapper.INSTANCE.toDTO(guide,guideLevels);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfo(AlarmDefenseGuideDTO guideDTO) {
        AlarmDefenseGuide guide = AlarmDefenseGuideMapper.INSTANCE.toEntity(guideDTO);
        this.updateById(guide);

        List<AlarmDefenseGuideDTO.GuideLevelDTO> guideLevelList = guideDTO.getGuideLevelList();
        List<AlarmGuideLevel> guideLevels = AlarmGuideLevelMapper.INSTANCE.toEntity(guideLevelList,guide);

        alarmGuideLevelService.remove(new LambdaQueryWrapper<AlarmGuideLevel>().eq(AlarmGuideLevel::getGuideId,guide.getId()));
        alarmGuideLevelService.saveBatch(guideLevels);
    }

    @Override
    public AlarmDefenseGuideDTO info(Long id) {
        AlarmDefenseGuide guide = this.getById(id);
        List<AlarmGuideLevel> guideLevelList = alarmGuideLevelService.list(new LambdaQueryWrapper<AlarmGuideLevel>().eq(AlarmGuideLevel::getGuideId, guide.getId()));

        return AlarmDefenseGuideMapper.INSTANCE.toDTO(guide,guideLevelList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInfo(Long id) {
        AlarmDefenseGuide one = this.getById(id);
        if(StatusEnum.OPEN.getStatus().equals(one.getStatus())) throw new BusinessException("开启状态数据无法删除");
        this.removeById(id);
        this.alarmGuideLevelService.remove(new LambdaQueryWrapper<AlarmGuideLevel>().eq(AlarmGuideLevel::getGuideId,id));
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        LambdaUpdateWrapper<AlarmDefenseGuide> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(AlarmDefenseGuide::getStatus,status);
        wrapper.eq(AlarmDefenseGuide::getId,id);
        this.update(wrapper);
    }

    @Override
    public ArrayList<AlarmDefenseGuideVO> list(Integer status) {
        LambdaUpdateWrapper<AlarmDefenseGuide> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Objects.nonNull(status),AlarmDefenseGuide::getStatus,status);
        List<AlarmDefenseGuide> list = this.list(wrapper);
        Map<Integer, List<AlarmDefenseGuide>> collect = list.stream().collect(Collectors.groupingBy(AlarmDefenseGuide::getAlarmType));
        ArrayList<AlarmDefenseGuideVO> templateDTOS = new ArrayList<>();
        templateType.forEach((key, value) -> templateDTOS.add(new AlarmDefenseGuideVO(value, key, collect.get(key))));
        return templateDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setOpen(Long id) {
        AlarmDefenseGuide guide = this.getById(id);
        this.update(new LambdaUpdateWrapper<AlarmDefenseGuide>()
                .eq(AlarmDefenseGuide::getAlarmType,guide.getAlarmType())
                .eq(AlarmDefenseGuide::getStatus, StatusEnum.OPEN.getStatus())
                .set(AlarmDefenseGuide::getStatus,StatusEnum.CLOSE.getStatus()));
        guide.setStatus(StatusEnum.OPEN.getStatus());
        this.updateById(guide);
    }

    @Override
    public AlarmDefenseGuideDTO selectByType(Integer type) {
        LambdaQueryWrapper<AlarmDefenseGuide> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AlarmDefenseGuide::getAlarmType,type);
        wrapper.eq(AlarmDefenseGuide::getStatus,StatusEnum.OPEN.getStatus());
        AlarmDefenseGuide guide = this.getOne(wrapper, false);

        if(guide==null) return new AlarmDefenseGuideDTO();

        List<AlarmGuideLevel> guideLevelList = alarmGuideLevelService.list(new LambdaQueryWrapper<AlarmGuideLevel>().eq(AlarmGuideLevel::getGuideId, guide.getId()));

        return AlarmDefenseGuideMapper.INSTANCE.toDTO(guide,guideLevelList);
    }
}

