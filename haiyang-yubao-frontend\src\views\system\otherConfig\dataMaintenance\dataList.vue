<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-11 11:07:22
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-22 15:01:29
 * @FilePath: \hainan-jianzai-web\src\views\system\otherConfig\dataMaintenance\dataList.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="data-maintenance-list">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>数据列表</h3>
      </div>
      <Tab :active="tabIndex" :tab-list="tabList" @change="changeTab" />
    </div>
    <qx-tree
      :search="true"
      :tree-data="data"
      :default-props="defaultProps"
      :edit="true"
      :default-expand-all="true"
      :default-selected-keys="selectedKey"
      :icons="['icon-add', 'icon-remove', '']"
      @selected="handleSelected"
      @search="onSearch"
      @click="onClick"
      @edit="onEdit"
      @delete="onDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { QxTree } from 'src/components/QxTreeEdit'
import type { TreeOption } from 'naive-ui'
import eventBus from 'src/utils/eventBus'
import { Tab } from 'src/components/Tab'
import { QxDialog } from 'src/components/QxDialog'
import { useMessage } from 'naive-ui'

const message = useMessage()
// tab 开始
const tabIndex = ref(2)
const tabList = ref<any[]>([
  {
    label: '区域',
    id: 1
  },
  {
    label: '站点',
    id: 2
  }
])
function changeTab(val: number) {
  tabIndex.value = val
  if (val === 1) {
    defaultProps.children = 'areaList'
    defaultProps.key = 'id'
  } else {
    defaultProps.children = 'childList'
    defaultProps.key = 'id'
  }
  data.value = []
  eventBus.emit('changeTab', val)
}
// tab 结束

const props = defineProps({
  treeData: {
    type: Array<TreeOption>,
    default: () => []
  }
})

// tree 默认属性
const defaultProps = reactive({
  key: 'id',
  label: 'name',
  children: 'childList'
})

//tree数据
const data = ref<TreeOption[]>([])

// tree 选中
const selectedKey = ref<string[]>([])
// tree 查询值
const searchVal = ref('')
const selectNode = ref<any>(null)
// 获取tree 选中的节点
function handleSelected(key: string[], option: TreeOption[] | null) {
  selectedKey.value = key
  eventBus.emit('selectedTree', option)
  selectNode.value = option
  console.log('123-------------------------------')
  // emits('selectedTree', option)
}
function onClick(option: TreeOption) {
  eventBus.emit('clickTree', option)
}

function findNodeByKey(
  tree: TreeOption[],
  key: string
): TreeOption | undefined {
  for (const node of tree) {
    if (node.key === key) {
      return node
    }
    if (node.children) {
      const foundNode = findNodeByKey(node.children, key)
      if (foundNode) {
        return foundNode
      }
    }
  }
  return undefined
}

// 获取第一个叶子节点
function getFirstLeafNode(tree: any[]): TreeOption | undefined {
  for (const node of tree) {
    if (!node.areaList || node.areaList.length === 0) {
      return node
    }
    const foundNode = getFirstLeafNode(node.areaList)
    if (foundNode) {
      return foundNode
    }
  }
  return undefined
}

const emits = defineEmits(['search', 'selectedTree', 'edit', 'delete'])
function onSearch(val: string) {
  searchVal.value = val
  emits('search', val)
}
function onEdit(option: any) {
  emits('edit', option)
}
function onDelete(option: any) {
  emits('delete', option)
}
watch(
  () => props.treeData,
  val => {
    data.value = val
  },
  {
    immediate: true
  }
)
// 改变选中节点
function setSelectedKey(key: any) {
  selectedKey.value = [key]
}
defineExpose({
  selectNode,
  selectedKey,
  setSelectedKey
})
</script>

<style lang="scss">
.data-maintenance-list {
  height: 100%;
  background: #fff;
  width: 260px;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  z-index: 1;
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  .aside-header {
    .title-wrap {
      padding: 18px 0 18px 26px;
    }
  }
  .qx-tree-wrap {
    box-sizing: border-box;
    padding-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    height: calc(100% - 100px);
    display: flex;
    flex-direction: column;
  }
}
</style>
