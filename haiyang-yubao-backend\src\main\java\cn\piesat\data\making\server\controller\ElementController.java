package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.ElementDTO;
import cn.piesat.data.making.server.service.ElementService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.ElementVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 要素字典表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/element")
public class ElementController {

    @Resource
    private ElementService elementService;

    /**
     * 查询要素字典分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "要素字典管理", operateType = OperateType.SELECT)
    public PageResult<ElementVO> getPage(@RequestParam(required = false) Long id,
                                         @RequestParam(defaultValue = "1") Integer pageNum,
                                         @RequestParam(defaultValue = "10") Integer pageSize) {
        ElementDTO dto = new ElementDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return elementService.getPage(dto, pageParam);
    }

    /**
     * 查询要素字典列表
     *
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "要素字典管理", operateType = OperateType.SELECT)
    public List<ElementVO> getList() {
        return elementService.getList();
    }

    /**
     * 根据要素字典id查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "要素字典管理", operateType = OperateType.SELECT)
    public ElementVO getById(@PathVariable("id") Long id) {
        return elementService.getInfoById(id);
    }

    /**
     * 保存要素字典
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "要素字典管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {ElementDTO.Save.class}) @RequestBody ElementDTO dto) {
        elementService.save(dto);
    }

    /**
     * 根据要素字典id删除要素字典
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "要素字典管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        elementService.deleteById(id);
    }
}

