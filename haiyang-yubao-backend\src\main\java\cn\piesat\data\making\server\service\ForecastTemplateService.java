package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.ForecastTemplateDTO;
import cn.piesat.data.making.server.entity.AreaType;
import cn.piesat.data.making.server.entity.ForecastTemplate;
import cn.piesat.data.making.server.model.ForecastTemplateInfo;
import cn.piesat.data.making.server.vo.ForecastTemplateVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 预报模板表服务接口
 *
 * <AUTHOR>
 */
public interface ForecastTemplateService extends IService<ForecastTemplate> {

    /**
     * 查询列表
     */
    List<ForecastTemplateInfo> getTreeList(Boolean status);

    /**
     * 查询列表
     */
    List<ForecastTemplateVO> getList(Boolean status);

    /**
     * 查询详情
     */
    ForecastTemplateVO getInfoById(Long id);

    /**
     * 保存
     */
    Long save(ForecastTemplateDTO dto);

    /**
     * 查询预报类型
     */
    LinkedHashMap<String, AreaType> getForecastType();

    /**
     * 删除
     */
    void deleteById(Long id);
}




