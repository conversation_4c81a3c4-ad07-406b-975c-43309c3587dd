import Map from 'ol/Map'
import { nextTick, onMounted } from 'vue'
import Feature from 'ol/Feature'
import { Point, Polygon } from 'ol/geom'
import { Style, Icon } from 'ol/style.js'
import { ScaleLineEnhance } from './scaleLineEnhance'
import html2canvas from 'html2canvas'

/**
 * 比例尺 (增强版)
 * @param mapGetter
 */
export function useScaleLineEnhance(mapGetter: () => Map) {
  const scaleLine = new ScaleLineEnhance({ units: 'metric' })
  onMounted(async () => {
    await nextTick()
    const map = mapGetter()
    map.addControl(scaleLine)
  })
  return {
    scaleLine
  }
}

/**
 * 获取矩形左下角点
 * @param feature
 */
export function getScalePosition(feature: Feature<Polygon>): number[] {
  const geometry = feature.getGeometry()
  if (!geometry) {
    throw new Error('无法获取几何')
  }
  const extent = geometry.getExtent()
  return [extent[0] + 0.22, extent[1] + 0.1]
}

export function generateScaleLineFeature(
  borderFeature: Feature<Polygon>,
  scaleLine: ScaleLineEnhance
) {
  const [minx, miny] = getScalePosition(borderFeature)
  const pointFeature = new Feature({
    geometry: new Point([minx, miny])
  })

  html2canvas(scaleLine.getElement())
    .then((canvas: any) => {
      const imgData = canvas.toDataURL('image/png')
      pointFeature.setStyle(
        new Style({
          image: new Icon({
            width: 70,
            height: 20,
            offsetOrigin: 'bottom-left',
            src: imgData,
            crossOrigin: 'anonymous'
          })
        })
      )
    })
    .catch((err: unknown) => {
      console.warn(err)
    })

  pointFeature.setProperties({ name: 'screentshot', role: 'scaleLine' })
  return pointFeature
}
