package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown=true)
public class OnlyOfficeCallBackDTO {
    private String key;
    private int status;
    private String url;
    @JsonProperty("changesurl")
    private String changesUrl;
    private History history;
    private List<String> users;
    private List<Map<String, Object>> actions;
    @JsonProperty("lastsave")
    private String lastSave;
    @JsonProperty("forcesavetype")
    private int forceSaveType;
    private String token;
    private String filetype;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getChangesUrl() {
        return changesUrl;
    }

    public void setChangesUrl(String changesUrl) {
        this.changesUrl = changesUrl;
    }

    public History getHistory() {
        return history;
    }

    public void setHistory(History history) {
        this.history = history;
    }

    public List<String> getUsers() {
        return users;
    }

    public void setUsers(List<String> users) {
        this.users = users;
    }

    public List<Map<String, Object>> getActions() {
        return actions;
    }

    public void setActions(List<Map<String, Object>> actions) {
        this.actions = actions;
    }

    public String getLastSave() {
        return lastSave;
    }

    public void setLastSave(String lastSave) {
        this.lastSave = lastSave;
    }

    public int getForceSaveType() {
        return forceSaveType;
    }

    public void setForceSaveType(int forceSaveType) {
        this.forceSaveType = forceSaveType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getFiletype() {
        return filetype;
    }

    public void setFiletype(String filetype) {
        this.filetype = filetype;
    }

    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class History {
        private String serverVersion;
        private List<Change> changes;

        public String getServerVersion() {
            return serverVersion;
        }

        public void setServerVersion(String serverVersion) {
            this.serverVersion = serverVersion;
        }

        public List<Change> getChanges() {
            return changes;
        }

        public void setChanges(List<Change> changes) {
            this.changes = changes;
        }

        @JsonIgnoreProperties(ignoreUnknown=true)
        public static class Change {
            private Date created;
            private User user;

            public Date getCreated() {
                return created;
            }

            public void setCreated(Date created) {
                this.created = created;
            }

            public User getUser() {
                return user;
            }

            public void setUser(User user) {
                this.user = user;
            }

            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class User {
                private String id;
                private String name;

                public String getId() {
                    return id;
                }

                public void setId(String id) {
                    this.id = id;
                }

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }
            }
        }

    }
}
