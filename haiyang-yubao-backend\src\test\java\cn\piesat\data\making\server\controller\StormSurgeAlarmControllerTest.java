package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.DataMakingServerApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class StormSurgeAlarmControllerTest {

    @Autowired
    private StormSurgeAlarmController stormSurgeAlarmControllerImpl;

    @Test
    public void testPushSelectOne(){
        stormSurgeAlarmControllerImpl.pushSelectOne(1951941754408783874L);
    }

    @Test
    public void testUpdate(){

    }
}
