package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 浮标站数据表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BuoyStationDataVO implements Serializable {

    private static final long serialVersionUID = -84166375255429917L;

    /**
     * id
     **/
    private Long id;
    /**
     * 浮标站编码
     **/
    private String buoyStationCode;
    /**
     * 时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    /**
     * 风速
     **/
    private String buoydataWs;
    /**
     * 风向
     **/
    private String buoydataWd;
    /**
     * 最大风速
     **/
    private String buoydataWsm;
    /**
     * 最大风速的风向
     **/
    private String buoydataWdm;
    /**
     * 瞬时风速
     **/
    private String buoydataWsa;
    /**
     * 瞬时风向
     **/
    private String buoydataWda;
    /**
     * 极大风速
     **/
    private String buoydataWsh;
    /**
     * 极大风速的风向
     **/
    private String buoydataWdh;
    /**
     * 平均波高
     **/
    private String buoydataBg;
    /**
     * 平均波向
     **/
    private String buoydataBx;
    /**
     * 平均波周期
     **/
    private String buoydataZq;
    /**
     * 有效波高
     **/
    private String buoydataYbg;
    /**
     * 有效波高周期
     **/
    private String buoydataYzq;
    /**
     * 最大波高
     **/
    private String buoydataZbg;
    /**
     * 最大波周期
     **/
    private String buoydataZzq;
    /**
     * 气温
     **/
    private String buoydataAt;
    /**
     * 气压
     **/
    private String buoydataBp;
    /**
     * 湿度
     **/
    private String buoydataHu;
    /**
     * 海温
     **/
    private String buoydataWt;
    /**
     * 盐度
     **/
    private String buoydataSl;
    /**
     * 能见度
     **/
    private String buoydataNjd;
    /**
     * 流速
     **/
    private String buoydataCs;
    /**
     * 流向
     **/
    private String buoydataCd;
    /**
     * 浮标站名称
     **/
    private String buoyStationName;
    /**
     * 浮标站位置
     **/
    private String buoyStationLocationJson;
    /**
     * 谱有效波高
     **/
    private String puPbg;
    /**
     * 谱峰周期
     **/
    private String puPfzq;
    /**
     * 涌浪谱有效波高
     **/
    private String ylpuPbg;
    /**
     * 涌浪谱峰周期
     **/
    private String ylpuPfzq;
}



