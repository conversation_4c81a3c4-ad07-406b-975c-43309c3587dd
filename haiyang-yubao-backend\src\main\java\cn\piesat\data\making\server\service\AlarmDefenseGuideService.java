package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.AlarmDefenseGuideDTO;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.AlarmDefenseGuideVO;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.piesat.data.making.server.entity.AlarmDefenseGuide;

import java.util.ArrayList;
import java.util.List;

/**
 * 警报防御指南(AlarmDefenseGuide)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:14
 */
public interface AlarmDefenseGuideService extends IService<AlarmDefenseGuide> {
    PageResult pageList(Integer pageNum, Integer pageSize);

    AlarmDefenseGuideDTO saveInfo(AlarmDefenseGuideDTO guideDTO);

    void updateInfo(AlarmDefenseGuideDTO guideDTO);

    AlarmDefenseGuideDTO info(Long id);

    void deleteInfo(Long id);

    void updateStatus(Long id, Integer status);

    ArrayList<AlarmDefenseGuideVO> list(Integer status);

    void setOpen(Long id);

    AlarmDefenseGuideDTO selectByType(Integer type);
}

