package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dto.FmGraphicTemplateMainDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateMain;
import cn.piesat.data.making.server.entity.FmIsolineData;
import cn.piesat.data.making.server.dto.FmIsolineDataDTO;
import cn.piesat.data.making.server.vo.FmIsolineDataVO;
import cn.piesat.data.making.server.dao.FmIsolineDataDao;
import cn.piesat.data.making.server.service.FmIsolineDataService;
import cn.piesat.data.making.server.mapper.FmIsolineDataMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:30
 */
@Service
@Slf4j
public class FmIsolineDataServiceImpl extends ServiceImpl<FmIsolineDataDao, FmIsolineData> implements FmIsolineDataService {

    @Resource
    private FmIsolineDataDao fmIsolineDataDao;

    @Override
    public List<FmIsolineDataVO> getList(FmIsolineDataDTO dto) {
        List<FmIsolineData> fmIsolineData = fmIsolineDataDao.selectByDataSourceAndDate(dto.getDataSource(), dto.getStartReportTime());
        List<FmIsolineData> fmIsolineData1 = fmIsolineDataDao.selectList(createQueryWrapper(dto));
        return FmIsolineDataMapper.INSTANCE.entityListToVoList(fmIsolineData1);
    }

    private LambdaQueryWrapper<FmIsolineData> createQueryWrapper(FmIsolineDataDTO dto) {
        LambdaQueryWrapper<FmIsolineData> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getDataSource())) {
            queryWrapper.eq(FmIsolineData::getDataSource, dto.getDataSource());
        }
        if (dto.getStartReportTime() != null) {
            queryWrapper.eq(FmIsolineData::getStartReportTime, dto.getStartReportTime());
        }
        return queryWrapper;
    }
}
