import Api from 'src/requests/forecast'
import { nextTick, onMounted, Ref, ref } from 'vue'
import { IForecastImageItem } from 'src/requests/forecast.type'
import eventBus from 'src/utils/eventBus'

export function useForecastList() {
  const forecastInfoArr = ref<IForecastImageItem[]>([])

  onMounted(() => {
    getImageInfo({ forecastInfoArr })
  })

  return {
    forecastInfoArr,
    getForecastList: getImageInfo,
    onForecastImageClicked
  }
}

function getImageInfo(opt: { forecastInfoArr: Ref<IForecastImageItem[]> }) {
  Api.getListByForecastId({
    graphicTemplateId: 2
  })
    .then(res => {
      opt.forecastInfoArr.value = res
    })
    .catch(err => {
      console.warn(err)
    })
}

/**
 * 预报图点击
 * @param forecastInfo
 */
async function onForecastImageClicked(forecastInfo: IForecastImageItem) {
  eventBus.emit('openYBT')
  await nextTick()
  eventBus.emit('forecastImageClicked', forecastInfo)
  console.log('forecastImageClicked')
}
