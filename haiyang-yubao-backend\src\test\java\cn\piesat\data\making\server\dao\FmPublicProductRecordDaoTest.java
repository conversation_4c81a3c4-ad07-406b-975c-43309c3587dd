package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmPublicProductRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicProductRecordDaoTest {

    @Autowired
    private FmPublicProductRecordDao fmPublicProductRecordDaoImpl;

    @Test
    public void testSave(){
        FmPublicProductRecord fmPublicTemplateRecord = new FmPublicProductRecord();

        fmPublicTemplateRecord.setRecordId(123L);
        fmPublicTemplateRecord.setName("name");
        fmPublicTemplateRecord.setProductTemplateId(234L);

        fmPublicProductRecordDaoImpl.insert(fmPublicTemplateRecord);
    }
}
