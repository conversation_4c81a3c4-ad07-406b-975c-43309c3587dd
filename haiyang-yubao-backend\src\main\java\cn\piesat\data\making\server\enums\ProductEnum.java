package cn.piesat.data.making.server.enums;

public enum ProductEnum {
    WAVE_WORD("O_AHSW_L4_WORD","海浪警报WORD报告产品"),
    WAVE_HTML("O_AHSW_L4_HTML","海浪警报HTML报告产品"),
    WAVE_SMS("O_AHSS_L4_TXT","海浪警报短信产品"),
    WAVE_MESSAGE_WORD("O_MHSW_L4_WORD","海浪消息WORD报告产品"),
    WAVE_MESSAGE_HTML("O_MHSW_L4_HTML","海浪消息HTML报告产品"),
    WAVE_MESSAGE_SMS("O_MHSW_L4_TXT","海浪消息短信产品"),
    SURGES_WORD("O_ASSW_L4_WORD","风暴潮警报WORD报告产品"),
    SURGES_HTML("O_ASSW_L4_HTML","风暴潮警报HTML报告产品"),
    SURGES_SMS("O_ASSS_L4_TXT","风暴潮警报短信产品"),
    SURGES_MESSAGE_WORD("O_MSSW_L4_WORD","风暴潮消息WORD报告产品"),
    SURGES_MESSAGE_HTML("O_MSSW_L4_HTML","风暴潮消息HTML报告产品"),
    SURGES_MESSAGE_SMS("O_MSSS_L4_TXT","风暴潮消息短信产品"),
    FORECAST_IMAGE("F_FSCl_S4_PNG","预报专题图产品"),
    FORECAST_VIDEO("F_FSCl_S4_WMV","预报视频产品");
    private String productId;

    private String productName;

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    ProductEnum(String productId, String productName) {
        this.productId = productId;
        this.productName = productName;
    }
}
