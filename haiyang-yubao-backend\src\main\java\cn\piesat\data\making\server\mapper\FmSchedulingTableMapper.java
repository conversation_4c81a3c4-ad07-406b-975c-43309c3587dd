package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmSchedulingTableDTO;
import cn.piesat.data.making.server.entity.FmSchedulingTable;
import cn.piesat.data.making.server.vo.FmSchedulingTableVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:06
 */
@Mapper(componentModel = "spring")
public interface FmSchedulingTableMapper {

    FmSchedulingTableMapper INSTANCE = Mappers.getMapper(FmSchedulingTableMapper.class);

    /**
     * entity-->vo
     */
    FmSchedulingTableVO entityToVo(FmSchedulingTable entity);

    /**
     * dto-->entity
     */
    FmSchedulingTable dtoToEntity(FmSchedulingTableDTO dto);

    /**
     * entityList-->voList
     */
    List<FmSchedulingTableVO> entityListToVoList(List<FmSchedulingTable> list);

    /**
     * dtoList-->entityList
     */
    List<FmSchedulingTable> dtoListToEntityList(List<FmSchedulingTableDTO> dtoList);
}
