package cn.piesat.data.making.server.dto;

import com.baomidou.mybatisplus.annotation.TableField;

import java.util.Date;

import java.io.Serializable;

/**
 * 台风实时数据信息DTO类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
public class FmTyphoonRealBDTO implements Serializable {

    private static final long serialVersionUID = -21140708296004323L;

    public interface Save {
    }

    /**
     * 主键
     */
    private Long id;
    /**
     * 台风编号
     */
    private String tfbh;
    /**
     * 数据时间
     */
    private Date time;
    /**
     * 经度
     */
    private Double lng;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 强度
     */
    private String strong;
    /**
     * 等级
     */
    private Integer power;
    /**
     * 中心速度
     */
    private Integer speed;
    /**
     * 移动方向
     */
    private String moveDir;
    /**
     * 移动速度
     */
    private Integer moveSpeed;
    /**
     * 中心气压
     */
    private Integer pressure;
    /**
     * 七级大风半径
     */
    private Integer radius7;
    /**
     * 十级大风半径
     */
    private Integer radius10;
    /**
     * 十二级大风半径
     */
    private Integer radius12;

    /**
     * 七级大风四边形
     */
    private String radius7Quad;
    /**
     * 十级大风四边形
     */
    private String radius10Quad;
    /**
     * 十二级大风四边形
     */
    private String radius12Quad;

    public String getRadius7Quad() {
        return radius7Quad;
    }

    public void setRadius7Quad(String radius7Quad) {
        this.radius7Quad = radius7Quad;
    }

    public String getRadius10Quad() {
        return radius10Quad;
    }

    public void setRadius10Quad(String radius10Quad) {
        this.radius10Quad = radius10Quad;
    }

    public String getRadius12Quad() {
        return radius12Quad;
    }

    public void setRadius12Quad(String radius12Quad) {
        this.radius12Quad = radius12Quad;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public Integer getPower() {
        return power;
    }

    public void setPower(Integer power) {
        this.power = power;
    }

    public Integer getSpeed() {
        return speed;
    }

    public void setSpeed(Integer speed) {
        this.speed = speed;
    }

    public String getMoveDir() {
        return moveDir;
    }

    public void setMoveDir(String moveDir) {
        this.moveDir = moveDir;
    }

    public Integer getMoveSpeed() {
        return moveSpeed;
    }

    public void setMoveSpeed(Integer moveSpeed) {
        this.moveSpeed = moveSpeed;
    }

    public Integer getPressure() {
        return pressure;
    }

    public void setPressure(Integer pressure) {
        this.pressure = pressure;
    }

    public Integer getRadius7() {
        return radius7;
    }

    public void setRadius7(Integer radius7) {
        this.radius7 = radius7;
    }

    public Integer getRadius10() {
        return radius10;
    }

    public void setRadius10(Integer radius10) {
        this.radius10 = radius10;
    }

    public Integer getRadius12() {
        return radius12;
    }

    public void setRadius12(Integer radius12) {
        this.radius12 = radius12;
    }
}
