.library-common{
  flex: 1;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
  background:#fff;
  .content {
    background: #fff;
  }
  .custom-form {
    .n-form-item-blank {
      justify-content: end;
    }
  }
  .content-header {
    box-sizing: border-box;
    padding: 20px 16px 16px;
    width: 100%;
    background: url(src/assets/images/common/content-header.png) no-repeat;
    background-size: 100% 100%;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 23px;
      position: relative;
      padding-left: 10px;
      &::before {
        width: 4px;
        height: 18px;
        background: #567bff;
        display: block;
        content: '';
        position: absolute;
        top: 2px;
        left: 0;
      }
    }
  }
  .content-filter {
    box-sizing: border-box;
    padding: 20px 15px;
    .n-form-item-label {
      font-size: 14px;
    }
    .qx-checkbox-group {
      display: flex;
    }
  }

  .qx-pagination {
    justify-content: flex-end;
    margin-top: 35px;
    box-sizing: border-box;
    padding-right: 20px;
  }
  .table-container{
    box-sizing:border-box;
    padding:0 20px 20px;
    background:#fff;
  }
  .n-checkbox__label{
    font-size:14px;
  }
}
.form-container {
  height: 800px;
}