package cn.piesat.data.making.server;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.model.RequestData;
import cn.piesat.data.making.server.service.RegionService;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.utils.HttpClientUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
public class StationServiceTest {

    @Resource
    private StationService stationService;
    @Resource
    private RegionService regionService;

    @Test
    public void saveTest() throws IOException {
        StationDTO stationDTO = new StationDTO();
        String name = "清澜";
        double y = 19.566667D;
        double x = 110.816667D;
        GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();
//        Point point = geometryFactory.createPoint(new Coordinate(x,y));\
        String geoJson = GeoToolsUtil.arrToGeojson(new double[][]{{x, y}});
        stationDTO.setLocationGeo(geoJson);


        stationDTO.setLocationJson(geoJson);
        stationDTO.setName(name);
        stationDTO.setCreateTime(new Date());
        stationDTO.setUpdateTime(new Date());
        stationService.save(stationDTO);

    }

    /**
     * 同步海洋台站
     *
     * @throws IOException IOException
     */
    @Test
    public void syncOceanStation() throws IOException {

        String url = "http://10.132.108.150:8089/service/api/hnhyt_station_code";
        String appKey = "802666031557dc6e0a9c46df59c62c806f3fa789";
        String sign = "dc71ee26908c4e75a37b994431f75e97";

        List<String> codeList = Arrays.asList("10000", "10001", "10002", "10003", "10004", "10005", "10006", "10007", "10008", "10009", "10010", "10011", "10012", "10013", "10014", "10015", "10016", "10017", "10018", "10019", "10020", "10021", "10022", "10023", "10024", "10025", "10026", "10027", "10028", "10029", "10030", "10031", "10032", "10033", "10034", "10035", "10036", "10037", "10038", "10039", "10040", "10041", "10042", "10043", "10044", "10045", "10046", "10047", "10048", "10049", "10050", "11755", "11804", "11805", "11816", "11817", "11818", "11819", "11820", "11821", "11822", "11823", "11826");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        List<StationVO> stations = new ArrayList<>();

        RequestData requestData = new RequestData();
        RequestData.Page page = new RequestData.Page();
        page.setPageNum(1);
        page.setPageSize(1000);
        requestData.setPage(page);
        String paramStr = JsonUtil.object2Json(requestData);
        String appParam = Base64.getEncoder().encodeToString(paramStr.getBytes());
        String contactStr = String.join("&", appKey, sign, appParam);
        String appSign = HttpClientUtil.md5(contactStr);

        try {
            String response = HttpClientUtil.post(url, "", appKey, appParam, appSign);
            String jsonString = response.trim();
            TypeReference<Wrapper> typeRef = new TypeReference<Wrapper>() {
            };
            Wrapper wrapper = objectMapper.readValue(jsonString, typeRef);
            if (wrapper.getCode() == 0) {
                if (wrapper.getData() != null) {
                    List<StationVO> newData = wrapper.getData().getResultList();
                    if (!CollectionUtils.isEmpty(newData)) {
                        for (StationVO station : newData) {
                            if (station.getSiteType().equals("海洋站") && !codeList.contains(station.getSiteCode())) {
                                stations.add(station);
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println("结束=====" + stations.size());

        for (StationVO station : stations) {
            StationDTO dto = voToEntity(station);
            stationService.save(dto);
        }
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    static class Wrapper implements Serializable {
        private String msg;
        private Integer code;
        private Data data;
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    static class Data implements Serializable {
        private Integer pageSize;
        private Integer pageNum;
        private Integer totalCount;
        private String encryKey;
        private List<StationVO> resultList;
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    static class StationVO implements Serializable {

        @JsonProperty("site_code_id")
        private String siteCodeId;
        @JsonProperty("distance_km")
        private Double distanceKm;
        @JsonProperty("site_type")
        private String siteType;
        @JsonProperty("initial_latitude")
        private Double initialLatitude;
        @JsonProperty("initial_longitude")
        private Double initialLongitude;
        @JsonProperty("administrative_district")
        private String administrativeDistrict;
        @JsonProperty("surveyed_longitude")
        private Double surveyedLongitude;
        @JsonProperty("site_type_min")
        private String siteTypeMin;
        @JsonProperty("site_code")
        private String siteCode;
        @JsonProperty("site_name")
        private String siteName;
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("site_type_code")
        private String siteTypeCode;
        @JsonProperty("site_type_min_code")
        private String siteTypeMinCode;
        @JsonProperty("surveyed_latitude")
        private Double surveyedLatitude;
    }

    public StationDTO voToEntity(StationVO vo) throws IOException {
        StationDTO stationDTO = new StationDTO();
        stationDTO.setCode(vo.getSiteCode());
        stationDTO.setName(vo.getSiteName());
        stationDTO.setStationTypeCode("oceanStation");
        stationDTO.setRegionCode(regionService.getCodeByName(vo.getAdministrativeDistrict()));
        stationDTO.setCreateUserId(1L);
        stationDTO.setCreateUser("超级管理员");
        stationDTO.setCreateTime(new Date());
        stationDTO.setUpdateUserId(1L);
        stationDTO.setUpdateUser("1");
        stationDTO.setUpdateTime(new Date());
        stationDTO.setType("liveObservation");
        stationDTO.setRelationStation(vo.getSiteCode());
        stationDTO.setEnable(true);

        Double longitude = vo.getInitialLongitude() == null ? vo.getSurveyedLongitude() : vo.getInitialLongitude();
        Double latitude = vo.getInitialLatitude() == null ? vo.getSurveyedLatitude() : vo.getInitialLatitude();
        String geoJson = GeoToolsUtil.arrToGeojson(new double[][]{{longitude, latitude}});
        stationDTO.setLocationJson(geoJson);
        stationDTO.setLocationGeo(geoJson);
        return stationDTO;
    }
}
