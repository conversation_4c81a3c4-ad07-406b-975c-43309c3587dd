import { Polygon } from 'ol/geom'

// 审图号
import Feature from 'ol/Feature'

export const mapLicense = '审图号：琼S（2024）041号'

export function getMapLisencePosition(
  borderFeature: Feature<Polygon>
): number[] {
  const geometry = borderFeature.getGeometry()
  if (!geometry) {
    throw new Error('无法获取几何')
  }
  const extent = geometry.getExtent()
  return [extent[0] + 0.52, extent[1] + 0.1]
}

export function getMapLicenseFeature() {

}
