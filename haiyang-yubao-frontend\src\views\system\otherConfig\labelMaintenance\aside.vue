<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-18 15:08:22
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-27 14:40:16
 * @FilePath: /hainan-jianzai-web/src/views/system/otherConfig/labelMaintenance/aside.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="common-aside aside">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>标签维护</h3>
        <qx-button class="primary" @click="createVisible = true"
          >新建</qx-button
        >
      </div>
    </div>
    <qx-tree
      ref="treeRef"
      v-loading="loading"
      :tree-data="tagList"
      :default-props="defaultProps"
      :default-expand-all="true"
      :default-selected-keys="defaultSelectedKeys"
      :edit="false"
      :have-right-menu="true"
      :switch-disabled="false"
      @selected="selectHandler"
      @re-name="reNameHandler"
      @delete-node="deleteNode"
    />
  </div>
  <qx-dialog
    v-model:visible="createVisible"
    title="新建便签"
    width="346px"
    class="create-label-dialog"
  >
    <template #content>
      <n-form
        ref="formRef"
        label-width="auto"
        label-placement="left"
        :model="form"
        :rules="rules"
      >
        <n-form-item label="姓名" path="user.name">
          <n-input v-model:value="form.labelName" placeholder="输入姓名" />
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btn-group">
        <qx-button class="primary" @click="onConfirm">确定</qx-button>
        <qx-button class="cancel" @click="createVisible = false"
          >取消</qx-button
        >
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { QxDialog } from 'src/components/QxDialog'
import { reactive, ref } from 'vue'
import TagApi from 'src/requests/tag'
import { useMessage, createDiscreteApi } from 'naive-ui'
import { QxTree } from 'src/components/QxTree'
const { dialog } = createDiscreteApi(['dialog'])

const message = useMessage()
const createVisible = ref(false)
const emit = defineEmits(['select'])
const form = reactive({
  labelName: ''
})
const rules = {
  labelName: {
    required: true,
    message: '请输入标签名称',
    trigger: 'blur'
  }
}

let tagList = ref<any>([])
let loading = ref(false)
let defaultSelectedKeys = ref<any>([])
const treeRef = ref()
const defaultProps = {
  key: 'id',
  label: 'name',
  children: 'children'
}
function getTagList() {
  tagList.value = []
  loading.value = true
  TagApi.getTagList()
    .then((res: any) => {
      if (res?.length) {
        tagList.value = res
        loading.value = false
        defaultSelectedKeys.value = [res[0].id]
        emit('select', res[0])
      }
    })
    .catch(e => {
      loading.value = false
      let { msg } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
    })
}
getTagList()

// 删除
function deleteNode(node: any) {
  dialog.warning({
    title: '提示',
    content: `是否删除${node.name}`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      TagApi.delTagById(node.id)
        .then((res: any) => {
          message.success('删除成功')
          getTagList()
          treeRef.value.hiddenRightMenu()
        })
        .catch((e: any) => {
          let { msg } = e?.response?.data || {}
          message.error(msg || '删除失败')
        })
    },
    onNegativeClick: () => {
      treeRef.value.hiddenRightMenu()
    }
  })
}

let editId = ref('')
type ParamsType = {
  name: string
  id?: string
}
// 新建
function onConfirm() {
  const params: ParamsType = { name: form.labelName }
  if (editId.value) {
    params.id = editId.value
  }
  TagApi.saveTag(params)
    .then(() => {
      message.success(editId.value ? '修改成功' : '创建成功')
      createVisible.value = false
      getTagList()
    })
    .finally(() => {
      treeRef.value.hiddenRightMenu()
    })
    .catch(e => {
      let { msg } = e?.response?.data || {}
      message.error(msg || '操作失败')
    })
}

// 修改
function reNameHandler(node: any) {
  form.labelName = node.name
  createVisible.value = true
  editId.value = node.id
}

function selectHandler(node: any, options: any) {
  emit('select', options)
}
</script>

<style scoped lang="scss">
@mixin box-style {
  box-sizing: border-box;
  padding: 20px;
}
.create-label-dialog {
  @include box-style;
  .n-form {
    @include box-style;
    padding-bottom: 0;
  }
  .btn-group {
    @include box-style;
    text-align: right;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
</style>
