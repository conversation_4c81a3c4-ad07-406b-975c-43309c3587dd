package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.dao.FmForecastProductDataDao;
import cn.piesat.data.making.server.dao.TideDailyDataDao;
import cn.piesat.data.making.server.dto.generate.WaveForecastDataDTO;
import cn.piesat.data.making.server.entity.FmForecastProductData;
import cn.piesat.data.making.server.entity.TideDailyData;
import cn.piesat.data.making.server.service.FmForecastProductDataService;
import cn.piesat.data.making.server.vo.AreaVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-27 11:22:38
 */
@Slf4j
@Service("fmForecastProductDataService")
public class FmForecastProductDataServiceImpl extends ServiceImpl<FmForecastProductDataDao, FmForecastProductData> implements FmForecastProductDataService {

    @Resource
    private FmForecastProductDataDao fmForecastProductDataDao;
    @Resource
    private TideDailyDataDao tideDailyDataDao;

    //区域站点映射
    static Map<String, String> areaStationMap;

    static {
        areaStationMap = new HashMap<>();
        areaStationMap.put("海口西海岸海域", "海口（秀英）");
        areaStationMap.put("海口海域", "海口（秀英）");
        areaStationMap.put("海口假日海滩浴场", "海口（秀英）");
        areaStationMap.put("博鳌玉带滩海域", "博鳌");
        areaStationMap.put("潭门", "博鳌");
        areaStationMap.put("万宁日月湾海域", "乌场");
        areaStationMap.put("三亚天涯海角海域", "三亚");
        areaStationMap.put("三亚海域", "三亚");
        areaStationMap.put("三亚蜈支洲岛浴场", "三亚");
        areaStationMap.put("三亚亚龙湾浴场", "三亚");
        areaStationMap.put("三亚大东海浴场", "三亚");
        areaStationMap.put("三亚三亚湾浴场", "三亚");
        areaStationMap.put("乐东龙栖湾海域", "莺歌海");
        areaStationMap.put("昌江棋子湾海域", "海尾");
        areaStationMap.put("文昌海域", "铺前");
        areaStationMap.put("洋浦海域", "洋浦");
        areaStationMap.put("西沙永兴岛海域", "永兴岛（西沙群岛）");
        areaStationMap.put("中沙黄岩岛海域", "黄岩岛");
        areaStationMap.put("南沙永暑礁海域", "永暑礁（南沙群岛）");
        areaStationMap.put("西沙赵述岛海域", "赵述岛");
        areaStationMap.put("南沙美济礁海域", "美济礁");
        areaStationMap.put("文昌高隆湾浴场", "高隆湾");
        areaStationMap.put("陵水分界洲岛浴场", "新村");
        areaStationMap.put("三亚海棠湾浴场", "海棠湾");
    }

    @Override
    public void saveResult(String filepath) throws Exception {
        String fileContent = FileUtil.readString(filepath, Charset.defaultCharset());
        ArrayList<Object> arrayList = JsonUtil.json2Object(fileContent, ArrayList.class);

        Path path = Paths.get(filepath);
        String fileName = path.getFileName().toString();
        String substring = fileName.substring(fileName.length() - 36, fileName.length() - 28);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = sdf.parse(substring);

        List<FmForecastProductData> list = new ArrayList<>();
        for (Object o : arrayList) {
            LinkedHashMap<String, Object> map = (LinkedHashMap) o;
            FmForecastProductData fmForecastProductData = new FmForecastProductData();
            //fmForecastProductData.setAreaId((String) map.get("id"));
            fmForecastProductData.setAveValue((String) map.get("ave"));

            fmForecastProductData.setName((String) map.get("name"));
            fmForecastProductData.setMaxValue((String) map.get("max"));
            fmForecastProductData.setMinValue((String) map.get("min"));
            fmForecastProductData.setType((String) map.get("type"));
            //fmForecastProductData.setForecastStartTime(date);
            fmForecastProductData.setElementCode((String) map.get("var"));
            fmForecastProductData.setDataSource("智能网络");
            list.add(fmForecastProductData);
//            this.save(fmForecastProductData);

        }
        //todo 改成批量插入
        this.saveBatch(list);
    }

    @Override
    public List<FmForecastProductData> getList(FmForecastProductData fmForecastProductData) {
        List<FmForecastProductData> list = this.list(createQueryWrapper(fmForecastProductData));
        return list;
    }

    @Override
    public Date getLastForecastStartTime(String dataSource) {
        LocalDateTime localDateTime = LocalDateTime.now().withHour(8).withMinute(0).withSecond(0).withNano(0);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
//        return fmForecastProductDataDao.getLastForecastStartTime(dataSource);
    }

    @Override
    public void updateModifyValue(List<FmForecastProductData> list) {
        list.stream().forEach(data -> fmForecastProductDataDao.updateById(data));
    }

    /**
     * 保存潮位数据
     **/
    @Override
    @SneakyThrows
    public void saveTideData(List<AreaVO> areaList, Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        List<TideDailyData> minTideInfoList = tideDailyDataDao.getMinTideInfoList(null, startTime, endTime);
        Map<String, TideDailyData> minTideInfoMap = minTideInfoList.stream().collect(Collectors.toMap(TideDailyData::getStationName, Function.identity(),
                (key1, key2) -> key2));
        List<TideDailyData> maxTideInfoList = tideDailyDataDao.getMaxTideInfoList(null, startTime, endTime);
        Map<String, TideDailyData> maxTideInfoMap = maxTideInfoList.stream().collect(Collectors.toMap(TideDailyData::getStationName, Function.identity(),
                (key1, key2) -> key2));

        //查询区域
        Map<String, Long> areaMap = areaList.stream().collect(Collectors.toMap(AreaVO::getName, AreaVO::getId, (key1, key2) -> key2));

        List<String> dataSourceList = Arrays.asList("forecast", "grid");

        dataSourceList.stream().forEach(dataSource -> {
            //查询最新的起报时间，目前默认是2024-09-01 12:00:00
            Date forecastStartTime = this.getLastForecastStartTime(dataSource);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            log.debug("同步潮位极值, 最新起报时间:{}", dateFormat.format(forecastStartTime));
            List<FmForecastProductData> tideInfoList = new ArrayList<>();
            //遍历区域
            areaList.stream().forEach(area -> {
                String areaName = area.getName();
                String stationName = areaStationMap.get(areaName);
                TideDailyData minTide = minTideInfoMap.get(stationName);
                TideDailyData maxTide = maxTideInfoMap.get(stationName);

                FmForecastProductData maxData = new FmForecastProductData();
                maxData.setDataSource(dataSource);
                maxData.setForecastTime(maxTide == null ? null : maxTide.getTideTime());
                maxData.setName(areaName);
                maxData.setAreaId(areaMap.get(areaName));
                maxData.setElementCode("tide");
                maxData.setStartTime(forecastStartTime);
                FmForecastProductData maxProductData = this.list(createQueryWrapper(maxData)).stream().findFirst().orElse(null);
                if (maxProductData != null) {
                    maxData.setId(maxProductData.getId());
                }
                maxData.setMinValue(minTide == null ? null : String.valueOf(minTide.getHeight()));
                maxData.setMaxValue(maxTide == null ? null : String.valueOf(maxTide.getHeight()));
                tideInfoList.add(maxData);

                FmForecastProductData minData = new FmForecastProductData();
                minData.setDataSource(dataSource);
                minData.setForecastTime(minTide == null ? null : minTide.getTideTime());
                minData.setName(areaName);
                minData.setAreaId(areaMap.get(areaName));
                minData.setElementCode("tideTime");
                minData.setStartTime(forecastStartTime);
                FmForecastProductData minProductData = this.list(createQueryWrapper(minData)).stream().findFirst().orElse(null);
                if (minProductData != null) {
                    minData.setId(minProductData.getId());
                }
                minData.setMinValue(minTide == null ? null : sdf.format(minTide.getTideTime()));
                minData.setMaxValue(maxTide == null ? null : sdf.format(maxTide.getTideTime()));
                tideInfoList.add(minData);
            });
            this.saveOrUpdateBatch(tideInfoList);
        });
    }

    @Override
    public List<FmForecastProductData> getWaveDataList(WaveForecastDataDTO waveForecastDataDTO) {
        LambdaQueryWrapper<FmForecastProductData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmForecastProductData::getDataSource, waveForecastDataDTO.getDataSource());
        wrapper.eq(FmForecastProductData::getProductCode, waveForecastDataDTO.getProductCode());
        wrapper.eq(FmForecastProductData::getElementCode, waveForecastDataDTO.getElementCode());
        wrapper.eq(FmForecastProductData::getStartTime, waveForecastDataDTO.getStartTime());
        wrapper.between(FmForecastProductData::getForecastTime, waveForecastDataDTO.getForecastStartTime(), waveForecastDataDTO.getForecastEndTime());
        wrapper.in(FmForecastProductData::getAreaId, waveForecastDataDTO.getAreaIds());
        return this.list(wrapper);
    }

    @Override
    public List<Date> waveStartTimeList(String dataSource, String productCode, Integer num) {
        String limit = String.format("limit %s", num);

        QueryWrapper<FmForecastProductData> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT start_time");
        wrapper.eq("data_source", dataSource);
        wrapper.eq("product_code", productCode);
        wrapper.eq("element_code", "swh");
        wrapper.orderByDesc("start_time");
        wrapper.last(limit);
        List<FmForecastProductData> list = this.list(wrapper);
        return list.stream().map(FmForecastProductData::getStartTime).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<FmForecastProductData> createQueryWrapper(FmForecastProductData dto) {
        LambdaQueryWrapper<FmForecastProductData> queryWrapper = new LambdaQueryWrapper<>();
        if (dto.getId() != null) {
            queryWrapper.eq(FmForecastProductData::getId, dto.getId());
        }
        if (dto.getName() != null) {
            queryWrapper.eq(FmForecastProductData::getName, dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getType())) {
            queryWrapper.eq(FmForecastProductData::getType, dto.getType());
        }
       /* if (dto.getForecastStartTime() != null) {
            queryWrapper.eq(FmForecastProductData::getForecastStartTime, dto.getForecastStartTime());
        }*/
        if (dto.getAreaId() != null) {
            queryWrapper.eq(FmForecastProductData::getAreaId, dto.getAreaId());
        }
        if (dto.getDataSource() != null) {
            queryWrapper.eq(FmForecastProductData::getDataSource, dto.getDataSource());
        }
        if (dto.getElementCode() != null) {
            queryWrapper.eq(FmForecastProductData::getElementCode, dto.getElementCode());
        }
        if (dto.getStartTime() != null) {
            queryWrapper.eq(FmForecastProductData::getStartTime, dto.getStartTime());
        }
      /*  if (dto.getEndTime() != null) {
            queryWrapper.eq(FmForecastProductData::getEndTime, dto.getEndTime());
        }*/
        return queryWrapper;
    }
}
