<template>
  <div class="sea-area-forecast">
    <div class="sea-area-aside">
      <n-data-table
        v-loading="areaDataLoading"
        class="qx-table"
        :data="areaTableData"
        :columns="areaColumns"
        :single-line="false"
        :max-height="610"
      ></n-data-table>
    </div>
    <div class="sea-area-content">
      <div class="table-wrap">
        <n-data-table
          class="qx-table"
          :data="contentTable"
          :columns="contentColumns"
          :single-line="false"
          :max-height="410"
        ></n-data-table>
      </div>
      <div class="text-area-wrap">
        <div v-if="bridge?.collect().tabIndex === 1" class="update-btn-wrap">
          <n-button @click.stop="updateParagraph">更新</n-button>
        </div>
        <n-input
          v-model:value="paragraph"
          type="textarea"
          ref="inputRef"
          show-count
          placeholder="请输入预报文字，完成后点击文本框右上角的更新按钮"
          class="my-text"
        >
          <template #count>
            <div>
              <span>共{{ cptParagraphStatistics.all }}字</span>
              <span>（</span>
              <span>汉字{{ cptParagraphStatistics.chinese }}字</span>
              <span>）</span>
              <span>，</span>
              <span>选中{{ cptParagraphStatistics.selected }}字</span>
              <span>（</span>
              <span>汉字{{ cptParagraphStatistics.selectedChinese }}字</span>
              <span>）</span>
            </div>
          </template>
        </n-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h, PropType, computed, inject, Ref } from 'vue'
import type { DataTableColumn, NInput } from 'naive-ui'
import QxTableEdit from 'src/components/QxTableEdit/index.js'
import { useMessage } from 'naive-ui'
import { useContentTable } from 'src/views/forecast/formProduction/seaAreaForecastHooks/useContentTable'
import { useParagraph } from 'src/views/forecast/formProduction/seaAreaForecastHooks/useParagraph'
import {
  bridgeKey,
  IForecastProductBridge
} from 'src/views/forecast/formProduction/forecastProductHooks/types'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import { useParagraphStatistics } from 'src/utils/vue-hooks/useParagraphStatistics/useParagraphStatistics'
import { useAreaTable } from 'src/views/forecast/formProduction/seaAreaForecastHooks/useAreaTable'

const bridge = inject<IBridge<IForecastProductBridge>>(bridgeKey)
const message = useMessage()
const loading = ref(false)
const props = defineProps({
  tableData: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})


const columns: DataTableColumn[] = [
  {
    title: '序号',
    width: 60,
    key: 'num',
    render(row: any, index: number) {
      return index + 1
    }
  },
  {
    title: '区域名称',
    key: 'name'
  }
]

const { areaTableData, areaColumns, areaDataLoading } = useAreaTable()
const { contentColumns, contentTable } = useContentTable({
  numberArr: areaTableData,
  setParagraph: (text: string) => setParagraph(text)
})

const { paragraph, updateParagraph, setParagraph } = useParagraph({
  areaGetter() {
    return areaTableData.value.map((item, index) => {
      return {
        ...item,
        index: item.index
      }
    })
  },
  contentArr() {
    return contentTable.value
  }
})

const inputRef = ref<InstanceType<typeof NInput> | null>(null)
const { cptParagraphStatistics } = useParagraphStatistics(paragraph, inputRef)

defineExpose({
  getParagraph() {
    return paragraph.value
  },
  getTableData() {
    contentTable.value.forEach((item, i) => {
      const indexArray = item.index.split(',').map(_ => Number(_))
      Reflect.set(
        item,
        '$names',
        areaTableData.value
          .filter(_ => indexArray.includes(_.index))
          .map(obj => obj.name)
      )
    })
    return contentTable.value
  }
})
</script>

<style scoped lang="scss">
.sea-area-forecast {
  display: flex;
  box-sizing: border-box;
  padding: 20px;
  .sea-area-aside {
    width: 280px;
    flex-shrink: 0;
    margin-right: 20px;
  }
  .sea-area-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .table-wrap {
      flex: 1;
    }

    .text-area-wrap {
      flex: 0 0 200px;
      position: relative;

      .update-btn-wrap {
        position: absolute;
        top: 0;
        right: 0;
        margin-top: 3px;
        margin-right: 3px;
        z-index: 1;
        //display: none;
      }

      .my-text {
        height: 100%;
      }
    }

    .text-area-wrap:has(.my-text:focus-within) {
      .update-btn-wrap {
        display: initial;
      }
    }
  }
}
</style>
