package cn.piesat.data.making.server.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 船舶数据
 *
 * <AUTHOR>
 */
@Data
public class ShipDataInfo {
    /**
     * id
     **/
    @JsonProperty("id")
    private String id;
    /**
     * 名称
     **/
    @JsonProperty("signShip")
    private String signShip;
    /**
     * 纬度
     **/
    @JsonProperty("latitude")
    private Double latitude;
    /**
     * 经度
     **/
    @JsonProperty("longitude")
    private Double longitude;
    /**
     * 可见度
     **/
    @JsonProperty("visibility")
    private Double visibility;
    /**
     * 气温
     **/
    @JsonProperty("airTemperature")
    private Double airTemperature;
    /**
     * 露点温度
     **/
    @JsonProperty("dewPointTemp")
    private Double dewPointTemp;
    /**
     * 风向
     **/
    @JsonProperty("windDirection")
    private Double windDirection;
    /**
     * 风速
     **/
    @JsonProperty("windSpeed")
    private Double windSpeed;
    /**
     * 气压
     **/
    @JsonProperty("airPressure")
    private Double airPressure;
    /**
     * 云总量
     **/
    @JsonProperty("totalCloudAmount")
    private Double totalCloudAmount;
    /**
     * 云量低
     **/
    @JsonProperty("lowCloudAmount")
    private Double lowCloudAmount;
    /**
     * 海温
     **/
    @JsonProperty("seaTemp")
    private Double seaTemp;
    /**
     * 波期
     **/
    @JsonProperty("wavePeriod")
    private Double wavePeriod;
    /**
     * 波高
     **/
    @JsonProperty("waveHeight")
    private Double waveHeight;
    /**
     * 浪涌方向
     **/
    @JsonProperty("surgeDirection")
    private Double surgeDirection;
    /**
     * 浪涌周期
     **/
    @JsonProperty("surgePeriod")
    private Double surgePeriod;
    /**
     * 浪涌高度
     **/
    @JsonProperty("surgeHeight")
    private Double surgeHeight;
    /**
     * 年
     **/
    @JsonProperty("year")
    private String year;
    /**
     * 月
     **/
    @JsonProperty("month")
    private String month;
    /**
     * 日
     **/
    @JsonProperty("day")
    private String day;
    /**
     * 时
     **/
    @JsonProperty("hour")
    private String hour;

    private Integer page;
    private Integer pageSize;
    private Integer total;
    private Integer totalPage;
    private Integer sort;
    private Integer order;
    private Double presentWeather;
    private String timeLiness;
    private String updateFrequency;
    private String dataFormat;
    private String temp;
}
