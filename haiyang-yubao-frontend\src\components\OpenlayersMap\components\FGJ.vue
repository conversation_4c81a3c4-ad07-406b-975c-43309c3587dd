<template>
  <div>
    <div class="btns">
      <qx-button
        class="edit-btn"
        :class="[activeBtn === 'draw' ? 'active' : '']"
        @click="trigger('draw')"
        >绘制</qx-button
      >
      <qx-button
        class="edit-btn"
        :class="[activeBtn === 'modify' ? 'active' : '']"
        @click="trigger('modify')"
        >修改</qx-button
      >
      <qx-button
        class="edit-btn"
        :class="[activeBtn === 'delete' ? 'active' : '']"
        @click="trigger('delete')"
        >删除</qx-button
      >
      <qx-button
        class="edit-btn"
        :class="[activeBtn === 'clear' ? 'active' : '']"
        @click="trigger('clear')"
        >清空</qx-button
      >
    </div>
    <div class="tools-container">
      <div
        v-for="item in tools"
        :key="item.id"
        class="tool-item"
        :class="activeTool === item.id ? 'toolAct' : ''"
        @click="activeTool = item.id"
      >
        <img :src="activeTool === item.id ? item.activeUrl : item.url" />
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, inject, onMounted, onUnmounted } from 'vue'
import { QxButton } from 'src/components/QxButton'
import PlottingLayer from 'src/utils/plotting/plot/PlottingLayer.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import FeatureOperatorEvent from 'src/utils/plotting/plot/events/FeatureOperatorEvent.js'
// import ColdFrontStyle from 'src/utils/plotting/plot/style/ColdFrontStyle.js'
import StyleFactory from 'src/utils/plotting/plot/style/StyleFactory.js'
// src\utils\plotting\plot\utils\plotutil.js

import * as PlotUtils from 'src/utils/plotting/plot/utils/plotutil.js'

import Feature from 'ol/Feature.js'
import LineString from 'ol/geom/LineString.js'
import { Stroke, Style } from 'ol/style.js'
// console.log(ColdFrontStyle)
const getMap = inject('getMap')
const activeBtn = ref('')
const tools = [
  {
    name: '台风',
    id: 'mete_typhon',
    // id: 'mete_point',
    url: '/src/assets/images/tools/tai.png',
    activeUrl: '/src/assets/images/tools/tai-active.png'
  },
  {
    name: '冷锋',
    id: 'mete_cold_front',
    url: '/src/assets/images/tools/lengfeng.png',
    activeUrl: '/src/assets/images/tools/lengfeng-active.png'
  },
  {
    name: '暖锋',
    id: 'mete_warm_front',
    url: '/src/assets/images/tools/nuanfeng.png',
    activeUrl: '/src/assets/images/tools/nuanfeng-active.png'
  },
  {
    name: '静止锋',
    id: 'mete_stationary_front',
    url: '/src/assets/images/tools/jingzhifeng.png',
    activeUrl: '/src/assets/images/tools/jingzhifeng-active.png'
  },
  {
    name: '折线',
    id: 'polyline',
    url: '/src/assets/images/tools/zhexian.png',
    activeUrl: '/src/assets/images/tools/zhexian-active.png'
  }
]
const activeTool = ref('')
function trigger(type) {
  activeBtn.value = type
  if (type === 'draw') {
    activate(activeTool.value)
  }
  if (type === 'clear') {
    if (g_pol_layer) {
      g_pol_layer.clearFeatures()
      g_op_feature = null
      vectorSource.clear()
    }
    activeBtn.value = ''
  }
  if (type === 'delete') {
    g_pol_layer.removeFeature(g_op_feature)
    g_op_feature = null
    drawTyphonPath()
  }
}
let g_pol_layer = null
let vectorSource = null
let vectorLayer = null
function creatVectorLayer(map) {
  vectorSource = new VectorSource()
  vectorLayer = new VectorLayer({
    source: vectorSource,
    zIndex: 12,
    transparent: 'true'
  })
  map.addLayer(vectorLayer)
  var layersArray = map.getLayers()
  // layersArray.insertAt(layersArray.length, vectorLayer)
  // const points = [
  //   [109.66031309034004, 20.854522401546383],
  //   [109.42846329485783, 20.094173023767084],
  //   [108.34470029737129, 19.560310694688]
  // ]
  // const aaa = PlotUtils.getCurvePoints(0.3, points)
  // let lineFeature = new Feature({
  //   geometry: new LineString(aaa)
  // })
  // const ft_style = StyleFactory.createFTStyle('mete_occluded_front')
  // console.log(2, ft_style)
  // if (!ft_style) {
  //   return
  // }
  // const style = ft_style.parse()
  // if (style instanceof Array) {
  //   style.forEach(s => s.setZIndex(1))
  // } else if (style instanceof Style) {
  //   style.setZIndex(1)
  // }

  // // this.feature.setStyle(style)
  // lineFeature.setStyle(style)
  // vectorSource.addFeatures([lineFeature])
  // console.log(style,"***********")
}
function activate(type) {
  console.log(type,'type------active---fgj')
  g_pol_layer.addFeature(type)
}
// 绘制结束后
function drawend(feature) {
  setTimeout(() => {
    if (activeTool.value === 'mete_typhon') {
      drawTyphonPath()
    }
    console.log(g_pol_layer, '*******g_pol_layer*******')
    console.log(feature, 'drawend')
    activeBtn.value = ''
  }, 1)
}
// 绘制台风路径
function drawTyphonPath() {
  vectorSource.refresh()
  const features = g_pol_layer.showLayer.values_.source.featuresRtree_.items_
  const points = []
  for (let key in features) {
    const fea = features[key].value.values_.geometry
    if (fea.type && fea.type === 'mete_typhon') {
      points.push(fea.points[0])
    }
  }
  console.log(points, 'points****points********points')
  if (points.length > 1) {
    const line = new Feature({
      geometry: new LineString(points)
    })
    line.setStyle(
      new Style({
        stroke: new Stroke({
          color: '#333333',
          width: 2,
          lineDash: [5, 10] // 设置虚线样式
        })
      })
    )
    vectorSource.addFeatures([line])
  }
}
let styletext = null
let g_op_feature = null
onMounted(() => {
  getMap(map => {
    g_pol_layer = new PlottingLayer(map, undefined, feature => {
      drawend(feature)
    })
    g_pol_layer.on(FeatureOperatorEvent.ACTIVATE, function (e) {
      console.log('aaaaaaaa', e.feature_operator, g_pol_layer)
      g_op_feature = e.feature_operator
      styletext = JSON.stringify(g_op_feature.getStyle())
      /* window.g_op_feature.iteratorAttribute(function (key) {
                that.tableData.push({key:key,value:this.getAttribute(key)})
            }, window.g_op_feature) */
    })
    g_pol_layer.on(FeatureOperatorEvent.DEACTIVATE, function (e) {
      g_op_feature = null
      styletext = ''
      // that.tableData=[];
    })
    creatVectorLayer(map)
  })
})
onUnmounted(() => {
  getMap(map => {
    map.removeLayer(vectorLayer)
  })
})
</script>

<style lang="scss" scoped>
.btns {
  display: flex;
  justify-content: space-between;
  .edit-btn {
    width: 80px;
    height: 32px;
    background: #1c81f8;
    border-radius: 4px 4px 4px 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-right: 0; */
    padding: 0px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 16px;
    &:nth-last-child(1) {
      margin-right: 0;
    }
    &.active {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
    &:hover {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
  }
}

.tools-container {
  // width: 346px;
  height: 119px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 12px 0px;
  padding: 0px 12px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  .tool-item {
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #222222;
    cursor: pointer;
    &.toolAct {
      color: #0026ff;
    }
    img {
      width: 28px;
      height: 28px;
    }
    span {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
    }
  }
}
</style>
