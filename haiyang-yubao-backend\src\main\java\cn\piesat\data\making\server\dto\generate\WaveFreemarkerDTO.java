package cn.piesat.data.making.server.dto.generate;

import java.util.List;

public class WaveFreemarkerDTO {
    private String time;
    private String levelColor;
    private String levelRoman;
    private String typhoonName;
    private String forecastStartTime;
    private String forecastEndTime;
    private List<AreaWaveDTO> areaList;
    private List<AreaWaveDTO> regionList;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getLevelColor() {
        return levelColor;
    }

    public void setLevelColor(String levelColor) {
        this.levelColor = levelColor;
    }

    public String getLevelRoman() {
        return levelRoman;
    }

    public void setLevelRoman(String levelRoman) {
        this.levelRoman = levelRoman;
    }

    public String getTyphoonName() {
        return typhoonName;
    }

    public void setTyphoonName(String typhoonName) {
        this.typhoonName = typhoonName;
    }

    public String getForecastStartTime() {
        return forecastStartTime;
    }

    public void setForecastStartTime(String forecastStartTime) {
        this.forecastStartTime = forecastStartTime;
    }

    public String getForecastEndTime() {
        return forecastEndTime;
    }

    public void setForecastEndTime(String forecastEndTime) {
        this.forecastEndTime = forecastEndTime;
    }

    public List<AreaWaveDTO> getAreaList() {
        return areaList;
    }

    public void setAreaList(List<AreaWaveDTO> areaList) {
        this.areaList = areaList;
    }

    public List<AreaWaveDTO> getRegionList() {
        return regionList;
    }

    public void setRegionList(List<AreaWaveDTO> regionList) {
        this.regionList = regionList;
    }

    public static class AreaWaveDTO{
        private String name;
        private String areaTypeCode;
        private Double max;
        private Double min;
        private String maxZh;
        private String minZh;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAreaTypeCode() {
            return areaTypeCode;
        }

        public void setAreaTypeCode(String areaTypeCode) {
            this.areaTypeCode = areaTypeCode;
        }

        public Double getMax() {
            return max;
        }

        public void setMax(Double max) {
            this.max = max;
        }

        public Double getMin() {
            return min;
        }

        public void setMin(Double min) {
            this.min = min;
        }

        public String getMaxZh() {
            return maxZh;
        }

        public void setMaxZh(String maxZh) {
            this.maxZh = maxZh;
        }

        public String getMinZh() {
            return minZh;
        }

        public void setMinZh(String minZh) {
            this.minZh = minZh;
        }
    }
}
