<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-01 15:16:03
 * @LastEditors: xuli<PERSON>e <EMAIL>
 * @LastEditTime: 2025-03-18 14:10:40
 * @FilePath: \hainan-jianzai-web\src\components\OpenlayersMap\components\XXYS.vue
 * @Description: 信息映射
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div id="popup5" v-show="showInfo" class="ol-popup2">
    <a id="popup5-closer" href="#" class="ol-popup2-closer"></a>
    <div id="popup5-content">
      <div class="info">区域名称：{{ area.name }}</div>
      <div class="info">海表温度：{{ area.sst }}K</div>
      <div class="info">u方向风速：{{ area.u10 }}m/s</div>
      <div class="info">v方向风速：{{ area.v10 }}m/s</div>
      <div class="info">平均波向：{{ area.mwd }}°</div>
      <div class="info">平均波周期：{{ area.mwp }}s</div>
      <div class="info">有效波高：{{ area.swh }}m</div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, onUnmounted, nextTick, reactive } from 'vue'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import info from './海区-新增要素.json'
import GeoJSON from 'ol/format/GeoJSON.js'
import Select from 'ol/interaction/Select.js'
import { Fill, Stroke, Style, Text, Icon, Circle } from 'ol/style.js'
import { click } from 'ol/events/condition.js'
import Overlay from 'ol/Overlay.js'
const showInfo = ref(false)
const getMap = inject('getMap')
const area = reactive({
  name: '',
  sst: '',
  u10: '',
  v10: '',
  mwd: '',
  mwp: '',
  swh: ''
})
const vectorSource = new VectorSource()
const vectorLayer = new VectorLayer({
  source: vectorSource
})
const selected = new Style({
  fill: new Fill({
    color: 'rgba(0, 255, 0, 0.25)'
  }),
  stroke: new Stroke({
    color: 'rgba(255, 255, 255, 0.7)',
    width: 2
  })
})
function selectStyle(feature) {
  const color = feature.get('COLOR') || 'rgba(0, 255, 0, 0.25)'

  selected.getFill().setColor(color)
  return selected
}

const selectClick = new Select({
  condition: click,
  style: selectStyle,
  layers: [vectorLayer],
  features: vectorSource.getFeaturesCollection()
})
let popup = null
onMounted(() => {
  getMap(map => {
    popup = new Overlay({
      element: document.getElementById('popup5')
    })
    map.addOverlay(popup)
    map.addLayer(vectorLayer)
    map.addInteraction(selectClick)
    selectClick.on('select', function (e) {
      // overlay.setPosition(coordinate)
      const feature = e.selected[0]
      const properties = feature.getProperties()
      area.name = properties.areaName
      area.sst = properties.sst
      area.u10 = properties.u10
      area.v10 = properties.v10
      area.mwd = properties.mwd
      area.mwp = properties.mwp
      area.swh = properties.swh
      showInfo.value = true
      nextTick(() => {
        popup.setPosition(e.mapBrowserEvent.coordinate)
        const closer = document.getElementById('popup5-closer')
        if (closer) {
          closer.onclick = function () {
            popup.setPosition(undefined)
            closer.blur()
            return false
          }
        }
      })

      // console.log(e.mapBrowserEvent.coordinate)
    })
  })

  const collection = []
  info.forEach(area => {
    area.areaList.forEach(item => {
      if (item.locationJson) {
        const features = new GeoJSON().readFeatures(item.locationJson)
        features[0].setProperties({
          mwd: (item.mwd * 1).toFixed(2),
          areaName: item.name,
          mwp: (item.mwp * 1).toFixed(2),
          sst: (item.sst * 1).toFixed(2),
          swh: (item.swh * 1).toFixed(2),
          u10: (item.u10 * 1).toFixed(2),
          v10: (item.v10 * 1).toFixed(2)
        })
        features[0].setStyle(
          new Style({
            fill: new Fill({
              color: 'rgba(255, 255, 255, 0.1)'
            }),
            stroke: new Stroke({
              color: '#ffffff',
              width: 2
            })
          })
        )
        vectorSource.addFeature(features[0])
        collection.push(features[0])
      }
    })
    console.log(collection)
  })
  // select interaction working on "click"
})
onUnmounted(() => {
  showInfo.value=false
  getMap(map => {
    map.removeLayer(vectorLayer)
    // map.removeOverlay(popup)
    map.removeInteraction(selectClick)
  })
})
</script>

<style lang="scss" scoped>
.ol-popup2 {
  position: fixed;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #cccccc;
  bottom: 12px;
  left: -50px;
  // min-width: 300px;
  width: auto;
  // height: 180px;
  display: block;
  #popup5-content {
    font-size: 16px;
    margin-top: 10px;
    .info {
      white-space: nowrap;
      height: 22px;
      /**
      typhoonInfo.strong === '热带低压(TD)' ? 'td' : '',
            typhoonInfo.strong === '热带风暴(TS)' ? 'ts' : '',
            typhoonInfo.strong === '强热带风暴(STS)' ? 'sts' : '',
            typhoonInfo.strong === '台风(TY)' ? 'ty' : '',
            typhoonInfo.strong === '强台风(STY)' ? 'sty' : '',
            typhoonInfo.strong === '超强台风(SuperTY)' ? 'superTy' : ''
      */
      .strong {
        padding: 3px;
        color: #fff;
        &.td {
          background-color: #0083fd;
        }
        &.ts {
          background-color: #6bb654;
        }
        &.sts {
          background-color: #fff300;
        }
        &.ty {
          background-color: #fe7b0e;
        }
        &.sty {
          background-color: #f90102;
        }
        &.superTy {
          background-color: #9a5bbc;
        }
      }
    }
    .wind-info {
      display: flex;
      flex-wrap: wrap;
      margin-top: 10px;
      .wind-level {
        width: 56px;
        text-align: center;
        height: 22px;
        line-height: 22px;
      }
    }
  }
}
.ol-popup2:after,
.ol-popup2:before {
  top: 100%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.ol-popup2:after {
  border-top-color: white;
  border-width: 10px;
  left: 48px;
  margin-left: -10px;
}
.ol-popup2:before {
  border-top-color: #cccccc;
  border-width: 11px;
  left: 48px;
  margin-left: -11px;
}
.ol-popup2-closer {
  text-decoration: none;
  position: absolute;
  top: 2px;
  right: 8px;
}
.ol-popup2-closer:after {
  content: '✖';
}
</style>
