<template>
  <div class="qx-tree-wrap">
    <div v-if="search" class="tree-search d-flex align-items-center">
      <label for="">名称搜索</label>
      <n-input
        v-model:value="searchVal"
        class="qx-input"
        placeholder="请输入"
        clearable
      >
        <template #suffix>
          <Search class="icon-search" @click="onSearch" />
        </template>
      </n-input>
    </div>
    <n-tree
      id="qx-tree"
      block-line
      :data="props.treeData"
      :default-expanded-keys="defaultExpandedKeys"
      :selectable="true"
      :key-field="props.defaultProps.key"
      :label-field="props.defaultProps.label"
      :children-field="props.defaultProps.children"
      :render-switcher-icon="renderSwitchIcon"
      :render-suffix="props.edit ? renderSuffix : undefined"
      :default-expand-all="defaultExpandAll"
      :default-selected-keys="defaultSelectedKeys"
      :expand-on-click="true"
      :node-props="nodeProps"
      :watch-props="['defaultSelectedKeys']"
      :cancelable="false"
      :allow-checking-not-loaded="false"
      @update:expanded-keys="handleExpandedKeys"
      @update:selected-keys="handleSelectedKeys"
    />
  </div>
</template>

<script setup lang="ts">
import { NSwitch } from 'naive-ui'
import { h, ref } from 'vue'
import type { TreeDropInfo, TreeOption } from 'naive-ui'
import { Search } from '@vicons/ionicons5'
const searchVal = ref<string>('')

const props = defineProps({
  defaultExpandedKeys: {
    type: Array<string>,
    default() {
      return []
    }
  },
  treeData: {
    type: Array<TreeOption>,
    default() {
      return []
    }
  },
  defaultProps: {
    type: Object,
    default() {
      return {
        key: 'id',
        label: 'label',
        children: 'children'
      }
    }
  },
  defaultExpandAll: {
    type: Boolean,
    default() {
      return false
    }
  },
  defaultSelectedKeys: {
    type: Array<string>,
    default() {
      return []
    }
  },
  search: {
    type: Boolean,
    default() {
      return false
    }
  },
  edit: {
    type: Boolean,
    default() {
      return false
    }
  },
  icons: {
    // 自定义图标类名
    type: Array<string>,
    default() {
      return ['icon-add', 'icon-remove']
    }
  },
  switchDisabled: {
    type: Boolean,
    default() {
      return false
    }
  }
})
// 渲染自定义图标
function renderSwitchIcon(info: { option: TreeOption; expanded: boolean }) {
  const option: TreeOption = info.option
  const expanded: boolean = info.expanded
  const children = props.defaultProps.children

  if (option[children]) {
    return h('i', {
      class: `icon ${expanded ? props.icons[1] : props.icons[0]}`
    })
  } else {
    return h('i', {
      class: `icon ${props.icons.length === 3 ? props.icons[2] : ''}`,
      style: {
        display: props.icons[2] ? 'block' : 'none'
      }
    })
  }
}
function nodeProps({ option }: { option: TreeOption }) {
  return {
    onClick() {
      emit('click', option)
    }
  }
}
// 渲染switch开关
function renderSuffix(info: { option: TreeOption }) {
  const option: TreeOption = info.option
  const children = props.defaultProps.children

  if (!option[children]) {
    if (props.defaultSelectedKeys.includes(option?.id as string)) {
      return [
        h('i', {
          class: 'icon icon-edit',
          onClick: (a: any) => {
            a.stopPropagation()
            emit('edit', option)
          }
        }),
        h('i', {
          class: 'icon icon-delete',
          onClick: (a: any) => {
            a.stopPropagation()
            emit('delete', option)
          }
        })
      ]
    }
  }
}

const emit = defineEmits([
  'changeSwitch',
  'search',
  'click',
  'expanded',
  'selected',
  'onLoad',
  'edit',
  'delete'
])

function changeSwitch(val: boolean, option: TreeOption) {
  changeStatus(props.treeData, option)
  emit('changeSwitch', val, option)
}

function changeStatus(data: any[], option: TreeOption) {
  const children = props.defaultProps.children
  data.forEach(item => {
    if (item.id == option.id) {
      item.status = !option.status
    } else if (item[children] && item[children].length) {
      changeStatus(item[children], option)
    }
  })
}

function handleSelectedKeys(
  keys: string[],
  option: Array<TreeOption | null>,
  meta: any
) {
  if (option?.length) {
    emit('selected', keys, option[0], meta)
  }
}

//获取当前点击数据key
function handleExpandedKeys(expandedKeys: string[]) {
  // emit('expanded', expandedKeys)
}

function onSearch() {
  emit('search', searchVal.value)
}
defineExpose({})
</script>

<style lang="scss">
.qx-tree-wrap {
  flex: 1;
}
#qx-tree {
  box-sizing: border-box;
  padding: 12px 20px;
  flex: 1;
  overflow-y: auto;
  .n-tree-node-switcher__icon {
    width: auto;
    height: auto;
    i.icon {
      width: 10px;
      height: 10px;
    }
  }
  .n-tree-node-content__text {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #000000;
    line-height: 20px;
    margin-left: 4px;
  }
  .n-tree-node-switcher--hide {
    visibility: visible;
  }
  .n-tree-node-switcher.n-tree-node-switcher--expanded {
    transform: rotate(0deg) !important;
  }
  // .n-switch{
  //   width: 32.76px;
  //   height: 16px;
  //   min-width: auto;
  //   .n-switch__rail{
  //     width:100%;
  //     height: 100%;
  //   }
  // }
}
.tree-search {
  box-sizing: border-box;
  padding: 0 20px;
  label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #222222;
    line-height: 32px;
    // width: 80px;
    flex-shrink: 0;
    text-align: right;
    margin-right: 15px;
  }
  .icon-search {
    width: 17px;
    height: 17px;
    cursor: pointer;
  }
}
</style>
