package cn.piesat.data.making.server.processor;

import cn.piesat.data.making.server.dto.GenerateWordDTO;

import java.util.Date;
import java.util.List;

public class WaveGenerateText extends GenerateWordDTO {
    /**
     * 区域ID列表
     */
    private List<Long> areaIds;
    /**
     * 数值NC文件时间
     */
    private Date ncFileDate;
    /**
     * 预报开始时刻
     */
    private Integer forecastStartTime;
    /**
     * 预报结束时刻
     */
    private Integer forecastEndTime;
    /**
     * 预警等级id
     */
    private Long alarmLevelId = -1L;

    public List<Long> getAreaIds() {
        return areaIds;
    }

    public void setAreaIds(List<Long> areaIds) {
        this.areaIds = areaIds;
    }


    public Date getNcFileDate() {
        return ncFileDate;
    }

    public void setNcFileDate(Date ncFileDate) {
        this.ncFileDate = ncFileDate;
    }

    public Integer getForecastStartTime() {
        return forecastStartTime;
    }

    public void setForecastStartTime(Integer forecastStartTime) {
        this.forecastStartTime = forecastStartTime;
    }

    public Integer getForecastEndTime() {
        return forecastEndTime;
    }

    public void setForecastEndTime(Integer forecastEndTime) {
        this.forecastEndTime = forecastEndTime;
    }

    public Long getAlarmLevelId() {
        return alarmLevelId;
    }

    public void setAlarmLevelId(Long alarmLevelId) {
        this.alarmLevelId = alarmLevelId;
    }
}
