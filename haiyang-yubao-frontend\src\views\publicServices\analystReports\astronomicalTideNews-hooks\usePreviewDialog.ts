import { useMessage } from 'naive-ui'
import { computed, ref } from 'vue'
import CommenService from 'src/requests/commenService'
import { download } from 'src/utils/util'


export function usePreviewDialog(opt: {
  idGetter: () => string | null
}) {
  const message = useMessage()
  const dialogVisible = ref(false)
  const dialogLoading = ref(false)
  const onlyOfficeObject = ref<{
    wordUrl: string
    callbackUrl: string
  }>({ callbackUrl: '', wordUrl: '' })
  const cptHasWord = computed(
    () => onlyOfficeObject.value.wordUrl && onlyOfficeObject.value.callbackUrl
  )

  function onDownloadClicked() {
    if (!opt.idGetter()) {
      message.warning('请保存后再查看文档')
      return
    }
    // dialogVisible.value = true
    download()
  }

  function download() {
    const id = opt.idGetter()
    if (!id) {
      message.warning('无法获取 id')
      return
    }
    const url = CommenService.downloadAstronomicalTideUrl({ id })
    window.open(url)
  }

  return {
    dialogVisible,
    onDownloadClicked,
    onlyOfficeObject,
    cptHasWord,
    dialogLoading
  }
}
