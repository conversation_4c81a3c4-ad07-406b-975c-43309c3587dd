import { DataTableColumns } from 'naive-ui'
import type { IBuoyTableData } from './type'
import { Ref, ref, watch } from 'vue'
import VectorSource from 'ol/source/Vector'
import { Feature } from 'ol'
import { Style } from 'ol/style'

export function useBuoyTable(opt: { vectorSourceGetter: () => VectorSource }) {
  const buoyData = ref<IBuoyTableData[]>([])
  const buoyCheckedRowKeys = ref<string[]>([])
  const buoyRowKey: keyof IBuoyTableData = 'buoyStationCode'

  watch(buoyCheckedRowKeys, val => {
    const source = opt.vectorSourceGetter()
    source.getFeatures().forEach((feature: Feature) => {
      const properties = feature.getProperties()
      if (val.find(i => i === properties[buoyRowKey])) {
        feature.setStyle(properties.$style)
      } else {
        feature.setStyle(emptyStyle())
      }
    })
  })
  return {
    buoyColumns: getBuoyColumns(),
    buoyData,
    buoyCheckedRowKeys,
    rowKeyGetter: (row: IBuoyTableData) => row[buoyRowKey],
    checkAllBuoy: () =>
      checkAll({
        buoyData,
        buoyCheckedRowKeys,
        buoyRowKey
      })
  }
}

function getBuoyColumns(): DataTableColumns<IBuoyTableData> {
  return [
    {
      type: 'selection'
    },
    {
      title: '站位名称',
      key: 'buoyStationName'
    },
    {
      title: '站位编号',
      key: 'buoyStationCode'
    }
  ]
}

function checkAll(opt: {
  buoyData: Ref<IBuoyTableData[]>
  buoyCheckedRowKeys: Ref<string[]>
  buoyRowKey: keyof IBuoyTableData
}) {
  const arr: string[] = []
  opt.buoyData.value.forEach((i: IBuoyTableData) => {
    const item = i[opt.buoyRowKey]
    if (item) {
      arr.push(item)
    }
  })
  opt.buoyCheckedRowKeys.value = arr
}

function emptyStyle() {
  return new Style({
    stroke: void 0,
    fill: void 0
  })
}
