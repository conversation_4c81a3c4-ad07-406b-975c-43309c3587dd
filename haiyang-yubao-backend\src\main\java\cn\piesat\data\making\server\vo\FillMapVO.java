package cn.piesat.data.making.server.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 海洋站、浮标站、船舶填图VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FillMapVO<T> implements Serializable {

    /**
     * 时间列表
     **/
    private List<Date> timeList;
    /**
     * k:时间 v:数据
     **/
    private Map<Date, List<T>> timeAndDataMap;
}



