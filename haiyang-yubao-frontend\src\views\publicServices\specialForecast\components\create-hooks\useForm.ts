import { Form } from 'src/views/publicServices/specialForecast/components/create-hooks/useForm.type'
import { reactive, type Reactive } from 'vue'

export function useForm() {
  const form = reactive({ ...defaultForm() })

  return {
    form,
    defaultForm,
    resetForm: () => resetForm(form),
    rules: formRules()
  }
}

/**
 * 重置表单
 * @param form
 */
function resetForm(form: Reactive<Form>) {
  Object.assign(form, defaultForm())
}

/**
 * 默认表单
 */
function defaultForm(): Form {
  return {
    productName: '',
    sendUnitName: '海南省海洋监测预报中心',
    sourceCode: 'grid',
    elementName: null,
    forecastTimeS: null,
    forecastTimeE: null,
    startTime: null,
    forecastArea: { type: 'FeatureCollection', features: [] },
    forecastAreaCustomize: [],
    forecastNcPath: [],
    timeType: 'H',
    imagePath: '',
    driftingImagePath: ''
  }
}

function formRules() {
  return {
    productName: {
      required: true,
      message: '请输入产品名称',
      trigger: 'blur'
    },
    sendUnitName: {
      required: true,
      message: '请输入发往单位名称',
      trigger: 'blur'
    },
    elementName: {
      required: true,
      trigger: ['change'],
      message: '请选择要素'
    },
    areaName: {
      required: true,
      message: '请输入区域名称',
      trigger: 'blur'
    },
    startTime: {
      required: true,
      message: '请选择起报时间',
      trigger: ['change', 'blur']
    },
    forecastTimeS: {
      required: true,
      message: '请选择预报范围开始时间',
      trigger: 'change'
    },
    forecastTimeE: {
      required: true,
      message: '请选择预报范围结束时间',
      trigger: 'change'
    }
  }
}
