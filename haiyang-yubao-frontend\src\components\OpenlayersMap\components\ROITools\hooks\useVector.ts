import VectorSource from 'ol/source/Vector'
import VectorLayer from 'ol/layer/Vector'
import Style from 'ol/style/Style'
import Color from 'color'
import { ActionType, type IROIOptions } from './useROIOptions'
import { type IGetMapFunction } from './types'
import { Fill, Stroke, Text } from 'ol/style'
import { inject, onMounted, onUnmounted, provide, Ref, watch } from 'vue'
import Select from 'ol/interaction/Select'
import { click } from 'ol/events/condition'
import { FeatureLike } from 'ol/Feature'
import { useROIInject } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIInject'
import { Point, Polygon, SimpleGeometry } from 'ol/geom'

/**
 * 落区矢量 hook
 */
export function useVector(options: {
  currentAction: Ref<ActionType>
  roiOptions: Ref<IROIOptions>
}) {
  const getMapInject = inject<IGetMapFunction>('getMap')
  if (!getMapInject) {
    throw new Error('注入地图失败')
  }
  // 落区工具矢量
  const vectorSource = new VectorSource<SimpleGeometry>({
    useSpatialIndex: false
  })
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    zIndex: 5,
    style: roiStyle({ roiOptions: options.roiOptions }),
    properties: {
      name: 'lqgj',
      canRedo: true
    }
  })
  // 选择交互
  const selectInteraction = new Select({
    layers: [vectorLayer],
    condition: click,
    style: selectStyle
  })
  // 相交点数据源
  const intersectPointSource = new VectorSource<Point>()
  const intersectPointLayer = new VectorLayer({
    source: intersectPointSource,
    declutter: true, //防止覆盖
    zIndex: 11
  })
  useROIInject('vectorLayer').provideROI(vectorLayer)
  useROIInject('vectorSource').provideROI(vectorSource)
  useROIInject('selectInteraction').provideROI(selectInteraction)
  useROIInject('intersectPointLayer').provideROI(intersectPointLayer)
  onMounted(() => {
    vectorSource.clear()
    selectInteraction.setActive(true)
    getMapInject(map => {
      // vectorLayer.setMap(map)
      // intersectPointLayer.setMap(map)
      map.addLayer(vectorLayer)
      map.addLayer(intersectPointLayer)
    })
  })

  onUnmounted(() => {
    getMapInject(map => {
      map.removeLayer(vectorLayer)
      map.removeLayer(intersectPointLayer)
    })
  })

  watch(options.currentAction, val => {
    selectInteraction.setActive(val === ActionType.NONE)
  })
  return {
    vectorLayer,
    vectorSource,
    roiStyle: () => roiStyle({ roiOptions: options.roiOptions }),
    selectInteraction
  }
}

/**
 * 获取落区样式
 */
export function roiStyle(opt: { roiOptions: Ref<IROIOptions> }) {
  const { roiOptions } = opt
  return new Style({
    stroke: new Stroke({
      color: roiOptions.value.strokeColor,
      width: roiOptions.value.strokeWidth
    }),
    fill: new Fill({
      color: Color(roiOptions.value.fillColor)
        .alpha(roiOptions.value.opacity / 100)
        .string()
    })
  })
}

/**
 * 设置选中要素的样式
 * @param feature
 */
export function selectStyle(feature: FeatureLike) {
  return new Style({
    fill: new Fill({
      color: feature.get('COLOR') || 'rgba(0, 0, 255, 0.2)'
    }),
    stroke: new Stroke({
      color: 'rgba(0, 0, 255, 0.7)',
      width: 2,
      lineDash: [5, 10] // 设置虚线样式
    })
  })
}
