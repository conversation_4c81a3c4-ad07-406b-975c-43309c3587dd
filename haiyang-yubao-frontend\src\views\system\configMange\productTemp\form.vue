<template>
  <div class="form-container">
    <n-form
      ref="form"
      :model="dialogForm"
      label-placement="left"
      label-width="auto"
      require-mark-placement="left"
      :rules="rules"
      :inline="props.inline"
    >
      <n-form-item label="模板名称" path="name">
        <n-input
          placeholder="请输入"
          clearable
          v-model:value="dialogForm.name"
        />
      </n-form-item>
      <n-form-item label="模板编码" path="code">
        <n-input
          placeholder="格式：F_FDRW_S4_WORD_xxxx"
          clearable
          v-model:value="dialogForm.code"
        />
      </n-form-item>
      <n-form-item label="模板类型" path="fileType">
        <n-radio-group
          v-model:value="dialogForm.fileType"
          name="radiogroup"
          @update:value="changeFileType"
        >
          <n-radio value=".txt">TXT</n-radio>
          <n-radio value=".docx">WORD</n-radio>
          <n-radio value=".xml">XML</n-radio>
          <n-radio value=".html">HTML</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item label="上传模板" path="fileUrl">
        <div class="file-name" v-if="fileName">
          <span>{{ fileName }}</span>
          <Close class="icon-close" @click="fileName = ''" />
        </div>
        <template v-else>
          <n-upload
            action=""
            @before-upload="onBeforeUpload"
            @change="onUpload"
            :default-upload="false"
            :max="1"
            :show-file-list="false"
          >
            <qx-button class="primary" size="small">上传文件</qx-button>
          </n-upload>
        </template>
      </n-form-item>
      <n-form-item label="关联预报单模板">
        <n-tree-select
          multiple
          :options="forecastTempList"
          :value="dialogForm.relationTemplateCode"
          label-field="name"
          key-field="code"
          children-field="forecastTemplateList"
          clearable
          @update:value="changeForecastTemp"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { ref, reactive, onMounted, watch, PropType } from 'vue'
import type { FormInst } from 'naive-ui'
import { useMessage } from 'naive-ui'
import Api from 'src/requests/forecast'
import { Close } from '@vicons/ionicons5'

const message = useMessage()
const props = defineProps({
  inline: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object as PropType<FormType>,
    default: () => ({
      name: '',
      fileType: '.txt',
      fileUrl: '',
      sort: 1,
      code: '',
      relationTemplateCode: []
    })
  }
})
let fileName = ref<string>('')

type FormType = {
  [propsName: string]: any
}

let dialogForm: FormType = reactive({
  name: '',
  fileType: '.txt',
  fileUrl: '',
  sort: 1,
  code: '',
  relationTemplateCode: []
})
const rules = {
  name: {
    required: true,
    message: '模板名称不能为空',
    trigger: 'blur'
  },
  code: {
    required: true,
    message: '模板编码不能为空',
    trigger: 'blur'
  },
  fileUrl: {
    required: true,
    message: '模板不能为空',
    trigger: 'change'
  }
}
let forecastTempList = ref<any[]>([])
const form = ref<FormInst | null>(null)

// 获取预报单模板
function getForecastTemp() {
  Api.getForecastTemp({ status: true })
    .then((res: any) => {
      if (res && res.length) {
        forecastTempList.value = res
      }
    })
    .catch(e => {
      let { msg = '' } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
}
function changeForecastTemp(val: any) {
  dialogForm.relationTemplateCode = val
}

function onBeforeUpload(file: any) {
  const { type = '' } = file.file
  console.log(type,'type')

  // if (type.indexOf(dialogForm.fileType) === -1) {
  //   message.error('文档格式错误')
  //   return false
  // }
  return true
}
function onUpload({ file }: any) {
  let formdata = new FormData()
  formdata.append('multipartFile', file.file)

  Api.uploadProductTemp(formdata)
    .then((res: any) => {
      dialogForm.fileUrl = res
      message.success('上传成功')
      fileName.value = file.file.name
    })
    .catch(e => {
      let { msg } = e?.response?.data
      message.error(msg || '上传失败')
    })
    .finally(() => {
      form.value?.restoreValidation()
    })
}

function changeFileType(val:string){
  console.log(val,'val=----')
}


defineExpose({
  dialogForm,
  form
})

onMounted(() => {
  getForecastTemp()
})

watch(
  () => props.info,
  val => {
    Object.keys(val).forEach((item: any) => {
      if (dialogForm.hasOwnProperty(item)) {
        dialogForm[item] = val[item]
      }
    })
    dialogForm.relationTemplateCode = val.relationTemplateCode.split(',')
    let file = dialogForm.fileUrl?.split('/')
    fileName.value = file[file.length - 1]
  }
)
</script>

<style lang="scss">
.form-container {
  .n-form-item-feedback-wrapper {
    min-height: 14px;
  }
  .file-name {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    display: flex;
    align-items: center;
    .icon-close {
      margin-left: 20px;
      width: 14px;
      height: 14px;
      cursor: pointer;
    }
  }
}
</style>
