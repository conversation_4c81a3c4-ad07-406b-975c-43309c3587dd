<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-04-09 16:10:57
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-16 16:08:28
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/createText.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-show="visible" class="create-text-dialog">
    <div class="create-text-title">
      <h3 class="title-name">文本标注</h3>
      <Close class="icon-close" @click="onClose" />
    </div>
    <keep-alive>
      <WZBZ :is-move="true" />
    </keep-alive>
  </div>
</template>

<script setup lang="ts">
import { Close } from '@vicons/ionicons5'
import WZBZ from 'src/components/OpenlayersMap/components/WZBZ.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])
function onClose() {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.create-text-dialog {
  width: 300px;
  position: absolute;
  top: 10px;
  left: 45px;
  z-index: 4;
  background: #fff;
  .create-text-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 60px;
    padding: 0 21px 0 34px;
    background: url(src/assets/images/common/dialog-header-bg1.png) no-repeat;
    background-size: 100% 100%;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #104cc0;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-top: -12px;
      position: relative;
      &::before {
        content: '';
        display: inline-block;
        width: 15.4px;
        height: 10.4px;
        background: url(src/assets/images/common/dialog-header-bg-icon.png)
          no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: -23px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .icon-close {
      cursor: pointer;
      width: 18px;
      height: 18px;
      margin-bottom: 15px;
    }
  }
  :deep(.tools-container) {
    height: auto;
    margin: 0;
    background: transparent;
    border: none;
  }
  :deep(.query-bottom) {
    height: auto;
    box-sizing: border-box;
    padding: 12px;
    & > .my-btn:nth-child(1) {
      width: 195px !important;
    }
  }
}
</style>
