package cn.piesat.data.making.server.dto;


import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * DTO类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:38
 */
public class FmGraphicToolsDTO implements Serializable {

    private static final long serialVersionUID = 595678755470905445L;

    public interface Save {
    }

    private Long id;
    private String type;
    private String name;
    private String context;
    private String url;
    private String activeUrl;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getActiveUrl() {
        return activeUrl;
    }

    public void setActiveUrl(String activeUrl) {
        this.activeUrl = activeUrl;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
