package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.StationVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 站点表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/station")
public class StationController {

    @Resource
    private StationService stationService;

    /**
     * 查询站点分页
     *
     * @param stationTypeCode 站点类型编码：海洋站oceanStation 浮标站buoyStation 船舶ship
     * @param type            类型：潮位tide 实况观测liveObservation 3米浮标站3m 10米浮标站10m 波浪谱浮标站waveSpectrum
     * @param pageNum         页数
     * @param pageSize        条数
     * @return
     */
    @GetMapping("/page")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点管理", operateType = OperateType.SELECT)
    public PageResult<StationVO> getPage(@RequestParam(required = false) String stationTypeCode, @RequestParam(required = false) String type,
                                         @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) {
        StationDTO dto = new StationDTO();
        dto.setStationTypeCode(stationTypeCode);
        dto.setType(type);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return stationService.getPage(dto, pageParam);
    }

    /**
     * 根据名称查询站点列表
     *
     * @param stationTypeCode    站点类型编码：海洋站oceanStation 浮标站buoyStation 船舶ship
     * @param type               类型：潮位tide 实况观测liveObservation 3米浮标站3m 10米浮标站10m 波浪谱浮标站waveSpectrum
     * @param isAstronomicalTide 是否过滤天文大潮站点
     * @param name               名称
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点管理", operateType = OperateType.SELECT)
    public List<StationVO> getList(@RequestParam(required = false) String stationTypeCode, @RequestParam(required = false) String type,
                                   @RequestParam(required = false) Boolean isAstronomicalTide, @RequestParam(required = false) String name) {
        StationDTO dto = new StationDTO();
        dto.setStationTypeCode(stationTypeCode);
        dto.setType(type);
        dto.setName(name);
        dto.setIsAstronomicalTide(isAstronomicalTide);
        return stationService.getList(dto);
    }

    /**
     * 根据站点类型查询站点列表
     *
     * @param stationTypeCode 站点类型编码：海洋站oceanStation 浮标站buoyStation 船舶ship
     * @return
     */
    @GetMapping("/listByStationTypeCode")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点管理", operateType = OperateType.SELECT)
    public List<StationVO> getListByStationTypeCode(@RequestParam(required = true) String stationTypeCode){
        StationDTO dto = new StationDTO();
        dto.setStationTypeCode("oceanStation");
        return stationService.getListByStationTypeCode(dto);
    }

    /**
     * 保存站点
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点管理", operateType = OperateType.INSERT)
    public Long save(@Validated(value = {StationDTO.Save.class}) @RequestBody StationDTO dto) {
        return stationService.save(dto);
    }

    /**
     * 根据站点id删除站点
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        stationService.deleteById(id);
    }

    /**
     * 批量导入站点
     *
     * @param file
     * @return
     */
    @PostMapping("/upload")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点管理", operateType = OperateType.INSERT)
    public void upload(@RequestParam("file") MultipartFile file) {
        stationService.upload(file);
    }

    /**
     * 同步关联站点字段
     *
     * @return
     */
    @GetMapping("/syncRelationStation/{stationTypeCode}")
    public void syncRelationStation(@PathVariable String stationTypeCode) {
        stationService.syncRelationStation(stationTypeCode);
    }
}

