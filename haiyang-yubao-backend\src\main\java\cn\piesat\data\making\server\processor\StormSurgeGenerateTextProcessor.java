package cn.piesat.data.making.server.processor;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.dto.GenerateWordDTO;
import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import cn.piesat.data.making.server.entity.AlarmLevel;
import cn.piesat.data.making.server.entity.AlarmProductTemplate;
import cn.piesat.data.making.server.entity.FmTyphoonB;
import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.enums.AlarmTemplateCodeEnum;
import cn.piesat.data.making.server.service.AlarmLevelService;
import cn.piesat.data.making.server.service.AlarmProductTemplateService;
import cn.piesat.data.making.server.service.FmTyphoonBService;
import cn.piesat.data.making.server.service.FmTyphoonRealBService;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.google.common.base.Preconditions;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class StormSurgeGenerateTextProcessor extends DefaultGenerateTextProcessor{

    @Autowired
    private AlarmProductTemplateService alarmProductTemplateService;
    @Autowired
    private AlarmLevelService alarmLevelService;
    @Autowired
    private FmTyphoonBService fmTyphoonBService;
    @Autowired
    private FmTyphoonRealBService fmTyphoonRealBService;

    @Override
    protected Map<String, Object> getParam(GenerateWordDTO generateWordDTO) throws Exception {
        StormSurgeGenerateText generateText = (StormSurgeGenerateText) generateWordDTO;
        //计算预报时间
        LocalTime localTime = generateText.getReleaseTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
        String timePeriod = getTimePeriod(localTime);

        FmTyphoonB fmTyphoonB = null;
        FmTyphoonRealB typhoonRealB = null;
        if(GenerateWordDTO.TYPHOON.equals(generateText.getSourceType()) || GenerateWordDTO.BOTH.equals(generateText.getSourceType())){
            //查询台风
            fmTyphoonB = fmTyphoonBService.getInfo(generateWordDTO.getTyphoonNo());
            typhoonRealB = fmTyphoonRealBService.getInfo(generateWordDTO.getTyphoonNo(), generateWordDTO.getTyphoonTime());
        }


        List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> stationWarning = generateText.getStationWarning();
        stationWarning = stationWarning.stream().filter(level -> level.getWarnHeight() != null ).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(stationWarning)) throw new BusinessException("站点预报数据为空或存在异常数据");

        Map<String, String> regionLevel = stationWarning.stream()
                .collect(Collectors.toMap(
                        StormSurgeAlarmDTO.TideDailyWarnLevelDTO::getRegionName,
                        StormSurgeAlarmDTO.TideDailyWarnLevelDTO::getLevelDesc,
                        (existing, replacement) -> existing // 冲突时保留第一个
                ));

        AlarmLevel alarmLevel = Optional.ofNullable(alarmLevelService.getById(generateText.getAlarmLevelId())).orElseThrow(()-> new BusinessException("警报级比不能为空!"));

        Map<String,Object> param = new HashMap<>();
        param.put("releaseTime",DateUtil.format(generateWordDTO.getReleaseTime(),"yyyy年MM月dd日HH时"));
        param.put("timePeriod",timePeriod);
        param.put("typhoonName",typhoonRealB==null?null:fmTyphoonB.getName());
        param.put("typhoonNo",generateWordDTO.getTyphoonNo());
        param.put("typhoonStrong",typhoonRealB==null?null:typhoonRealB.getStrong());
        param.put("forecastSurge",generateText.getForecastSurge());
        param.put("stationWarning",stationWarning);
        param.put("regionLevel",regionLevel);
        param.put("alarmLevel",alarmLevel);
        param.put("sourceType",generateText.getSourceType());
        return param;
    }

    @Override
    public GenerateTextVO generate(GenerateWordDTO generateWordDTO, boolean isAlarm) {
        try {
            Map<String, Object> param = this.getParam(generateWordDTO);
            AlarmProductTemplate contentTemplate;
            AlarmProductTemplate smsTemplate;
            if(isAlarm){
                //查询内容、短信模板
                contentTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.StormSurgeAlarmContent);
                smsTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.StormSurgeSMS);
            }else {
                contentTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.StormSurgeMSGAlarmContent);
                smsTemplate = alarmProductTemplateService.selectByCode(AlarmTemplateCodeEnum.StormSurgeMSGSMS);
            }
            Preconditions.checkArgument(contentTemplate!=null,"内容模板为空");
            Preconditions.checkArgument(smsTemplate!=null,"短信模板为空");
            return this.templateReplace(param,contentTemplate.getTemplateContent(),smsTemplate.getTemplateContent());
        } catch (Exception e) {
            logger.error("文字生成失败",e);
            throw new BusinessException("文字生成异常："+e.getMessage());
        }
    }


    public static String getTimePeriod(LocalTime time) {
        if (time.isBefore(LocalTime.of(13, 0, 0))) {
            return "今天中午到明天中午";
        } else {
            return "今天夜间到明天白天";
        }
    }
}
