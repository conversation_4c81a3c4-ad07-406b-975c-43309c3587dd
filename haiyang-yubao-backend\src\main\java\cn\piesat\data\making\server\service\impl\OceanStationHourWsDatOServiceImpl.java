package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourWsDatODao;
import cn.piesat.data.making.server.entity.OceanStationHourWsDatO;
import cn.piesat.data.making.server.service.OceanStationHourWsDatOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-从测量日界开始历时风速风向-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourWsDatOServiceImpl extends ServiceImpl<OceanStationHourWsDatODao, OceanStationHourWsDatO>
        implements OceanStationHourWsDatOService {

    @Resource
    private OceanStationHourWsDatODao oceanStationHourWsDatODao;

    @Override
    public List<OceanStationHourWsDatO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourWsDatODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourWsDatODao.getMaxCreateTime();
    }
}





