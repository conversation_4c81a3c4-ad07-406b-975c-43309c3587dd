package cn.piesat.data.making.server.dto;

import cn.piesat.webconfig.validation.ValidatedGroup;
import com.baomidou.mybatisplus.annotation.TableId;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

public class AlarmDefenseGuideDTO {
    /**
     * 主键
     */
    @NotNull(message = "主键ID不能为空",groups = {ValidatedGroup.Update.class})
    private Long id;
    /**
     * 警报类型 1 海浪 2风暴潮
     */
    @NotNull(message = "警报类型不能为空",groups = {ValidatedGroup.Save.class,ValidatedGroup.Update.class})
    private Integer alarmType;
    /**
     * 指南名称
     */
    @NotEmpty(message = "警报类型不能为空",groups = {ValidatedGroup.Save.class,ValidatedGroup.Update.class})
    private String guideName;
    /**
     * 状态
     */
    private Integer status = 0;
    /**
     * 指南等级信息
     */
    @Size(message = "指南等级信息不能为空",min = 1,groups = {ValidatedGroup.Update.class})
    @NotNull(message = "指南等级信息不能为空",groups = {ValidatedGroup.Update.class})
    private List<GuideLevelDTO> guideLevelList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(Integer alarmType) {
        this.alarmType = alarmType;
    }

    public String getGuideName() {
        return guideName;
    }

    public void setGuideName(String guideName) {
        this.guideName = guideName;
    }

    public List<GuideLevelDTO> getGuideLevelList() {
        return guideLevelList;
    }

    public void setGuideLevelList(List<GuideLevelDTO> guideLevelList) {
        this.guideLevelList = guideLevelList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public static class GuideLevelDTO{

        private Long id;
        /**
         * 等级ID
         */
        private Long levelId;
        /**
         * 指南内容
         */
        private String guideContent;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getLevelId() {
            return levelId;
        }

        public void setLevelId(Long levelId) {
            this.levelId = levelId;
        }

        public String getGuideContent() {
            return guideContent;
        }

        public void setGuideContent(String guideContent) {
            this.guideContent = guideContent;
        }
    }
}
