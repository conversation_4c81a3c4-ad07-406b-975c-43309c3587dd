package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicTemplateMainDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateMain;
import cn.piesat.data.making.server.vo.FmGraphicTemplateMainVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2024-10-13 09:43:29
 */
@Mapper(componentModel = "spring")
public interface FmGraphicTemplateMainMapper {

    FmGraphicTemplateMainMapper INSTANCE = Mappers.getMapper(FmGraphicTemplateMainMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicTemplateMainVO entityToVo(FmGraphicTemplateMain entity);

    /**
     * dto-->entity
     */
    FmGraphicTemplateMain dtoToEntity(FmGraphicTemplateMainDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicTemplateMainVO> entityListToVoList(List<FmGraphicTemplateMain> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicTemplateMain> dtoListToEntityList(List<FmGraphicTemplateMainDTO> dtoList);
}
