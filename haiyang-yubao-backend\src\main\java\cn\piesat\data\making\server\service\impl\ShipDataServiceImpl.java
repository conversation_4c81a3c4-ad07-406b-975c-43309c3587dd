package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.ShipDataDao;
import cn.piesat.data.making.server.dto.ShipDataDTO;
import cn.piesat.data.making.server.entity.ShipData;
import cn.piesat.data.making.server.model.ShipDataInfo;
import cn.piesat.data.making.server.service.ShipDataService;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.ShipDataVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 船舶表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShipDataServiceImpl extends ServiceImpl<ShipDataDao, ShipData> implements ShipDataService {

    @Resource
    private ShipDataDao shipDataDao;

    @Override
    public List<String> getShipList() {
        return shipDataDao.getDistinctList();
    }

    @Override
    @SneakyThrows
    public List<ShipDataVO> getList(ShipDataDTO dto) {
        String geoRange = null;
        if (StringUtils.isNotBlank(dto.getGeoRange())) {
            geoRange = GeoToolsUtil.geojsonToGeo(dto.getGeoRange());
        }
        return shipDataDao.getDataList(dto.getStartTime(), dto.getEndTime(), geoRange, dto.getSignShip());
    }

    @Override
    public FillMapVO getRangeList(ShipDataDTO dto) {
        //船舶数据
        List<ShipDataVO> dataList = this.getList(dto);
        //按照数据时间对船舶数据进行分组
        Map<Date, List<ShipDataVO>> dataMap = dataList.stream().collect(Collectors.groupingBy(ShipDataVO::getTime));
        //数据时间列表
        List<Date> timeList = dataMap.keySet().stream().collect(Collectors.toList()).stream().sorted().collect(Collectors.toList());
        FillMapVO fillMap = new FillMapVO();
        fillMap.setTimeList(timeList);
        fillMap.setTimeAndDataMap(dataMap);
        return fillMap;
    }

    @lombok.Data
    static class Wrapper {
        private Boolean success;
        private String msg;
        private Data data;
        private Object page;
        private Object attr;
    }

    @lombok.Data
    static class Data {
        private String title;
        private List<ShipDataInfo> data;
    }

    @Override
    public void readAndSaveData(String directoryPath) throws IOException {
        File directory = new File(directoryPath);
        //检查目录是否存在和是否是一个文件夹
        if (!directory.exists() || !directory.isDirectory()) {
            return;
        }
        List<ShipData> list = new ArrayList<>();
        File[] fileList = directory.listFiles();
        for (File file : fileList) {
            Preconditions.checkArgument(file.exists() || file.isFile(), "文件不存在或不是文件类型");
            log.info("开始读取文件：{}", file.getName());
            ObjectMapper mapper = new ObjectMapper();
            Wrapper wrapper = mapper.readValue(file, Wrapper.class);
            List<ShipDataInfo> dataList = wrapper.getData().getData();
            for (ShipDataInfo data : dataList) {
                ShipData shipData = new ShipData();
                shipData.setSignShip(data.getSignShip());
                shipData.setLatitude(data.getLatitude());
                shipData.setLongitude(data.getLongitude());
                shipData.setVisibility(data.getVisibility());
                shipData.setAirTemperature(data.getAirTemperature());
                shipData.setDewPointTemp(data.getDewPointTemp());
                shipData.setWindDirection(data.getWindDirection());
                shipData.setWindSpeed(data.getWindSpeed());
                shipData.setAirPressure(data.getAirPressure());
                shipData.setTotalCloudAmount(data.getTotalCloudAmount());
                shipData.setLowCloudAmount(data.getLowCloudAmount());
                shipData.setSeaTemp(data.getSeaTemp());
                shipData.setWavePeriod(data.getWavePeriod());
                shipData.setWaveHeight(data.getWaveHeight());
                shipData.setSurgeDirection(data.getSurgeDirection());
                shipData.setSurgePeriod(data.getSurgePeriod());
                shipData.setSurgeHeight(data.getSurgeHeight());
                String geoJson = GeoToolsUtil.arrToGeojson(new double[][]{{data.getLongitude(), data.getLatitude()}});
                shipData.setLocationGeo(geoJson);
                shipData.setLocationJson(geoJson);
                int year = Integer.parseInt(data.getYear());
                int month = Integer.parseInt(data.getMonth());
                int day = Integer.parseInt(data.getDay());
                int hour = Integer.parseInt(data.getHour());
                if (hour > 23 || hour < 0) {
                    continue;
                }
                LocalDateTime localDateTime = LocalDateTime.of(year, month, day, hour, 0);
                Date dateTime = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
                shipData.setTime(dateTime);
                list.add(shipData);
            }
            log.info("读取文件结束：{}，数据条数：{}", file.getName(), dataList.size());
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.saveBatch(list);
        }
    }
}





