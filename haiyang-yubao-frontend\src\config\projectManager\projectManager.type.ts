import {
  IExternalProperties,
  IFeatureCollection,
  IInternalProperties, IProperties
} from '../featureManager/featureManager.type'
import { instanceToPlain, plainToInstance } from 'class-transformer'

export interface IProjectModel {
  name: string
  properties: IProperties
  collection: IFeatureCollection
}

/**
 * 工程文件基类
 */
export class ProjectModel implements IProjectModel {
  protected _collection: IFeatureCollection = {
    type: 'FeatureCollection',
    features: []
  }
  protected _name = 'Project'
  protected _properties: IProperties = {
    $type: 'Project',
    $version: 'v1'
  }

  /**
   * 普通对象转实例
   * @param plainObject
   */
  static fromPlain(plainObject: IProjectModel): ProjectModel {
    return plainToInstance(ProjectModel, plainObject)
  }

  /**
   * 实例转普通对象
   */
  toPlainObject(): IProjectModel {
    return instanceToPlain(this) as IProjectModel
  }

  getInternalProperties(): IInternalProperties {
    return {
      $type: this._properties.$type,
      $version: this._properties.$version
    }
  }

  getExternalProperties(): IExternalProperties {
    const ownKeys = Reflect.ownKeys(this._properties) as string[]
    const externalProperties = {}
    for (const key of ownKeys) {
      if (!key.startsWith('$')) {
        Reflect.set(externalProperties, key, this._properties[key])
      }
    }
    return externalProperties
  }

  get collection(): IFeatureCollection {
    return this._collection
  }

  set collection(value: IFeatureCollection) {
    this._collection = value
  }

  get name(): string {
    return this._name
  }

  set name(value: string) {
    this._name = value
  }

  get properties(): IProperties {
    return this._properties
  }

  set properties(value: IProperties) {
    this._properties = value
  }
}
