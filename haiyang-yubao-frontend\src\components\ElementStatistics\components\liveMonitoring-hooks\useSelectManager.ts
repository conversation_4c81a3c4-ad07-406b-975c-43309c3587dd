import { DragBox, Select } from 'ol/interaction'
import Map from 'ol/Map'
import VectorSource from 'ol/source/Vector'
import { SimpleGeometry } from 'ol/geom'
import VectorLayer from 'ol/layer/Vector'
import { platformModifierKeyOnly } from 'ol/events/condition'
import { SelectEvent } from 'ol/interaction/Select'
import { DragBoxEvent } from 'ol/interaction/DragBox'
import { getWidth } from 'ol/extent'
import { MaybeRefOrGetter, onUnmounted, ref, toValue } from 'vue'
import Api from 'src/requests/toolRequest'
import Collection from 'ol/Collection.js'
import { Feature } from 'ol'

export function useSelectManager(opt: {
  map: MaybeRefOrGetter<Map>
  vectorSource: VectorSource<SimpleGeometry>
  vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
  onFinish: (collection: Collection<Feature<SimpleGeometry>>) => void
}) {
  const selectStart = ref(false)
  let selectObject: SelectManager | null = null

  onUnmounted(() => {
    selectObject?.close()
    selectObject = null
  })

  function startOrFinish() {
    if (selectObject) {
      selectObject.close()
      selectStart.value = false
      selectObject = null
      return
    }
    const mapValue = toValue(opt.map)
    selectObject = new SelectManager({
      map: mapValue,
      vectorLayer: opt.vectorLayer,
      vectorSource: opt.vectorSource,
      onFinish: opt.onFinish
    })
    selectObject.start()
    selectStart.value = true
  }

  return {
    startOrFinish,
    selectStart
  }
}

class SelectManager {
  protected select: Select | null = null
  dragBox: DragBox | null = null

  constructor(
    protected opt: {
      map: Map
      vectorSource: VectorSource<SimpleGeometry>
      vectorLayer: VectorLayer<VectorSource<SimpleGeometry>>
      onFinish: (collection: Collection<Feature<SimpleGeometry>>) => void
    }
  ) {}

  start() {
    this.initSelect()
    this.initDragBox()
  }

  initSelect() {
    this.select = new Select({
      layers: [this.opt.vectorLayer],
      style: null
    })
    this.select.setActive(true)
    this.opt.map.addInteraction(this.select)
    this.select.on('select', this.onSelectEnd.bind(this))
  }

  initDragBox() {
    this.dragBox = new DragBox()
    this.opt.map.addInteraction(this.dragBox)
    this.dragBox.on('boxstart', (e: DragBoxEvent) => {
      const collection = this.select!.getFeatures()
      if (collection.getLength() !== 0) {
        collection.clear()
      }
    })
    this.dragBox.on('boxend', this.onDragBoxEnd.bind(this))
  }

  onSelectEnd(e: SelectEvent) {
    // const selected = e.selected
  }

  /**
   * box选择结束
   * @see {https://openlayers.org/en/latest/examples/box-selection.html} Box Selection
   * @param e
   */
  onDragBoxEnd(e: DragBoxEvent) {
    if (!this.dragBox || !this.select) {
      return
    }
    const selectedFeatures = this.select.getFeatures()
    const boxExtent = this.dragBox.getGeometry().getExtent()

    // if the extent crosses the antimeridian process each world separately
    const worldExtent = this.opt.map.getView().getProjection().getExtent()
    const worldWidth = getWidth(worldExtent)
    const startWorld = Math.floor((boxExtent[0] - worldExtent[0]) / worldWidth)
    const endWorld = Math.floor((boxExtent[2] - worldExtent[0]) / worldWidth)

    for (let world = startWorld; world <= endWorld; ++world) {
      const left = Math.max(boxExtent[0] - world * worldWidth, worldExtent[0])
      const right = Math.min(boxExtent[2] - world * worldWidth, worldExtent[2])
      const extent = [left, boxExtent[1], right, boxExtent[3]]

      const boxFeatures = this.opt.vectorSource
        .getFeaturesInExtent(extent)
        .filter(
          feature =>
            !selectedFeatures.getArray().includes(feature) &&
            feature.getGeometry()?.intersectsExtent(extent)
        )

      const rotation = this.opt.map.getView().getRotation()
      const oblique = rotation % (Math.PI / 2) !== 0

      if (oblique) {
        const anchor = [0, 0]
        const geometry = this.dragBox.getGeometry().clone()
        geometry.translate(-world * worldWidth, 0)
        geometry.rotate(-rotation, anchor)
        const extent = geometry.getExtent()
        boxFeatures.forEach(feature => {
          const geom = feature.getGeometry()
          if (!geom) {
            return
          }
          const geometry = geom.clone()
          geometry.rotate(-rotation, anchor)
          if (geometry.intersectsExtent(extent)) {
            selectedFeatures.push(feature)
          }
        })
      } else {
        selectedFeatures.extend(boxFeatures)
      }
    }
    this.opt.onFinish(selectedFeatures as Collection<Feature<SimpleGeometry>>)
  }

  /**
   * 获取所有选中站点的属性对象
   */
  getSelectedProperties() {
    const selectedFeatures = this.select!.getFeatures()
    return selectedFeatures.getArray().map(feature => feature.getProperties())
  }

  /**
   * 获取所有浮标站数据
   */
  async getBuoyStationData() {
    const propertiesArr = this.getSelectedProperties()
    for (let i = 0; i < propertiesArr.length; i++) {
      const res = await Api.getBuoyStationStatistics({
        buoyStationCodeList: [],
        element: 'puPbg',
        endTime: '',
        relationStation: '',
        startTime: '',
        type: ''
      })
    }
  }

  close() {
    this.select && this.opt.map.removeInteraction(this.select)
    this.dragBox && this.opt.map.removeInteraction(this.dragBox)
    this.select = null
    this.dragBox = null
  }
}
