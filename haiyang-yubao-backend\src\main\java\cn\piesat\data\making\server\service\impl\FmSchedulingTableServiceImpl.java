package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmSchedulingMainTableDao;
import cn.piesat.data.making.server.dao.FmSchedulingUserDao;
import cn.piesat.data.making.server.dto.SchedulingAddDTO;
import cn.piesat.data.making.server.dto.SchedulingExcelDto;
import cn.piesat.data.making.server.dto.UserInfoDTO;
import cn.piesat.data.making.server.entity.FmSchedulingMainTable;
import cn.piesat.data.making.server.entity.FmSchedulingTable;
import cn.piesat.data.making.server.dto.FmSchedulingTableDTO;
import cn.piesat.data.making.server.entity.FmSchedulingUser;
import cn.piesat.data.making.server.entity.SchedulingTable;
import cn.piesat.security.ucenter.starter.utils.UserUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.vo.FmSchedulingTableVO;
import cn.piesat.data.making.server.dao.FmSchedulingTableDao;
import cn.piesat.data.making.server.service.FmSchedulingTableService;
import cn.piesat.data.making.server.mapper.FmSchedulingTableMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.list.AbstractListDecorator;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:05
 */
@Service("fmSchedulingTableService")
public class FmSchedulingTableServiceImpl extends ServiceImpl<FmSchedulingTableDao, FmSchedulingTable> implements FmSchedulingTableService {

    @Resource
    private FmSchedulingTableDao fmSchedulingTableDao;
    @Resource
    private FmSchedulingMainTableDao fmSchedulingMainTableDao;
    @Resource
    private FmSchedulingUserDao fmSchedulingUserDao;
    @Value(("${excelUrl}"))
    private String excelUrl;

    @Override
    public PageResult<FmSchedulingTableVO> getPage(FmSchedulingTableDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmSchedulingTable> getList(FmSchedulingTableDTO dto) {
        List<FmSchedulingUser> fmSchedulingUsers = fmSchedulingUserDao.selectAll();
        FmSchedulingTable fmSchedulingTable = new FmSchedulingTable();
        //获取该月份第一天
        Date firstDayOfMonth = getFirstDayOfMonth(dto.getSchedulingDate());
        Date lastDayOfMonth = getLastDayOfMonth(dto.getSchedulingDate());
        FmSchedulingMainTable byDate = fmSchedulingMainTableDao.findByDate(firstDayOfMonth, lastDayOfMonth);
        if(byDate == null){
            //this.schedulingTableDao.save(SchedulingTableMapper.INSTANCE.dtoToEntity(dto));
            FmSchedulingMainTable schedulingMain = new FmSchedulingMainTable();
            schedulingMain.setSchedulingDate(dto.getSchedulingDate());
            schedulingMain.setCreateTime(new Date());
//        schedulingMain.setCreateUser(UserUtils.getUserInfo().getRealName());
            fmSchedulingMainTableDao.insert(schedulingMain);
            Long mainId = schedulingMain.getId();
            Calendar calendar = Calendar.getInstance();

            //获取上个月的最后一天
            calendar.add(Calendar.MONTH, -1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            //把时分秒设置成0
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date latsMonth = calendar.getTime();

            FmSchedulingTable schedulingTableLast = fmSchedulingTableDao.finBySchedulingDate(latsMonth);

            //如果上个月最后一天的值班人 跟列表的第一个人相同 重新排序列表保证排班的第一个人 与上个月最后一个人 不是同一个人
            if(schedulingTableLast != null && schedulingTableLast.getUserName().equals(fmSchedulingUsers.get(0).getUserName())){
                Random random = new Random();
                List<FmSchedulingUser> shuffledList = new ArrayList<>(fmSchedulingUsers);
                while (shuffledList.equals(fmSchedulingUsers)) {
                    Collections.shuffle(shuffledList, random);
                }
                fmSchedulingUsers.clear();
                fmSchedulingUsers.addAll(shuffledList);
            }

            calendar.setTime(dto.getSchedulingDate());
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

            List<FmSchedulingTable> list = new ArrayList<>();
            List<Date> dateList = new ArrayList<>();
            for(int i = 1;i <= day;i++){
                calendar.set(Calendar.MONTH,month-1);
                calendar.set(Calendar.DAY_OF_MONTH,i);
                //跳过周末
                if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                    continue;
                }
                dateList.add(calendar.getTime());
            }
            int userIndex = 0;
            for (Date date : dateList) {
                FmSchedulingTable schedulingTable = new FmSchedulingTable();
                schedulingTable.setSchedulingDate(date);
                schedulingTable.setUserName(fmSchedulingUsers.get(userIndex).getUserName());
                schedulingTable.setUserId(fmSchedulingUsers.get(userIndex).getUserId());
                schedulingTable.setSchedulingMainId(mainId);
                calendar.setTime(date);
                schedulingTable.setDay(calendar.get(Calendar.DAY_OF_MONTH));
                userIndex = (userIndex + 1) % fmSchedulingUsers.size();
                list.add(schedulingTable);
            }
            List<SchedulingExcelDto> exceList = new ArrayList<>();
            for (FmSchedulingTable schedulingTable : list) {
                SchedulingExcelDto schedulingExcelDto = new SchedulingExcelDto();
                schedulingExcelDto.setUsername(schedulingTable.getUserName());
                schedulingExcelDto.setDay(schedulingTable.getDay());
                exceList.add(schedulingExcelDto);
            }
            createExcel(dateList,exceList);
            this.saveBatch(list);

            return list;
        }
        return fmSchedulingTableDao.findByMainId(byDate.getId());
    }

    @Override
    public FmSchedulingTableVO getById() {
        return null;
    }

    @Override
    public List<FmSchedulingTable> save(SchedulingAddDTO dto) {
        //        this.schedulingTableDao.save(SchedulingTableMapper.INSTANCE.dtoToEntity(dto));
        FmSchedulingMainTable schedulingMain = new FmSchedulingMainTable();
        schedulingMain.setSchedulingDate(dto.getSchedulingDate());
        schedulingMain.setCreateTime(new Date());
//        schedulingMain.setCreateUser(UserUtils.getUserInfo().getRealName());
        int mainId = fmSchedulingMainTableDao.insert(schedulingMain);
        Calendar calendar = Calendar.getInstance();

        //获取上个月的最后一天
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        //把时分秒设置成0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date latsMonth = calendar.getTime();

        List<FmSchedulingUser> userList = dto.getUserList();

        FmSchedulingTable schedulingTableLast = fmSchedulingTableDao.finBySchedulingDate(latsMonth);

        //如果上个月最后一天的值班人 跟列表的第一个人相同 重新排序列表保证排班的第一个人 与上个月最后一个人 不是同一个人
        if(schedulingTableLast.getUserName().equals(userList.get(0).getUserName())){
            Random random = new Random();
            List<FmSchedulingUser> shuffledList = new ArrayList<>(userList);
            while (shuffledList.equals(userList)) {
                Collections.shuffle(shuffledList, random);
            }
            userList.clear();
            userList.addAll(shuffledList);
        }

        calendar.setTime(dto.getSchedulingDate());
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

        List<FmSchedulingTable> list = new ArrayList<>();
        List<Date> dateList = new ArrayList<>();
        for(int i = 1;i <= day;i++){
            calendar.set(Calendar.MONTH,month-1);
            calendar.set(Calendar.DAY_OF_MONTH,i);
            //跳过周末
            if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                continue;
            }
            dateList.add(calendar.getTime());
        }
        int userIndex = 0;
        for (Date date : dateList) {
            FmSchedulingTable schedulingTable = new FmSchedulingTable();
            schedulingTable.setSchedulingDate(date);
            schedulingTable.setUserName(userList.get(userIndex).getUserName());
            schedulingTable.setUserId(userList.get(userIndex).getUserId());
            schedulingTable.setSchedulingMainId((long)mainId);
            calendar.setTime(date);
            schedulingTable.setDay(calendar.get(Calendar.DAY_OF_MONTH));
            userIndex = (userIndex + 1) % userList.size();
            list.add(schedulingTable);
        }
        List<SchedulingExcelDto> exceList = new ArrayList<>();
        for (FmSchedulingTable schedulingTable : list) {
            SchedulingExcelDto schedulingExcelDto = new SchedulingExcelDto();
            schedulingExcelDto.setUsername(schedulingTable.getUserName());
            schedulingExcelDto.setDay(schedulingTable.getDay());
            exceList.add(schedulingExcelDto);
        }
        createExcel(dateList,exceList);
        this.saveBatch(list);

        return list;
    }

    @Override
    public List<FmSchedulingTable> update(List<FmSchedulingTable> list) {
        FmSchedulingTable schedulingTable = list.get(0);

        fmSchedulingTableDao.deleteBySchedulingMainId(schedulingTable.getSchedulingMainId());
        return null;
    }

    @Override
    public int sign(FmSchedulingTable schedulingTable) {
        FmSchedulingTable fmSchedulingTable = fmSchedulingTableDao.selectById(schedulingTable.getId());
        if(schedulingTable.getSignDate() == null){
            fmSchedulingTable.setSignDate(new Date());
        }else {
            fmSchedulingTable.setSignDate(schedulingTable.getSignDate());
        }
        fmSchedulingTable.setSignUserId(schedulingTable.getSignUserId());
        fmSchedulingTable.setSignUserName(schedulingTable.getSignUserName());
        return fmSchedulingTableDao.insert(fmSchedulingTable);
    }

    @Override
    public void saveList(List<FmSchedulingTableDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }

    @Override
    public void saveAll(List<FmSchedulingTable> dto) {
        FmSchedulingMainTable fmSchedulingMainTable = fmSchedulingMainTableDao.selectById(dto.get(0).getSchedulingMainId());
        Date firstDayOfMonth = getFirstDayOfMonth(dto.get(0).getSchedulingDate());
        Date lastDayOfMonth = getLastDayOfMonth(dto.get(0).getSchedulingDate());
        FmSchedulingMainTable byDate = fmSchedulingMainTableDao.findByDate(firstDayOfMonth, lastDayOfMonth);
        Long mainId = null;
        if(byDate == null){
            FmSchedulingMainTable schedulingMain = new FmSchedulingMainTable();
            schedulingMain.setSchedulingDate(dto.get(0).getSchedulingDate());
            schedulingMain.setCreateTime(new Date());
            fmSchedulingMainTableDao.insert(schedulingMain);
            mainId = schedulingMain.getId();

        }else{
            mainId = byDate.getId();
            fmSchedulingTableDao.deleteBySchedulingMainId(mainId);
        }
        fmSchedulingTableDao.deleteBySchedulingMainId(dto.get(0).getSchedulingMainId());
        for (FmSchedulingTable fmSchedulingTable : dto) {
            fmSchedulingTable.setSchedulingMainId(mainId);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fmSchedulingTable.getSchedulingDate());
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            fmSchedulingTable.setDay(day);
            fmSchedulingTableDao.insert(fmSchedulingTable);
        }
        List<Date> dateList = new ArrayList<>();
        List<SchedulingExcelDto> list = new ArrayList<>();
        for (FmSchedulingTable fmSchedulingTable : dto) {
            SchedulingExcelDto schedulingExcelDto = new SchedulingExcelDto();
            schedulingExcelDto.setUsername(fmSchedulingTable.getUserName());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fmSchedulingTable.getSchedulingDate());
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            schedulingExcelDto.setDay(day);
            dateList.add(fmSchedulingTable.getSchedulingDate());
            list.add(schedulingExcelDto);
        }
        String excel = createExcel(dateList, list);
    }

    @Override
    public List<FmSchedulingTable> upload(MultipartFile multipartFile,Date date) {
        try{
            InputStream inputStream = multipartFile.getInputStream();
            Workbook workbook;
            if (multipartFile.getOriginalFilename().toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else {
                workbook = new HSSFWorkbook(inputStream);
            }
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            List<FmSchedulingTable> userList = new ArrayList<>();

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                FmSchedulingTable user = new FmSchedulingTable();
                Iterator<Cell> cellIterator = row.iterator();
                int cellIndex = 0;
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    switch (cell.getCellType()) {
                        case STRING:
                            if (cellIndex == 0) {
                                user.setUserName(cell.getStringCellValue());
                                FmSchedulingUser byUserName = fmSchedulingUserDao.findByUserName(cell.getStringCellValue());
                                if(byUserName == null){
                                    throw new Exception("没有用户"+cell.getStringCellValue()+"的信息！");
                                }
                                user.setUserId(byUserName.getUserId());
                            }
                            break;
                        case NUMERIC:
                            if (cellIndex == 1) {
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(date);
                                int year = calendar.get(Calendar.YEAR);
                                int month = calendar.get(Calendar.MONTH);
                                int day = (int)cell.getNumericCellValue();
                                calendar.set(year,month,(int) cell.getNumericCellValue());
                                user.setSchedulingDate(calendar.getTime());
                                user.setDay(day);
                            }
                            break;
                        default:
                            break;
                    }
                    cellIndex++;
                }
                userList.add(user);
            }

            Date firstDayOfMonth = getFirstDayOfMonth(userList.get(0).getSchedulingDate());
            Date lastDayOfMonth = getLastDayOfMonth(userList.get(0).getSchedulingDate());
            FmSchedulingMainTable byDate = fmSchedulingMainTableDao.findByDate(firstDayOfMonth, lastDayOfMonth);
            Long mainId = null;
            if(byDate == null){
                FmSchedulingMainTable schedulingMain = new FmSchedulingMainTable();
                schedulingMain.setSchedulingDate(userList.get(0).getSchedulingDate());
                schedulingMain.setCreateTime(new Date());
                fmSchedulingMainTableDao.insert(schedulingMain);
                mainId = schedulingMain.getId();

            }else{
                mainId = byDate.getId();
                fmSchedulingTableDao.deleteBySchedulingMainId(mainId);
            }
            List<FmSchedulingTable> result = new ArrayList<>();
            for (FmSchedulingTable fmSchedulingTable : userList) {
                fmSchedulingTable.setSchedulingMainId(mainId);
                result.add(fmSchedulingTable);
            }

            String fileName = multipartFile.getOriginalFilename();
            Path path = Paths.get(excelUrl + fileName);
            Files.write(path, multipartFile.getBytes());
            this.saveBatch(result);
            return result;
        }catch(Exception e){
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void download(Date date) {
        Date firstDayOfMonth = getFirstDayOfMonth(date);
        Date lastDayOfMonth = getLastDayOfMonth(date);
        FmSchedulingMainTable byDate = fmSchedulingMainTableDao.findByDate(firstDayOfMonth, lastDayOfMonth);

        List<FmSchedulingTable> byMainId = fmSchedulingTableDao.findByMainId(byDate.getId());
        List<SchedulingExcelDto> list = new ArrayList<>();
        List<Date> dateList = new ArrayList<>();
        for (FmSchedulingTable fmSchedulingTable : byMainId) {
            SchedulingExcelDto schedulingExcelDto = new SchedulingExcelDto();
            schedulingExcelDto.setUsername(fmSchedulingTable.getUserName());
            schedulingExcelDto.setDay(fmSchedulingTable.getDay());
            dateList.add(fmSchedulingTable.getSchedulingDate());
            list.add(schedulingExcelDto);
        }
        String excel = createExcel(dateList, list);

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();

        if (null == requestAttributes) {
            throw new RuntimeException("请求失败");
        }
        HttpServletResponse response = requestAttributes.getResponse();

        ServletOutputStream outputStream = null;
        InputStream inputStream = null;

        try {
            // 获取网络文件
            URL urlFile = new URL("file://" + excel);
            URLConnection conn = urlFile.openConnection();

            // 通过输入流获取图片数据
            inputStream = conn.getInputStream();

            // 文件类型
            response.setContentType("application/xlsx");
            // 设置请求头
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(new Date().getTime()+".xlsx", "UTF-8"));

            // 获取输出流
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while( (len = inputStream.read(buffer)) != -1 ){
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush();
        } catch (Exception e) {
            //log.error("【下载PDF文件失败】原因：{}", e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
                if (null != outputStream) {
                    outputStream.close();
                }
            } catch (Exception ignored) {}
        }
    }

    String createExcel(List<Date> dateList,List<SchedulingExcelDto> list){
        try{
            Date date1 = dateList.get(0);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            String fileName = excelUrl+new Date().getTime()+".xlsx";
            ExcelWriter excelWriter = EasyExcel.write(fileName).build();
            WriteSheet writeSheet =  EasyExcel.writerSheet("test").build();

            List<List<String>> data = new ArrayList<>();
            //创建第一行
            List<String> dateRowData = new ArrayList<>();
            dateRowData.add("员工姓名");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (Date date : dateList) {
                dateRowData.add(sdf.format(date));
            }
            data.add(dateRowData);

            excelWriter.write(list,writeSheet);
            excelWriter.finish();

            return fileName;
        }catch(Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static Date getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
}
