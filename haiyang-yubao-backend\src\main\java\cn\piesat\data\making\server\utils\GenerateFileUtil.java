package cn.piesat.data.making.server.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.piesat.common.utils.IdFactory;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.common.utils.MustacheUtil;
import cn.piesat.data.making.server.dto.generate.AlgorithmResultDTO;
import cn.piesat.data.making.server.dto.generate.ManualExecutionDTO;
import cn.piesat.data.making.server.fegin.AlgorithmExecClient;
import cn.piesat.webconfig.exception.BusinessException;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class GenerateFileUtil {
    private static final Logger logger = LoggerFactory.getLogger(GenerateFileUtil.class);

    public static List<AlgorithmResultDTO.Result> generateExecJson(String baseOutputPath, Date date, String ncFilePath, String areaJsonFilePath,
                                                                   String forecastTimeFilePath, AlgorithmExecClient algorithmExecClient, String scriptPath) throws IOException {
        String yyyyMMdd = DateUtil.format(date, "yyyyMMdd");
        //使用雪花ID
        String id = String.valueOf(IdFactory.getID());
        logger.info("String id = String.valueOf(IdFactory.getID());-------------------------{}",id);
        String execJsonFilePath = String.format("%s/%s/%s/%s.json",baseOutputPath+"/.logs","alarm",yyyyMMdd,id+ "_exec");
        createFile(execJsonFilePath);
        //创建result文件
        String resultJsonFile = String.format("%s/%s/%s/%s.json",baseOutputPath+"/.logs","alarm",yyyyMMdd,id+ "_result");
        createFile(resultJsonFile);
        //创建log文件
        String resultLogFile = String.format("%s/%s/%s/%s.log",baseOutputPath+"/.logs","alarm",yyyyMMdd,id);
        createFile(resultLogFile);
        //创建流程文件
        String resultFlowFile = String.format("%s/%s/%s/%s.json",baseOutputPath+"/.logs","alarm",yyyyMMdd,id + "_flow");
        createFile(resultFlowFile);
        //创建信息输出json文件
        String resultDataPath = String.format("%s/%s/%s/%s.json",baseOutputPath+"/.logs","alarm",yyyyMMdd,id + "_Data");
        createFile(resultDataPath);

        HashMap<String, Object> map = new HashMap<>();
        map.put("baseOutputPath",baseOutputPath);
        map.put("resultJsonFile",resultJsonFile);
        map.put("resultLogFile",resultLogFile);
        map.put("resultFlowFile",resultFlowFile);
        map.put("ncFilePath",ncFilePath);
        map.put("areaJsonFilePath",areaJsonFilePath);
        map.put("forecastTimeFilePath",forecastTimeFilePath);
        map.put("time",date.getTime());

        String compile = MustacheUtil.compile("mustache-template/execJson.mustache", map);
        FileUtil.writeUtf8String(compile,execJsonFilePath);

        ManualExecutionDTO dto = new ManualExecutionDTO();
        dto.setExecJsonFilePath(execJsonFilePath);
        dto.setExecScriptPath(scriptPath);
        Boolean execution = algorithmExecClient.manualExecution(dto);
        if(!execution) throw new BusinessException("算法执行异常");

        String resultJson = FileUtil.readUtf8String(resultJsonFile);
        if(StringUtils.isEmpty(resultJson)) throw new BusinessException("算法执行异常");
        AlgorithmResultDTO algorithmResultDTO = JsonUtil.json2Object(resultJson, AlgorithmResultDTO.class);
        if(!Objects.equals(0,algorithmResultDTO.getStatus())) throw new BusinessException("算法执行异常:"+algorithmResultDTO.getMessage());

//
//        String resultJson = FileUtil.readUtf8String("E:\\项目相关\\海南海灾\\测试数据\\.logs\\alarm\\20250331\\969809520629833728_result.json");
//        AlgorithmResultDTO algorithmResultDTO = JsonUtil.json2Object(resultJson, AlgorithmResultDTO.class);

        return algorithmResultDTO.getResult();
    }


    public static void createFile(String path){
        File file = new File(path);
        if (!file.exists()){
            file.getParentFile().mkdirs();
            try {
                file.createNewFile();
            } catch (IOException e) {
                // e.printStackTrace();
                logger.error("文件目录创建失败");
            }
        }
    }

    public static  List<PictureRenderData> generatePicture(String alarmImages){
        List<PictureRenderData> pictures =new ArrayList<>();
        if(StringUtils.hasText(alarmImages)){
            List<String> images = Arrays.asList(alarmImages.split(","));
            pictures.addAll(images.stream().map(image-> Pictures.ofLocal(image).size(500,500).create()).collect(Collectors.toList()));
        }
        return pictures;
    }

    public static  List<PictureRenderData> generateUrlPicture(String alarmImages){
        List<PictureRenderData> pictures =new ArrayList<>();
        if(StringUtils.hasText(alarmImages)){
            List<String> images = Arrays.asList(alarmImages.split(","));
            pictures.addAll(images.stream().map(image-> Pictures.ofUrl(image).size(500,373).create()).collect(Collectors.toList()));
        }
        return pictures;
    }
}
