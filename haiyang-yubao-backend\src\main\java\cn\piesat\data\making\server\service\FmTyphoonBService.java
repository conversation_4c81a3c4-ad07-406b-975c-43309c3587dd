package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmTyphoonBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonB;
import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import cn.piesat.data.making.server.vo.FmTyphoonScoreVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 台风信息服务接口
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:21
 */
public interface FmTyphoonBService extends IService<FmTyphoonB>{

    /**
     * 根据参数查询分页
     */
    PageResult<FmTyphoonBVO> getPage(FmTyphoonBDTO dto,PageParam pageParam);

    /**
     * 根据参数查询列表
     */
    List<FmTyphoonBVO> getList(FmTyphoonBDTO dto);

    /**
     * 根据id查询数据
     */
    FmTyphoonBVO getById(Long id);

    /**
     * 保存数据
     */
    void save(FmTyphoonBDTO dto);

    /**
     * 批量保存数据
     */
    void saveList(List<FmTyphoonBDTO> dtoList);

    /**
     * 根据id删除数据
     */
    void deleteById(Long id);

    /**
     * 根据idList批量删除数据
     */
    void deleteByIdList(List<Long> idList);

    FmTyphoonB getInfo(String typhoonNo);

    /**
     * 根据台风编码查询相似路径台风
     * @param code 台风编码
     * @param startYear 查询开始年份
     * @param points 最小匹配路径点数
     * @param distance 匹配距离参数
     * @param score 最小匹配分值
     * @return
     */
    List<FmTyphoonScoreVO> getSimilarTyphoonList(String code, int startYear, int points, int distance, int score) throws Exception;

    /**
     * 请求中台接口台风数据
     * @param startYear 开始年份
     * @param endYear 结束年份
     * @return value
     */
    String syncTyphoon(Integer startYear, Integer endYear);
}
