package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.dto.FmGraphicToolsDTO;
import cn.piesat.data.making.server.entity.FmGraphicTools;
import cn.piesat.data.making.server.vo.FmGraphicToolsVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:39
 */
public interface FmGraphicToolsService extends IService<FmGraphicTools>{
    List<FmGraphicToolsVO> getList(String type);

    void saveByTemplateId(List<FmGraphicTemplateToolsDTO> list, Long templateId);
}
