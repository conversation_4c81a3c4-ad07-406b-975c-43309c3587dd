package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.FmSchedulingUser;

import cn.piesat.data.making.server.dao.FmSchedulingUserDao;
import cn.piesat.data.making.server.service.FmSchedulingUserService;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.piesat.data.making.server.utils.page.PageParam;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-30 16:08:29
 */
@Service("fmSchedulingUserService")
public class FmSchedulingUserServiceImpl extends ServiceImpl<FmSchedulingUserDao, FmSchedulingUser> implements FmSchedulingUserService {

    @Resource
    private FmSchedulingUserDao fmSchedulingUserDao;

    @Override
    public PageResult<FmSchedulingUser> getPage(FmSchedulingUser dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmSchedulingUser> getList(FmSchedulingUser dto) {
        return null;
    }

    @Override
    public void saveUser(FmSchedulingUser dto){
        FmSchedulingUser byUserId = fmSchedulingUserDao.findByUserId(dto.getUserId());

        if(byUserId == null){
            fmSchedulingUserDao.insert(dto);
        }else{

        }

    }
}
