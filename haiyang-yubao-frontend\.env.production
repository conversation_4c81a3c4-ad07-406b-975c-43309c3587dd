###
 # @Author: 樊海玲 <EMAIL>
 # @Date: 2024-11-22 09:36:14
 # @LastEditors: 樊海玲 <EMAIL>
 # @LastEditTime: 2025-03-05 09:12:32
 # @FilePath: /hainan-jianzai-web/.env.production
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
###
VITE_Base_Url='/'
VITE_Busi_Url=''
VITE_Host_Url='https://*************:80'
# VITE_Host_Url='http://**********:8989'

### security-passport-server
VITE_AUTH_BASE_URL='/api/auth'
VITE_AUTH_BASE_API_URL='https://**********:26081'

### sys-api-server
VITE_SYS_BASE_URL='/api/basic'
VITE_SYS_BASE_API_URL='https://**********:23080'

### sys-admin-api
VITE_SYS_ADMIN_URL='/api/sysadmin'
VITE_SYS_ADMIN_API_URL='https://**********:25080'

### data-scrap-api
VITE_DATA_SCRAP_BASE_URL="/api/dataScrap"
VITE_DATA_SCRAP_BASE_API_URL='https://**********:28080'

### product-push-api
VITE_PRODUCT_PUSH_BASE_URL="/api/push"
VITE_PRODUCT_PUSH_BASE_API_URL='https://**********:24080'

### security-ucenter-server
VITE_PERS_BASE_URL="/api/pers"
VITE_PERS_BASE_API_URL="https://**********:26080"

### monitor-api-server
VITE_MONITOR_BASE_URL="/api/monitor"
VITE_MONITOR_BASE_API_URL="https://**********:28081"

### schedule-api-server
VITE_DISPATCH_BASE_URL='/api/schedule'
VITE_DISPATCH_BASE_API_URL='https://**********:38080'

VITE_FORECAST_BASE_URL = '/api/data'
VITE_FORECAST_BASE_API_URL='https://**********:38081'

VITE_PRODUCT_BASE_URL = '/api/product'
VITE_PRODUCT_BASE_API_URL='https://**********:8088'
VITE_TIF_BASE_API_URL='https://**********:80'


VITE_ETTP_SERVICE_BASE_API ='/api/meteo_ettp'
VITE_ETTP_SERVICE_BASE_API_URL='https://10.2.25.241:8675'

VITE_ETTP_BASE_API ='/api/ettp'
VITE_ETTP_BASE_API_URL='https://**********:38280'
