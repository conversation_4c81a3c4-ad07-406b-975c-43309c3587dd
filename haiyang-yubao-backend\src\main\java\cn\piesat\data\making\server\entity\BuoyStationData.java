package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 浮标站数据表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_buoy_station_data_b")
public class BuoyStationData implements Serializable {

    private static final long serialVersionUID = -92287499657588403L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 浮标站编码
     **/
    @TableField("buoy_station_code")
    private String buoyStationCode;
    /**
     * 时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("time")
    private Date time;
    /**
     * 风速
     **/
    @TableField("wind_speed")
    private Double windSpeed;
    /**
     * 风向
     **/
    @TableField("wind_dir")
    private Double windDir;
    /**
     * 波高
     **/
    @TableField("wind_wave_height")
    private Double windWaveHeight;
}



