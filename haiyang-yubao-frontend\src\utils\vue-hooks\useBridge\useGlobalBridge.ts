import { GetterMap } from './types'

export class GlobalBridge<T extends Record<string, any> = any> {
  private fields: GetterMap<T> = {}

  register<K extends keyof T>(key: K, getter: () => T[K]) {
    this.fields[key] = getter
  }

  collect(): Partial<T> {
    const result: Partial<T> = {}
    for (const key in this.fields) {
      const getter = this.fields[key]
      if (getter) result[key] = getter()
    }
    return result
  }

  clear() {
    this.fields = {}
  }
}

// 所有命名桥共享的容器
const globalBridgeMap = new Map<string, GlobalBridge<any>>()

export function useGlobalBridge<T extends Record<string, any>>(name: string): GlobalBridge<T> {
  if (!globalBridgeMap.has(name)) {
    globalBridgeMap.set(name, new GlobalBridge<T>())
  }
  return globalBridgeMap.get(name)!
}
