package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmIsolineDataDTO;
import cn.piesat.data.making.server.vo.FmIsolineDataVO;
import cn.piesat.data.making.server.service.FmIsolineDataService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 等值线控制层
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:30
 */
@RestController
@RequestMapping("fmIsolineData")
public class FmIsolineDataController {

    @Resource
    private FmIsolineDataService fmIsolineDataService;

    /**
     * 查询列表
     *
     * @param dataSource
     * @param date
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "等值线查询", operateType = OperateType.SELECT)
    public List<FmIsolineDataVO> getList(@RequestParam(required = false) String dataSource,
                                         @RequestParam(required = false) Date date) {
        FmIsolineDataDTO dto = new FmIsolineDataDTO();
        dto.setDataSource(dataSource);
        dto.setStartReportTime(date);
        return fmIsolineDataService.getList(dto);
    }

}
