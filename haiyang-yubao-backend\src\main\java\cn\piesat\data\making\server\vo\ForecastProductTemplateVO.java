package cn.piesat.data.making.server.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报产品模板表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastProductTemplateVO implements Serializable {

    private static final long serialVersionUID = -83974175145231347L;

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    private String code;
    /**
     * 名称
     **/
    private String name;
    /**
     * 状态
     **/
    private Boolean status;
    /**
     * 排序
     **/
    private Integer sort;
    /**
     * 文件类型
     **/
    private String fileType;
    /**
     * 关联的模板编码
     **/
    private String relationTemplateCode;
    /**
     * 文件内容
     **/
    private String fileContent;
    /**
     * 文件地址
     **/
    private String fileUrl;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 数管-数据产品id
     **/
    private Long productId;
}



