import CommenService from 'src/requests/commenService'
import { computed, onMounted, ref } from 'vue'
import { IAstronomicalTideStationItem } from 'src/requests/commenService.type'

export function useStation() {
  const stationFilterStr = ref('') // 筛选文字
  const stations = ref<IAstronomicalTideStationItem[]>([])
  const selectedStationIDArr = ref<string[]>([])
  const cptSelectedStationInfoArr = computed(() => {
    return stations.value.filter(i => selectedStationIDArr.value.includes(i.id))
  })
  // 显示结果筛选
  const cptStationFilter = computed(() => {
    if (stationFilterStr.value === '') {
      return stations.value
    } else {
      return stations.value.filter(i => i.name.includes(stationFilterStr.value))
    }
  })

  onMounted(async () => {
    void (await updateStation())
    // selectAll()
  })

  async function updateStation() {
    const res = await CommenService.getStationList({
      stationTypeCode: 'oceanStation'
    })
    stations.value = res
  }

  function selectAll() {
    if (selectedStationIDArr.value.length === stations.value.length) {
      selectedStationIDArr.value = []
      return
    }
    selectedStationIDArr.value = stations.value.map(i => i.id)
  }

  function resetStation() {
    selectedStationIDArr.value = []
  }

  return {
    updateStation,
    stations,
    stationFilterStr,
    cptStationFilter,
    selectedStationIDArr,
    cptSelectedStationInfoArr,
    resetStation,
    selectAll
  }
}
