package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.entity.FmPublicProductRecord;
import cn.piesat.data.making.server.vo.FmPublicProductRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Mapper(componentModel = "spring")
public interface FmPublicProductRecordMapper {

    FmPublicProductRecordMapper INSTANCE = Mappers.getMapper(FmPublicProductRecordMapper.class);

    List<FmPublicProductRecordVO> entityListToVoList(List<FmPublicProductRecord> list);

    FmPublicProductRecord voToEntity(FmPublicProductRecordVO vo);

    FmPublicProductRecordVO entityToVo(FmPublicProductRecord entity);
}
