package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.Station;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 站点表表数据库访问层
 *
 * <AUTHOR>
 */
public interface StationDao extends BaseMapper<Station> {

    @Update("update fm_station_b set code = #{code}, location_geo = st_geomfromgeojson(#{locationGeo}), location_json = #{locationJson} WHERE name = #{name}")
    void updateLocationByName(String code, String name, String locationGeo, String locationJson);

    @Select("SELECT code, name, station_type_code, relation_station FROM fm_station_b WHERE ST_Contains(ST_GeomFromText(#{geoRange},4326), location_geo)")
    List<Station> getListByRange(String geoRange);

    @Select("SELECT id, code, name, station_type_code, relation_station, datumBlueWarn FROM fm_station_b WHERE enable = true")
    List<Station> getListByStationTypeCode(String StationTypeCode);
}
