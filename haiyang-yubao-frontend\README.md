# Vue 3 + Typescript + Vite

This template should help get you started developing with Vue 3 and Typescript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

## 工程介绍

### typescript

`typescript` 是自我对代码的约束，在项目中，如无必要（如`postcss.config.js`等配置文件）请使用 ts 编写代码。代码中应该尽量避免将变量类型定义为`any`，多使用 interface 描述变量的类型，这有助于理清思路，掌控代码。

### eslint

`eslint` 是使用工具加强对代码的约束，该工程在`.eslintrc.js`里配置了 eslint，vue，typescript 总结的一些规范，`eslint` 会依据这些规范约束开发者的编码行为，在开发的过程中，若遇到一些没有必要的约束，可以讨论后在 rules 里关闭此约束。

### prettier

`prettier` 是对代码美观，风格做出了统一的要求，方便团队合作时能比较舒适的阅读他人的代码，增加可读性。在`.prettierrc.js`内配置类常见的样式选项，后续可讨论完善。

## 插件推荐

### ESLint & Prettier - Code formatter

这两个插件能配合配置文件在编写代码时就提供代码检查，能及早发现问题。一些简单的语法错误，格式错误，保存文件即可自动修复，自动修复后还存在的问题，需要开发者对照报错内容修改。

### Todo Tree

一款可以提醒开发者哪里还需要修改完善的插件。

### koroFileHeader

一款可以自动生成或通过快捷键生成文档注释、方法注释的插件，建议设置时把 FilePath 去掉，建议多多使用，增加代码的可读性。

### Git History & GitLens — Git supercharged

git 相关的插件，能够让开发者更好的与团队合作，查看提交记录和修改历史。

### Volar

vue3 建议安装。

## 一些其他规范

### 关于文件名

在创建`.vue`或`.tsx`文件时，建议命名为多个单词组成的文件名，各个单词均大写，如`HelloWorld.vue`，`HomePage.tsx`。
在创建`.ts`文件时，建议命名为一个单词或者多个用下划线连接单词，每个单词都小写，如`router.ts`，`login_service.ts`。
在命名时，尽量使用英文单词命名，不要使用汉语拼音，尽量做到通过看文件名就能大致知道这个文件时做什么的。

### 关于与后台交互

推荐在`requests`文件夹内按照类型创建相应的文件，比如，现在有一批`/account/auth`开头的权限类接口需要接入，我们可以在`requests`文件夹内创建一个名为`auth_request.ts`的文件，在此文件内编写所有权限相关的请求，请为每个请求方法注明参数和用途。

我们也可以提取`/account/auth`出来成为环境参数，在`.env.development`和`.env.production`中分别定义，注意参数名要以`VITE_`开头，例如`VITE_Auth_Base_Url = '/account/auth'`，在项目中，可以用`import.meta.env.VITE_Auth_Base_Url as string`获取。

我们不建议在业务代码里使用`域名+端口`的方式写死请求路径，在长远来看这样做的维护代价很大，更加建议的做法是在`vite.config.ts`内配置`server.proxy`，请查看[vite 官方文档](https://cn.vitejs.dev/config/#server-proxy)配置。

### 各文件夹的约定

`assets` 文件夹用来存放一些静态资源，如图片，css 样式，媒体资源等。
`components` 文件夹用来存放一些项目中的可复用组件。
`map` 文件夹是地图相关的文件。
`requests` 文件夹用来整合整个项目的交互接口。
`router` 文件夹存放路由信息。
`utils` 文件夹存放一些通用的方法。
`views` 文件夹存放项目的业务组件。

### postcss 样式预处理插件

目前引入了`postcss-preset-env`，`tailwindcss`，`postcss-preset-env`是 css 的 polyfill，`tailwindcss`是部分开发者简写样式的选择。
