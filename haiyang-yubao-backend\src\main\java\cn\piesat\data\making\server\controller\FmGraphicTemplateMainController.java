package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmGraphicTemplateMainDTO;
import cn.piesat.data.making.server.vo.FmGraphicTemplateMainVO;
import cn.piesat.data.making.server.service.FmGraphicTemplateMainService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 图形模版字典
 *
 * <AUTHOR>
 * @date 2024-10-13 09:43:29
 */
@RestController
@RequestMapping("fmGraphicTemplateMain")
public class FmGraphicTemplateMainController {

    @Resource
    private FmGraphicTemplateMainService fmGraphicTemplateMainService;

    /**
     * 查询列表
     *
     * @param productType
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
    public List<FmGraphicTemplateMainVO> getList(@RequestParam(required = false) String productType) {
        FmGraphicTemplateMainDTO dto = new FmGraphicTemplateMainDTO();
        dto.setProductType(productType);
        return fmGraphicTemplateMainService.getList(dto);
    }

}
