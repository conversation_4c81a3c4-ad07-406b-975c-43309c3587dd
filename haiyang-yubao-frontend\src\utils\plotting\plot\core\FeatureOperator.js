import Style from 'ol/style/Style'
import { guid } from '../../util/core'
import StyleFactory from '../style/StyleFactory'
import MarkerStyleFactory from '../style/MarkerStyleFactory'
import Constants from '../Constants'
class FeatureOperator {
  /**
   * @classdesc 标绘图元操作类
   * 提供对标绘图元的封装操作.所有的地图点击事件的回调函数中均有该对象，可以通过该对象实现图元的基本操作
   * @constructs
   * <AUTHOR>
   * @param {ol.Feature} feature 图元
   * @param {ol.layer.SourceLayer} layer 放置的layer
   * @param {number} zindex 当前图元的zindex
   */
  constructor(feature, layer, zindex) {
    /**
     * 名称
     * @type {String}
     */
    this.name = '未命名'
    /**
     * feature对象
     * @type {ol.Feature}
     */
    this.feature = feature
    /**
     * 图层对象
     * @type {ol.layer.SourceLayer}
     */
    this.layer = layer
    /**
     * 是否被销毁
     * @type {Boolean}
     */
    this.isDestoryed = false
    /**
     * 唯一区分值
     * @type {String}
     */
    this.guid = guid(true)
    /**
     * 属性列表
     * @type {Object}
     */
    this.attrs = {}
    // --初始化样式
    this._initStyle(zindex)
    // --添加到图层
    this._addToLayer()
  }
  /**
   * @ignore
   * 初始化样式
   * 
   * METE_TYPHOON_POINT: 'mete_typhoon_point', // 台风点
  METE_WIND_BARB: 'mete_wind_barb', // 风羽
  METE_POINT: 'mete_point', // 位置点
  METE_HIGH_PRESSURE_POINT: 'mete_high_pressure_point', // 高压位置
  METE_LOW_PRESSURE_POINT: 'mete_low_pressure_point', // 低压位置
  METE_HIGH_PRESSURE1: 'mete_high_pressure1', // 高压
  METE_LOW_PRESSURE1: 'mete_low_pressure1', // 低压
  METE_TYPHOON_PRESSURE: 'mete_typhoon_pressure', // 台风气压
  METE_WAVE_DIRECTION: 'mete_wave_direction', //主波向
  METE_TYPHOON1: 'mete_typhon1', // 台风
   * 
   */
  _initStyle(zindex) {
    let ft_style = null
    const type = this.getType()
    // 修改图标的图片，由于不知道该如何传递图片的url，先使用localStorage来存储
    const newMarkers = [
      'mete_typhoon_point',
      'mete_wind_barb',
      'mete_point',
      'mete_high_pressure_point',
      'mete_low_pressure_point',
      'mete_high_pressure1',
      'mete_low_pressure1',
      'mete_typhoon_pressure',
      'mete_wave_direction',
      'mete_typhon1'
    ]
    const url = localStorage.getItem('markUrl')
    if (newMarkers.includes(type)) {
      let iconImg = new URL(url, import.meta.url).href
      if (type === 'mete_wind_barb' || type === 'mete_wave_direction') {
        const rotation = Number(localStorage.getItem('rotation'))
        ft_style = MarkerStyleFactory.createFTStyle(iconImg, rotation, type)
      } else {
        ft_style = MarkerStyleFactory.createFTStyle(iconImg)
      }
    } else {
      ft_style = StyleFactory.createFTStyle(this.getType(), this.feature)
    }

    if (!ft_style) {
      return
    }
    const style = ft_style.parse()
    if (style instanceof Array) {
      style.forEach(s => s.setZIndex(zindex))
    } else if (style instanceof Style) {
      style.setZIndex(zindex)
    }

    this.feature.setStyle(style)
    this.ft_style = ft_style
    // console.log(this.feature, "************initStyle", style, this.feature)
  }
  /**
   * @ignore
   */
  _addToLayer() {
    if (!this.layer) {
      return
    }
    this.layer.getSource().addFeature(this.feature)
  }
  /**
   * 设置图元样式。不同图元提供不同的属性设置。可以通过getStyle获取图元默认样式
   * @param {JSON} json_style
   */
  setStyle(json_style) {
    if (!json_style) return
    const zIndex = this.getZIndex()
    this.ft_style.setStyle(json_style)
    const style = this.ft_style.parse()
    if (style instanceof Array) {
      style.forEach(s => s.setZIndex(zIndex))
    } else if (style instanceof Style) {
      style.setZIndex(zIndex)
    }
    if (
      this.getType() == 'mete_warm_front' ||
      this.getType() == 'mete_cold_front' ||
      this.getType() == 'mete_occluded_front' ||
      this.getType() == 'mete_stationary_front'
    ) {
      var st = this.feature.getStyle()
      st[0].stroke_.setColor(json_style.color)
      this.feature.setStyle(st)
      this.ft_style.setColor(json_style.color)
    } else {
      this.feature.setStyle(style)
    }
  }
  /**
   * 获取图元当前样式
   * @return {JSON} json_style
   */
  getStyle() {
    if (!this.ft_style) {
      return ''
    }
    return this.ft_style.serialize()
  }
  /**
   * 获取图元当前ZIndex
   * @return {Number} zindex
   */
  getZIndex() {
    var stylelist = this.feature.getStyle()
    if (stylelist instanceof Array) {
      return this.feature.getStyle()[0].getZIndex()
    } else {
      return this.feature.getStyle().getZIndex()
    }
  }
  /**
   * 获取图元类型。点，线，面等。
   * @return {String} 类型
   */
  getType() {
    console.log(this.feature.values_)
    return this.feature.values_.geometry.type
  }
  /**
   * 设置图元的名称
   * @param {String} str_name 名称
   */
  setName(str_name) {
    this.name = str_name
  }
  /**
   * 设置图元的名称
   * @return {String}  名称
   */
  getName() {
    return this.name
  }
  /**
   * 设置图元不可被点击
   * @return {String}  名称
   */
  disable() {
    this.feature.set(Constants.SE_DISABLED, true)
  }
  /**
   * 设置图元可以被点击
   * @return {String}  名称
   */
  enable() {
    this.feature.set(Constants.SE_DISABLED, false)
  }
  /**
   * 获取图元自定义属性
   * @param {String} key
   * @return {Object} value
   */
  getAttribute(key) {
    return this.attrs[key]
  }
  /**
   * 设置图元属性。相同的属性键值会被覆盖
   * @param {String} key
   * @param {Object} value
   */
  setAttribute(key, value) {
    this.attrs[key] = value
  }
  /**
   * 删除图元属性
   * @param {String} key
   * @return {Boolean} 是否操作成功
   */
  removeAttribute(key) {
    if (this.attrs[key]) {
      delete this.attrs[key]
      return true
    }
    return false
  }
  /**
   * 迭代自定义属性
   * @param {Function} fn  回调函数
   * @param {Object} scope 回调函数this值 可选
   */
  iteratorAttribute(fn, scope) {
    for (const sKey in this.attrs) {
      if (fn && typeof fn === 'function') fn.call(scope, sKey)
    }
  }
  /**
   * 更新对象的控制点
   */
  setCoordinates(coordinates) {
    const plot = this.feature.values_.geometry
    if (plot) plot.setPoints(coordinates)
  }
  /**
   * 销毁对象
   */
  destory() {
    this.isDestoryed = true
    this.feature = null
    this.layer = null
    this.attrs = {}
    this.ft_style.destory()
  }
  /**
   * 序列化
   */
  serialize() {
    const plot = this.feature.values_.geometry
    if (!plot) return {}

    return {
      config: {
        cresda_flag: true,
        z_index: this.getZIndex(),
        disabled: !!this.feature.get(Constants.SE_DISABLED)
      },
      name: this.getName(),
      ext_attr: this.attrs,
      plotting_type: plot.type,
      geo_type: plot.geo_type,
      geo_data: {
        coordinates: plot.getPoints(),
        type: plot.geo_type
      },

      style: this.getStyle()
    }
  }
}
export default FeatureOperator
