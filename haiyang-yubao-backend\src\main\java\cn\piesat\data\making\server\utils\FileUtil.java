package cn.piesat.data.making.server.utils;

import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.model.FileInfo;
import cn.piesat.webconfig.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件工具类
 */
@Slf4j
public class FileUtil {

    /**
     * 上传文件
     *
     * @param basePath
     * @param multipartFile
     **/
    public static String uploadFile(String basePath, MultipartFile multipartFile) {
        File uploadDirectory = new File(basePath);
        if (!uploadDirectory.exists()) {
            uploadDirectory.mkdirs();
        }
        String time = DateUtil.format(new Date(), "HHmmss");
        String fileSuffix = multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf("."));
        String filePath = basePath + File.separator + time + fileSuffix;
        File file = new File(filePath);
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            IOUtils.copy(multipartFile.getInputStream(), outputStream);
        } catch (IOException e) {
            log.error("上传异常，异常原因：" + e.getMessage());
            throw new BusinessException("上传失败！");
        } finally {
            try {
                if (!ObjectUtils.isEmpty(outputStream)) {
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return filePath;
    }

    /**
     * 复制文件
     *
     * @param tempFileUrl     实际临时文件URL
     * @param destinationPath 目标文件路径
     **/
    public static void copyFile(String tempFileUrl, String destinationPath) throws IOException {
        URL url = new URL(tempFileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//        connection.setRequestMethod("GET");
//        connection.setDoOutput(true);
        int responseCode = connection.getResponseCode();

        //检查响应码是否为HTTP OK
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream inputStream = connection.getInputStream();
            FileOutputStream outputStream = new FileOutputStream(destinationPath);
            try {
                byte[] buffer = new byte[1024];
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } finally {
                inputStream.close();
            }
        } else {
            log.error("文件复制异常，HTTP code: " + responseCode);
            throw new IOException("文件复制异常");
        }
        connection.disconnect();
    }

    /**
     * 浏览器下载文件
     *
     * @param filePath 文件路径(包含文件名称和文件后缀)
     * @param name     名称(下载后的文件名称)
     **/
    public static void downloadFile(String filePath, String name, HttpServletResponse response) {
        String fileName = filePath.substring(filePath.lastIndexOf("\\") + 1);
        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
        OutputStream out = null;
        try {
            byte[] data = FileUtils.readFileToByteArray(new File(filePath));
            // 响应到浏览器
            response.reset();
            // 设置响应文件类型
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            // 设置编码UTF_8
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(name + fileSuffix, "UTF-8"));
            out = new BufferedOutputStream(response.getOutputStream());
            out.write(data);
            out.flush();
            out.close();
            response.flushBuffer();
        } catch (IOException e) {
            log.error("下载异常，异常原因：" + e.getMessage());
            throw new BusinessException("下载失败！");
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 打包下载
     *
     * @param filePathList 文件路径list
     * @param zipFileName  压缩包名称
     * @param response
     **/
    public static void zipFiles(List<String> filePathList, String zipFileName, HttpServletResponse response) {
        byte[] buf = new byte[1024];
        BufferedOutputStream bos = null;
        ZipOutputStream out = null;
        try {
            bos = new BufferedOutputStream(response.getOutputStream());
            // 响应到浏览器
            response.reset();
            // 设置响应文件类型
            response.setContentType(Files.probeContentType(Paths.get(".zip")));
            // 设置编码UTF_8
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(zipFileName, "UTF-8"));
            out = new ZipOutputStream(bos);
            for (String filePath : filePathList) {
                File file = new File(filePath);
                FileInputStream in = new FileInputStream(file);
                out.putNextEntry(new ZipEntry(file.getName()));
                int len;
                while ((len = in.read(buf)) != -1) {
                    out.write(buf, 0, len);
                }
                in.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (out != null) out.close();
                if (bos != null) bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 打包下载
     *
     * @param filePathList 文件路径list
     * @param fileNameList 文件名称list
     * @param zipFileName  压缩包名称
     * @param response
     **/
    public static void zipFiles(List<String> filePathList,List<String> fileNameList, String zipFileName, HttpServletResponse response) {
        byte[] buf = new byte[1024];
        BufferedOutputStream bos = null;
        ZipOutputStream out = null;
        try {
            bos = new BufferedOutputStream(response.getOutputStream());
            // 响应到浏览器
            response.reset();
            // 设置响应文件类型
            response.setContentType(Files.probeContentType(Paths.get(".zip")));
            // 设置编码UTF_8
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(zipFileName, "UTF-8"));
            out = new ZipOutputStream(bos);
            /*
            for (String filePath : filePathList) {
                File file = new File(filePath);
                FileInputStream in = new FileInputStream(file);
                out.putNextEntry(new ZipEntry(file.getName()));
                int len;
                while ((len = in.read(buf)) != -1) {
                    out.write(buf, 0, len);
                }
                in.close();
            }
            */
            String filePath = null;
            String fileName = null;

            for (int i = 0; i<filePathList.size();i++) {
                filePath = filePathList.get(i);
                fileName = fileNameList.get(i);
                File file = new File(filePath);
                FileInputStream in = new FileInputStream(file);
                out.putNextEntry(new ZipEntry(fileName));
                int len;
                while ((len = in.read(buf)) != -1) {
                    out.write(buf, 0, len);
                }
                in.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (out != null) out.close();
                if (bos != null) bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    public static void deleteFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            log.error("删除文件失败：{}", filePath);
        }
    }

    /**
     * 更新文件内容
     *
     * @param fileInfo 文件信息
     **/
    public static void updateFile(FileInfo fileInfo) {
        cn.hutool.core.io.FileUtil fileUtil = new cn.hutool.core.io.FileUtil();
        fileUtil.writeUtf8String(fileInfo.getContent(), fileInfo.getPath());
    }
}