package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.ShipDataDTO;
import cn.piesat.data.making.server.service.ShipDataService;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.ShipDataVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 船舶表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/shipData")
public class ShipDataController {

    @Resource
    private ShipDataService shipDataService;

    /**
     * 查询船舶列表 数据维护使用
     *
     * @return
     */
    @GetMapping("/shipList")
    @SysLog(systemName = "预报制作系统", moduleName = "船舶管理", operateType = OperateType.SELECT)
    public List<String> getShipList() {
        return shipDataService.getShipList();
    }

    /**
     * 根据船舶名称查询船舶轨迹列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "船舶管理", operateType = OperateType.SELECT)
    public List<ShipDataVO> getList(@Validated(value = {ShipDataDTO.Query.class}) @RequestBody ShipDataDTO dto) {
        return shipDataService.getList(dto);
    }

    /**
     * 根据时间范围、空间范围查询船舶轨迹列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/rangeList")
    @SysLog(systemName = "预报制作系统", moduleName = "船舶管理", operateType = OperateType.SELECT)
    public FillMapVO getRangeList(@Validated(value = {ShipDataDTO.RangeQuery.class}) @RequestBody ShipDataDTO dto) {
        return shipDataService.getRangeList(dto);
    }
}

