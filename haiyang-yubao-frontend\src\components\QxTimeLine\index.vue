<template>
  <div class="time-line">
    <div class="position-btn position-btn-left">
      <arrow-back class="icon" @click="goto(-300)"></arrow-back>
    </div>
    <div ref="timeLine" class="time-line-content">
      <div class="time-slider">
        <div ref="activeSlider" class="active-slider"></div>
        <div ref="slider" class="slider"></div>
      </div>
      <div class="times">
        <div
          v-for="(item, index) in timeList"
          :key="item"
          class="time-item"
          @click="changeTime(item, index)"
        >
          <n-tooltip trigger="hover">
            <template #trigger>
              <span class="text">{{ item.time }}</span>
            </template>
            {{ item.label }}
          </n-tooltip>
        </div>
      </div>
    </div>
    <div class="position-btn position-btn-right">
      <arrow-forward class="icon" @click="goto(300)"></arrow-forward>
    </div>
  </div>
</template>

<script setup>
import { ArrowBack, ArrowForward } from '@vicons/ionicons5'
import { onMounted, ref } from 'vue'

const timeLine = ref(null)
const boxWidth = '150px'
import moment from 'moment'

const emits = defineEmits(['timeClick'])
const activeSlider = ref()
const slider = ref()
let timeList = ref([])

function changeTime(item, index) {
  let width = 50
  const element = document.getElementsByClassName('time-item')[0]
  if (element) {
    width = window.getComputedStyle(element).width.replace('px', '')
  }
  slider.value.style.width =
    (timeList.value.length - index) * Number(width) + 'px'
  activeSlider.value.style.width =
    index * Number(width) + Number(width) / 2 + 'px'

  console.log(width, '*****')
  emits('timeClick', item, index)
}

function reRenderTimeLine(list) {
  timeList.value = []
  if (list.length > 0) {
    list.forEach(element => {
      const time = moment(element).format('HH:mm')
      const label = moment(element).format('YYYY-MM-DD HH:mm:ss')
      timeList.value.push({
        time,
        label
      })
    })
  }
  let width = 50
  if (document.getElementsByClassName('time-item')[0]) {
    const element = document.getElementsByClassName('time-item')[0]
    const w = window.getComputedStyle(element).width.replace('px', '')
    width = Number(w)
  }
  activeSlider.value.style.width = 0 + 'px'
  slider.value.style.width = list.length * width + 'px'
}

/**
 * 调整横向滚动条
 * @param target {Number}
 */
function goto(target) {
  /** @type {HTMLElement} */
  const dom = timeLine.value
  dom.scrollTo({ left: dom.scrollLeft + target, behavior: 'smooth' })
}

onMounted(() => {
  activeSlider.value.style.width = 0 + 'px'
  slider.value.style.width = timeList.value.length * 50 + 'px'
})
defineExpose({
  reRenderTimeLine,
  changeTime
})
</script>

<style lang="scss" scoped>
.time-line {
  display: flex;
  position: fixed;
  bottom: 19px;
  left: 416px;
  width: 1100px;
  height: 40px;
  background: linear-gradient(180deg, #ffffff 0%, #e8effa 100%), #d9d9d9;
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.25);
  border-radius: 6px 6px 6px 6px;
  z-index: 10;
  padding-left: 20px;
  padding-right: 20px;

  .position-btn {
    height: 100%;
    display: flex;
    width: 20px;
    align-items: center;
    justify-content: center;
    flex: 0 0 10px;

    .icon {
      height: 20px;
      width: 20px;
      cursor: pointer;
    }
  }

  .time-line-content {
    flex: 1;
    margin-left: 20px;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    overflow-x: auto;
    flex-wrap: nowrap;
    overflow-y: hidden;

    .time-slider {
      display: flex;
      flex-shrink: 0;

      .active-slider {
        margin-top: 10px;
        height: 6px;
        background: linear-gradient(90deg, #006fff 0%, #66edff 100%);
        border-radius: 0px 0px 0px 0px;
        width: 0px;
        flex-shrink: 0;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          right: -5px;
          top: -4px;
          width: 10px;
          height: 10px;
          background: linear-gradient(180deg, #5ec4ff 0%, #0372ff 70%);
          box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.2);
          border-radius: 10px;
          border: 2px solid #ffffff;
        }
      }

      .slider {
        margin-top: 10px;
        width: 100%;
        height: 6px;
        flex-shrink: 0;
        background: linear-gradient(
          180deg,
          rgba(195, 217, 246, 0.74) 0%,
          #cfe4f3 100%
        );
        box-shadow: inset 0px 1px 4px 0px rgba(42, 52, 73, 0.22);
        border-radius: 0px 0px 0px 0px;
      }
    }
  }

  .times {
    display: flex;
    flex-shrink: 0;

    .time-item {
      width: 50px;
      // border-left: 1px solid #333;
      flex-shrink: 0;
      height: 20px;
      cursor: pointer;
      box-sizing: border-box;
      text-align: center;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 24px;
        top: 0;
        width: 1px;
        height: 7px;
        background: #222;
      }

      .text {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 15px;
        line-height: 20px;
        font-size: 12px;
        color: #333;
        margin-top: 5px;
      }
    }
  }
}
</style>
