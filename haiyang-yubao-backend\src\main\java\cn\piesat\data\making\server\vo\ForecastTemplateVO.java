package cn.piesat.data.making.server.vo;

import cn.piesat.data.making.server.entity.ForecastTemplateColumn;
import cn.piesat.data.making.server.entity.ForecastTemplateRow;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预报模板表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastTemplateVO implements Serializable {

    private static final long serialVersionUID = -10533262308946060L;

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    private String code;
    /**
     * 名称
     **/
    private String name;
    /**
     * 预报类型
     **/
    private String forecastType;
    /**
     * 状态
     **/
    private Boolean status;
    /**
     * 排序
     **/
    private Integer sort;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 行
     */
    private List<ForecastTemplateRow> rowList;
    /**
     * 列
     */
    private List<ForecastTemplateColumn> columnList;
}



