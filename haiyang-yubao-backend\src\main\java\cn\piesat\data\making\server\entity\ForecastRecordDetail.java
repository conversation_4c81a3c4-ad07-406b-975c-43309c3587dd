package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报记录详情表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_forecast_record_detail_b")
public class ForecastRecordDetail implements Serializable {

    private static final long serialVersionUID = 474693466821292148L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 预报记录id
     **/
    @TableField("forecast_record_id")
    private Long forecastRecordId;
    /**
     * 区域id
     **/
    @TableField("area_id")
    private Long areaId;
    /**
     * 区域编码
     **/
    @TableField("area_code")
    private String areaCode;
    /**
     * 区域名称
     **/
    @TableField("area_name")
    private String areaName;
    /**
     * 要素编码
     **/
    @TableField("element_code")
    private String elementCode;
    /**
     * 要素名称
     **/
    @TableField("element_name")
    private String elementName;
    /**
     * 列编码
     **/
    @TableField("column_code")
    private String columnCode;
    /**
     * 列名
     **/
    @TableField("column_name")
    private String columnName;
    /**
     * 要素数值处理
     **/
    @TableField("element_value_handle")
    private String elementValueHandle;
    /**
     * 要素是否显示
     **/
    @TableField("element_display")
    private Boolean elementDisplay;
    /**
     * 值
     **/
    @TableField("value")
    private String value;
    /**
     * 创建人id
     **/
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建人
     **/
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人id
     **/
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;
    /**
     * 更新人
     **/
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}



