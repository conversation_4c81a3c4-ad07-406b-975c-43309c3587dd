# coding=utf8

import codecs
import json
import re
import shutil
from os.path import basename, join, dirname, exists
import os
from datetime import datetime

REAL_RUN = True


def run(command):
    # type: (unicode) -> int
    print u':::' + command + u'\n'
    if REAL_RUN:
        return os.system(command)
    else:
        return 0


def get_manifest(tar_path):
    result = os.popen(u'tar -xOf ' + tar_path + ' manifest.json')
    output = result.read()
    dic = json.loads(output)
    tag = dic[0]['RepoTags'][0]
    res = re.findall(r'(?<=hainan-jianzai-web:)\d+\.\d+\.\d+', tag)
    if len(res) != 0:
        return res[0]
    return None


def rm_dist(root, dist_name):
    # type: (unicode) -> None
    dist_dir_path = join(tar_dir, dist_name)
    if REAL_RUN and exists(dist_dir_path):
        shutil.rmtree(dist_dir_path)
    print u':::clear dist dir'


def extract_tar(path):
    # type: (unicode) -> tuple[int, Union[unicode, None]]
    dt = None
    file_path = None
    for origin_name in os.listdir(path):
        name = origin_name.replace(u'_', u':')
        res = re.findall(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', name)
        if len(res) == 0:
            continue
        _dt = datetime.strptime(res[0], "%Y-%m-%dT%H:%M:%S")
        if dt is None or _dt > dt:
            dt = _dt
            file_path = join(path, origin_name)
    if file_path is None:
        return -1, file_path
    n = run(u'tar -xzvf ' + file_path + u' --warning=no-timestamp')
    return n, file_path


def latest_version(versions):
    # type: (list[unicode]) -> unicode
    first = u'0'
    second = u'0'
    third = u'0'
    for version in versions:
        version_group = version.split(u'.')
        if int(version_group[0]) >= int(first):
            first = version_group[0]
        if int(version_group[1]) >= int(second) and first == version_group[0]:
            second = version_group[1]
        if int(version_group[2]) >= int(third) and second == version_group[1] and first == version_group[0]:
            third = version_group[2]
    return first + u'.' + second + u'.' + third


def find_latest_image_tag(root):
    images_name = u'images.txt'
    images_list_path = join(root, images_name)
    run(u'docker images | grep "hainan-jianzai-web" > ' + images_name)
    tags_list = []
    with codecs.open(images_list_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for line in lines:
            res = re.findall(r'\d+\.\d+\.\d+', line)
            if len(res) != 0:
                tags_list.append(res[0])
    return latest_version(tags_list)


def backup_yml(docker_compose_config_backup_dir, docker_compose_config_path):
    dt = datetime.now().strftime(u'%Y-%m-%dT%H:%M:%S')
    if not exists(docker_compose_config_backup_dir):
        os.mkdir(docker_compose_config_backup_dir)
    shutil.copy(docker_compose_config_path, join(docker_compose_config_backup_dir, dt + u'.yml'))
    print u':::backup config file to ' + join(docker_compose_config_backup_dir, dt + u'.yml\n')


def update_yml(docker_compose_config_path, new_tag):
    lines = []
    with codecs.open(docker_compose_config_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines):
            res = re.findall(r'(?<=hainan-jianzai-web:)\d+\.\d+\.\d+', line)
            if not (len(res) == 0):
                lines[i] = lines[i].replace(res[0], new_tag)
                print u'----------'
                change = line.strip() + u'>>>>>' + lines[i].strip()
                print change
                print u'----------'
    if REAL_RUN:
        with codecs.open(docker_compose_config_path, 'w', encoding='utf-8') as f:
            f.write(u''.join(lines))
            print u':::write: ' + docker_compose_config_path + u'\n'


def refresh_config(docker_compose_config_path):
    return run(u'docker-compose -f ' + docker_compose_config_path + ' up -d')


def show_log():
    return run(u'docker logs -f hainanWeb')


def build_image(tag):
    return run(u'docker build -t hainan-jianzai-web:' + tag + u' .')


# /data/server/web
# /data/docker-compose/docker-compose.yml.bak
if __name__ == '__main__':
    print u'run in real mode: ' + unicode(REAL_RUN)
    tar_dir = u'/data/server/web/docker'
    docker_compose_root = u'/data/docker-compose'
    docker_compose_config_backup_dir = join(docker_compose_root, u'backup-yml')
    docker_compose_config_path = join(docker_compose_root, u'docker-compose.yml')

    latest_tag = find_latest_image_tag(tar_dir)
    new_tag = raw_input(u'input tag(latest is ' + latest_tag + u'):')

    rm_dist(tar_dir, u'dist')
    x, tar_path = extract_tar(tar_dir)
    build_image(new_tag)
    if not x == 0:
        raise OSError('can not load docker image')
    backup_yml(docker_compose_config_backup_dir, docker_compose_config_path)
    update_yml(docker_compose_config_path, new_tag)
    x = refresh_config(docker_compose_config_path)
    if not x == 0:
        raise OSError('can not refresh config')

    print 'Done!!!'
