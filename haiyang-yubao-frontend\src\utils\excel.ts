// import exceljs from 'exceljs'

export interface ICsvColumn {
  title: string
  key: string
  width: number
}

// export function toExcel(data: Record<string, any>[], columns: IColumn[]) {
//   const workbook = new exceljs.Workbook()
//   const sheet = workbook.addWorksheet('data')
//   sheet.columns = columns.map(col => {
//     return { header: col.title, key: col.key, width: col.width }
//   })
//   data.map(item => {
//     sheet.addRow(item)
//   })
// }

export function toCsvString(
  data: Record<string, any>[],
  columns: ICsvColumn[]
) {
  let content = '\uFEFF'
  columns.map((col, index) => {
    if (index === columns.length - 1) {
      content += `"${col.title}"\n`
    } else {
      content += `"${col.title}",`
    }
  })
  data.map((row, index) => {
    columns.map((col, i) => {
      if (i === columns.length - 1) {
        content += `"${row[col.key]}"\n`
      } else {
        content += `"${row[col.key]}",`
      }
    })
  })
  return content
}

export function downloadCsvString(content: string, filename: string) {
  const blob = new Blob([content])
  const objectURL = URL.createObjectURL(blob)
  const a: HTMLAnchorElement = document.createElement('a')
  a.download = filename
  a.href = objectURL
  a.click()
}
