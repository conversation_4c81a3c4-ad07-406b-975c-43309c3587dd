import { MaybeRefOrGetter } from '@vueuse/core'
import { computed, onMounted, Ref, ref, toValue } from 'vue'
import CommenService from 'src/requests/commenService'
import {
  IAstronomicalTideQueryResDTO,
  IAstronomicalTideStationRow
} from 'src/requests/commenService.type'
import moment from 'moment'
import { DataTableBaseColumn, useMessage } from 'naive-ui'
import { ITagItem } from 'src/requests/tag.type'
import TagApi from 'src/requests/tag'
import { Nullable } from 'src/utils/type'

export function useContent(
  timeRange: MaybeRefOrGetter<[number, number]>,
  selectedStationIDArr: MaybeRefOrGetter<string[]>
) {
  const _timeRange = toValue(timeRange)
  // 消息综述
  const seaConditionAnalysis = ref<string>('')
  const defenseAdvice = ref<string>('')
  const saveDate = ref('')
  const commitDate = ref('')
  // 上期或本期内容数据
  const contentData = ref<IAstronomicalTideQueryResDTO | null>(null)
  // 根据选择日期查询出的当期表格
  const tableData = ref<IAstronomicalTideStationRow[]>([])
  // 根据选中站点筛选后的表格
  const cptTableData = computed(() => {
    const _stationIDArr = toValue(selectedStationIDArr)
    return tableData.value.filter(row => _stationIDArr.includes(row.stationId))
  })

  const message = useMessage()
  // 头部表单
  const useHeaderReturned = useHeader()

  onMounted(async () => {
    void (await loadTableData())
    // 加载上一期内容
    void (await loadLastContent())
  })

  /**
   * 加载上一期内容
   */
  async function loadLastContent() {
    contentData.value = await CommenService.getAstronomicalLastContent({
      publicType: 1
    })
    syncDataFromResponse()
    const selectedStationIDArrValue = toValue(selectedStationIDArr)
    selectedStationIDArrValue.length = 0
    const filter = contentData.value.list?.map(i => i.stationId).filter(Boolean)
    selectedStationIDArrValue.push(
      ...Array.from(new Set(filter))
    )
    saveDate.value = contentData.value.updateTime
    commitDate.value = contentData.value.reportTime || ''
  }

  /**
   * 重置内容
   */
  async function resetContent() {
    seaConditionAnalysis.value = ''
    defenseAdvice.value = ''
    await loadTableData()
  }

  async function createNewContent() {
    contentData.value!.id = null
    await resetContent()
  }

  /**
   * 本地数据 -> DTO
   */
  function syncDataToResponse() {
    contentData.value!.contentF = defenseAdvice.value
    contentData.value!.contentS = seaConditionAnalysis.value
    contentData.value!.signUserId = useHeaderReturned.signUserInfo.value!.id
    contentData.value!.signUserName = useHeaderReturned.signUserInfo.value!.name
    contentData.value!.signMakerId = useHeaderReturned.makeUserInfo.value!.id
    contentData.value!.signMakerName = useHeaderReturned.makeUserInfo.value!.name
    contentData.value!.msgNo = useHeaderReturned.serialNumber.value
    contentData.value!.msgTime = moment(useHeaderReturned.publishDatetime.value).format('YYYY-MM-DD HH:mm:ss')
    contentData.value!.list = cptTableData.value
  }

  /**
   * DTO -> 本地数据
   */
  function syncDataFromResponse() {
    defenseAdvice.value = contentData.value!.contentF || ''
    seaConditionAnalysis.value = contentData.value!.contentS || ''
    useHeaderReturned.signUserId.value = contentData.value!.signUserId || ''
    useHeaderReturned.makeUserId.value = contentData.value!.signMakerId || ''
    useHeaderReturned.serialNumber.value = contentData.value!.msgNo || ''
    useHeaderReturned.publishDatetime.value = moment(contentData.value!.msgTime).valueOf()
  }

  /**
   * 加载表格数据
   */
  async function loadTableData() {
    const res = await CommenService.getAstronomicalTable({
      yearMonth: moment(_timeRange[1]).format('YYYY-MM')
    })
    tableData.value = res
  }

  /**
   * 保存
   */
  async function onSave() {
    syncDataToResponse()
    try {
      void (await CommenService.saveAstronomicalTideDataList(
        contentData.value!
      ))
      message.success('保存成功')
      const dto = await CommenService.getAstronomicalLastContent({ publicType: 1 })
      contentData.value!.id = dto.id
    } catch (e) {
      console.warn(e)
      message.error('保存失败')
    }
  }

  /**
   * 提交
   */
  async function onSubmit() {
    void (await onSave())
    syncDataToResponse()
    try {
      void (await CommenService.submitAstronomicalTideDataList(
        contentData.value!
      ))
      message.success('提交成功')
    } catch (e) {
      console.warn(e)
      message.error('提交失败')
    }
  }

  return {
    seaConditionAnalysis,
    defenseAdvice,
    saveDate,
    commitDate,
    resetContent,
    createNewContent,
    contentData,
    onSave,
    onSubmit,
    tableData,
    cptTableData,
    tableHead: getTableColumns(),
    ...useHeaderReturned
  }
}

function getTableColumns(): DataTableBaseColumn<IAstronomicalTideStationRow>[] {
  return [
    {
      title: '站点',
      key: 'stationName'
    },
    {
      title: '日期',
      key: 'tideDate'
    },
    {
      title: '高潮时',
      key: 'tideOnlyTime'
    },
    {
      title: '高潮位(cm)',
      key: 'height'
    },
    {
      title: '蓝色警戒潮位值(cm)',
      key: 'datumBlueWarn'
    },
    {
      title: '差值(cm)',
      key: 'datumBlueDif'
    }
  ]
}

/**
 * 签发/拟稿 hook
 */
function useHeader() {
  // 拟稿人
  const makeUserInfo = computed(() =>
    makeUserArr.value.find(i => i.id === makeUserId.value)
  )
  const makeUserId = ref<string>('')
  const makeUserArr = ref<Nullable<ITagItem>[]>([])
  // 签发人
  const signUserInfo = computed(() =>
    signUserArr.value.find(i => i.id === signUserId.value)
  )
  const signUserId = ref<string>('')
  const signUserArr = ref<Nullable<ITagItem>[]>([])
  const publishDatetime = ref<number>(moment().startOf('hours').valueOf())
  const serialNumber = ref('')

  onMounted(async () => {
    void (await loadSignUserList())
    void (await loadMakeUserList())
  })

  async function loadSignUserList() {
    const tagItems = await TagApi.getSignUserList()
    signUserArr.value = tagItems
    // if (tagItems.length >= 1) {
    //   signUserId.value = tagItems[0].id
    // }
  }

  async function loadMakeUserList() {
    const tagItems = await TagApi.getMakeUserList()
    makeUserArr.value = tagItems
    // if (tagItems.length >= 1) {
    //   makeUserId.value = tagItems[0].id
    // }
  }

  return {
    makeUserArr,
    makeUserInfo,
    makeUserId,
    signUserInfo,
    signUserArr,
    signUserId,
    loadMakeUserList,
    loadSignUserList,
    publishDatetime,
    serialNumber,
    renderLabelFactory
  }
}

function renderLabelFactory() {
  return (option: ITagItem) => {
    return <div>{option.name}</div>
  }
}
