<template>
  <div class="query-item">
    <div class="query-title">观测设备：</div>
    <div class="query-info">
      <div
        v-for="item in equipmentList"
        :key="item.value"
        class="radio-btn"
        :class="[curStationType == item.value ? 'active' : '']"
        @click="changeStationType(item)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
  <template v-if="curStationType === 'buoyStation'">
    <div class="query-item">
      <div class="query-title">浮标类型：</div>
      <div class="query-info">
        <div
          v-for="item in buoyTypeList"
          :key="item.value"
          class="radio-btn"
          :class="[buoyTypes === item.value ? 'active' : '']"
          @click="changeBuoyType(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </template>
  <div class="query-item">
    <div class="query-title">海洋要素：</div>
    <div class="query-info">
      <n-select
        v-model:value="element"
        :options="targetOption"
        size="small"
        clearable
        label-field="name"
        value-field="code"
        :loading="loading"
        @update:value="changeElement"
      />
    </div>
  </div>
  <div class="query-item">
    <div class="query-title">时间范围：</div>
    <div class="query-info">
      <n-date-picker
        v-model:formatted-value="range"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetimerange"
        size="small"
        clearable
        :default-time="['00:08:00', '00:08:00']"
      />
    </div>
  </div>
  <!--
  <div class="query-item">
    <div class="query-title">空间范围：</div>
    <div class="query-info">
      <n-input
        v-model:value="locationJson"
        placeholder="请输入"
        clearable
        size="small"
        @update:value="updateFeature"
      />

      <Scan class="icon-scan" @click="drawHandler" />
    </div>
  </div>
  -->
  <div class="center-btn">
    <qx-button class="query-btn" @click="queryInfo">查询</qx-button>
  </div>
  <div class="table-wrap">
    <n-data-table
      v-if="curStationType === 'oceanStation'"
      v-model:checked-row-keys="checkedRowKeys"
      max-height="300"
      :row-key="rowKeyGetter"
      :columns="oceanColumns"
      :data="oceanData"
    ></n-data-table>
  </div>
  <div class="table-wrap">
    <n-data-table
      v-if="curStationType === 'buoyStation'"
      v-model:checked-row-keys="buoyCheckedRowKeys"
      max-height="300"
      :row-key="buoyRowKeyGetter"
      :columns="buoyColumns"
      :data="buoyData"
    ></n-data-table>
  </div>
  <!-- <div class="alis-mouse-position">
    <div class="legend">
      <div class="text">{{ curElement }}</div>
      <div class="color-ramp">
        <span v-for="item in colorRamp.elements" :key="item">{{ item }}</span>
      </div>
    </div>
  </div> -->
  <QxTimeLine
    v-if="showTimeLine"
    ref="timeLineRef"
    @time-click="timeClick"
  ></QxTimeLine>
</template>

<script setup>
import { ref, inject, onMounted, onUnmounted, nextTick, reactive } from 'vue'
import { Scan } from '@vicons/ionicons5'
import { Fill, Icon, Stroke, Style, Text, Circle } from 'ol/style.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { GeoJSON } from 'ol/format'
import { Point } from 'ol/geom.js'
import { Draw } from 'ol/interaction'
import { useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import { QxTimeLine } from 'src/components/QxTimeLine'
import Feature from 'ol/Feature.js'
import * as turf from '@turf/turf'
import ToolApi from 'src/requests/toolRequest'
import Rainbow from 'src/utils/rainbowvis.js'
import { startOfDay } from 'date-fns'
import moment from 'moment'
import Popup from 'src/utils/olPopup/ol-popup.js'
import { unByKey } from 'ol/Observable'
import { useOceanTable } from './marineMeteorology-hooks/useOceanTable'
import { useBuoyTable } from 'src/components/ElementStatistics/components/marineMeteorology-hooks/useBuoyTable'

const { oceanColumns, oceanData, rowKeyGetter, checkedRowKeys, checkAllOcean } =
  useOceanTable({
    vectorSourceGetter() {
      return elementSource
    }
  })

const {
  buoyColumns,
  buoyData,
  buoyCheckedRowKeys,
  rowKeyGetter: buoyRowKeyGetter,
  checkAllBuoy
} = useBuoyTable({
  vectorSourceGetter() {
    return elementSource
  }
})

const jet = [
  '#00007F',
  '#000093',
  '#0000A9',
  '#0000C1',
  '#0000D7',
  '#0000ED',
  '#0000FF',
  '#0009FF',
  '#001EFF',
  '#0032FF',
  '#0045FF',
  '#005AFF',
  '#006EFF',
  '#0083FF',
  '#0096FF',
  '#00AAFF',
  '#00BFFF',
  '#00D2FF',
  '#01E7F4',
  '#11FAE4',
  '#20FFD5',
  '#31FFC3',
  '#41FFB4',
  '#52FFA3',
  '#62FF93',
  '#72FF83',
  '#82FF73',
  '#92FF63',
  '#A1FF54',
  '#B2FF42',
  '#C2FF33',
  '#D4FF21',
  '#E3FF12',
  '#F3F902',
  '#FFE500',
  '#FFD300',
  '#FFBF00',
  '#FFAE00',
  '#FF9B00',
  '#FF8800',
  '#FF7600',
  '#FF6300',
  '#FF5000',
  '#FF3F00',
  '#FF2B00',
  '#FF1A00',
  '#EF0600',
  '#D90000',
  '#C30000',
  '#AB0000',
  '#950000',
  '#7F0000'
]
const d = 864e5
const h = 36e5
const m = 6e4
const s = 1e3

function isRangeDateDisabled(ts, type, range) {
  if (type === 'start' && range !== null) {
    return startOfDay(range[1]).valueOf() - startOfDay(ts).valueOf() >= d * 3
  }
  if (type === 'end' && range !== null) {
    return startOfDay(ts).valueOf() - startOfDay(range[0]).valueOf() >= d * 3
  }
  return false
}

const timeLineRef = ref()
const message = useMessage()
const getMap = inject('getMap')
const curStationType = ref('oceanStation')
const equipmentList = [
  {
    label: '海洋站',
    value: 'oceanStation'
  },
  {
    label: '浮标站',
    value: 'buoyStation'
  },
  {
    label: '船舶',
    value: 'ship'
  }
]
const buoyTypeList = [
  {
    label: '3米',
    value: '3m'
  },
  {
    label: '10米',
    value: '10m'
  },
  {
    label: '波浪谱',
    value: 'waveSpectrum'
  }
]
let buoyTypes = ref('3m')

let element = ref('') //海洋要素
let targetOption = []

const curElement = ref('')
const colorRamp = ref({
  label: '风速',
  value: 'windSpeed',
  max: 12,
  min: 1,
  elements: [1, 4, 7, 10, 13]
})

function changeElement(val, option) {
  curElement.value = option.name
  colorRamp.value = option
}

let loading = ref(false)

// 获取要素
function getElementList() {
  targetOption = []
  loading.value = true
  let mapper = {
    oceanStation: 'oceanStation',
    buoyStation: buoyTypes.value,
    ship: 'ship'
  }
  const params = {
    type: mapper[curStationType.value]
  }
  ToolApi.getELementList(params)
    .then(res => {
      loading.value = false
      if (res?.length) {
        targetOption = res
        element.value = res[0].code
        curElement.value = res[0].name
        colorRamp.value = res[0]
      }
    })
    .catch(e => {
      loading.value = false
      console.error(e, 'eeeeee==')
      message.error('获取数据失败')
    })
}

const range = ref([
  moment().subtract(1, 'd').format('YYYY-MM-DD 00:08:00'),
  moment().format('YYYY-MM-DD 00:08:08')
]) //时间范围
const locationJson = ref('') //空间范围
/** 空间范围修改功能 */
const source = new VectorSource()
const layer = new VectorLayer({
  source: source,
  zIndex: 8
})

function updateFeature(value) {
  source.clear()
  if (value.trim() !== '') {
    const feature = new GeoJSON().readFeatures(value)
    feature[0].setStyle(
      new Style({
        fill: new Fill({
          color: 'rgba(0, 0, 255, 0.1)'
        }),
        stroke: new Stroke({
          color: '#ff0000'
        })
      })
    )
    source.addFeature(feature[0])
  }
}

let draw = null

function drawHandler() {
  getMap(map => {
    if (draw) {
      map.removeInteraction(draw)
    }
    draw = new Draw({
      type: 'Polygon', //绘制的几何图形的几何类型
      source
    })
    map.addInteraction(draw)

    draw.on('drawstart', drawstart)
    draw.on('drawend', drawend)
  })
}

function drawstart(evt) {
  source.refresh()

  message.warning('开始绘制,双击击结束')
}

function drawend(event) {
  getMap(map => {
    const json = new GeoJSON().writeFeature(event.feature)
    locationJson.value = json
    map.removeInteraction(draw)
  })
}

/** 海洋气象观测地面填图 */
// 更改观测设备
function changeStationType(item) {
  curStationType.value = item.value
  getElementList()
  // if (item.value === 'oceanStation') {
  //   range.value = ['2023-06-01 00:00:00', '2023-06-02 00:00:00']
  // } else if (item.value === 'buoyStation') {
  //   range.value = ['2024-04-14 00:00:00', '2024-04-14 23:00:00']
  // } else if (item.value === 'ship') {
  //   range.value = ['2000-10-31 00:00:00', '2000-11-01 00:00:00']
  // }
  if (['oceanStation', 'buoyStation'].includes(item.value)) {
    range.value = [
      moment().format('YYYY-MM-DD 00:00:00'),
      moment().format('YYYY-MM-DD 23:59:59')
    ]
  } else {
    range.value = ['2000-10-31 00:00:00', '2000-11-01 00:00:00']
  }
}

// 修改浮标类型
function changeBuoyType(item) {
  buoyTypes.value = item.value
  getElementList()
}

const elementSource = new VectorSource()
let elementLayer = new VectorLayer({
  source: elementSource,
  zIndex: 10
})
const showTimeLine = ref(false)
let dataList = ref({
  timeAndDataMap: {},
  timeList: []
})

async function queryInfo() {
  popup.hide()
  try {
    const apis = {
      oceanStation: 'getOceanStationStatisticsR',
      buoyStation: 'getBuoyStationsR',
      ship: 'getShipListR'
    }
    const params = {
      startTime: range.value[0],
      endTime: range.value[1],
      geoRange:
        '{"type":"Feature","geometry":{"type":"Polygon","coordinates":[[[106.98205645900086,20.691814225986363],[112.59806625250872,20.832147557190492],[114.14246894572337,18.961036474468806],[116.16657247546682,15.651508746904828],[114.06056880290139,14.63409209567491],[109.48586082527311,14.680869872742953],[108.3509588461684,15.897092076512049],[106.42045547965007,17.94361982323889],[105.9758547043307,19.87320312729563],[106.98205645900086,20.691814225986363]]]},"properties":null}'
    }
    if (curStationType.value === 'buoyStation') {
      params.type = buoyTypes.value
      params.element = element.value
    }
    dataList.value = await ToolApi[apis[curStationType.value]](params)
    if (curStationType.value === 'oceanStation') {
      oceanData.value =
        dataList.value.timeAndDataMap[dataList.value.timeList[0]]
      checkAllOcean()
    } else if (curStationType.value === 'buoyStation') {
      buoyData.value = dataList.value.timeAndDataMap[dataList.value.timeList[0]]
      checkAllBuoy()
    }
  } catch (error) {
    dataList.value.timeList = []
  }
  // ToolApi.getOceanStationStatisticsR({

  // }).then(res=>{

  // }).catch(()=>{

  // })
  elementSource.refresh()
  if (dataList.value.timeList.length > 0) {
    showTimeLine.value = true
    nextTick(() => {
      timeLineRef.value.reRenderTimeLine(dataList.value.timeList)
      timeLineRef.value.changeTime({ label: dataList.value.timeList[0] }, 0)
    })
  } else {
    showTimeLine.value = false
    message.warning('暂无数据')
  }
}

/** 时间轴部分 */
function timeClick(time, index) {
  if (curStationType.value === 'oceanStation') {
    oceanData.value =
      dataList.value.timeAndDataMap[dataList.value.timeList[index]]
    checkAllOcean()
  } else if (curStationType.value === 'buoyStation') {
    buoyData.value = dataList.value.timeAndDataMap[dataList.value.timeList[0]]
    checkAllBuoy()
  }
  elementSource.refresh()
  dataList.value.timeAndDataMap[time.label].forEach((item, index) => {
    const json =
      curStationType.value === 'oceanStation'
        ? item.oceanStationLocationJson
        : curStationType.value === 'buoyStation'
        ? item.buoyStationLocationJson
        : item.shipLocationJson
    let point = JSON.parse(json)
    const geo = point.geometry
    // let buffered = turf.buffer(point, 30, { units: 'kilometers', steps: 20 })
    // const feature = new GeoJSON().readFeatures(JSON.stringify(buffered))
    // feature[0].setProperties(item)
    // feature[0].setStyle(
    //   new Style({
    //     fill: new Fill({
    //       color: "#ff0000"
    //     }),
    //     stroke: new Stroke({
    //       color: '#ffffff'
    //     })
    //   })
    // )
    const circleFeature = new Feature({
      geometry: new Point(geo.coordinates) //new Circle(geo.coordinates, 0.02)
    })
    var rainbow = new Rainbow() // by default, range is 0 to 100
    rainbow.setSpectrum(jet)
    // rainbow.setNumberRange(colorRamp.value.min, colorRamp.value.max)
    const color = '#' + rainbow.colourAt(item[element.value])
    const labelStyle = new Style({
      text: new Text({
        text: item[element.value] + '',
        font: '13px Calibri,sans-serif',
        fill: new Fill({
          color: '#000'
        }),
        stroke: new Stroke({
          color: '#fff',
          width: 4
        })
      })
    })
    const pointStyle = new Style({
      // fill: new Fill({
      //   color: color
      // }),
      // stroke: new Stroke({
      //   color: '#ffffff'
      // }),
      image: new Circle({
        radius: 20,
        fill: new Fill({
          color: color
        }),
        stroke: new Stroke({
          color: 'white',
          width: 1
        })
      })
    })
    circleFeature.setStyle([pointStyle, labelStyle])

    // circleFeature.setStyle(
    //   new Style({
    //     renderer(coordinates, state) {
    //       const [[x, y], [x1, y1]] = coordinates
    //       const ctx = state.context
    //       const dx = x1 - x
    //       const dy = y1 - y
    //       const radius = Math.sqrt(dx * dx + dy * dy)

    //       const innerRadius = 0
    //       const outerRadius = radius * 1.4

    //       const gradient = ctx.createRadialGradient(
    //         x,
    //         y,
    //         innerRadius,
    //         x,
    //         y,
    //         outerRadius
    //       )
    //       gradient.addColorStop(0, `rgba(${colors[index]},0.8)`)
    //       gradient.addColorStop(0.6, `rgba(${colors[index]},0.3)`)
    //       gradient.addColorStop(1, `rgba(${colors[index]},0.0)`)
    //       ctx.beginPath()
    //       ctx.arc(x, y, radius, 0, 2 * Math.PI, true)
    //       ctx.fillStyle = gradient
    //       ctx.fill()

    //       ctx.arc(x, y, radius, 0, 2 * Math.PI, true)
    //       ctx.strokeStyle = 'rgba(255,255,255,0.0)'
    //       ctx.stroke()
    //     }
    //   })
    // )

    elementSource.addFeature(circleFeature)
    circleFeature.setProperties(item)
    circleFeature.set('typeInfo', 'point')
    circleFeature.set('$style', circleFeature.getStyle())
  })
  const extent = elementSource.getExtent()
  getMap(map => {
    map.getView().fit(extent, {
      // duration: 500,
      padding: [100, 100, 100, 100]
    })
  })
}

const popup = new Popup()
let clickKey = null
onMounted(() => {
  getElementList()
  getMap(map => {
    map.addLayer(layer)
    map.addLayer(elementLayer)
    map.addOverlay(popup)
    clickKey = map.on('click', evt => {
      const pixel = map.getEventPixel(evt.originalEvent)
      const feature = map.forEachFeatureAtPixel(pixel, function (feature) {
        return feature
      })
      popup.hide()
      const properties = feature.getProperties()
      let html = ''
      if (properties.typeInfo === 'point') {
        if (curStationType.value === 'oceanStation') {
          html = `<div class="infoResult">
                <p class="title-popup"> 站名: ${properties.oceanStationName}</p>
                <p class="title-popup"> 时间: ${properties.time}</p>
                <p class="title-popup"> 浪高: ${properties.windWaveHeight}</p>
                <p class="title-popup"> 水温: ${properties.seaTemperature}</p>
                <p class="title-popup"> 风速: ${properties.windSpeed}</p>
                <p class="title-popup"> 风向: ${properties.windDir}</p>
                </div>`
          popup.show(evt.coordinate, html)
        }
        if (
          curStationType.value === 'buoyStation' &&
          (buoyTypes.value === '3m' || buoyTypes.value === '10m')
        ) {
          html = `<div class="infoResult">
                <p class="title-popup"> 站名: ${properties.buoyStationName}</p>
                <p class="title-popup"> 时间: ${properties.time}</p>
                <p class="title-popup"> 有效波高: ${properties.buoydataYbg}</p>
                <p class="title-popup"> 最大波高: ${properties.buoydataZbg}</p>
                </div>`
          popup.show(evt.coordinate, html)
        }
      }
    })
  })
})
onUnmounted(() => {
  getMap(map => {
    popup.hide()
    clickKey && unByKey(clickKey)
    clickKey = null
    map.removeOverlay(popup)
    if (elementLayer) {
      map.removeLayer(elementLayer)
    }
    map.removeLayer(layer)
    if (draw) {
      map.removeInteraction(draw)
    }
  })
})
</script>

<style lang="scss" scoped>
.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;

  .query-title {
    white-space: nowrap;
    width: 70px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }

  .query-info {
    width: 346px;
    display: flex;
    align-items: center;

    .radio-btn {
      background-color: rgb(231, 241, 253);
      padding: 5px 8px;
      margin: 0px 5px;
      border-radius: 4px;
      color: #222222;
      cursor: pointer;

      &:hover {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }

      &.active {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
    }

    .icon-scan {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}

.center-btn {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .query-btn {
    background: #1c81f8;
    color: #fff;

    &:hover {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
  }
}

.alis-mouse-position {
  position: fixed;
  z-index: 800;
  right: 20px;
  bottom: 18px;
  width: 370px;
  color: #fff;
  background: #eff4fc;
  border-radius: 4px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;

  .legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 15px;
    width: 100%;
    height: 100%;

    .text {
      white-space: nowrap;
      margin-right: 8px;
    }

    .color-ramp {
      flex: 1;
      position: relative;
      height: 15px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 12px;
      padding: 0px 6px;
      background: linear-gradient(
        90deg,
        #00007f,
        #000093,
        #0000a9,
        #0000c1,
        #0000d7,
        #0000ed,
        #0000ff,
        #0009ff,
        #001eff,
        #0032ff,
        #0045ff,
        #005aff,
        #006eff,
        #0083ff,
        #0096ff,
        #00aaff,
        #00bfff,
        #00d2ff,
        #01e7f4,
        #11fae4,
        #20ffd5,
        #31ffc3,
        #41ffb4,
        #52ffa3,
        #62ff93,
        #72ff83,
        #82ff73,
        #92ff63,
        #a1ff54,
        #b2ff42,
        #c2ff33,
        #d4ff21,
        #e3ff12,
        #f3f902,
        #ffe500,
        #ffd300,
        #ffbf00,
        #ffae00,
        #ff9b00,
        #ff8800,
        #ff7600,
        #ff6300,
        #ff5000,
        #ff3f00,
        #ff2b00,
        #ff1a00,
        #ef0600,
        #d90000,
        #c30000,
        #ab0000,
        #950000,
        #7f0000
      );
    }
  }
}
</style>
