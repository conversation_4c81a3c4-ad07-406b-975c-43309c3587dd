<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-24 18:06:46
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-25 10:26:07
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/create.vue
 * @Description: 专项预报新建/编辑
-->
<template>
  <qx-dialog
    v-if="visible"
    :title="title"
    :visible="visible"
    width="1597px"
    class="create-special-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <div class="dialog-content d-flex">
        <div class="dialog-content-left">
          <n-form
            ref="formRef"
            :model="form"
            label-placement="left"
            label-width="110px"
            required-mark-placement="left"
            class="qx-form"
          >
            <n-form-item label="产品名称" path="user.productName">
              <n-input
                v-model:value="form.productName"
                placeholder="请输入"
                clearable
              />
            </n-form-item>
            <n-form-item label="发布单位" path="sendUnitName">
              <n-input
                v-model:value="form.sendUnitName"
                placeholder="请输入"
                clearable
              />
            </n-form-item>
            <n-form-item label="数据源" path="sourceCode">
              <n-radio-group
                v-model:value="form.sourceCode"
                name="radiogroup"
                @update:value="onChangeSource"
              >
                <n-space>
                  <n-radio value="grid">智能网格</n-radio>
                  <n-radio value="nmefc">NMEFC</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="要素">
              <n-select
                v-model:value="form.elementName"
                :options="elementList"
                label-field="name"
                value-field="nameWithUnit"
                :multiple="form.sourceCode == 'grid' ? true : false"
                @update:value="onSelectElement"
              />
            </n-form-item>
            <n-form-item label="起报时间" path="startTime">
              <n-select
                v-model:value="form.startTime"
                :options="forecastTimeList"
                label-field="data_time"
                value-field="data_time"
                format="yyyy-MM-DD HH:mm:ss"
                @update:value="onSelectTime"
              />
            </n-form-item>
            <n-form-item label="预报时效" path="">
              <n-radio-group v-model:value="form.timeType" name="radiogroup">
                <n-space>
                  <n-radio value="H">逐小时</n-radio>
                  <n-radio value="D">逐天</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              label="预报范围"
              path="forecastTime"
              :show-feedback="false"
            >
              <n-grid :cols="24">
                <n-form-item-gi :span="24" label="" path="forecastTimeS">
                  <n-date-picker
                    :formatted-value="form.forecastTimeS"
                    type="datetime"
                    clearable
                    :is-date-disabled="disableStartTime"
                    @update:formatted-value="timeStartUpdate"
                  />
                  至
                </n-form-item-gi>
                <n-form-item-gi :span="24" label="" path="forecastTimeE">
                  <n-date-picker
                    :formatted-value="form.forecastTimeE"
                    type="datetime"
                    clearable
                    :is-date-disabled="disablePreviousDate"
                    @update:formatted-value="timeEndUpdate"
                  />
                </n-form-item-gi>
              </n-grid>
            </n-form-item>
            <n-form-item label="预报区域" path="forecastArea">
              <n-grid>
                <n-form-item-gi :span="24" :show-feedback="false">
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <i
                        class="icon icon-point"
                        @click="onAddArea('point')"
                      ></i>
                    </template>
                    添加点
                  </n-tooltip>
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <i class="icon icon-area" @click="onAddArea('area')"></i>
                    </template>
                    添加区域
                  </n-tooltip>
                  <n-upload
                    action="#"
                    :show-file-list="false"
                    @before-upload="beforeUpload"
                    @change="uploadFile"
                  >
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <i class="icon icon-export"></i>
                      </template>
                      导入
                    </n-tooltip>
                  </n-upload>
                </n-form-item-gi>
                <n-form-item-gi
                  v-if="areaList.length > 0"
                  :span="24"
                  :show-feedback="false"
                >
                  <div class="area-list">
                    <div
                      v-for="(item, index) in areaList"
                      :key="index"
                      class="area-item d-flex"
                    >
                      <div
                        class="area-name"
                        title="双击修改区域名称"
                        @click="clickFun(item, index)"
                      >
                        {{ item.name }}
                      </div>
                      <template v-if="item.isPoint">
                        <n-tooltip trigger="hover">
                          <template #trigger>
                            <i
                              class="icon icon-input"
                              @click="onAddPointPosition(item, index)"
                            ></i>
                          </template>
                          输入点坐标
                        </n-tooltip>
                        <n-tooltip trigger="hover">
                          <template #trigger>
                            <i
                              class="icon icon-point1"
                              @click="drawPoint(index)"
                            ></i>
                          </template>
                          选点
                        </n-tooltip>
                      </template>
                      <i
                        v-else
                        class="icon icon-edit1"
                        @click="drawPolygon(item, index)"
                      ></i>
                      <i class="icon icon-del1" @click="onDel(index, item)"></i>
                    </div>
                  </div>
                </n-form-item-gi>
              </n-grid>
            </n-form-item>
            <n-form-item label="预报图">
              <div class="upload-container">
                <div class="images">
                  <n-spin v-if="!form.imagePath" :show="loading">
                    <div class="empty-img" @click="addImage">
                      <div class="add-icon">
                        <Add></Add>
                      </div>
                      <div>添加预报图</div>
                    </div>
                  </n-spin>

                  <div v-else style="max-height: 100%">
                    <div class="delete-icon">
                      <CloseCircle
                        color="#f90102"
                        @click="removeImage(0)"
                      ></CloseCircle>
                    </div>
                    <n-image width="80" :src="form.imagePath" />
                  </div>
                </div>
              </div>
            </n-form-item>
            <n-form-item label="漂移路径">
              <div class="upload-container">
                <div class="images">
                  <n-spin v-if="!form.driftingImagePath" :show="riftingLoading">
                    <n-upload
                      :show-file-list="false"
                      @update:file-list="addRiftingImage"
                      @before-upload="checkRiftingImage"
                    >
                      <div class="empty-img">
                        <div class="add-icon">
                          <Add></Add>
                        </div>
                        <div>添加漂移路径</div>
                      </div>
                    </n-upload>
                  </n-spin>

                  <div v-else style="max-height: 100%">
                    <div class="delete-icon">
                      <CloseCircle
                        color="#f90102"
                        @click="removeImage(1)"
                      ></CloseCircle>
                    </div>
                    <n-image width="80" :src="form.driftingImagePath" />
                  </div>
                </div>
              </div>
            </n-form-item>
          </n-form>
          <div class="btn-group text-right">
            <qx-button class="cancel" @click="onClose">取消</qx-button>
            <qx-button class="primary" @click="onCreate">确定</qx-button>
          </div>
        </div>
        <base-map :show-tool="false" @ready="onMapReady">
          <map-tools
            @text="onChangeText"
            @area-edit="onAreaEdit"
            @screenshot="onScreenshot"
            @scale="onScale"
            @graticule="toggleGraticule"
          />
        </base-map>
      </div>
    </template>
  </qx-dialog>

  <qx-dialog
    :title="isPoint ? '新建点' : '新建区域'"
    width="332px"
    :visible="areaVisible"
    class="add-area-dialog"
  >
    <template #content>
      <n-form
        :model="areaForm"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        required-mark-placement="left"
        class="qx-form"
      >
        <n-form-item :label="isPoint ? '点名称' : '区域名称'" path="areaName">
          <n-input
            v-model:value="areaForm.areaName"
            placeholder="请输入"
            clearable
          />
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btn-group">
        <qx-button class="cancel" @click="areaVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onDialogConfirm">确定</qx-button>
      </div>
    </template>
  </qx-dialog>
  <add-point-position
    v-model:visible="pointVisible"
    :point="pointPosition"
    @save="onSavePoint"
  />
</template>
<script lang="ts" setup>
import { QxDialog } from 'src/components/QxDialog'
import { BaseMap } from 'src/components/OpenlayersMap'
import {
  reactive,
  ref,
  onMounted,
  watch,
  onBeforeUnmount,
  toRaw,
  provide
} from 'vue'
import { UploadFileInfo, useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import 'ol-ext/render/Cspline.js'
import Draw from 'ol/interaction/Draw.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import GeoJSON from 'ol/format/GeoJSON.js'
import Polygon from 'ol/geom/Polygon.js'
import { Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import { Feature } from 'ol'
import { Point, SimpleGeometry } from 'ol/geom'
import ProductApi from 'src/requests/product'
import PublicService from 'src/requests/publicService'
import { nmefcElementList, gridElementList, MapTools } from './index'
import moment from 'moment'
import AnalysisApi from 'src/requests/analysis'
import { Modify } from 'ol/interaction.js'
import eventBus from 'src/utils/eventBus'
import pointImg from 'src/assets/images/points/youtian.png'
import gisUtils from 'src/utils/gis'
import AlarmApi from 'src/requests/alarm'
import { CloseCircle, Add } from '@vicons/ionicons5'
import type { SelectOption } from 'naive-ui'
import { ScaleLine } from 'ol/control'
import { AddPointPosition } from './index'
import { useUpload } from '../hooks/useUpload'
import {
  generateScaleLineFeature,
  getScalePosition,
  useScaleLineEnhance
} from 'src/views/publicServices/specialForecast/components/create-hooks/useScaleLineEnhance'
import { ScaleLineEnhance } from 'src/views/publicServices/specialForecast/components/create-hooks/scaleLineEnhance'
import { modifyStyle } from 'src/views/publicServices/specialForecast/components/create-hooks/useScreenshotBox'
import { useForm } from 'src/views/publicServices/specialForecast/components/create-hooks/useForm'
import type { ForecastNcPathItem, Form } from './create-hooks/useForm.type'
import {
  makeSureFeatureId,
  useVector
} from 'src/views/publicServices/specialForecast/components/create-hooks/useVector'
import { blobToBase64 } from 'src/utils/util'
import {
  ContentManager,
  DriftingContent,
  NetCageContent,
  SeaRanchContent,
  WaveHeightContent
} from 'src/views/publicServices/specialForecast/components/create-utils/contentBuilder'
import { useGraticule } from 'src/views/publicServices/specialForecast/components/create-hooks/useGraticule'

const { uploadFile, beforeUpload, shpObj } = useUpload()
const { defaultForm, form, resetForm, rules } = useForm()
let { vectorLayer, vectorSource, labelSource, labelLayer, projectPointStyle } =
  useVector()

const screenshotExtent = ref<number[]>([108.11, 17.95, 111.4, 20.4])

const message = useMessage()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object,
    default() {
      return {}
    }
  },
  title: {
    type: String,
    default: '新建专项预报'
  }
})
const emit = defineEmits(['update:visible'])
// 比例尺对象
let scaleLine: ScaleLineEnhance | null = null
const formRef = ref()

function onClose() {
  emit('update:visible', false)
  map.value.removeLayer(vectorLayer)
  Object.assign(form, defaultForm()) // 重置form为初始值
  areaList.value = []
  vectorSource.clear()
}

// watch(
//   () => props.info,
//   val => {
//     if (val) {
//       Object.keys(form).forEach((item: string) => {
//         if (Object.prototype.hasOwnProperty.call(form, item)) {
//           form[item as keyof Form] = val[item]
//         }
//       })
//       form.forecastArea = JSON.parse(val.forecastArea)
//       form.forecastAreaCustomize = JSON.parse(val.forecastAreaCustomize)
//       if (form.sourceCode === 'grid' && typeof form.elementName === 'string') {
//         form.elementName = form.elementName?.split(',')
//         elementList.value.forEach(item => {
//           if (form.elementName?.includes(item.name)) {
//             elementSelected.value.push(item)
//           }
//         })
//       }
//       if (typeof form.forecastNcPath === 'string') {
//         form.forecastNcPath = JSON.parse(form.forecastNcPath)
//       }
//
//       areaList.value = JSON.parse(val.forecastAreaCustomize)
//     }
//   }
// )

onMounted(() => {
  const val = props.info
  if (val) {
    Object.keys(form).forEach((item: string) => {
      if (Object.prototype.hasOwnProperty.call(form, item)) {
        form[item as keyof Form] = val[item]
      }
    })
    form.forecastArea = JSON.parse(val.forecastArea)
    form.forecastAreaCustomize = JSON.parse(val.forecastAreaCustomize)
    if (form.sourceCode === 'grid' && typeof form.elementName === 'string') {
      form.elementName = form.elementName?.split(',')
      elementList.value.forEach(item => {
        if (form.elementName?.includes(item.name)) {
          elementSelected.value.push(item)
        }
      })
    }
    if (typeof form.forecastNcPath === 'string') {
      form.forecastNcPath = JSON.parse(form.forecastNcPath)
    }

    areaList.value = JSON.parse(val.forecastAreaCustomize)
  }
})

let pointVisible = ref(false)
let isPoint = ref(false) // 预报区域是否添加点
function onAddArea(type: string) {
  pointPosition.value = []
  areaVisible.value = true
  isPoint.value = type === 'point'
}

function onAddPointPosition(item: AreaType, index: number) {
  editIndex.value = index
  let { graphicJson } = areaList.value[editIndex.value] || {}
  if (graphicJson) {
    let graphicObj = JSON.parse(graphicJson)
    pointPosition.value = graphicObj.geometry.coordinates
  }
  pointVisible.value = true
}

function onSavePoint({ lon, lat }: { lon: string; lat: string }) {
  let feature = vectorSource.getFeatureById(areaList.value[editIndex.value].id)
  if (feature) {
    vectorSource.removeFeature(feature)
  }
  let point = new Feature({
    geometry: new Point([parseFloat(lon), parseFloat(lat)])
  })
  point.setId(areaList.value[editIndex.value].id)
  point.setProperties({
    name: areaList.value[editIndex.value].name,
    id: areaList.value[editIndex.value].id
  })
  point.setStyle(
    new Style({
      image: new Icon({
        src: pointImg,
        // anchor: [0.5, 1], // 设置图标锚点
        scale: [0.6, 0.6]
      })
    })
  )
  vectorSource.addFeature(point)
  formatGeoJson()
}

function drawPoint(index: number) {
  isPoint.value = true
  editIndex.value = index
  map.value.removeInteraction(draw)
  addInteractions()
}

function addInteractions() {
  draw = new Draw({
    source: vectorSource,
    type: isPoint.value ? 'Point' : 'Polygon'
  })
  map.value.addInteraction(draw)
  draw.on('drawend', drawend)
}

function disableStartTime(ts: number) {
  if (form.startTime) {
    return ts < new Date(form.startTime).getTime()
  }
  return false
}

function disablePreviousDate(ts: number) {
  if (form.startTime) {
    return ts < moment(form.forecastTimeS).valueOf()
  }
  return false
}

let map = ref()

const { toggleGraticule } = useGraticule({
  map: () => toRaw(map.value),
  extent: screenshotExtent
})
// {
//   // return [108.11, 17.95, 111.4, 20.4]
//   const features = vectorSource.getFeatures()
//   const featureFilter = features.filter(
//     (item: any) => item.getProperties().name === 'screenshot'
//   )
//   const element = featureFilter[0] as Feature<Polygon> | undefined
//   const geometry = element?.getGeometry()
//   const extent = geometry?.getExtent() as [number, number, number, number] | undefined
//
//   return screenshotExtent
// }

function onMapReady(val: any) {
  if (val) {
    map.value = val
    let { scaleLine: _scaleLine } = useScaleLineEnhance(() => map.value)
    scaleLine = _scaleLine
    map.value.addLayer(vectorLayer) // 添加绘制图层
    // map.value.addInteraction(draw) // 添加绘制交互
    map.value.addInteraction(modify)
    map.value.addLayer(pointLayer)
    map.value.addControl(scaleLine)
    draw.setActive(false)
    modify.setActive(false)
    areaList.value.forEach((item: any, index: number) => {
      showPolygon(item)
    })
  }
}

function onChangeSource(val: string) {
  if (val === 'grid') {
    elementList.value = gridElementList
  } else {
    elementList.value = nmefcElementList
  }

  form.elementName = null
  form.forecastTimeS = null
  form.forecastTimeE = null
  form.startTime = null
}

let elementList = ref<any[]>(gridElementList) //要素列表
let elementSelected = ref<SelectOption[]>([]) //要素选中

async function onSelectElement(val: any, option: SelectOption[]) {
  form.forecastNcPath = []
  elementSelected.value = option
  const arr: any[] = []
  for (let element of elementSelected.value) {
    if (form.sourceCode === 'grid') {
      arr.push(...(await getForecastTime(element.code as string)))
    } else {
      let result = elementList.value.find((item: any) => item.name === val)
      arr.push(...(await getForecastTime(result?.code as string)))
    }
  }
  forecastTimeList.value = filterCommonTime(
    arr.map(_ => _.data_time),
    elementSelected.value.length
  ).map(_ => ({ data_time: _ }))

  form.startTime && (await formatForecastNcPath())
}

function filterCommonTime(timeArr: string[], count: number): string[] {
  const obj: Record<string, number> = {}
  timeArr.forEach(item => {
    if (obj[item] === undefined) {
      obj[item] = 1
    } else {
      obj[item] += 1
    }
  })
  const arr: string[] = []
  Object.keys(obj).forEach((key: string) => {
    if (obj[key] === count) {
      arr.push(key)
    }
  })
  return arr
}

function formatNcPath(data: ForecastNcPathItem[]) {
  const mergeMap = new Map()
  data.forEach((item: ForecastNcPathItem) => {
    if (mergeMap.has(item.ncPath)) {
      mergeMap.get(item.ncPath).push(...item.product)
    } else {
      mergeMap.set(item.ncPath, item.product)
    }
  })

  const result = Array.from(mergeMap).map(([ncPath, product]) => ({
    ncPath,
    product
  }))
  return result
}

let forecastTimeList = ref<any[]>([]) // 起报时间列表

async function getForecastTime(productId: string) {
  const params = {
    orderBy: 'data_time',
    sort: 'desc',
    _tz: 'GMT'
  }
  return ProductApi.getForecastTime(productId, params)
    .then((res: any) => {
      if (res?.length) {
        // forecastTimeList.value = res
      } else {
        message.error('暂无数据')
      }
      return res?.length ? res : []
    })
    .catch(e => {
      console.error(e, 'eeee')
    })
}

async function onSelectTime(val: any) {
  form.forecastTimeS = val
  formatForecastNcPath()
}

async function formatForecastNcPath() {
  if (form.sourceCode === 'grid') {
    let result: ForecastNcPathItem[] = []
    for (let i = 0, len = elementList.value.length; i < len; i++) {
      let item = elementList.value[i]
      if (form.elementName?.includes(item.nameWithUnit)) {
        let elementCode = elementSelected.value.find(
          (ite: any) => ite.name === item.name
        )
        let ncPath = await getOriginNcPath(elementCode?.code as string)
        const obj: ForecastNcPathItem = {
          ncPath,
          product: []
        }
        obj.product.push({
          productId: item.identity,
          productName: item.nameWithUnit
        })
        result.push(obj)
      }
    }

    form.forecastNcPath = formatNcPath(result)
  } else {
    let result = elementList.value.find(
      (item: any) => item.name === form.elementName
    )
    let ncPath = await getOriginNcPath(result?.code)
    const obj: ForecastNcPathItem = {
      ncPath,
      product: [
        {
          productId: result?.identity,
          productName: result?.nameWithUnit
        }
      ]
    }
    form.forecastNcPath.push(obj)
  }
}

async function getOriginNcPath(code: string) {
  if (!form.startTime) {
    return null
  }

  const params = {
    beginTime: form.startTime,
    endTime: moment(form.startTime).format('YYYY-MM-DD 23:59:59'),
    _tz: 'GMT',
    orderBy: 'forecast_time',
    sort: 'asc'
  }

  try {
    const res = (await AnalysisApi.getProductGeojson(code, params)) as any
    const result = res.map((item: any) => item.file_path)
    return result.join(',')
  } catch (e: any) {
    const { msg = null } = e?.response?.data || e?.data || {}
    message.error(msg || '获取数据失败')
    return null
  }
}

type AreaType = {
  name?: string
  graphicJson?: string
  id: number | string
  isPoint?: boolean
}

let areaList = ref<AreaType[]>([]) // 区域列表
provide('areaList', areaList.value)
let areaVisible = ref(false)

let editIndex = ref(-1)
let isDBClick = ref(false)

function onDel(index: number, item: AreaType) {
  areaList.value.splice(index, 1)
  form.forecastArea.features.splice(index, 1)
  const features = vectorSource.getFeatures()
  features.forEach((feature: any) => {
    if (feature.get('name') === item.name) {
      vectorSource.removeFeature(feature)
    }
  })
  const features2 = pointSource.getFeatures()
  features2.forEach((feature: any) => {
    if (feature.get('name') === item.name) {
      pointSource.removeFeature(feature)
    }
  })
}

const areaForm = reactive({
  areaName: ''
})

function onDialogConfirm() {
  if (isDBClick.value) {
    areaList.value[editIndex.value].name = areaForm.areaName
    if (areaList.value[editIndex.value].graphicJson) {
      let graphicJson = JSON.parse(
        areaList.value[editIndex.value]?.graphicJson as string
      )
      form.forecastArea.features.forEach((item: any) => {
        let { id } = item.properties || {}
        if (id === graphicJson.properties.id) {
          item.properties.name = areaForm.areaName
        }
      })
    }

    isDBClick.value = false
  } else {
    areaList.value.push({
      name: areaForm.areaName,
      id: new Date().getTime(),
      isPoint: isPoint.value
    })
  }
  areaVisible.value = false
  areaForm.areaName = ''
}

let draw = new Draw({
  source: vectorSource,
  type: 'Polygon',
  trace: true,
  style: {
    'stroke-color': 'rgba(255, 255, 100, 0.25)',
    'stroke-width': 1.5,
    'fill-color': 'rgba(255, 255, 100, 0.25)'
  }
})

let clickTimes = 0

function clickFun(item: any, index: number) {
  clickTimes++
  if (clickTimes === 2) {
    // 处理双击事件
    clickTimes = 0
    onChangeName(item, index)
  } else {
    setTimeout(() => {
      if (clickTimes === 1) {
        // 处理单击事件
        clickTimes = 0
        // showPolygon(item)
      }
    }, 250) // 设置一个小于用户双击的时间间隔的延时
  }
}

// 双击修改区域名称
function onChangeName(item: any, index: number) {
  areaForm.areaName = item.name
  areaVisible.value = true
  isDBClick.value = true
  editIndex.value = index
  draw.setActive(false)
  modify.setActive(false)
}

// 点击显示绘制区域
function showPolygon(item: any) {
  // vectorSource.refresh()
  if (item.graphicJson) {
    const json = JSON.parse(item.graphicJson)
    const geojsonFormat = new GeoJSON()
    const features = geojsonFormat.readFeatures(
      json
    ) as Feature<SimpleGeometry>[]
    vectorSource.addFeatures(features)
  }
}

function drawPolygon(item: any, index: number) {
  editIndex.value = index
  if (item.graphicJson) {
    modify.setActive(true)
  } else {
    addInteractions()
  }
  message.warning('开始编辑')
}

draw.on('drawend', drawend)
const pointPosition = ref<number[]>([])

function drawend(event: any) {
  draw.setActive(false)
  const feature = event.feature
  let type = feature.getGeometry()?.getType()
  if (type === 'Point') {
    let coords = feature.getGeometry()?.getCoordinates()
    pointPosition.value = coords
    let pointFeature = vectorSource.getFeatureById(
      areaList.value[editIndex.value].id
    )
    pointFeature && vectorSource.removeFeature(pointFeature)
    // feature.setStyle(
    //   new Style({
    //     image: new Icon({
    //       src: pointImg,
    //       scale: [0.6, 0.6]
    //     })
    //   })
    // )
  }
  // 设置 properties
  feature.setId(areaList.value[editIndex.value].id)
  feature.setProperties({
    id: areaList.value[editIndex.value].id,
    name: areaList.value[editIndex.value].name
  })

  formatGeoJson()
}

function formatGeoJson() {
  setTimeout(() => {
    const length = pointSource.getFeatures().length
    const features: Feature<SimpleGeometry>[] = []
    vectorSource.forEachFeature((item: Feature<SimpleGeometry>) => {
      if (item.getProperties()?.name !== 'screenshot') {
        features.push(item)
      }
    })
    const geojsonFormat = new GeoJSON()
    const geojsonObject = geojsonFormat.writeFeatures(features)
    const json = JSON.parse(geojsonObject)
    // 现在 geojsonObject 是一个 GeoJSON 对象，你可以将其转换为字符串或者直接使用
    const graphicJson = JSON.stringify(json.features[editIndex.value - length])
    areaList.value[editIndex.value].graphicJson = graphicJson
    form.forecastArea.features.push(json.features[editIndex.value - length])
    console.log(areaList.value, 'value-----')
  }, 0)
}

/**
 * shite
 */
async function formatGeoJsonForProjectPoint(): Promise<void> {
  return new Promise(resolve => {
    setTimeout(() => {
      const length = vectorSource
        .getFeatures()
        .filter(f => f.get('name') !== 'screenshot').length
      const features: Feature<SimpleGeometry>[] = []
      pointSource.forEachFeature((item: Feature<SimpleGeometry>) => {
        if (item.getProperties()?.name !== 'screenshot') {
          features.push(item)
        }
      })
      const geojsonFormat = new GeoJSON()
      const geojsonObject = geojsonFormat.writeFeatures(features)
      const json = JSON.parse(geojsonObject)
      const graphicJson = JSON.stringify(
        json.features[editIndex.value - length]
      )
      areaList.value[editIndex.value].graphicJson = graphicJson
      form.forecastArea.features.push(json.features[editIndex.value - length])
      resolve()
    }, 0)
  })
}

// 修改多边形
const modify = new Modify({
  source: vectorSource,
  deleteCondition: () => false,
  insertVertexCondition: () => false // 禁止添加新顶点
})

modify.on('modifystart', function (event) {
  event.features.forEach(function (feature) {
    feature.set(
      'modifyGeometry',
      { geometry: feature?.getGeometry()?.clone() },
      true
    )
  })
})

modify.on('modifyend', function (event) {
  console.log('modifyend', event)
  try {
    event.features.forEach(function (feature) {
      console.log(feature, 'feature')
      const modifyGeometry = feature.get('modifyGeometry')
      let props = feature.getProperties()
      const geometry = feature.getGeometry()
      let type = geometry?.getType()
      // 截图保持矩形
      if (type === 'Polygon') {
        if (props.name === 'screenshot') {
          const _geom = geometry as Polygon
          let newCoordinates = gisUtils.screenshotEditRect(
            feature,
            modifyGeometry
          )
          // 设置新的坐标
          // @ts-ignore
          _geom.setCoordinates([newCoordinates])
          const extent = _geom.getExtent()
          if (extent) {
            screenshotExtent.value = extent
          }
        }
        feature.set(
          'modifyGeometry',
          { geometry: feature?.getGeometry()?.clone() },
          true
        )
      }

      form.forecastArea.features.forEach((item: any) => {
        let { id } = item.properties || {}
        if (id === feature.getId()) {
          //@ts-ignore
          let coords = geometry?.getCoordinates()
          item.geometry.coordinates = coords
        }
      })

      let areaIndex = areaList.value.findIndex(
        (item: any) => item.id === feature.getId()
      )
      if (areaIndex !== -1) {
        const geojsonFormat = new GeoJSON()
        const geojsonObject = geojsonFormat.writeFeature(feature)
        areaList.value[areaIndex].graphicJson = JSON.stringify(geojsonObject)
      }
    })
  } catch (e) {
    console.warn(e)
  }
})

function onCreate() {
  formRef.value.validate((valid: any) => {
    if (!valid) {
      let params = JSON.parse(JSON.stringify(form))
      makeSureFeatureId(params.forecastArea)

      params.forecastAreaCustomize = JSON.stringify(areaList.value)
      params.forecastArea = JSON.stringify(params.forecastArea)
      params.forecastNcPath = JSON.stringify(params.forecastNcPath)
      if (form.sourceCode === 'grid') {
        params.elementName = params.elementName.join(',')
      }
      if (props.info?.id) {
        params.id = props.info.id
        PublicService.updateInfoOfForecast(params)
          .then(() => {
            message.success('修改成功')
            onClose()
          })
          .catch((e: any) => {
            let { msg = null } = e?.response?.data || e?.data || {}
            message.error(msg || '请求失败')
          })
      } else {
        const propArr = form.forecastArea.features.map((item: any) => {
          return item.properties || {}
        })
        // 生成警报信息
        const contentManager = new ContentManager({
          generator: [
            new WaveHeightContent(),
            new NetCageContent({
              categoryNames: propArr
                .map((i: Record<string, any>) => i.category)
                .filter(Boolean)
            }),
            new SeaRanchContent({
              seaRanchStationInfo: propArr.filter((i: Record<string, any>) => {
                const keys = Object.keys(SeaRanchContent.mapping)
                return keys.includes(i.type)
              }),
              driftingImagePath: form.driftingImagePath
            })
          ]
        })
        Object.assign(params, contentManager.contentsMap)
        // 拼接 url
        if (params.driftingImagePath) {
          params.imagePath = `${params.imagePath},${params.driftingImagePath}`
        }
        PublicService.saveInfoOfForecast(params)
          .then(() => {
            message.success('创建成功')
            onClose()
          })
          .catch((e: any) => {
            let { msg = null } = e?.response?.data || e?.data || {}
            message.error(msg || '请求失败')
          })
      }
      onReset()
    }
  })
}

const pointSource = new VectorSource<SimpleGeometry>()
const pointLayer = new VectorLayer({
  source: pointSource
})

/**
 * 增加海上工程点
 * @param data
 */
async function addPoint(
  data: {
    code: string
    height: number
    id: string
    latitude: number
    longitude: number
    type: string
    category: string
  }[]
) {
  // pointSource.refresh()
  for (let i = 0; i < data.length; i++) {
    const features = pointSource.getFeatures()
    const idList = features.map((f: Feature<SimpleGeometry>) => {
      const properties = f.getProperties()
      return properties.id
    })
    const item = data[i]
    if (!idList.includes(item.id)) {
      let feature = new Feature({
        geometry: new Point([item.longitude, item.latitude])
      })
      const iconStyle = projectPointStyle({
        name: item.code,
        category: item.category
      })
      feature.setStyle([iconStyle])
      feature.setProperties({
        ...item,
        name: item.code
      })
      pointSource.addFeature(feature)
      areaList.value.push({
        id: item.id,
        name: item.code,
        isPoint: true
      })
      editIndex.value = areaList.value.length - 1
      await formatGeoJsonForProjectPoint()
    }
  }

  const features = pointSource.getFeatures()
  const delIndexArr: number[] = []

  for (let i = 0; i < features.length; i++) {
    const id = features[i].getProperties().id
    if (!data.map(i => i.id).includes(id)) {
      const indexOf = areaList.value.findIndex(item => item.id === id)
      delIndexArr.push(indexOf)
    }
  }
  delIndexArr.sort((a, b) => b - a)
  delIndexArr.forEach(i => onDel(i, areaList.value[i]))
}

function onReset() {
  resetForm()
  vectorSource.clear()
}

function onChangeText() {
  modify.setActive(false)
}

function onAreaEdit() {
  modify.setActive(true)
}

// 截图区域红色边框添加
function onScreenshot() {
  const features = vectorSource.getFeatures()
  const hasScreenshot = features.find(f => {
    const properties = f.getProperties()
    return properties.name === 'screenshot'
  })
  if (hasScreenshot) {
    return
  }
  modify.setActive(false)
  const coordinates = [
    [
      [108.11, 17.95],
      [111.4, 17.95],
      [111.4, 20.4],
      [108.11, 20.4],
      [108.11, 17.95]
    ]
  ]
  const rectangle = new Feature({
    geometry: new Polygon(coordinates)
  })
  rectangle.setProperties({
    name: 'screenshot',
    role: 'border',
    $originalPoints: coordinates
  })
  // 设置矩形的样式
  rectangle.setStyle(modifyStyle)
  // rectangle.setStyle(
  //   new Style({
  //     fill: new Fill({
  //       color: 'transparent'
  //     }),
  //     stroke: new Stroke({
  //       color: 'red',
  //       width: 2
  //     })
  //   })
  // )
  // 左下角比例尺
  // if (scaleLine) {
  //   const scaleLineFeature = generateScaleLineFeature(rectangle, scaleLine)
  //   vectorSource.addFeature(scaleLineFeature)
  // }
  vectorSource.addFeature(rectangle)
}

// 比例尺
function onScale() {
  let dom = document.getElementsByClassName('ol-scale-line')[0] as HTMLElement
  if (dom.style.display === 'none') {
    dom.style.display = 'block'
  } else {
    dom.style.display = 'none'
  }
}

// 预报图
const loading = ref(false)
// 漂移路径
const riftingLoading = ref(false)

function checkRiftingImage(data: {
  file: UploadFileInfo
  fileList: UploadFileInfo[]
}) {
  if (data.file.file?.type !== 'image/gif') {
    message.error('请选择 gif 图片')
    return false
  }
  return true
}

function addRiftingImage(fileList: UploadFileInfo[]) {
  const uploadFileInfo = fileList?.[0]
  const file = uploadFileInfo.file
  if (!file) {
    return
  }
  riftingLoading.value = true
  blobToBase64(file)
    .then(str => {
      return AlarmApi.upLoadBase64Image({
        base64Image: str
      })
    })
    .then(res => {
      form.driftingImagePath = config.fileService + res
    })
    .finally(() => {
      riftingLoading.value = false
    })
    .catch(e => {
      message.error('无法上传图片')
    })
}

function addImage() {
  // onScreenshot()
  let features = vectorSource.getFeatures()
  const featureFilter = features.filter(
    (item: any) => item.getProperties().name === 'screenshot'
  )
  featureFilter.forEach(feature => {
    if (feature) {
      const properties = feature.getProperties()
      console.log(properties)
      properties.role === 'border' &&
        feature.setStyle(
          new Style({
            fill: new Fill({
              color: 'transparent'
            }),
            stroke: new Stroke({
              color: 'transparent',
              width: 2
            })
          })
        )
      loading.value = true
      setTimeout(() => {
        const geometry: any = feature?.getGeometry()
        const extent = geometry.getExtent()
        let url = gisUtils.exportImageV2(map.value, extent)
        AlarmApi.upLoadBase64Image({
          base64Image: url
        })
          .then((res: any) => {
            loading.value = false
            form.imagePath = config.fileService + res

            properties.role === 'border' && feature?.setStyle(modifyStyle)
          })
          .catch((e: any) => {
            loading.value = false
            console.error(e, 'eee')
          })
      }, 0)
    } else {
      message.error('请先点击截图工具')
      return false
    }
  })
}

function removeImage(index: number) {
  if (index === 0) {
    form.imagePath = ''
  } else if (index === 1) {
    form.driftingImagePath = ''
  }
}

function mergeArrays(A: AreaType[], B: AreaType[]) {
  return [...A, ...B.filter(b => !A.some(a => isEqual(a, b)))]
}

function isEqual(obj1: AreaType, obj2: AreaType) {
  return JSON.stringify(obj1) === JSON.stringify(obj2)
}

function timeStartUpdate(time: string) {
  const m = moment(time)
  if (!m.startOf('hours').isSame(m)) {
    form.forecastTimeS = m.startOf('hours').format('YYYY-MM-DD HH:mm:ss')
  }
  form.forecastTimeS = m.format('YYYY-MM-DD HH:mm:ss')
}

function timeEndUpdate(time: string) {
  const m = moment(time)
  if (!m.startOf('hours').isSame(m)) {
    form.forecastTimeE = m.startOf('hours').format('YYYY-MM-DD HH:mm:ss')
  }
  form.forecastTimeE = m.format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  eventBus.on('addDeviceLayer', (params: any) => {
    addPoint(params)
  })
  eventBus.on('uploadShp', (params: any) => {
    let result = mergeArrays(areaList.value, params)
    areaList.value = result
    params.forEach((item: AreaType) => {
      showPolygon(item)
    })
  })
})
onBeforeUnmount(() => {
  eventBus.off('addDeviceLayer')
  eventBus.off('uploadShp')
})
</script>
<style lang="scss" scoped>
.create-special-dialog {
  .dialog-content {
    box-sizing: border-box;
    padding: 20px;
  }

  .dialog-content-left {
    margin-right: 10px;
    width: 25%;
    flex-shrink: 0;
  }

  :deep(.n-form) {
    width: 100%;
    height: 589px;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 10px;
  }

  .btn-group {
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  #openlayers-map {
    width: 75%;
    height: 642px;
    position: relative;
  }

  i.icon-export {
    width: 26px;
    height: 26px;
    background-image: url(src/assets/images/icons/icon-export.png);
  }

  i.icon-point {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    background-image: url(src/assets/images/icons/icon-point.png);
  }

  i.icon-point1 {
    width: 32px;
    height: 32px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
      width: 20px;
      height: 20px;
      line-height: 32px;
      content: '';
      display: inline-block;
      background-image: url(src/assets/images/icons/icon-point.png);
      background-size: 100% 100%;
    }
  }

  i.icon-area {
    width: 20px;
    height: 20px;
    background-image: url(src/assets/images/icons/icon-area.png);
    margin: 0 10px;
    flex-shrink: 0;
  }

  i.icon-input {
    width: 32px;
    height: 32px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
      width: 20px;
      height: 20px;
      content: '';
      display: inline-block;
      background-image: url(src/assets/images/icons/icon-input.png);
      background-size: 100% 100%;
    }
  }

  i.icon-edit1 {
    width: 32px;
    height: 32px;
    background-image: url(src/assets/images/icons/icon-edit1.png);
    margin-right: 5px;
  }

  i.icon-del1 {
    width: 32px;
    height: 32px;
    background-image: url(src/assets/images/icons/icon-del1.png);
  }

  .area-name {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    color: #5a94db;
    background: #fafcfe;
    border-radius: 4px;
    border: 1px solid #e7f0fb;
    padding: 0 5px;
    margin-right: 5px;
    min-width: 126px;
  }

  .area-item {
    margin-bottom: 5px;
  }

  .upload-container {
    // box-sizing: border-box;
    // padding: 9px 12px;
    // background: #fafafa;
    // border-radius: 8px;
    // border: 1px solid #d9d9d9;
    // display: flex;
    .images {
      background: #fafafa;
      display: flex;
      flex-direction: column;
      justify-content: end;
      width: 103px;
      align-items: flex-end;
      position: relative;
      height: 105px;
      margin-right: 10px;

      .empty-img {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .add-icon {
          width: 24px;
          height: 24px;
          margin-bottom: 10px;
        }
      }

      .delete-icon {
        position: absolute;
        top: -6px;
        right: -12px;
        height: 24px;
        width: 24px;
      }
    }
  }
}

.add-area-dialog {
  .qx-form {
    box-sizing: border-box;
    padding: 20px 20px 0;
  }

  .btn-group {
    text-align: right;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 20px;
  }
}
</style>
