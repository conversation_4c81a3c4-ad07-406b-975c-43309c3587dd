package cn.piesat.data.making.server.model;

import lombok.Data;

import java.io.Serializable;

/**
 * kafka 警报信息
 *
 * <AUTHOR>
 */
@Data
public class AlarmInfo implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 原id
     */
    private String ids;
    /**
     * 预警类型：1风暴潮 2海浪
     */
    private Integer category;
    /**
     * 类型：0解除警报 1新增警报
     */
    private Integer releaseState;
    /**
     * 时间
     */
    private String alarmReleaseTime;
    /**
     * 描述
     */
    private String descriptionInformation;
}
