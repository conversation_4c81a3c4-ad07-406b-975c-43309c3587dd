package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.AreaTypeDao;
import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.entity.AreaType;
import cn.piesat.data.making.server.mapper.AreaTypeMapper;
import cn.piesat.data.making.server.service.AreaService;
import cn.piesat.data.making.server.service.AreaTypeService;
import cn.piesat.data.making.server.vo.AreaTypeVO;
import cn.piesat.data.making.server.vo.AreaVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 区域类型表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class AreaTypeServiceImpl extends ServiceImpl<AreaTypeDao, AreaType>
        implements AreaTypeService {

    @Resource
    private AreaService areaService;

    @Override
    public List<AreaTypeVO> getTreeList(String name, List<String> codes) {
        List<AreaType> typeList = this.list(new LambdaQueryWrapper<AreaType>().in(!CollectionUtils.isEmpty(codes),AreaType::getCode,codes));
        if (CollectionUtils.isEmpty(typeList)) {
            return Collections.EMPTY_LIST;
        }
        List<AreaTypeVO> voList = AreaTypeMapper.INSTANCE.entityListToVoList(typeList);
        //区域列表
        AreaDTO area = new AreaDTO();
        area.setName(name);
        List<AreaVO> areaList = areaService.getList(area);
        //封装区域
        if (!CollectionUtils.isEmpty(areaList)) {
            Map<String, List<AreaVO>> areaMap = areaList.stream().collect(Collectors.groupingBy(AreaVO::getAreaTypeCode));
            voList.stream().forEach(vo -> vo.setAreaList(areaMap.get(vo.getCode())));
        }
        return voList;
    }
}





