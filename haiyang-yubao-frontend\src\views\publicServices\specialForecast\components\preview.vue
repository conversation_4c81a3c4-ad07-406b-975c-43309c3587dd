<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-26 10:04:12
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-30 15:42:40
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/preview.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <qx-dialog
    title="预览"
    :visible="visible"
    width="906px"
    class="preview-special-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <div class="report-content">
        <iframe
          :src="filePath"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </div>
    </template>
    <template #suffix>
      <div class="btn-group text-right">
        <qx-button class="cancel" @click="onClose">取消</qx-button>
        <qx-button class="primary" @click="onDownload">下载</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { QxDialog } from 'src/components/QxDialog'
import { QxButton } from 'src/components/QxButton'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  filePath: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:visible'])

function onClose() {
  emit('update:visible', false)
}

function onDownload() {
  const link = document.createElement('a')
  link.href = props.filePath
  let arr = props.filePath.split('/')
  let fileName = arr[arr.length - 1].split('.')[0]
  link.download = fileName //下载的文件名称
  link.click()
}
</script>

<style lang="scss">
.preview-special-dialog {
  .btn-group {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  .report-content {
    height: 700px;
  }
}
</style>
