package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourWsODao;
import cn.piesat.data.making.server.entity.OceanStationHourWsO;
import cn.piesat.data.making.server.service.OceanStationHourWsOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-从测量日界开始十分钟风速风向-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourWsOServiceImpl extends ServiceImpl<OceanStationHourWsODao, OceanStationHourWsO>
        implements OceanStationHourWsOService {

    @Resource
    private OceanStationHourWsODao oceanStationHourWsODao;

    @Override
    public List<OceanStationHourWsO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourWsODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourWsODao.getMaxCreateTime();
    }
}





