package cn.piesat.data.making.server.dto;

import java.util.Date;

import java.io.Serializable;

/**
 * 台风位置预警完成数据DTO类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:21
 */
public class FmTyphoonCompletionBDTO implements Serializable {

    private static final long serialVersionUID = -20207487686020285L;

    public interface Save {
    }

    /**
     * 主键
     */
    private Long id;
    /**
     * 台风编号
     */
    private String tfbh;
    /**
     * 时间
     */
    private Date time;
    /**
     * 位置
     */
    private String location;
    /**
     * 完成
     */
    private String completion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCompletion() {
        return completion;
    }

    public void setCompletion(String completion) {
        this.completion = completion;
    }
}
