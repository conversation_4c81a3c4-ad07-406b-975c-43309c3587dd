package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.ForecastRecordDetailDao;
import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import cn.piesat.data.making.server.service.ForecastRecordDetailService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 预报记录详情表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastRecordDetailServiceImpl extends ServiceImpl<ForecastRecordDetailDao, ForecastRecordDetail>
        implements ForecastRecordDetailService {

    @Override
    public List<ForecastRecordDetail> getList(Long forecastRecordId) {
        return this.list(createQueryWrapper(forecastRecordId));
    }

    private LambdaQueryWrapper<ForecastRecordDetail> createQueryWrapper(Long forecastRecordId) {
        LambdaQueryWrapper<ForecastRecordDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (forecastRecordId != null) {
            queryWrapper.eq(ForecastRecordDetail::getForecastRecordId, forecastRecordId);
        }
        return queryWrapper.orderByAsc(ForecastRecordDetail::getCreateTime);
    }
}





