package cn.piesat.data.making.server.config;

import cn.piesat.security.ucenter.starter.utils.UserUtils;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Component
public class MybatisMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        Boolean isLogin = this.isLogin();
        if (isLogin) {
            this.setFieldValByName("createUserId", UserUtils.getUserId(), metaObject);
            this.setFieldValByName("createUser", UserUtils.getUserInfo().getRealName(), metaObject);
            this.setFieldValByName("createTime", new Date(), metaObject);
            this.setFieldValByName("updateUserId", UserUtils.getUserId(), metaObject);
            this.setFieldValByName("updateUser", UserUtils.getUserInfo().getRealName(), metaObject);
            this.setFieldValByName("updateTime", new Date(), metaObject);
        } else {
            this.setFieldValByName("createUserId", 1L, metaObject);
            this.setFieldValByName("createUser", "1", metaObject);
            this.setFieldValByName("createTime", new Date(), metaObject);
            this.setFieldValByName("updateUserId", 1L, metaObject);
            this.setFieldValByName("updateUser", "1", metaObject);
            this.setFieldValByName("updateTime", new Date(), metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Boolean isLogin = this.isLogin();
        if (isLogin) {
            this.setFieldValByName("updateUserId", UserUtils.getUserId(), metaObject);
            this.setFieldValByName("updateUser", UserUtils.getUserInfo().getRealName(), metaObject);
            this.setFieldValByName("updateTime", new Date(), metaObject);
        } else {
            this.setFieldValByName("updateUserId", 1L, metaObject);
            this.setFieldValByName("updateUser", "1", metaObject);
            this.setFieldValByName("updateTime", new Date(), metaObject);
        }
    }

    private Boolean isLogin() {
        Boolean isLogin = false;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Cookie[] cookies = request.getCookies();
            if (cookies == null) {
                return isLogin;
            }
            for (Cookie cookie : cookies) {
                if ("u-token".equals(cookie.getName()) && StringUtils.isNotBlank(cookie.getValue())) {
                    isLogin = true;
                }
            }
        }
        return isLogin;
    }
}

