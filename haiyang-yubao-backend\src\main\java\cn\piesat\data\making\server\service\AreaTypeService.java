package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.AreaType;
import cn.piesat.data.making.server.vo.AreaTypeVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 区域类型表服务接口
 *
 * <AUTHOR>
 */
public interface AreaTypeService extends IService<AreaType> {

    /**
     * 查询列表
     */
    List<AreaTypeVO> getTreeList(String name, List<String> codes);
}




