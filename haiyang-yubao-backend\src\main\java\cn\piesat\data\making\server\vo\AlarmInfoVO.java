package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 警报VO类
 *
 * <AUTHOR>
 */
@Data
public class AlarmInfoVO implements Serializable {

    /**
     * 预警来源
     */
    private String alarmSource;
    /**
     * 预警标题
     */
    private String alarmTitle;
    /**
     * 预警级别
     */
    private String alarmLevel;
    /**
     * 预警时效
     */
    private String alarmTime;
    /**
     * 警报内容
     */
    private String alarmContent;
    /**
     * 警报图
     */
    private String alarmImages;
    /**
     * 预警区域
     */
    private String alarmArea;
    /**
     * 发布时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date releaseTime;
}



