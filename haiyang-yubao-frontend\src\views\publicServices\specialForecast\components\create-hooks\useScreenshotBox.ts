import type { FeatureLike } from 'ol/Feature'
import { Geometry } from 'ol/geom'
import { Circle, Stroke, Style } from 'ol/style'
import Polygon, { fromExtent } from 'ol/geom/Polygon'
import { Feature } from 'ol'
import { vectorFactory } from './useVector'

export function useScreenshotBox() {
  const { vectorLayer: screenshotVecLayer, vectorSource: screenshotVecSource } =
    vectorFactory()
  return {
    defaultScreenshotFeature,
    screenshotVecLayer,
    screenshotVecSource
  }
}

/**
 * 判断是否是 Polygon
 * @param geom
 */
function isPolygon(geom: Geometry | unknown): geom is Polygon {
  return geom instanceof Polygon
}

export function modifyStyle(f: FeatureLike, i: number) {
  const geom = f.getGeometry() as Polygon
  const coordinates = geom.getCoordinates()
  const extent = geom.getExtent()
  const polygon = fromExtent(extent!)

  return [
    new Style({
      stroke: new Stroke({ color: 'red', width: 1 }),
      geometry: polygon
    }),
    new Style({
      image: new Circle({
        stroke: new Stroke({ color: 'red', width: 4 }),
        radius: 2
      })
    })
  ]
}

export function defaultScreenshotFeature() {
  return new Feature({
    geometry: new Polygon([
      [
        [108.11, 17.95],
        [111.4, 17.95],
        [111.4, 20.4],
        [108.11, 20.4],
        [108.11, 17.95]
      ]
    ])
  })
}
