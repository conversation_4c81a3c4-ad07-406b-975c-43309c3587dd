package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.ElementDao;
import cn.piesat.data.making.server.dto.ElementDTO;
import cn.piesat.data.making.server.entity.Element;
import cn.piesat.data.making.server.mapper.ElementMapper;
import cn.piesat.data.making.server.service.ElementService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.ElementVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 要素字典表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ElementServiceImpl extends ServiceImpl<ElementDao, Element>
        implements ElementService {

    @Override
    public PageResult<ElementVO> getPage(ElementDTO dto, PageParam pageParam) {
        Page<Element> page = this.page(new Page<>(pageParam.getPageNum(), pageParam.getPageSize()), createQueryWrapper(dto));
        List<ElementVO> voList = ElementMapper.INSTANCE.entityListToVoList(page.getRecords());
        return new PageResult(voList, pageParam.getPageNum(), pageParam.getPageSize(), page.getTotal());
    }

    @Override
    public List<ElementVO> getList() {
        List<Element> list = this.list(createQueryWrapper(null));
        return ElementMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public ElementVO getInfoById(Long id) {
        return ElementMapper.INSTANCE.entityToVo(this.getById(id));
    }

    @Override
    public void save(ElementDTO dto) {
        if (dto.getId() == null) {
            this.save(ElementMapper.INSTANCE.dtoToEntity(dto));
        } else {
            this.updateById(ElementMapper.INSTANCE.dtoToEntity(dto));
        }
    }

    @Override
    public void deleteById(Long id) {
        this.deleteById(id);
    }

    private LambdaQueryWrapper<Element> createQueryWrapper(ElementDTO dto) {
        LambdaQueryWrapper<Element> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper.orderByAsc(Element::getCreateTime);
    }
}





