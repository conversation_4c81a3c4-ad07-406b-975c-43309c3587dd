<template>
  <template v-for="item in props.menuData" :key="item.path">
    <div v-if="item.meta.showMenu" class="qx-menu-item">
      <template v-if="item.children && item.children.length > 0">
        <div class="qx-sub-menu">
          <div
            class="qx-menu-item__label"
            :class="activePath === item.path ? 'active' : ''"
          >
            <span class="label">{{ item.meta.title }}</span>
          </div>
          <qx-menu-item
            :menu-data="item.children"
            @node-click="handleClickItem"
          ></qx-menu-item>
        </div>
      </template>
      <template v-else>
        <div
          class="qx-menu-item__label"
          :class="[
            activePath === item.path ? 'active' : '',
            collapsed ? 'qx-menu-item__label--collapsed' : ''
          ]"
          @click="handleClickItem(item)"
        >
          <i
            v-if="item.meta.icon"
            :class="item.meta.icon"
            :title="collapsed ? item.meta.title : ''"
          ></i>
          <span class="label">{{ item.meta.title }}</span>
        </div>
      </template>
    </div>
  </template>
</template>
<script setup lang="ts" name="qx-menu-item">
import { MenuType } from './types'
import { ref, watch, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import eventBus from 'src/utils/eventBus'

const route = useRoute()
const activePath = ref<string>('')
const collapsed = ref<boolean>(false)

const props = defineProps({
  menuData: {
    type: Array<MenuType>,
    default: () => []
  },
  defaultActive: {
    type: String,
    default: () => ''
  }
})

watch(
  () => route.path,
  val => {
    activePath.value = val
  },
  { immediate: true }
)

const emit = defineEmits(['nodeClick'])
function handleClickItem(item: any) {
  emit('nodeClick', item)
}

eventBus.on('collapsed', params => {
  collapsed.value = params
})
onBeforeUnmount(() => {
  eventBus.off('collapsed')
})
</script>

<style lang="scss">
.qx-menu-item {
  .qx-menu-item__label--collapsed {
    span.label {
      display: none;
    }
    i.icon {
      margin-right: 0;
    }
  }
}
</style>
