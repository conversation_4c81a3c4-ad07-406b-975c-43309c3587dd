package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.DataMakingServerApplication;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPushResultDaoTest {

    @Autowired
    private FmPushResultDao fmPushResultDaoImpl;

    @Test
    public void testQueryListMapByParam(){
        List<Map<String,Object>> resList = fmPushResultDaoImpl.queryListMapByParam("SASFCR");

        Assert.assertNotNull(resList);
    }
}
