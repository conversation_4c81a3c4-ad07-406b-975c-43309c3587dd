package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.FmPushResult;
import com.baomidou.mybatisplus.extension.service.IService;

public interface FmPushResultService extends IService<FmPushResult> {

    /**
     * 保存要推送的信息
     *
     * @param pushChannel 推送途径
     * @param bizCode 推送业务编码
     * @param pushMsg 推送消息，如果是邮件，就代表邮件体，如果是传真，可以置空
     * @param pushContent 推送内容或文件地址
     * @param fileName
     * @param filePath
     */
    void addFmPushResList(String pushChannel,String bizCode,String pushMsg,String pushContent,String fileName,String filePath);


    /**
     * 异步发送相关信息到外部接口
     */
    void syncPushData();

    /**
     * 更新FmPushResult对象
     *
     * @param fmPushResult
     */
    void updateFmPushResult(FmPushResult fmPushResult);
}
