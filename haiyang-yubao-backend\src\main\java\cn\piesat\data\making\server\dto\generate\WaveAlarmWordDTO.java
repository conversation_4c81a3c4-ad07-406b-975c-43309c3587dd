package cn.piesat.data.making.server.dto.generate;


import com.deepoove.poi.data.PictureRenderData;

import java.util.List;

public class WaveAlarmWordDTO {
    private String levelColor;
    private String time;
    private String number;
    private String signUserName;
    private String title;
    private String summarize;
    private String alarmContent;
    private String defenseGuide;
    private List<PictureRenderData> images;
    private String makeUserName;
    private String phone;
    private String fax;
    private String eMail;
    private Typhoon typhoon;
    private Integer sourceType;

    private String cityAlarmContent;

    public String getCityAlarmContent() {
        return cityAlarmContent;
    }

    public void setCityAlarmContent(String cityAlarmContent) {
        this.cityAlarmContent = cityAlarmContent;
    }

    public String getLevelColor() {
        return levelColor;
    }

    public void setLevelColor(String levelColor) {
        this.levelColor = levelColor;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummarize() {
        return summarize;
    }

    public void setSummarize(String summarize) {
        this.summarize = summarize;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getDefenseGuide() {
        return defenseGuide;
    }

    public void setDefenseGuide(String defenseGuide) {
        this.defenseGuide = defenseGuide;
    }


    public List<PictureRenderData> getImages() {
        return images;
    }

    public void setImages(List<PictureRenderData> images) {
        this.images = images;
    }

    public String getMakeUserName() {
        return makeUserName;
    }

    public void setMakeUserName(String makeUserName) {
        this.makeUserName = makeUserName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public Typhoon getTyphoon() {
        return typhoon;
    }

    public void setTyphoon(Typhoon typhoon) {
        this.typhoon = typhoon;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public static class Typhoon{
        private String tfbh;
        private String time;
        private String name;
        /**
         * 强度
         */
        private String strong;
        /**
         * 经度
         */
        private Double lng;
        /**
         * 纬度
         */
        private Double lat;
        /**
         * 等级
         */
        private Integer power;
        /**
         * 中心气压
         */
        private Integer pressure;
        /**
         * 完成
         */
        private String completion;
        /**
         * 七级大风半径
         */
        private Integer radius7;

        public String getTfbh() {
            return tfbh;
        }

        public void setTfbh(String tfbh) {
            this.tfbh = tfbh;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStrong() {
            return strong;
        }

        public void setStrong(String strong) {
            this.strong = strong;
        }

        public Double getLng() {
            return lng;
        }

        public void setLng(Double lng) {
            this.lng = lng;
        }

        public Double getLat() {
            return lat;
        }

        public void setLat(Double lat) {
            this.lat = lat;
        }

        public Integer getPower() {
            return power;
        }

        public void setPower(Integer power) {
            this.power = power;
        }

        public Integer getPressure() {
            return pressure;
        }

        public void setPressure(Integer pressure) {
            this.pressure = pressure;
        }

        public String getCompletion() {
            return completion;
        }

        public void setCompletion(String completion) {
            this.completion = completion;
        }

        public Integer getRadius7() {
            return radius7;
        }

        public void setRadius7(Integer radius7) {
            this.radius7 = radius7;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }


    }
}
