package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.FmPublicRecordDao;
import cn.piesat.data.making.server.dto.generate.PublicProductWordDTO;
import cn.piesat.data.making.server.entity.FmPublicRecord;
import cn.piesat.data.making.server.entity.FmPublicTemplate;
import cn.piesat.data.making.server.mapper.FmPublicRecordMapper;
import cn.piesat.data.making.server.mapper.PublicProductWordMapper;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class FmPublicRecordServiceImpl extends ServiceImpl<FmPublicRecordDao,FmPublicRecord> implements FmPublicRecordService {

    //@Autowired
    //private FmPublicRecordDao fmPublicRecordDaoImpl;


    @Value("${piesat.base-output-path}")
    private String baseOutputPath;

    @Value("${piesat.make.fax}")
    private String fax;

    @Autowired
    private PublicProductWordMapper publicProductWordMapperImpl;

    @Autowired
    private FmPublicRecordMapper fmPublicRecordMapperImpl;

    @Autowired
    private FmPublicRecordDataService fmPublicRecordDataServiceImpl;

    @Autowired
    private GenerateProductService generateProductServiceImpl;

    @Autowired
    private FmPublicProductTemplateService fmPublicProductTemplateServiceImpl;

    @Autowired
    private FmPublicProductRecordService fmPublicProductRecordServiceImpl;

    @Autowired
    private UserService userServiceImpl;

    @Override
    public FmPublicRecordVO getByPk(Long id) {
        FmPublicRecord dto = this.getById(id);
        if(dto!=null){
            FmPublicRecordVO vo = fmPublicRecordMapperImpl.entityToVo(dto);
            vo.setList(fmPublicRecordDataServiceImpl.getListByRecordId(vo.getId()));
            return vo;
        }
        return null;
    }

    @Override
    public FmPublicRecordVO save(FmPublicRecordVO fmPublicRecordVO) {
        FmPublicRecord fmPublicRecord = fmPublicRecordMapperImpl.voToEntity(fmPublicRecordVO);
        this.save(fmPublicRecord);
        
        String fileUrl = this.makeFile(fmPublicRecordVO);
        //String fileUrl = null;

        FmPublicProductRecordVO fmPublicProductRecordVO = new FmPublicProductRecordVO();
        fmPublicProductRecordVO.setProductTemplateId(fmPublicRecord.getTemplateId());
        fmPublicProductRecordVO.setRecordId(fmPublicRecord.getId());
        fmPublicProductRecordVO.setFileUrl(fileUrl);

        fmPublicProductRecordServiceImpl.save(fmPublicProductRecordVO);

        if(!CollectionUtils.isEmpty(fmPublicRecordVO.getList())){
            fmPublicRecordDataServiceImpl.deleteAll(fmPublicRecord.getId());

            List<FmPublicRecordDataVO> list = fmPublicRecordVO.getList();
            list.stream().forEach(vo -> vo.setRecordId(fmPublicRecord.getId()));

            fmPublicRecordDataServiceImpl.saveList(list);
        }

        return this.getByPk(fmPublicRecord.getId());
    }

    public FmPublicRecordVO update(FmPublicRecordVO fmPublicRecordVO){
        FmPublicRecord fmPublicRecord = fmPublicRecordMapperImpl.voToEntity(fmPublicRecordVO);
        this.updateById(fmPublicRecord);

        String fileUrl = this.makeFile(fmPublicRecordVO);

        FmPublicProductRecordVO fmPublicProductRecordVO = fmPublicProductRecordServiceImpl.findByRecordId(fmPublicRecord.getId());
        if(fmPublicProductRecordVO!=null){
            fmPublicProductRecordVO.setFileUrl(fileUrl);
            fmPublicProductRecordServiceImpl.update(fmPublicProductRecordVO);
        }else{
            fmPublicProductRecordVO = new FmPublicProductRecordVO();
            fmPublicProductRecordVO.setProductTemplateId(fmPublicRecord.getTemplateId());
            fmPublicProductRecordVO.setRecordId(fmPublicRecord.getId());
            fmPublicProductRecordVO.setFileUrl(fileUrl);

            fmPublicProductRecordServiceImpl.save(fmPublicProductRecordVO);
        }

        fmPublicRecordDataServiceImpl.deleteAll(fmPublicRecord.getId());

        if(!CollectionUtils.isEmpty(fmPublicRecordVO.getList())){
            List<FmPublicRecordDataVO> list = fmPublicRecordVO.getList();
            list.stream().forEach(vo -> vo.setRecordId(fmPublicRecord.getId()));

            fmPublicRecordDataServiceImpl.saveList(list);
        }
        return this.getByPk(fmPublicRecord.getId());
    }

    @Override
    public FmPublicRecordVO getLastRecord(String publicType) {

        LambdaQueryWrapper<FmPublicRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicRecord::getPublicType, publicType);
        queryWrapper.orderByDesc(FmPublicRecord::getReportTime);
        queryWrapper.orderByDesc(FmPublicRecord::getUpdateTime);
        queryWrapper.isNotNull(true,FmPublicRecord::getReportTime);
        queryWrapper.last("limit 1");

        FmPublicRecord fmPublicRecord = this.getOne(queryWrapper);

        if(fmPublicRecord!=null){
            FmPublicRecordVO fmPublicRecordVO = fmPublicRecordMapperImpl.entityToVo(fmPublicRecord);
            fmPublicRecordVO.setList(fmPublicRecordDataServiceImpl.getListByRecordId(fmPublicRecordVO.getId()));
            return fmPublicRecordVO;
        }
        return null;
    }

    public String makeFile(FmPublicRecordVO vo){

        UserVO userVO = null;
        if(userServiceImpl!=null){
            userVO = userServiceImpl.getInfoById(vo.getSignMakerId());
        }
        //

        FmPublicProductTemplateVO fmPublicProductTemplateVO = fmPublicProductTemplateServiceImpl.getByTemplateCode(vo.getTemplateCode());

        Date releaseTime = vo.getMsgTime();
        String wordFilePath = String.format(CommonConstant.MESSAGE_PUBLIC_WORD_FILE_NAME,baseOutputPath,vo.getTemplateCode(), DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"),vo.getTemplateCode(), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));

        PublicProductWordDTO dto = publicProductWordMapperImpl.toDTO(vo,userVO,fax);
        dto.setList(publicProductWordMapperImpl.toList(vo.getList()));
        if(dto.getList()!=null&&dto.getList().size()>0&&dto.getList().get(0).getTideTime()!=null){
            SimpleDateFormat sdf = new SimpleDateFormat("M");
            dto.setMonth(sdf.format(dto.getList().get(0).getTideTime()));
        }


        generateProductServiceImpl.generateWord(wordFilePath,baseOutputPath+fmPublicProductTemplateVO.getFileUrl(), dto,"list");
        //word转html
        return wordFilePath;
    }
}
