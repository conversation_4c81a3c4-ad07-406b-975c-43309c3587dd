package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.dao.OceanStationDao;
import cn.piesat.data.making.server.dto.OceanStationDataDTO;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.model.RequestData;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.utils.HttpClientUtil;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.OceanStationDataVO;
import cn.piesat.data.making.server.vo.StationVO;
import cn.piesat.data.making.server.vo.TideDailyHourDataVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 海洋站表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class OceanStationServiceImpl extends ServiceImpl<OceanStationDao, OceanStation> implements OceanStationService {

    @Resource
    private StationService stationService;
    @Resource
    private OceanStationDao oceanStationDao;
    @Resource
    private OceanStationHourAtOService oceanStationHourAtOService;
    @Resource
    private OceanStationHourBpOService oceanStationHourBpOService;
    @Resource
    private OceanStationHourHuOService oceanStationHourHuOService;
    @Resource
    private OceanStationHourRnOService oceanStationHourRnOService;
    @Resource
    private OceanStationHourSlOService oceanStationHourSlOService;
    @Resource
    private OceanStationHourVbOService oceanStationHourVbOService;
    @Resource
    private OceanStationHourWtOService oceanStationHourWtOService;
    @Resource
    private OceanStationHourWlDatOService oceanStationHourWlDatOService;
    @Resource
    private OceanStationHourWsDatOService oceanStationHourWsDatOService;
    @Resource
    private OceanStationHourWvOService oceanStationHourWvOService;
    @Resource
    private OceanStationHourWsOService oceanStationHourWsOService;
    @Resource
    private OceanStationOntimeDataOService oceanStationOntimeDataOService;
    @Resource
    private GroupOceanLargeDeepseaMooringbuoyOService groupOceanLargeDeepseaMooringbuoyOService;
    @Resource
    private OceanLargeDeepseaMooringbuoyOService oceanLargeDeepseaMooringbuoyOService;
    @Resource
    private OceanStationHourWlOService oceanStationHourWlOService;
    @Resource
    private OceanStationMinuteSqOService oceanStationMinuteSqOService;
    @Resource
    private OceanSmallShallowseauoyOService oceanSmallShallowseauoyOService;

    @Value(("${thrid.station-data-base-url}"))
    private String stationDataBaseUrl;

    private static final LinkedHashMap<String, Class> tableMap = new LinkedHashMap<>();

    static {
        tableMap.put("ocean_station_minute_sq_o", OceanStationMinuteSqO.class);
        tableMap.put("ocean_station_hour_at_o", OceanStationHourAtO.class);
        tableMap.put("ocean_station_hour_bp_o", OceanStationHourBpO.class);
        tableMap.put("ocean_station_hour_hu_o", OceanStationHourHuO.class);
        tableMap.put("ocean_station_hour_rn_o", OceanStationHourRnO.class);
        tableMap.put("ocean_station_hour_sl_o", OceanStationHourSlO.class);
        tableMap.put("ocean_station_hour_vb_o", OceanStationHourVbO.class);
        tableMap.put("ocean_station_hour_wl_dat_o", OceanStationHourWlDatO.class);
        tableMap.put("ocean_station_hour_wl_o", OceanStationHourWlO.class);
        tableMap.put("ocean_station_hour_ws_dat_o", OceanStationHourWsDatO.class);
        tableMap.put("ocean_station_hour_ws_o", OceanStationHourWsO.class);
        tableMap.put("ocean_station_hour_wt_o", OceanStationHourWtO.class);
        tableMap.put("ocean_station_hour_wv_o", OceanStationHourWvO.class);
        tableMap.put("ocean_station_ontime_data_o", OceanStationOntimeDataO.class);
        tableMap.put("orderbyasc_ocean_large_deepsea_mooringbuoy_o", GroupOceanLargeDeepseaMooringbuoyO.class);
        tableMap.put("ocean_large_deepsea_mooringbuoy_o", OceanLargeDeepseaMooringbuoyO.class);
        tableMap.put("ocean_small_shallowsea_buoy_o", OceanSmallShallowseauoyO.class);
    }

    @Override
    public List<StationInfo> getList(String name) {
        return oceanStationDao.getList(name);
    }

    /**
     * 统计时间范围内不同站点的不同要素的最大值
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return Map<站点名称, Map < 要素名称, 最大值>>
     */
    @Override
    public List<OceanStationDataVO> getDataList(Date startTime, Date endTime) {
        //查询数据维护中有实况观测数据的海洋站列表
        StationDTO dto = new StationDTO();
//        dto.setType("liveObservation");
        dto.setType("sheng,shi,chao");
        dto.setEnable(Boolean.TRUE);
        List<StationVO> stationList = stationService.getList(dto);
        if (CollectionUtils.isEmpty(stationList)) {
            return new ArrayList<>();
        }
        //key中台编码 value系统站点名称
        Map<String, String> stationMap = stationList.stream().collect(Collectors.toMap(StationVO::getRelationStation,
                StationVO::getName, (key1, key2) -> key2));
        Map<String, Map<String, Double>> result = new HashMap<>();
        //查询气温
        processTable(oceanStationHourAtOService, stationMap, startTime, endTime, "气温", result);
        //查询气压
        processTable(oceanStationHourBpOService, stationMap, startTime, endTime, "气压", result);
        //查询相对湿度
        processTable(oceanStationHourHuOService, stationMap, startTime, endTime, "湿度", result);
        //查询降水
        processTable(oceanStationHourRnOService, stationMap, startTime, endTime, "降水", result);
        //查询表层海水盐度
        processTable(oceanStationHourSlOService, stationMap, startTime, endTime, "盐度", result);
        //查询能见度
        processTable(oceanStationHourVbOService, stationMap, startTime, endTime, "能见度", result);
        //查询潮位
        processTable(oceanStationHourWlOService, stationMap, startTime, endTime, "潮位", result);
        //查询风速
        processTable(oceanStationHourWsDatOService, stationMap, startTime, endTime, "风速", result);
        //查询风向
        processTable(oceanStationHourWsDatOService, stationMap, startTime, endTime, "风向", result);
        //查询表层海水温度
        processTable(oceanStationHourWtOService, stationMap, startTime, endTime, "海温", result);
        //查询波高
        processTable(oceanStationHourWvOService, stationMap, startTime, endTime, "波高", result);
        //查询波周期
        processTable(oceanStationHourWvOService, stationMap, startTime, endTime, "波周期", result);
        List<OceanStationDataVO> list = new ArrayList<>();
        result.entrySet().forEach(entry -> {
            String name = entry.getKey();
            Map<String, Double> value = entry.getValue();
            OceanStationDataVO vo = new OceanStationDataVO();
            vo.setOceanStationName(name);
            vo.setAirTemperature(value.get("气温") + "");
            vo.setAirPressure(value.get("气压") + "");
            vo.setHumidity(value.get("湿度") + "");
            vo.setRain(value.get("降水") + "");
            vo.setSalinity(value.get("盐度") + "");
            vo.setVisibility(value.get("能见度") + "");
            vo.setTide(value.get("潮位") + "");
            vo.setWindSpeed(value.get("风速") + "");
            vo.setWindDir(value.get("风向") + "");
            vo.setSeaTemperature(value.get("海温") + "");
            vo.setWindWaveHeight(value.get("波高") + "");
            vo.setWindWavePeriod(value.get("波周期") + "");
            list.add(vo);
        });
        return list;
    }

    /**
     * 处理单张表的数据
     *
     * @param service     表的Service接口
     * @param stationMap 数据维护中有实况观测数据的海洋站列表
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param element     要素名称
     * @param result      结果集
     */
    private <T> void processTable(IService<T> service, Map<String, String> stationMap, Date startTime, Date endTime, String element,
                                  Map<String, Map<String, Double>> result) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("monitoringdate", startTime, endTime);
        //key中台编码 value系统站点名称
        /*Map<String, String> stationMap = stationList.stream().collect(Collectors.toMap(StationVO::getRelationStation,
                StationVO::getName, (key1, key2) -> key2));*/
        queryWrapper.in("station_num", stationMap.keySet());
        List<T> records = service.list(queryWrapper);

        log.info("-----------------【查询到要素element：{}，条数为：{}】",element,records.size());
        for (T record : records) {
            String relationStation = this.getRelationStation(record);
            Double maxValue = this.getMaxValue(record, element);
            //更新结果集
            if (maxValue == null) {
                result.computeIfAbsent(stationMap.get(relationStation), k -> new HashMap<>()).put(element, null);
            } else {
                result.computeIfAbsent(stationMap.get(relationStation), k -> new HashMap<>()).merge(element, maxValue, Math::max);
            }
        }
    }

    /**
     * 获取关联站点
     */
    private <T> String getRelationStation(T record) {
        if (record instanceof OceanStationHourAtO) {
            return ((OceanStationHourAtO) record).getStationNum();
        } else if (record instanceof OceanStationHourBpO) {
            return ((OceanStationHourBpO) record).getStationNum();
        } else if (record instanceof OceanStationHourHuO) {
            return ((OceanStationHourHuO) record).getStationNum();
        } else if (record instanceof OceanStationHourRnO) {
            return ((OceanStationHourRnO) record).getStationNum();
        } else if (record instanceof OceanStationHourSlO) {
            return ((OceanStationHourSlO) record).getStationNum();
        } else if (record instanceof OceanStationHourVbO) {
            return ((OceanStationHourVbO) record).getStationNum();
        } else if (record instanceof OceanStationHourWlDatO) {
            return ((OceanStationHourWlDatO) record).getStationNum();
        } else if (record instanceof OceanStationHourWsDatO) {
            return ((OceanStationHourWsDatO) record).getStationNum();
        } else if (record instanceof OceanStationHourWtO) {
            return ((OceanStationHourWtO) record).getStationNum();
        } else if (record instanceof OceanStationHourWvO) {
            return ((OceanStationHourWvO) record).getStationNum();
        }else if (record instanceof OceanStationHourWlO) {
            return ((OceanStationHourWlO) record).getStationNum();
        }
        throw new IllegalArgumentException("未知的站点数据");
    }

    /**
     * 获取每小时监测值中的最大值
     */
    private <T> Double getMaxValue(T record, String element) {
        List<Double> values = new ArrayList<>();
        if (record instanceof OceanStationHourAtO) {
            OceanStationHourAtO data = (OceanStationHourAtO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxValue()) ? Double.valueOf(data.getDayMaxValue()) : null);
        } else if (record instanceof OceanStationHourBpO) {
            OceanStationHourBpO data = (OceanStationHourBpO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxValue()) ? Double.valueOf(data.getDayMaxValue()) : null);
        } else if (record instanceof OceanStationHourHuO) {
            OceanStationHourHuO data = (OceanStationHourHuO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxValue()) ? Double.valueOf(data.getDayMaxValue()) : null);
        } else if (record instanceof OceanStationHourRnO) {
            OceanStationHourRnO data = (OceanStationHourRnO) record;
            values.add(StringUtils.isNotBlank(data.getMeasuredValue00()) ? Double.valueOf(data.getMeasuredValue00()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue01()) ? Double.valueOf(data.getMeasuredValue01()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue02()) ? Double.valueOf(data.getMeasuredValue02()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue03()) ? Double.valueOf(data.getMeasuredValue03()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue04()) ? Double.valueOf(data.getMeasuredValue04()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue05()) ? Double.valueOf(data.getMeasuredValue05()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue06()) ? Double.valueOf(data.getMeasuredValue06()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue07()) ? Double.valueOf(data.getMeasuredValue07()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue08()) ? Double.valueOf(data.getMeasuredValue08()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue09()) ? Double.valueOf(data.getMeasuredValue09()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue10()) ? Double.valueOf(data.getMeasuredValue10()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue11()) ? Double.valueOf(data.getMeasuredValue11()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue12()) ? Double.valueOf(data.getMeasuredValue12()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue13()) ? Double.valueOf(data.getMeasuredValue13()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue14()) ? Double.valueOf(data.getMeasuredValue14()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue15()) ? Double.valueOf(data.getMeasuredValue15()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue16()) ? Double.valueOf(data.getMeasuredValue16()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue17()) ? Double.valueOf(data.getMeasuredValue17()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue18()) ? Double.valueOf(data.getMeasuredValue18()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue19()) ? Double.valueOf(data.getMeasuredValue19()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue20()) ? Double.valueOf(data.getMeasuredValue20()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue21()) ? Double.valueOf(data.getMeasuredValue21()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue22()) ? Double.valueOf(data.getMeasuredValue22()) : null);
            values.add(StringUtils.isNotBlank(data.getMeasuredValue23()) ? Double.valueOf(data.getMeasuredValue23()) : null);
        } else if (record instanceof OceanStationHourSlO) {
            OceanStationHourSlO data = (OceanStationHourSlO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxValue()) ? Double.valueOf(data.getDayMaxValue()) : null);
        } else if (record instanceof OceanStationHourVbO) {
            OceanStationHourVbO data = (OceanStationHourVbO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxValue()) ? Double.valueOf(data.getDayMaxValue()) : null);
        } else if (record instanceof OceanStationHourWlO) {
            OceanStationHourWlO data = (OceanStationHourWlO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxMinTideHigh1()) ? Double.valueOf(data.getDayMaxMinTideHigh1()) : null);
        } else if ("风速".equals(element) && record instanceof OceanStationHourWsDatO) {
            OceanStationHourWsDatO data = (OceanStationHourWsDatO) record;
            values.add(StringUtils.isNotBlank(data.getExtremeWindSpeed()) ? Double.valueOf(data.getExtremeWindSpeed()) : null);
        } else if ("风向".equals(element) && record instanceof OceanStationHourWsDatO) {
            OceanStationHourWsDatO data = (OceanStationHourWsDatO) record;
            values.add(StringUtils.isNotBlank(data.getExtremeWindDir()) ? Double.valueOf(data.getExtremeWindDir()) : null);
        } else if (record instanceof OceanStationHourWtO) {
            OceanStationHourWtO data = (OceanStationHourWtO) record;
            values.add(StringUtils.isNotBlank(data.getDayMaxValue()) ? Double.valueOf(data.getDayMaxValue()) : null);
        } else if ("波高".equals(element) && record instanceof OceanStationHourWvO) {
            OceanStationHourWvO data = (OceanStationHourWvO) record;
            values.add(StringUtils.isNotBlank(data.getMaximumWaveHeight()) ? Double.valueOf(data.getMaximumWaveHeight()) : null);
        } else if ("波周期".equals(element) && record instanceof OceanStationHourWvO) {
            OceanStationHourWvO data = (OceanStationHourWvO) record;
            values.add(StringUtils.isNotBlank(data.getMaximumCycle()) ? Double.valueOf(data.getMaximumCycle()) : null);
        } else {
            throw new IllegalArgumentException("未知的站点数据");
        }
        // 返回最大值
        List<Double> doubleList = values.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(doubleList)) {
            return null;
        } else {
            return doubleList.stream().mapToDouble(Double::doubleValue).max().getAsDouble();
        }
    }

    @Override
    public List<OceanStationDataVO> getHourDataList(Date startTime, Date endTime, List<String> oceanStationCodeList) {
        //查询气温
        List<OceanStationHourAtO> atData = oceanStationHourAtOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询气压
        List<OceanStationHourBpO> bpData = oceanStationHourBpOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询相对湿度
        List<OceanStationHourHuO> huData = oceanStationHourHuOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询降水
        List<OceanStationHourRnO> rnData = oceanStationHourRnOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询表层海水盐度
        List<OceanStationHourSlO> slData = oceanStationHourSlOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询能见度
        List<OceanStationHourVbO> vbData = oceanStationHourVbOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询潮位
        List<OceanStationHourWlDatO> wlData = oceanStationHourWlDatOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询风速 风向
        List<OceanStationHourWsDatO> wsData = oceanStationHourWsDatOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //查询海温
        List<OceanStationHourWtO> wtData = oceanStationHourWtOService.getByStationNumListAndDateRange(startTime, endTime, oceanStationCodeList);
        //合并数据并转换为 OceanStationDataVO
        StationDTO dto = new StationDTO();
        dto.setStationTypeCode("oceanStation");
        List<StationVO> stationList = stationService.getList(dto);
        Map<String, StationVO> stationMap = stationList.stream().collect(Collectors.toMap(StationVO::getRelationStation, Function.identity(),
                (key1, key2) -> key2));
        return this.mergeData(stationMap, startTime, endTime, atData, bpData, huData, rnData, slData, vbData, wlData, wsData, wtData);
    }

    private List<OceanStationDataVO> mergeData(Map<String, StationVO> stationMap, Date startTime, Date endTime, List<OceanStationHourAtO> atData,
                                               List<OceanStationHourBpO> bpData,
                                               List<OceanStationHourHuO> huData,
                                               List<OceanStationHourRnO> rnData, List<OceanStationHourSlO> slData, List<OceanStationHourVbO> vbData,
                                               List<OceanStationHourWlDatO> wlData, List<OceanStationHourWsDatO> wsData, List<OceanStationHourWtO> wtData) {
        Map<String, Map<Date, OceanStationDataVO>> resultMap = new HashMap<>();

        //处理气温
        for (OceanStationHourAtO data : atData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setAirTemperature(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理气压
        for (OceanStationHourBpO data : bpData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setAirPressure(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理湿度
        for (OceanStationHourHuO data : huData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setHumidity(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理降水
        for (OceanStationHourRnO data : rnData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setRain(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理盐度
        for (OceanStationHourSlO data : slData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setSalinity(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理能见度
        for (OceanStationHourVbO data : vbData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setVisibility(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理潮位
        for (OceanStationHourWlDatO data : wlData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setTide(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理风速
        for (OceanStationHourWsDatO data : wsData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, "风速");

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setWindSpeed(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理风向
        for (OceanStationHourWsDatO data : wsData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, "风向");

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setWindDir(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //处理海温
        for (OceanStationHourWtO data : wtData) {
            String stationName = data.getName();
            Date monitorDate = data.getMonitoringdate();

            for (int hour = 0; hour < 24; hour++) {
                Date monitorTime = this.getHourlyTime(monitorDate, hour);
                // 只处理在 startDate 和 endDate 之间的数据
                if (monitorTime.before(startTime) || monitorTime.after(endTime)) {
                    continue;
                }
                String value = this.getHourlyValue(data, hour, null);

                if (value != null) {
                    OceanStationDataVO vo = resultMap
                            .computeIfAbsent(stationName, k -> new HashMap<>())
                            .computeIfAbsent(monitorTime, k -> new OceanStationDataVO());
                    vo.setOceanStationCode(data.getStationNum());
                    vo.setTime(monitorTime);
                    vo.setSeaTemperature(value);
                    StationVO station = stationMap.get(data.getStationNum());
                    vo.setOceanStationName(station.getName());
                    vo.setOceanStationLocationJson(station.getLocationJson());
                }
            }
        }
        //将 Map 转换为 List
        List<OceanStationDataVO> result = new ArrayList<>();
        for (Map<Date, OceanStationDataVO> stationData : resultMap.values()) {
            result.addAll(stationData.values());
        }
        result.sort(Comparator
                .comparing(OceanStationDataVO::getOceanStationName)
                .thenComparing(OceanStationDataVO::getTime)
        );
        return result;
    }

    private Date getHourlyTime(Date date, int hour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    private static Integer checkValue(Integer value){
        return Objects.isNull(value) ? 9 : value;
    }

    private static final List<Function<OceanStationHourAtO, String>> ATO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourBpO, String>> BPO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourHuO, String>> HUO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourRnO, String>> RNO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourSlO, String>> SLO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourVbO, String>> VBO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourWlDatO, String>> WLDATO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private static final List<Function<OceanStationHourWsDatO, String>> WIND_GETTERS = Arrays.asList(
            data -> checkValue(data.getWindSpeed00Qc2()) != 1 ? null : data.getWindSpeed00(),
            data -> checkValue(data.getWindSpeed01Qc2()) != 1 ? null : data.getWindSpeed01(),
            data -> checkValue(data.getWindSpeed02Qc2()) != 1 ? null : data.getWindSpeed02(),
            data -> checkValue(data.getWindSpeed03Qc2()) != 1 ? null : data.getWindSpeed03(),
            data -> checkValue(data.getWindSpeed04Qc2()) != 1 ? null : data.getWindSpeed04(),
            data -> checkValue(data.getWindSpeed05Qc2()) != 1 ? null : data.getWindSpeed05(),
            data -> checkValue(data.getWindSpeed06Qc2()) != 1 ? null : data.getWindSpeed06(),
            data -> checkValue(data.getWindSpeed07Qc2()) != 1 ? null : data.getWindSpeed07(),
            data -> checkValue(data.getWindSpeed08Qc2()) != 1 ? null : data.getWindSpeed08(),
            data -> checkValue(data.getWindSpeed09Qc2()) != 1 ? null : data.getWindSpeed09(),
            data -> checkValue(data.getWindSpeed10Qc2()) != 1 ? null : data.getWindSpeed10(),
            data -> checkValue(data.getWindSpeed11Qc2()) != 1 ? null : data.getWindSpeed11(),
            data -> checkValue(data.getWindSpeed12Qc2()) != 1 ? null : data.getWindSpeed12(),
            data -> checkValue(data.getWindSpeed13Qc2()) != 1 ? null : data.getWindSpeed13(),
            data -> checkValue(data.getWindSpeed14Qc2()) != 1 ? null : data.getWindSpeed14(),
            data -> checkValue(data.getWindSpeed15Qc2()) != 1 ? null : data.getWindSpeed15(),
            data -> checkValue(data.getWindSpeed16Qc2()) != 1 ? null : data.getWindSpeed16(),
            data -> checkValue(data.getWindSpeed17Qc2()) != 1 ? null : data.getWindSpeed17(),
            data -> checkValue(data.getWindSpeed18Qc2()) != 1 ? null : data.getWindSpeed18(),
            data -> checkValue(data.getWindSpeed19Qc2()) != 1 ? null : data.getWindSpeed19(),
            data -> checkValue(data.getWindSpeed20Qc2()) != 1 ? null : data.getWindSpeed20(),
            data -> checkValue(data.getWindSpeed21Qc2()) != 1 ? null : data.getWindSpeed21(),
            data -> checkValue(data.getWindSpeed22Qc2()) != 1 ? null : data.getWindSpeed22(),
            data -> checkValue(data.getWindSpeed23Qc2()) != 1 ? null : data.getWindSpeed23()
    );
    private static final List<Function<OceanStationHourWsDatO, String>> WINDDIR_GETTERS = Arrays.asList(
            data -> checkValue(data.getWindDir00Qc2()) != 1 ? null : data.getWindDir00(),
            data -> checkValue(data.getWindDir01Qc2()) != 1 ? null : data.getWindDir01(),
            data -> checkValue(data.getWindDir02Qc2()) != 1 ? null : data.getWindDir02(),
            data -> checkValue(data.getWindDir03Qc2()) != 1 ? null : data.getWindDir03(),
            data -> checkValue(data.getWindDir04Qc2()) != 1 ? null : data.getWindDir04(),
            data -> checkValue(data.getWindDir05Qc2()) != 1 ? null : data.getWindDir05(),
            data -> checkValue(data.getWindDir06Qc2()) != 1 ? null : data.getWindDir06(),
            data -> checkValue(data.getWindDir07Qc2()) != 1 ? null : data.getWindDir07(),
            data -> checkValue(data.getWindDir08Qc2()) != 1 ? null : data.getWindDir08(),
            data -> checkValue(data.getWindDir09Qc2()) != 1 ? null : data.getWindDir09(),
            data -> checkValue(data.getWindDir10Qc2()) != 1 ? null : data.getWindDir10(),
            data -> checkValue(data.getWindDir11Qc2()) != 1 ? null : data.getWindDir11(),
            data -> checkValue(data.getWindDir12Qc2()) != 1 ? null : data.getWindDir12(),
            data -> checkValue(data.getWindDir13Qc2()) != 1 ? null : data.getWindDir13(),
            data -> checkValue(data.getWindDir14Qc2()) != 1 ? null : data.getWindDir14(),
            data -> checkValue(data.getWindDir15Qc2()) != 1 ? null : data.getWindDir15(),
            data -> checkValue(data.getWindDir16Qc2()) != 1 ? null : data.getWindDir16(),
            data -> checkValue(data.getWindDir17Qc2()) != 1 ? null : data.getWindDir17(),
            data -> checkValue(data.getWindDir18Qc2()) != 1 ? null : data.getWindDir18(),
            data -> checkValue(data.getWindDir19Qc2()) != 1 ? null : data.getWindDir19(),
            data -> checkValue(data.getWindDir20Qc2()) != 1 ? null : data.getWindDir20(),
            data -> checkValue(data.getWindDir21Qc2()) != 1 ? null : data.getWindDir21(),
            data -> checkValue(data.getWindDir22Qc2()) != 1 ? null : data.getWindDir22(),
            data -> checkValue(data.getWindDir23Qc2()) != 1 ? null : data.getWindDir23()
    );
    private static final List<Function<OceanStationHourWtO, String>> WTO_GETTERS = Arrays.asList(
            data -> checkValue(data.getMeasuredValue00Qc2()) != 1 ? null : data.getMeasuredValue00(),
            data -> checkValue(data.getMeasuredValue01Qc2()) != 1 ? null : data.getMeasuredValue01(),
            data -> checkValue(data.getMeasuredValue02Qc2()) != 1 ? null : data.getMeasuredValue02(),
            data -> checkValue(data.getMeasuredValue03Qc2()) != 1 ? null : data.getMeasuredValue03(),
            data -> checkValue(data.getMeasuredValue04Qc2()) != 1 ? null : data.getMeasuredValue04(),
            data -> checkValue(data.getMeasuredValue05Qc2()) != 1 ? null : data.getMeasuredValue05(),
            data -> checkValue(data.getMeasuredValue06Qc2()) != 1 ? null : data.getMeasuredValue06(),
            data -> checkValue(data.getMeasuredValue07Qc2()) != 1 ? null : data.getMeasuredValue07(),
            data -> checkValue(data.getMeasuredValue08Qc2()) != 1 ? null : data.getMeasuredValue08(),
            data -> checkValue(data.getMeasuredValue09Qc2()) != 1 ? null : data.getMeasuredValue09(),
            data -> checkValue(data.getMeasuredValue10Qc2()) != 1 ? null : data.getMeasuredValue10(),
            data -> checkValue(data.getMeasuredValue11Qc2()) != 1 ? null : data.getMeasuredValue11(),
            data -> checkValue(data.getMeasuredValue12Qc2()) != 1 ? null : data.getMeasuredValue12(),
            data -> checkValue(data.getMeasuredValue13Qc2()) != 1 ? null : data.getMeasuredValue13(),
            data -> checkValue(data.getMeasuredValue14Qc2()) != 1 ? null : data.getMeasuredValue14(),
            data -> checkValue(data.getMeasuredValue15Qc2()) != 1 ? null : data.getMeasuredValue15(),
            data -> checkValue(data.getMeasuredValue16Qc2()) != 1 ? null : data.getMeasuredValue16(),
            data -> checkValue(data.getMeasuredValue17Qc2()) != 1 ? null : data.getMeasuredValue17(),
            data -> checkValue(data.getMeasuredValue18Qc2()) != 1 ? null : data.getMeasuredValue18(),
            data -> checkValue(data.getMeasuredValue19Qc2()) != 1 ? null : data.getMeasuredValue19(),
            data -> checkValue(data.getMeasuredValue20Qc2()) != 1 ? null : data.getMeasuredValue20(),
            data -> checkValue(data.getMeasuredValue21Qc2()) != 1 ? null : data.getMeasuredValue21(),
            data -> checkValue(data.getMeasuredValue22Qc2()) != 1 ? null : data.getMeasuredValue22(),
            data -> checkValue(data.getMeasuredValue23Qc2()) != 1 ? null : data.getMeasuredValue23()
    );
    private String getHourlyValue(Object obj, int hour, String element) {
        if (hour < 0 || hour > 23) return null;

        if (obj instanceof OceanStationHourAtO) {
            return ATO_GETTERS.get(hour).apply((OceanStationHourAtO) obj);
        } else if (obj instanceof OceanStationHourBpO) {
            return BPO_GETTERS.get(hour).apply((OceanStationHourBpO) obj);
        } else if (obj instanceof OceanStationHourHuO) {
            return HUO_GETTERS.get(hour).apply((OceanStationHourHuO) obj);
        } else if (obj instanceof OceanStationHourRnO) {
            return RNO_GETTERS.get(hour).apply((OceanStationHourRnO) obj);
        } else if (obj instanceof OceanStationHourSlO) {
            return SLO_GETTERS.get(hour).apply((OceanStationHourSlO) obj);
        } else if (obj instanceof OceanStationHourVbO) {
            return VBO_GETTERS.get(hour).apply((OceanStationHourVbO) obj);
        } else if (obj instanceof OceanStationHourWlDatO) {
            return WLDATO_GETTERS.get(hour).apply((OceanStationHourWlDatO) obj);
        } else if ("风速".equals(element) & obj instanceof OceanStationHourWsDatO) {
            return WIND_GETTERS.get(hour).apply((OceanStationHourWsDatO) obj);
        } else if ("风向".equals(element) & obj instanceof OceanStationHourWsDatO) {
            return WINDDIR_GETTERS.get(hour).apply((OceanStationHourWsDatO) obj);
        } else if (obj instanceof OceanStationHourWtO) {
            return WTO_GETTERS.get(hour).apply((OceanStationHourWtO) obj);
        }
        return null;
    }
    /*private String getHourlyValue(Object obj, int hour, String element) {
        if (obj instanceof OceanStationHourAtO) {
            OceanStationHourAtO data = (OceanStationHourAtO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourBpO) {
            OceanStationHourBpO data = (OceanStationHourBpO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourHuO) {
            OceanStationHourHuO data = (OceanStationHourHuO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourRnO) {
            OceanStationHourRnO data = (OceanStationHourRnO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourSlO) {
            OceanStationHourSlO data = (OceanStationHourSlO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourVbO) {
            OceanStationHourVbO data = (OceanStationHourVbO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourWlDatO) {
            OceanStationHourWlDatO data = (OceanStationHourWlDatO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        } else if ("风速".equals(element) & obj instanceof OceanStationHourWsDatO) {
            OceanStationHourWsDatO data = (OceanStationHourWsDatO) obj;
            switch (hour) {
                case 0:
                    return data.getWindSpeed00();
                case 1:
                    return data.getWindSpeed01();
                case 2:
                    return data.getWindSpeed02();
                case 3:
                    return data.getWindSpeed03();
                case 4:
                    return data.getWindSpeed04();
                case 5:
                    return data.getWindSpeed05();
                case 6:
                    return data.getWindSpeed06();
                case 7:
                    return data.getWindSpeed07();
                case 8:
                    return data.getWindSpeed08();
                case 9:
                    return data.getWindSpeed09();
                case 10:
                    return data.getWindSpeed10();
                case 11:
                    return data.getWindSpeed11();
                case 12:
                    return data.getWindSpeed12();
                case 13:
                    return data.getWindSpeed13();
                case 14:
                    return data.getWindSpeed14();
                case 15:
                    return data.getWindSpeed15();
                case 16:
                    return data.getWindSpeed16();
                case 17:
                    return data.getWindSpeed17();
                case 18:
                    return data.getWindSpeed18();
                case 19:
                    return data.getWindSpeed19();
                case 20:
                    return data.getWindSpeed20();
                case 21:
                    return data.getWindSpeed21();
                case 22:
                    return data.getWindSpeed22();
                case 23:
                    return data.getWindSpeed23();
                default:
                    return null;
            }
        } else if ("风向".equals(element) & obj instanceof OceanStationHourWsDatO) {
            OceanStationHourWsDatO data = (OceanStationHourWsDatO) obj;
            switch (hour) {
                case 0:
                    return data.getWindDir00();
                case 1:
                    return data.getWindDir01();
                case 2:
                    return data.getWindDir02();
                case 3:
                    return data.getWindDir03();
                case 4:
                    return data.getWindDir04();
                case 5:
                    return data.getWindDir05();
                case 6:
                    return data.getWindDir06();
                case 7:
                    return data.getWindDir07();
                case 8:
                    return data.getWindDir08();
                case 9:
                    return data.getWindDir09();
                case 10:
                    return data.getWindDir10();
                case 11:
                    return data.getWindDir11();
                case 12:
                    return data.getWindDir12();
                case 13:
                    return data.getWindDir13();
                case 14:
                    return data.getWindDir14();
                case 15:
                    return data.getWindDir15();
                case 16:
                    return data.getWindDir16();
                case 17:
                    return data.getWindDir17();
                case 18:
                    return data.getWindDir18();
                case 19:
                    return data.getWindDir19();
                case 20:
                    return data.getWindDir20();
                case 21:
                    return data.getWindDir21();
                case 22:
                    return data.getWindDir22();
                case 23:
                    return data.getWindDir23();
                default:
                    return null;
            }
        } else if (obj instanceof OceanStationHourWtO) {
            OceanStationHourWtO data = (OceanStationHourWtO) obj;
            switch (hour) {
                case 0:
                    return data.getMeasuredValue00();
                case 1:
                    return data.getMeasuredValue01();
                case 2:
                    return data.getMeasuredValue02();
                case 3:
                    return data.getMeasuredValue03();
                case 4:
                    return data.getMeasuredValue04();
                case 5:
                    return data.getMeasuredValue05();
                case 6:
                    return data.getMeasuredValue06();
                case 7:
                    return data.getMeasuredValue07();
                case 8:
                    return data.getMeasuredValue08();
                case 9:
                    return data.getMeasuredValue09();
                case 10:
                    return data.getMeasuredValue10();
                case 11:
                    return data.getMeasuredValue11();
                case 12:
                    return data.getMeasuredValue12();
                case 13:
                    return data.getMeasuredValue13();
                case 14:
                    return data.getMeasuredValue14();
                case 15:
                    return data.getMeasuredValue15();
                case 16:
                    return data.getMeasuredValue16();
                case 17:
                    return data.getMeasuredValue17();
                case 18:
                    return data.getMeasuredValue18();
                case 19:
                    return data.getMeasuredValue19();
                case 20:
                    return data.getMeasuredValue20();
                case 21:
                    return data.getMeasuredValue21();
                case 22:
                    return data.getMeasuredValue22();
                case 23:
                    return data.getMeasuredValue23();
                default:
                    return null;
            }
        }
        return null;
    }*/

    @Override
    public List<OceanStationDataVO> getMinuteDataList(Date startTime, Date endTime, List<String> oceanStationCodeList) {
        LambdaQueryWrapper<OceanStationMinuteSqO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(OceanStationMinuteSqO::getMonitoringdate, startTime, endTime);
        queryWrapper.in(OceanStationMinuteSqO::getStationNum, oceanStationCodeList);
        List<OceanStationMinuteSqO> oList = oceanStationMinuteSqOService.list(queryWrapper);
        List<OceanStationDataVO> list = new ArrayList<>();
        oList.stream().forEach(o -> {
            OceanStationDataVO vo = new OceanStationDataVO();
            vo.setOceanStationCode(o.getStationNum());
            vo.setOceanStationName(o.getName());
            vo.setAirTemperature(checkValue(o.getAirTemperatureQc2()) != 1 ? null : o.getAirTemperature());
            vo.setAirPressure(checkValue(o.getAtmosphericPressureQc2()) != 1 ? null : o.getAtmosphericPressure());
            vo.setHumidity(checkValue(o.getHumidityQc2()) != 1 ? null : o.getHumidity());
            vo.setRain(null);
            vo.setSalinity(checkValue(o.getSalinityQc2()) != 1 ? null : o.getSalinity());
            vo.setVisibility(checkValue(o.getVbQc2()) != 1 ? null : o.getVb());
            vo.setTide(checkValue(o.getTidalLevelQc2()) != 1 ? null : o.getTidalLevel());
            vo.setWindSpeed(checkValue(o.getGustWindSpeedQc2()) != 1 ? null : o.getGustWindSpeed());
            vo.setWindDir(checkValue(o.getGustWindDirQc2()) != 1 ? null : o.getGustWindDir());
            vo.setSeaTemperature(checkValue(o.getWaterTemperatureQc2()) != 1 ? null : o.getWaterTemperature());
            vo.setWindWaveHeight(null);
            vo.setWindWavePeriod(null);
            vo.setTime(o.getMonitoringdate());
            list.add(vo);
        });
        return list;
    }

    @Override
    @SneakyThrows
    public FillMapVO getGeoRangeDataList(OceanStationDataDTO dto) {
        FillMapVO fillMap = new FillMapVO();
        //查询范围内的海洋站
        String geoRange = null;
        if (StringUtils.isNotBlank(dto.getGeoRange())) {
            geoRange = GeoToolsUtil.geojsonToGeo(dto.getGeoRange());
        }
        List<Station> stationList = stationService.getListByRange(geoRange);
        if (!CollectionUtils.isEmpty(stationList)) {
            //海洋站数据
            List<String> codeList =
                    stationList.stream().filter(s -> s.getRelationStation() != null && s.getStationTypeCode().equals("oceanStation")).map(Station::getRelationStation).collect(Collectors.toList());
            List<OceanStationDataVO> dataList = this.getHourDataList(dto.getStartTime(), dto.getEndTime(), codeList);
            //按照数据时间对海洋站数据进行分组
            Map<Date, List<OceanStationDataVO>> dataMap = dataList.stream().collect(Collectors.groupingBy(OceanStationDataVO::getTime));
            //数据时间列表
            List<Date> timeList = dataMap.keySet().stream().collect(Collectors.toList()).stream().sorted().collect(Collectors.toList());
            fillMap.setTimeList(timeList);
            fillMap.setTimeAndDataMap(dataMap);
        }
        return fillMap;
    }

    @lombok.Data
    static class Wrapper {
        private String msg;
        private Integer code;
        private Data data;
    }

    @lombok.Data
    static class Nt {
        private String name;
        private String type;
    }

    @lombok.Data
    static class Data<T> {
        private Integer pageSize;
        private Integer pageNum;
        private List<Nt> columns;
        private Integer totalCount;
        private String encryKey;
        private List<T> resultList;
    }

    @Override
    public void syncOceanAndSmallBuoyData(String table, LocalDateTime startTimeL, LocalDateTime endTimeL) {
        if (StringUtils.isNotBlank(table)) {
            this.requestStationData(table, null, startTimeL, endTimeL);
        } else {
            tableMap.keySet().stream().forEach(tableName -> {
                if (tableName.equals("orderbyasc_ocean_large_deepsea_mooringbuoy_o") || tableName.equals("ocean_large_deepsea_mooringbuoy_o")) {
                    return;
                }
                this.requestStationData(tableName, null, startTimeL, endTimeL);
            });
        }
    }

    @Override
    public void syncLargeBuoyData() {
        this.requestStationData("orderbyasc_ocean_large_deepsea_mooringbuoy_o", null, null, null);
        //查询浮标编码
        List<String> buoyIdList = groupOceanLargeDeepseaMooringbuoyOService.getBuoyIdList();
        if (!CollectionUtils.isEmpty(buoyIdList)) {
            buoyIdList.stream().forEach(buoyId -> {
                this.requestStationData("ocean_large_deepsea_mooringbuoy_o", buoyId, null, null);
            });
        }
    }

    @Override
    public List<TideDailyHourDataVO> getTideList(String stationNum, Date startTime, Date endTime) {
        List<TideDailyHourDataVO> resultList = new ArrayList<>();

        LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Station::getRelationStation, stationNum);
        Station station = stationService.getOne(queryWrapper);
        if (station == null) {
            return resultList;
        }
        LambdaQueryWrapper<OceanStationHourWlDatO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OceanStationHourWlDatO::getStationNum, stationNum);
        wrapper.between(OceanStationHourWlDatO::getMonitoringdate, startTime, endTime);
        List<OceanStationHourWlDatO> list = oceanStationHourWlDatOService.list(wrapper);
        for (OceanStationHourWlDatO record : list) {
            Date monitoringdate = record.getMonitoringdate();
            for (int hour = 0; hour < 24; hour++) {
                String fieldName = "measuredValue" + String.format("%02d", hour);
                String measuredValue = getFieldValue(record, fieldName);
                if (measuredValue != null && !measuredValue.isEmpty()) {
                    TideDailyHourDataVO tideDailyHourData = new TideDailyHourDataVO();
                    tideDailyHourData.setStationNum(stationNum);
                    tideDailyHourData.setStationId(station.getId().toString());
                    tideDailyHourData.setStationName(station.getName());
                    tideDailyHourData.setTideTime(this.addHoursToTimestamp(monitoringdate, hour));
                    tideDailyHourData.setHeight(measuredValue);
                    resultList.add(tideDailyHourData);
                }
            }
        }
        resultList.sort(Comparator.comparing(TideDailyHourDataVO::getTideTime));
        return resultList;
    }

    private String getFieldValue(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return (String) field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }

    private Date addHoursToTimestamp(Date date, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }

    public void requestStationData(String table, String buoyInfoId, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("requestStationData开始:{}", table);
        String appKey = "ace6aa751bfc35ce79b8f8a0ab6b18ee51cad1fa";
        String sign = "b5f3b0cf79a44b919f744fe06d79f3d6";
        RequestData requestData = new RequestData();
        RequestData.Page page = new RequestData.Page();
        RequestData.Param param = new RequestData.Param();
        if ("ocean_large_deepsea_mooringbuoy_o".equals(table) && buoyInfoId != null) {
            param.setBuoyInfo_id(buoyInfoId);
        }
        //定义数据起止时间
        Class<?> entityClass = tableMap.get(table);
        LocalDateTime lastCreatetime = this.getMaxCreateTime(entityClass, buoyInfoId);
        //ocean_station_minute_sq_o从2025-01-01 00:00:00开始
        if (lastCreatetime == null) {
            if (table.equals("ocean_station_minute_sq_o")) {
                lastCreatetime = LocalDateTime.of(2025, 01, 01, 00, 00, 00);
            } else {
                lastCreatetime = LocalDateTime.of(2020, 01, 01, 00, 00, 00);
            }
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (startTime != null) {
            param.setStartTime("'" + startTime.format(formatter) + "'");
        } else {
            param.setStartTime("'" + lastCreatetime.format(formatter) + "'");
        }
        if (endTime != null) {
            param.setEndTime("'" + endTime.format(formatter) + "'");
        } else {
            param.setEndTime("'" + LocalDateTime.now().format(formatter) + "'");
        }
        requestData.setParam(param);

        int pageNum = 1;
        boolean hasMoreData = true;
        while (hasMoreData) {
            page.setPageNum(pageNum);
            page.setPageSize(100);
            requestData.setPage(page);
            String paramStr = JsonUtil.object2Json(requestData);
            String appParam = Base64.getEncoder().encodeToString(paramStr.getBytes());
            String contactStr = String.join("&", appKey, sign, appParam);
            String appSign = HttpClientUtil.md5(contactStr);

            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());

            try {
                log.debug("表名:{},过滤条件:{}", table, paramStr);
                String url = stationDataBaseUrl + table;
                String response = HttpClientUtil.post(url, "", appKey, appParam, appSign);
                String jsonString = response.trim();
                TypeReference<Wrapper> typeRef = new TypeReference<Wrapper>() {
                };
                Wrapper wrapper = mapper.readValue(jsonString, typeRef);
                if (wrapper.getCode() == 0) {
                    if (wrapper.getData() != null) {
                        log.debug("表名:{},过滤条件:{},总条数:{}", table, paramStr, wrapper.getData().getTotalCount());
                        List newData = wrapper.getData().getResultList();
                        if (!CollectionUtils.isEmpty(newData)) {
                            this.saveOrUpdateStationData(entityClass, JsonUtil.object2Json(newData));
                            log.debug("表名:{},新增数据:{}条", table, newData.size());
                        } else {
                            hasMoreData = false;
                            log.debug("表名:{},过滤条件:{},resultList为空", table, paramStr);
                        }
                    } else {
                        hasMoreData = false;
                        log.debug("表名:{},过滤条件:{},data为空", table, paramStr);
                    }
                } else {
                    hasMoreData = false;
                    log.debug("表名:{},过滤条件:{},获取数据失败:{}", table, paramStr, wrapper.getMsg());
                }
                pageNum++;
                log.debug("hasMoreData:{},过滤条件:{}", hasMoreData, paramStr);
            } catch (IOException e) {
                hasMoreData = false;
                log.debug("表名:{},过滤条件:{},保存数据失败:{}", table, paramStr, e.getMessage());
            }
        }
        log.debug("requestStationData结束:{}", table);
    }

    public LocalDateTime getMaxCreateTime(Class<?> entityClass, String buoyInfoId) {
        if (entityClass.equals(OceanStationMinuteSqO.class)) {
            return oceanStationMinuteSqOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourAtO.class)) {
            return oceanStationHourAtOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourBpO.class)) {
            return oceanStationHourBpOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourHuO.class)) {
            return oceanStationHourHuOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourRnO.class)) {
            return oceanStationHourRnOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourSlO.class)) {
            return oceanStationHourSlOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourVbO.class)) {
            return oceanStationHourVbOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourWlDatO.class)) {
            return oceanStationHourWlDatOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourWlO.class)) {
            return oceanStationHourWlOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourWsDatO.class)) {
            return oceanStationHourWsDatOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourWsO.class)) {
            return oceanStationHourWsOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourWtO.class)) {
            return oceanStationHourWtOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationHourWvO.class)) {
            return oceanStationHourWvOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanStationOntimeDataO.class)) {
            return oceanStationOntimeDataOService.getMaxCreateTime();
        }
        if (entityClass.equals(GroupOceanLargeDeepseaMooringbuoyO.class)) {
            return groupOceanLargeDeepseaMooringbuoyOService.getMaxCreateTime();
        }
        if (entityClass.equals(OceanLargeDeepseaMooringbuoyO.class)) {
            return oceanLargeDeepseaMooringbuoyOService.getMaxCreateTime(buoyInfoId);
        }
        if (entityClass.equals(OceanSmallShallowseauoyO.class)) {
            return oceanSmallShallowseauoyOService.getMaxCreateTime();
        }
        return null;
    }

    public void saveOrUpdateStationData(Class<?> entityClass, String jsonString) throws IOException {
        if (entityClass.equals(OceanStationMinuteSqO.class)) {
            List<OceanStationMinuteSqO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationMinuteSqO>>() {
            });
            oceanStationMinuteSqOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourAtO.class)) {
            List<OceanStationHourAtO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourAtO>>() {
            });
            oceanStationHourAtOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourBpO.class)) {
            List<OceanStationHourBpO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourBpO>>() {
            });
            oceanStationHourBpOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourHuO.class)) {
            List<OceanStationHourHuO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourHuO>>() {
            });
            oceanStationHourHuOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourRnO.class)) {
            List<OceanStationHourRnO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourRnO>>() {
            });
            oceanStationHourRnOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourSlO.class)) {
            List<OceanStationHourSlO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourSlO>>() {
            });
            oceanStationHourSlOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourVbO.class)) {
            List<OceanStationHourVbO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourVbO>>() {
            });
            oceanStationHourVbOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourWlDatO.class)) {
            List<OceanStationHourWlDatO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourWlDatO>>() {
            });
            oceanStationHourWlDatOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourWlO.class)) {
            List<OceanStationHourWlO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourWlO>>() {
            });
            oceanStationHourWlOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourWsDatO.class)) {
            List<OceanStationHourWsDatO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourWsDatO>>() {
            });
            oceanStationHourWsDatOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourWsO.class)) {
            List<OceanStationHourWsO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourWsO>>() {
            });
            oceanStationHourWsOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourWtO.class)) {
            List<OceanStationHourWtO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourWtO>>() {
            });
            oceanStationHourWtOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationHourWvO.class)) {
            List<OceanStationHourWvO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationHourWvO>>() {
            });
            oceanStationHourWvOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanStationOntimeDataO.class)) {
            List<OceanStationOntimeDataO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanStationOntimeDataO>>() {
            });
            oceanStationOntimeDataOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(GroupOceanLargeDeepseaMooringbuoyO.class)) {
            List<GroupOceanLargeDeepseaMooringbuoyO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<GroupOceanLargeDeepseaMooringbuoyO>>() {
            });
            groupOceanLargeDeepseaMooringbuoyOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanLargeDeepseaMooringbuoyO.class)) {
            List<OceanLargeDeepseaMooringbuoyO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanLargeDeepseaMooringbuoyO>>() {
            });
            oceanLargeDeepseaMooringbuoyOService.saveOrUpdateBatch(list);
        }
        if (entityClass.equals(OceanSmallShallowseauoyO.class)) {
            List<OceanSmallShallowseauoyO> list = JsonUtil.json2ObjectList(jsonString, new TypeReference<List<OceanSmallShallowseauoyO>>() {
            });
            oceanSmallShallowseauoyOService.saveOrUpdateBatch(list);
        }
    }
}





