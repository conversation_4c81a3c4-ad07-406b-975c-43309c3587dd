<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-24 17:00:21
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-02 15:37:45
 * @FilePath: \hainan-jianzai-web\src\views\publicServices\tsunamiProducts\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="special-forecast">
    <div class="column-header">
      <h3>海啸警报产品</h3>
    </div>
    <div class="search-filter d-flex flex-justify-between">
      <n-form
        ref="formRef"
        :model="form"
        label-placement="left"
        label-width="auto"
        require-mark-placement="left"
        inline
      >
        <n-form-item label="产品名称">
          <n-input v-model:value="form.name" placeholder="请输入" clearable />
        </n-form-item>
        <n-form-item label="生成时间">
          <n-date-picker
            v-model:formatted-value="form.dataTime"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            clearable
          />
        </n-form-item>
      </n-form>
      <div class="btn-group d-flex">
        <qx-button @click="resetForm">重置</qx-button>
        <qx-button class="primary" @click="onSearch">查询</qx-button>
        <qx-button class="warning" @click="multiDownLoad"> 批量下载 </qx-button>
      </div>
    </div>
    <div class="table-container">
      <n-data-table
        :bordered="false"
        :single-line="false"
        :columns="columns"
        :data="tableData"
        :row-key="rowKey"
        @update:checked-row-keys="handleCheck"
      />
      <n-pagination
        v-model:page="pagination.page"
        class="qx-pagination flex-justify-end"
        :page-count="pagination.pageCount"
        @update:page="onChangePage"
      />
    </div>
  </div>
  <preview-dialog
    :id="previewId"
    v-model:visible="previewVisible"
    :file-path="filePath"
  />
</template>

<script setup lang="ts">
import { ref, h, reactive, onMounted } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { PreviewDialog } from './components'
import { useMessage } from 'naive-ui'
import type { DataTableRowKey } from 'naive-ui'
import PublicService from 'src/requests/publicService'
const message = useMessage()
const formRef = ref(null)
type Form = {
  name: string
  dataTime: any
}

let form: Form = reactive({
  name: '',
  dataTime: null
})
const rowKey = (row: any) => row.id
const tableData = ref<any[]>([])
const columns = ref<any>([
  {
    type: 'selection'
  },
  {
    title: '序号',
    render(row: any, index: number) {
      return index + 1
    }
  },
  {
    title: '产品名称',
    key: 'name'
  },
  {
    title: '生成时间',
    key: 'createTime'
  },
  {
    title: '操作',
    render(row: any) {
      return [
        h(
          'span',
          {
            size: 'small',
            class: 'operate-btn',
            style: { cursor: 'pointer' },
            onClick: () => onPreview(row)
          },
          { default: () => '预览' }
        ),
        h(
          'span',
          {
            size: 'small',
            class: 'operate-btn del',
            style: { cursor: 'pointer' },
            onClick: () => onDownLoad(row)
          },
          { default: () => '下载' }
        )
      ]
    }
  }
])
const checkedRowKeysRef = ref<DataTableRowKey[]>([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 0
})
type Params = {
  pageSize: number
  pageNum: number
  name: string
  startTime?: string
  endTime?: string
}

function getTableData() {
  tableData.value = []
  const params: Params = {
    pageNum: pagination.page,
    pageSize: pagination.pageSize,
    name: form.name
  }
  if (form.dataTime?.length) {
    params.startTime = form.dataTime[0]
    params.endTime = form.dataTime[1]
  }
  PublicService.getTsunamiProductPage(params)
    .then((res: any) => {
      if (res) {
        const { pageResult, pages } = res
        tableData.value = pageResult
        pagination.pageCount = pages
      }
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
    })
}
function onChangePage(page: number) {
  pagination.page = page
  getTableData()
}

function handleCheck(rowKeys: DataTableRowKey[]) {
  checkedRowKeysRef.value = rowKeys
}
function resetForm() {
  form.name = ''
  form.dataTime = null
  onSearch()
}
function onSearch() {
  pagination.page = 1
  getTableData()
}
function multiDownLoad() {
  if (checkedRowKeysRef.value.length == 0) {
    message.warning('请选择下载项')
    return false
  }
  const ids = checkedRowKeysRef.value.join(',')
  PublicService.multipleDownloadTsunamiProduct({ ids })
    .then((res: any) => {
      download(res, 'application/zip')
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '下载失败')
    })
}
let previewVisible = ref(false)
let previewTitle = ref('')
const previewId = ref('')
let filePath = ref('')

function onPreview(row: any) {
  previewTitle.value = row.name
  previewVisible.value = true
  previewId.value = row.id
  let url = config.onlyOfficeServerUrl + row.fileUrl
  filePath.value = config.kkFileUrl + encodeToBase64(url)
}
// TextEncoder 将字符串编码为字节数组，然后将字节数组转换为二进制字符串，最后使用 btoa 进行 Base64 编码。
function encodeToBase64(str: string) {
  const encoder = new TextEncoder()
  const data = encoder.encode(str)
  const binaryString = String.fromCharCode(...data)
  return btoa(binaryString)
}
function onDownLoad(row: any) {
  PublicService.downloadTsunamiProduct(row.id)
    .then((res: any) => {
      download(res)
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '下载失败')
    })
}

function download(res: any, type?: string) {
  const link = document.createElement('a')
  // @ts-ignore
  const url: any = window.URL || window.webkitURL || window.moxURL
  link.href = url.createObjectURL(
    new Blob([res.data], { type: type ? type : res.data.type })
  )
  link.download = decodeURIComponent(res.fileName) //下载的文件名称
  link.click()
  window.URL.revokeObjectURL(url)
}

onMounted(() => {
  getTableData()
})
</script>

<style lang="scss">
.special-forecast {
  box-sizing: border-box;
  padding: 20px;
  background: #fff;
  .qx-pagination {
    margin-top: 20px;
  }
  .column-header {
    box-sizing: border-box;
    padding: 20px;
    background: url(src/assets/images/common/content-header.png) no-repeat;
    background-size: 100% 100%;
    border-bottom: 1px solid #dfdfdf;
    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      position: relative;
      padding-left: 20px;
      &::before {
        content: '';
        position: absolute;
        width: 4px;
        height: 18px;
        background: #0091ff;
        left: 0px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .btn-group {
    flex-shrink: 0;
  }
  .search-filter {
    box-sizing: border-box;
    padding: 20px 20px 0;
  }
  .table-container {
    box-sizing: border-box;
    padding: 20px;
    padding-top: 0;
  }
}
</style>
