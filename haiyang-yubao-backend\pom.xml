<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.piesat</groupId>
        <artifactId>piesat-springboot-parent</artifactId>
        <version>*******</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>cn.piesat.data.making.server</groupId>
    <artifactId>data-making-server</artifactId>
    <version>1.0.0</version>
    <name>data-making-server</name>
    <description>data-making-server</description>
    <properties>
        <docker.repositry>**********:8083</docker.repositry>
        <java.version>1.8</java.version>
        <druid.version>1.2.8</druid.version>
        <lombok.version>1.18.16</lombok.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <hutool.version>5.5.2</hutool.version>
        <geotools.version>24.2</geotools.version>
        <common-utils.version>1.3.1</common-utils.version>
        <sys-client.version>1.0-SNAPSHOT</sys-client.version>
        <!--<sys-client.version>*******</sys-client.version>-->
        <sys-log.version>1.0.2</sys-log.version>
        <security-ucenter.version>*******</security-ucenter.version>
        <basics-ptc-webconfig.version>1.0.7-SNAPSHOT</basics-ptc-webconfig.version>
        <data-push-starter.revision>5.0.0-SNAPSHOT</data-push-starter.revision>
    </properties>
    <dependencies>
        <!-- springboot web包 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- springboot 测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.junit.jupiter</groupId>-->
<!--            <artifactId>junit-jupiter</artifactId>-->
<!--            <version>5.9.2</version>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->

        <!-- MyBatis-Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.2</version>
        </dependency>
        <!-- druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!-- pg数据库 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- postgis-jdbc -->
        <dependency>
            <groupId>net.postgis</groupId>
            <artifactId>postgis-jdbc</artifactId>
            <version>2.5.0</version>
        </dependency>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <!-- geotools -->
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-main</artifactId>
            <version>${geotools.version}</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-geojson</artifactId>
            <version>${geotools.version}</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-shapefile</artifactId>
            <version>${geotools.version}</version>
        </dependency>

        <!-- freemarker -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
            <version>2.6.1</version>
        </dependency>

        <!-- word工具 -->
        <dependency>
            <groupId>com.luhuiguo</groupId>
            <artifactId>aspose-words</artifactId>
            <version>23.1</version>
        </dependency>

        <!-- poi-tl -->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.2</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.17.1</version>
        </dependency>

        <!-- xml -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.1</version>
        </dependency>

        <!-- pinyin4j -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <!-- okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.3.70</version>
        </dependency>

        <!-- 基础依赖 -->
        <dependency>
            <groupId>cn.piesat.common</groupId>
            <artifactId>common-utils</artifactId>
            <version>${common-utils.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat.sys</groupId>
            <artifactId>sys-client-starter</artifactId>
            <version>${sys-client.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat.sys</groupId>
            <artifactId>sys-core</artifactId>
            <version>${sys-client.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat.common</groupId>
            <artifactId>sys-log-starter</artifactId>
            <version>${sys-log.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat.security</groupId>
            <artifactId>security-ucenter-starter</artifactId>
            <version>${security-ucenter.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat</groupId>
            <artifactId>basics-ptc-webconfig-starter</artifactId>
            <version>${basics-ptc-webconfig.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat.data</groupId>
            <artifactId>data-push-starter</artifactId>
            <version>${data-push-starter.revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.piesat</groupId>
            <artifactId>sys-admin-starter-inner</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.10.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 添加smart-doc插件 -->
<!--            <plugin>-->
<!--                <groupId>com.github.shalousun</groupId>-->
<!--                <artifactId>smart-doc-maven-plugin</artifactId>-->
<!--                <version>2.3.6</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash;指定生成文档的使用的配置文件,配置文件放在自己的项目中&ndash;&gt;-->
<!--                    <configFile>./src/main/resources/smart-doc.json</configFile>-->
<!--                    &lt;!&ndash;指定项目名称&ndash;&gt;-->
<!--                    <projectName>data-making-server</projectName>-->
<!--                    &lt;!&ndash;smart-doc实现自动分析依赖树加载第三方依赖的源码，如果一些框架依赖库加载不到导致报错，这时请使用excludes排除掉&ndash;&gt;-->
<!--                    <excludes>-->
<!--                        &lt;!&ndash;格式为：groupId:artifactId;参考如下&ndash;&gt;-->
<!--                        &lt;!&ndash;也可以支持正则式如：com.alibaba:.* &ndash;&gt;-->
<!--                        <exclude>com.alibaba:fastjson</exclude>-->
<!--                    </excludes>-->
<!--                    &lt;!&ndash;includes配置用于配置加载外部依赖源码,配置后插件会按照配置项加载外部源代码而不是自动加载所有，因此使用时需要注意&ndash;&gt;-->
<!--                    &lt;!&ndash;smart-doc能自动分析依赖树加载所有依赖源码，原则上会影响文档构建效率，因此你可以使用includes来让插件加载你配置的组件&ndash;&gt;-->
<!--                    <includes>-->
<!--                        &lt;!&ndash;格式为：groupId:artifactId;参考如下&ndash;&gt;-->
<!--                        &lt;!&ndash;也可以支持正则式如：com.alibaba:.* &ndash;&gt;-->
<!--                        <include>com.alibaba:fastjson</include>-->
<!--                    </includes>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        &lt;!&ndash;如果不需要在执行编译时启动smart-doc，则将phase注释掉&ndash;&gt;-->
<!--                        <phase>compile</phase>-->
<!--                        <goals>-->
<!--                            &lt;!&ndash;smart-doc提供了html、openapi、markdown等goal，可按需配置&ndash;&gt;-->
<!--                            <goal>html</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <skipTests>true</skipTests> <!-- 跳过测试 -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>dockerfile-maven-plugin</artifactId>-->
<!--                <version>1.4.10</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default</id>-->
<!--                        <phase>install</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                            <goal>push</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <repository>${docker.repositry}/${project.artifactId}</repository>-->
<!--                    <tag>${project.version}</tag>-->
<!--                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>-->
<!--                    <buildArgs>-->
<!--                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>-->
<!--                    </buildArgs>-->
<!--                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
