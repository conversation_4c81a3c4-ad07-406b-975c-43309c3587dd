package cn.piesat.data.making.server;

import cn.piesat.common.syslog.aspect.OperateLogAspect;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan(value="cn.piesat.*")
@EnableScheduling
@EnableFeignClients
@EnableAspectJAutoProxy(exposeProxy = true)
@Import({OperateLogAspect.class})
public class DataMakingServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataMakingServerApplication.class, args);
    }
}
