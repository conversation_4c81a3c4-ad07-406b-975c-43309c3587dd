package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.SeaWaveMessageDTO;
import cn.piesat.data.making.server.entity.SeaWaveMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface SeaWaveMessageMapper {
    SeaWaveMessageMapper INSTANCE = Mappers.getMapper(SeaWaveMessageMapper.class);

    SeaWaveMessage toEntity(SeaWaveMessageDTO seaWaveMessageDTO);
}
