package cn.piesat.data.making.server.enums;

public enum AlarmTemplateCodeEnum {
    SeaWaveSMS(1,"SMS"),
    SeaWaveAlarmContent(1,"CONTENT"),
    SeaWaveMSGSMS(2,"SMS"),
    SeaWaveMSGAlarmContent(2,"CONTENT"),
    StormSurgeSMS(3,"SMS"),
    StormSurgeAlarmContent(3,"CONTENT"),
    StormSurgeMSGSMS(4,"SMS"),
    StormSurgeMSGAlarmContent(4,"CONTENT");
    private Integer templateType;

    private String templateCode;

    AlarmTemplateCodeEnum(Integer templateType, String templateCode) {
        this.templateType = templateType;
        this.templateCode = templateCode;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }
}
