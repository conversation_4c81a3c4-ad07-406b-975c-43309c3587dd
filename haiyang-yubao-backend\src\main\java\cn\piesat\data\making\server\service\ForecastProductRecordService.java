package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.ForecastProductRecord;
import cn.piesat.data.making.server.model.ForecastProductPushResultInfo;
import cn.piesat.data.making.server.model.ProductReleaseResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.ForecastProductRecordVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预报产品记录表服务接口
 *
 * <AUTHOR>
 */
public interface ForecastProductRecordService extends IService<ForecastProductRecord> {

    /**
     * 查询列表
     */
    List<ForecastProductRecordVO> getList();

    /**
     * 保存
     */
    List<ForecastProductRecordVO> save();

    /**
     * 发布
     */
    void release();

    /**
     * 下载
     */
    void download(HttpServletResponse response);

    /**
     * 跟据文件Id下载
     *
     * @param response
     * @param productRecordId
     */
    void downloadDoc(HttpServletResponse response,Long productRecordId);

    /**
     * 查询预报产品发布结果分页
     */
    PageResult<ForecastProductPushResultInfo> getReleaseResultPage(PageParam pageParam);

    /**
     * 根据推送任务id、产品名称、状态获取产品列表
     */
    List<ProductReleaseResult> getReleaseResultByTaskId(Long pushTaskId, String productName, Integer state);
}




