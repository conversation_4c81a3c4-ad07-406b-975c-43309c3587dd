package cn.piesat.data.making.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站数据表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OceanStationDataVO implements Serializable {

    private static final long serialVersionUID = -70512251811336617L;

    /**
     * id
     **/
    private Long id;
    /**
     * 海洋站编码
     **/
    private String oceanStationCode;
    /**
     * 时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    /**
     * 气温
     **/
    private String airTemperature;
    /**
     * 气压
     **/
    private String airPressure;
    /**
     * 湿度
     **/
    private String humidity;
    /**
     * 降水
     **/
    private String rain;
    /**
     * 盐度
     **/
    private String salinity;
    /**
     * 能见度
     **/
    private String visibility;
    /**
     * 潮位
     **/
    private String tide;
    /**
     * 风速
     **/
    private String windSpeed;
    /**
     * 风向
     **/
    private String windDir;
    /**
     * 海温
     **/
    private String seaTemperature;
    /**
     * 波高
     **/
    private String windWaveHeight;
    /**
     * 波周期
     **/
    private String windWavePeriod;
    /**
     * 海洋站名称
     **/
    private String oceanStationName;
    /**
     * 海洋站位置
     **/
    private String oceanStationLocationJson;
}



