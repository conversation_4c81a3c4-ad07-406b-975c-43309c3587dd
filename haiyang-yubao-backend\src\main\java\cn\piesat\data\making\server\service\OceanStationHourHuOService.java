package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationHourHuO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-相对湿度-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationHourHuOService extends IService<OceanStationHourHuO> {

    List<OceanStationHourHuO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




