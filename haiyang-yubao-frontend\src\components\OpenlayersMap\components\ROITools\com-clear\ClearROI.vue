<script setup lang="ts">
import { useROIInject } from '../hooks/useROIInject'
import { onMounted } from 'vue'
import { ActionType } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIOptions'

const currentAction = useROIInject('currentAction').injectROI()
const vectorSource = useROIInject('vectorSource').injectROI()

onMounted(() => {
  vectorSource?.clear()
  currentAction && (currentAction.value = ActionType.NONE)
})
</script>

<template><div></div></template>

<style scoped lang="scss"></style>
