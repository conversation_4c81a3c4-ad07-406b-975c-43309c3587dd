package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 台风实时数据信息Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:23
 */
@Mapper(componentModel = "spring")
public interface FmTyphoonRealBMapper {

    FmTyphoonRealBMapper INSTANCE = Mappers.getMapper(FmTyphoonRealBMapper.class);

    /**
     * entity-->vo
     */
    FmTyphoonRealBVO entityToVo(FmTyphoonRealB entity);

    /**
     * dto-->entity
     */
    FmTyphoonRealB dtoToEntity(FmTyphoonRealBDTO dto);

    /**
     * entityList-->voList
     */
    List<FmTyphoonRealBVO> entityListToVoList(List<FmTyphoonRealB> list);

    /**
     * dtoList-->entityList
     */
    List<FmTyphoonRealB> dtoListToEntityList(List<FmTyphoonRealBDTO> dtoList);
}
