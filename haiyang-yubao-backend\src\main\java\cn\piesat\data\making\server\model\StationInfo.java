package cn.piesat.data.making.server.model;

import lombok.Data;

import java.util.List;

/**
 * 站点信息
 *
 * <AUTHOR>
 */
@Data
public class StationInfo {
    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    private String code;
    /**
     * 父级编码
     **/
    private String parentCode;
    /**
     * 站点类型编码
     **/
    private String stationTypeCode;
    /**
     * 名称
     **/
    private String name;
    /**
     * 位置
     **/
    private String locationJson;
    /**
     * 子级列表
     **/
    private List<StationInfo> childList;
    /**
     * 类型：潮位tide 实况观测liveObservation 3米浮标站3m 10米浮标站10m 波浪谱浮标站waveSpectrum
     **/
    private String type;
    /**
     * 关联站点
     **/
    private String relationStation;
    /**
     * 是否启用
     **/
    private Boolean enable;
}
