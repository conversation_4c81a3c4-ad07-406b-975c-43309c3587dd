package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.TagDTO;
import cn.piesat.data.making.server.entity.Tag;
import cn.piesat.data.making.server.vo.TagVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标签表服务接口
 *
 * <AUTHOR>
 */
public interface TagService extends IService<Tag> {

    /**
     * 查询列表
     */
    List<TagVO> getList(TagDTO dto);

    /**
     * 保存
     */
    void save(TagDTO dto);

    /**
     * 删除
     */
    void deleteById(Long id);
}




