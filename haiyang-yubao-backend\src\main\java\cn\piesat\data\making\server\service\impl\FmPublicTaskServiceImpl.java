package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmPublicTaskDao;
import cn.piesat.data.making.server.entity.FmPublicTask;
import cn.piesat.data.making.server.enums.PublicTaskStatus;
import cn.piesat.data.making.server.service.FmPublicTaskService;
import cn.piesat.data.making.server.service.FmPublicTemplateService;
import cn.piesat.data.making.server.vo.FmPublicTemplateVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class FmPublicTaskServiceImpl extends ServiceImpl<FmPublicTaskDao, FmPublicTask> implements FmPublicTaskService {

    @Autowired
    private FmPublicTemplateService fmPublicTemplateServiceImpl;

    @Autowired
    private FmPublicTaskDao fmPublicTaskImpl;

    @Override
    public void createPublicTask() {
        List<FmPublicTask> list = new ArrayList<>();
        LocalTime start = LocalTime.of(00, 00, 00);
        LocalTime end = LocalTime.of(23, 59, 59);
        LocalDate localDate = LocalDate.now();
        Date startTime = Date.from(LocalDateTime.of(localDate, start).atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(LocalDateTime.of(localDate, end).atZone(ZoneId.systemDefault()).toInstant());
        //查询预报模板
        List<FmPublicTemplateVO> fmPublicTemplateResList = fmPublicTemplateServiceImpl.queryList(true);
        if (!CollectionUtils.isEmpty(fmPublicTemplateResList)) {
            fmPublicTemplateResList.forEach(template -> {
                FmPublicTask fmPublicTask = new FmPublicTask();
                fmPublicTask.setName(template.getName());
                fmPublicTask.setTemplateId(template.getId());
                fmPublicTask.setPublicType(template.getPublicType());
                fmPublicTask.setStartTime(startTime);
                fmPublicTask.setEndTime(endTime);
                fmPublicTask.setStatus(PublicTaskStatus.WAIT_MAKE.getValue());
                list.add(fmPublicTask);
            });
        }
        this.saveBatch(list);
    }

    @Override
    public Long getLastTaskId(String publicType) {
        LambdaQueryWrapper<FmPublicTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicTask::getPublicType, publicType);
        queryWrapper.orderByDesc(FmPublicTask::getStartTime);
        queryWrapper.last("limit 1");
        FmPublicTask fmPublicTask = this.getOne(queryWrapper);
        if(fmPublicTask!=null){
            return fmPublicTask.getId();
        }
        return null;
    }
}
