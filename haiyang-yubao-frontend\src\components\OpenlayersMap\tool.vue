<template>
  <div class="tool">
    <div
      v-for="item in toolList"
      :key="item.id"
      class="tool-item"
      :class="activeVal === item.type ? 'toolAct' : ''"
      :title="item.name"
      @click="operation(item.type)"
    >
      <img :src="getUrl(item)" />
      <!-- <div class="tool-item-img" :style="{'background-image':`url(${activeVal == item.type?item.activeUrl:item.url})`}"></div> -->
    </div>

    <div
      v-show="jwwShow"
      class="tool-item"
      title="经纬网"
      :class="graticulesOpen ? 'toolAct' : ''"
      @click="graticules"
    >
      <img
        :src="graticulesOpen ? imagesMap['active14'] : imagesMap[14]"
        alt=""
      />
    </div>
    <div
      v-if="haveXXYS"
      class="tool-item"
      title="信息映射"
      :class="xxysShow ? 'toolAct' : ''"
      @click="xxysCtrl"
    >
      <img :src="xxysShow ? imagesMap['active17'] : imagesMap[17]" alt="" />
    </div>
    <div v-show="toolShow" class="tool-container">
      <div class="tool-title">
        <div class="title-container">
          <div class="title-name">{{ toolTitle }}</div>
          <Close class="icon-close" @click="close" />
        </div>
      </div>
      <div class="tool-info">
        <keep-alive>
          <component
            :is="toolComponent"
            ref="toolRef"
            @change-model-layer="changeModelLayer"
            @clearModelLayer="clearModelLayer"
          ></component>
        </keep-alive>
        <div v-show="activeVal === 'tfxz'">
          <tfxz></tfxz>
        </div>

        <!-- <MSYB></MSYB> -->
      </div>
    </div>
    <xxys v-if="xxysShow"></xxys>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  shallowReactive,
  onMounted,
  getCurrentInstance,
  inject,
  onUnmounted,
  nextTick
} from 'vue'
import { useRoute } from 'vue-router'
import { Close } from '@vicons/ionicons5'
import nanhaiPNG from 'src/assets/images/forecast/nanhai_tiny.png'
import {
  msyb,
  // lqgj,
  bjgj,
  tfxz,
  // xqgj,
  fgj,
  wzbz,
  xxys,
  mbxz,
  ybt
} from './components/index'
import lqgj from './components/ROITools/ROITools.vue'
import xqgj from './components/CoastalSegmentsTools/CoastalSegmentsTools.vue'
import Api from 'src/requests/forecast'
import gisUtils from 'src/utils/gis'
import moment from 'moment'
import Fill from 'ol/style/Fill.js'
import Stroke from 'ol/style/Stroke.js'
import Style from 'ol/style/Style.js'
import eventBus from 'src/utils/eventBus'
import { useStackStore } from 'src/stores/snapshot.js'
import { useMessage } from 'naive-ui'
const message = useMessage()
const stackStore = useStackStore()
const getMap = inject<(map: any) => void>('getMap')
type tool = {
  [propsName: string]: any
}
let component: tool = shallowReactive({
  msyb,
  lqgj,
  bjgj,
  tfxz,
  xqgj,
  fgj,
  wzbz,
  mbxz,
  ybt
})
const jwwShow = ref(false)
let toolRef = ref()
const xxysShow = ref(false)
const haveXXYS = ref(false)
function xxysCtrl() {
  xxysShow.value = !xxysShow.value
}
const req1 = import.meta.glob('src/assets/images/tools/*.*', { eager: true })
const req: any = { ...req1 }
let imagesMap: any = {}
// 循环所有图片，将图片名设置成键，值为导入该图片的地址
for (const key in req) {
  // let name = key.replace(/(\.\/images\/|\..*)/g, '')
  let name = key.split('/').slice(-1)[0].split('.')[0]

  // 抛出图片大对象后，文件页面直接引入后将图片的具体名称作为属性就能导入该图片
  imagesMap[name] = req[key].default
}

const toolList = ref<any>([])
const emits = defineEmits(['zoomType', 'bState', 'pState'])
const graticulesOpen = ref(false)

function getUrl(item: any) {
  let name = item.url.split('/').slice(-1)[0].split('.')[0]
  if (activeVal.value === item.type) {
    name = item.activeUrl.split('/').slice(-1)[0].split('.')[0]
  }
  return imagesMap[name]
}
function graticules() {
  graticulesOpen.value = !graticulesOpen.value

  if (graticulesOpen.value) {
    if (getMap) {
      getMap((map: any) => {
        gridLayer = gisUtils.addGrid(map, gridLayer, {
          leftLongitude: extent.value[0],
          leftLatitude: extent.value[1],
          rightLongitude: extent.value[2],
          rightLatitude: extent.value[3]
          //   extent.value = [
          //   res.leftLongitude,
          //   res.leftLatitude,
          //   res.rightLongitude,
          //   res.rightLatitude
          // ]
        })
      })
    }
  } else {
    if (getMap) {
      getMap((map: any) => {
        map.removeLayer(gridLayer)
      })
    }
  }
}
const activeVal = ref('')
const toolTitle = ref('')
const toolComponent = ref<any>('')
const toolShow = ref(false)
let gridLayer: any = null
const operation = (type: string) => {
  console.log(type, '*********type')
  toolShow.value = true
  activeVal.value = type
  if (type === 'fgj') {
    toolComponent.value = component['bjgj']
  } else {
    if (type === 'sthkzkg') {
    } else if (type === 'jwwg') {
    } else if (type === 'tfxz') {
      toolComponent.value = ''
      toolTitle.value =
        toolList.value.find((item: any) => item.type === type)?.name || ''
    } else if (type === 'qj') {
      toolShow.value = false
      // 前进
      undo()
    } else if (type === 'ht') {
      toolShow.value = false
      // 后退
      redo()
    } else {
      toolTitle.value =
        toolList.value.find((item: any) => item.type === type)?.name || ''
      toolComponent.value = component[type]
    }
  }
  nextTick(() => {
    if (toolRef.value) {
      // toolRef.value.ctrlPopup(type === 'tfxz')
    }
  })
}
function close() {
  toolShow.value = false
  activeVal.value = ''
}

let legendLayer: any = null
let labelLayer: any = null
let curTempInfo = ref<any>({})
function updateLabel(time: any) {
  const labelInfo: any = []
  let value = curTempInfo.value?.sign
  let str = ''
  let form = {
    leftLon: curTempInfo.value.leftLongitude,
    leftLat: curTempInfo.value.leftLatitude,
    rightLon: curTempInfo.value.rightLongitude,
    rightLat: curTempInfo.value.rightLatitude
  }
  value.forEach((item: any) => {
    if (item && item.position === 'leftDown') {
      labelInfo.push({
        label: item.context,
        name: item.position,
        coordinate: [Number(form.leftLon), Number(form.rightLat)] //[107.35, 17.76]
      })
    } else {
      if (item.context.includes('yyyy-MM-dd hh')) {
        str =
          str +
          moment(time).format('YYYY-MM-DD 17') +
          '时发布' +
          '\n'
      } else {
        str = str + item.context + '\n'
      }
    }
  })
  labelInfo.push({
    label: str,
    name: 'topLabel',
    coordinate: [Number(form.leftLon), Number(form.leftLat)]
  })
  if (getMap) {
    getMap((map: any) => {
      labelLayer = gisUtils.addLabel(map, labelInfo, labelLayer)
    })
  }
}
function screenshot() {
  if (getMap) {
    getMap((map: any) => {
      map.once('rendercomplete', function () {
        const leftTopPosition = map.getPixelFromCoordinate([
          extent.value[0],
          extent.value[1]
        ])
        // 地理坐标转换屏幕坐标
        const bottomRightPosition = map.getPixelFromCoordinate([
          extent.value[2],
          extent.value[3]
        ])
        // 计算框选矩形的宽度以及高度像素
        const width = Math.abs(bottomRightPosition[0] - leftTopPosition[0])
        const height = Math.abs(bottomRightPosition[1] - leftTopPosition[1])
        // 计算框选矩形的左上角屏幕坐标,放置用户反过来绘制
        const minx =
          leftTopPosition[0] <= bottomRightPosition[0]
            ? leftTopPosition[0]
            : bottomRightPosition[0]
        const miny =
          leftTopPosition[1] <= bottomRightPosition[1]
            ? leftTopPosition[1]
            : bottomRightPosition[1]

        const mapCanvas = document.createElement('canvas')
        mapCanvas.width = width
        mapCanvas.height = height
        let mapContext: any = mapCanvas.getContext('2d')
        Array.prototype.forEach.call(
          map
            .getViewport()
            .querySelectorAll('.ol-layer canvas, canvas.ol-layer'),
          function (canvas) {
            if (canvas.width > 0) {
              const opacity =
                canvas.parentNode.style.opacity || canvas.style.opacity
              mapContext.globalAlpha = opacity === '' ? 1 : Number(opacity)
              let matrix
              const transform = canvas.style.transform
              if (transform) {
                // Get the transform parameters from the style's transform matrix
                matrix = transform
                  .match(/^matrix\(([^\(]*)\)$/)[1]
                  .split(',')
                  .map(Number)
              } else {
                matrix = [
                  parseFloat(canvas.style.width) / canvas.width,
                  0,
                  0,
                  parseFloat(canvas.style.height) / canvas.height,
                  0,
                  0
                ]
              }
              // Apply the transform to the export map context
              CanvasRenderingContext2D.prototype.setTransform.apply(
                mapContext,
                matrix
              )
              const backgroundColor = canvas.parentNode.style.backgroundColor
              if (backgroundColor) {
                mapContext.fillStyle = backgroundColor
                mapContext.fillRect(minx, miny, width, height)
              }
              mapContext.drawImage(canvas, -minx, -miny)
            }
          }
        )
        mapContext.globalAlpha = 1
        mapContext.setTransform(1, 0, 0, 1, 0, 0)
        // const link = document.getElementById('image-download')
        // link.href = mapCanvas.toDataURL()
        // link.click()
        const fileUrlColorful = mapCanvas.toDataURL('image/png')
        const ctx: any = mapCanvas.getContext('2d')
        const imgData = ctx.getImageData(
          0,
          0,
          mapCanvas.width,
          mapCanvas.height
        )
        const data = imgData.data

        for (let i = 0; i < data.length; i += 4) {
          const r = data[i]
          const g = data[i + 1]
          const b = data[i + 2]

          // 计算灰度值
          const gray = 0.3 * r + 0.59 * g + 0.11 * b

          // 设置为灰度值
          data[i] = data[i + 1] = data[i + 2] = gray
        }

        ctx.putImageData(imgData, 0, 0)

        const href = mapCanvas.toDataURL('image/png') // base64格式数据
        return { fileUrlColorful, href }
      })
      map.renderSync()
    })
  }
}
// 撤销（后退）
function redo() {
  console.log(stackStore.operateStack, stackStore.redoStatck, '*********')
  if (stackStore.operateStack.length > 0) {
    const obj = stackStore.redo()
    drawMap(obj)
  } else if (stackStore.operateStack.length === 0) {
    if (getMap) {
      getMap((map: any) => {
        const layers = map.getAllLayers()
        layers.forEach((layer: any) => {
          const properties = layer.getProperties()
          if (properties && properties.canRedo) {
            layer.getSource().refresh()
          }
        })
      })
    }
  }
}
// 恢复（前进）
function undo() {
  console.log(stackStore.operateStack, stackStore.redoStatck, '*********')
  if (stackStore.redoStatck.length > 0) {
    const obj = stackStore.undo()
    drawMap(obj)
  } else {
    message.warning('没有可恢复的步骤')
  }
}
function drawMap(obj: any) {
  obj.forEach((item: any) => {
    item.layer.getSource().refresh()
    item.layer.getSource().addFeatures(item.features)
  })
}
let extent = ref<any>([])
let rectLayer: any = null
const highlightStyle = new Style({
  fill: new Fill({
    color: '#EEE'
  }),
  stroke: new Stroke({
    color: '#3399CC',
    width: 2
  })
})
function changeModelLayer(id: any) {
  clearModelLayer()
  Api.getForecastTmpById(id)
    .then((res: any) => {
      curTempInfo.value = res
      eventBus.emit('tempInfo', res)
      if (!res.leftLongitude) {
        return false
      }
      let form = {
        leftLon: res.leftLongitude,
        leftLat: res.leftLatitude,
        rightLon: res.rightLongitude,
        rightLat: res.rightLatitude
      }
      extent.value = [
        res.leftLongitude,
        res.leftLatitude,
        res.rightLongitude,
        res.rightLatitude
      ]
      if (getMap) {
        getMap((map: any) => {
          // 添加矩形
          rectLayer = gisUtils.addRect(map, form, rectLayer)
          // 添加图例
          let legendInfo = {
            src: res?.imageUrl,
            coordinate: [Number(form.leftLon), Number(form.rightLat)] // [107.35, 17.76]
          }
          if (res.imageUrl) {
            legendLayer = gisUtils.addLegend(map, legendInfo, null)
          }
          // 添加南海诸岛
          const figureInfo = {
            src: nanhaiPNG,
            coordinate: [Number(form.rightLon), Number(form.rightLat)]
          }
          legendLayer = gisUtils.addLegend(map, figureInfo, null)

          // 添加lable
          const labelInfo = []
          let value = res?.sign
          let str = ''
          // value.sort((a: any, b: any) => a - b)
          value.forEach((item: any) => {
            if (item && item.position === 'leftDown') {
              labelInfo.push({
                label: item.context,
                name: item.position,
                coordinate: [Number(form.leftLon), Number(form.rightLat)] //[107.35, 17.76]
              })
            } else {
              // str = str + item.context + '\n'
              if (item.context.includes('yyyy-MM-dd hh')) {
                str =
                  str +
                  moment().format('YYYY-MM-DD 17') +
                  '时发布' +
                  '\n'
              } else {
                str = str + item.context + '\n'
              }
            }
          })
          labelInfo.push({
            label: str,
            name: 'topLabel',
            coordinate: [Number(form.leftLon), Number(form.leftLat)]
          })
          if (
            form.leftLon !== '' &&
            form.leftLat !== '' &&
            form.rightLon !== '' &&
            form.rightLat !== ''
          ) {
            labelLayer = gisUtils.addLabel(map, labelInfo, null)
          }

          map.getView().on('change:resolution', function () {
            legendLayer?.getSource().forEachFeature((feature: any) => {
              let style = feature.getStyle()
              // 重新设置图标的缩放率，基于层级10来做缩放
              style[0].getImage().setScale(map.getView().getZoom() / 10)
              legendLayer?.setStyle(style)
            })

            labelLayer?.getSource().forEachFeature((feature: any) => {
              let style = feature.getStyle()
              style[0]
                .getText()
                .setFont(map.getView().getZoom() * 3 + 'px Calibri,sans-serif')
            })
          })
        })
      }
    })
    .catch(() => {})
}
function clearModelLayer() {
  // rectLayer?.getSource().refresh()
  // legendLayer?.getSource().refresh()
  // labelLayer?.getSource().refresh()
  if (getMap) {
    getMap((map: any) => {
      map.removeLayer(legendLayer)
      map.removeLayer(labelLayer)
      map.removeLayer(rectLayer)
    })
  }
}
onMounted(() => {
  const currentRoute = useRoute()
  haveXXYS.value = currentRoute.name === 'drawing'
  const { proxy } = getCurrentInstance() as any
  const parent = proxy.$parent
  console.log(parent.currentProduct,"**********parent.currentProduct")
  // 获取当前路由信息，如果当前是预报制作
  if (
    parent.currentProduct &&
    (parent.currentProduct.templateId
      ? parent.currentProduct.templateId
      : parent.currentProduct.id)
  ) {
    Api.getForecastTmpById(
      parent.currentProduct.templateId
        ? parent.currentProduct.templateId
        : parent.currentProduct.id
    )
      .then((res: any) => {
        console.log(res, 'res')
        curTempInfo.value = res
        eventBus.emit('tempInfo', res)
        jwwShow.value = res.tools.some((item: any) => item.type === 'jwwg')
        if (jwwShow.value) {
          res.tools = res.tools.filter((item: any) => item.type !== 'jwwg')
        }
        toolList.value = res.tools
        if (!res.leftLongitude) {
          return false
        }
        let form = {
          leftLon: res.leftLongitude,
          leftLat: res.leftLatitude,
          rightLon: res.rightLongitude,
          rightLat: res.rightLatitude
        }
        extent.value = [
          res.leftLongitude,
          res.leftLatitude,
          res.rightLongitude,
          res.rightLatitude
        ]
        if (getMap) {
          getMap((map: any) => {
            // 添加矩形
            rectLayer = gisUtils.addRect(map, form, rectLayer)
            // 添加图例
            let legendInfo = {
              src: res?.imageUrl,
              coordinate: [Number(form.leftLon), Number(form.rightLat)] // [107.35, 17.76]
            }
            if (res.imageUrl) {
              legendLayer = gisUtils.addLegend(map, legendInfo, null)
            }
            // 添加南海诸岛
            const figureInfo = {
              src: nanhaiPNG,
              coordinate: [Number(form.rightLon), Number(form.rightLat)]
            }
            legendLayer = gisUtils.addLegend(map, figureInfo, null, [1.1, 1])

            // 添加lable
            const labelInfo = []
            let value = res?.sign
            let str = ''
            // value.sort((a: any, b: any) => a - b)
            value.forEach((item: any) => {
              if (item && item.position === 'leftDown') {
                /**
           * leftLon: '107.35',
            leftLat: '21.95',
            rightLon: '112.63',
            rightLat: '17.76'
           */
                labelInfo.push({
                  label: item.context,
                  name: item.position,
                  coordinate: [Number(form.leftLon), Number(form.rightLat)] //[107.35, 17.76]
                })
              } else {
                // str = str + item.context + '\n'
                if (item.context.includes('yyyy-MM-dd hh')) {
                  str =
                    str +
                    moment().format('YYYY-MM-DD 17') +
                    '时发布' +
                    '\n'
                } else {
                  str = str + item.context + '\n'
                }
              }
            })
            labelInfo.push({
              label: str,
              name: 'topLabel',
              coordinate: [Number(form.leftLon), Number(form.leftLat)]
            })
            if (
              form.leftLon !== '' &&
              form.leftLat !== '' &&
              form.rightLon !== '' &&
              form.rightLat !== ''
            ) {
              labelLayer = gisUtils.addLabel(map, labelInfo, null)
            }

            map.getView().on('change:resolution', function () {
              legendLayer?.getSource().forEachFeature((feature: any) => {
                let style = feature.getStyle()
                // 重新设置图标的缩放率，基于层级10来做缩放
                style[0].getImage().setScale(map.getView().getZoom() / 10)
                legendLayer?.setStyle(style)
              })

              labelLayer?.getSource().forEachFeature((feature: any) => {
                let style = feature.getStyle()
                style[0]
                  .getText()
                  .setFont(
                    map.getView().getZoom() * 3 + 'px Calibri,sans-serif'
                  )
              })
            })
          })
        }
      })
      .catch(() => {})

    eventBus.on('layerControl', (params: boolean) => {
      labelLayer?.setVisible(params)
      legendLayer?.setVisible(params)
      rectLayer?.setVisible(params)
    })
  }
})

onUnmounted(() => {
  eventBus.off('layerControl')
  if (getMap) {
    getMap((map: any) => {
      map.removeLayer(legendLayer)
      map.removeLayer(labelLayer)
      map.removeLayer(rectLayer)
      map.removeLayer(gridLayer)
    })
  }
})

onMounted(() => {
  eventBus.on('openYBT', () => {
    toolComponent.value = ybt
  })
})
onUnmounted(() => {
  eventBus.off('openYBT')
})
defineExpose({
  activeVal,
  updateLabel,
  screenshot,
  component
})
</script>

<style scoped lang="scss">
.tool {
  position: absolute;
  top: 30px;
  left: 20px;
  z-index: 99;
  background: #fff;
  border-radius: 8px;
  .tool-item {
    width: 35px;
    height: 35px;
    border-bottom: 1px solid #dfdfdf;
    &:nth-last-child(1) {
      border: none;
    }
    .tool-item-img,
    img {
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
    }
  }
}
.tool-container {
  position: absolute;
  top: 0;
  left: 41px;
  width: 370px;
  .tool-title {
    width: 100%;
    height: 60px;
    background: url('src/assets/images/common/dialog-head-bg.png') no-repeat;
    background-size: 100% 100%;
    .title-container {
      display: flex;
      align-items: end;
      height: 33px;
      margin-left: 34px;
      margin-right: 22px;
      justify-content: space-between;
      .title-name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #104cc0;
        line-height: 19px;
      }
    }
  }
  .tool-info {
    padding: 10px 15px 10px 15px;
    background: #fff;
  }
}
</style>
