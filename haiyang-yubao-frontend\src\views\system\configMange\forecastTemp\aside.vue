<template>
  <div class="forecast-temp-aside common-aside">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>预报单列表</h3>
        <n-button
          type="primary"
          :bordered="false"
          :focusable="false"
          @click="dialogVisible = true"
        >
          新建
        </n-button>
      </div>
      <div class="qx-select d-flex flex-justify-end">
        <n-select
          v-model:value="status"
          :options="tabList"
          :bordered="false"
          style="width: 25%"
          @update:value="filterData"
        />
      </div>
    </div>
    <qx-tree
      ref="treeRef"
      v-loading="loading"
      :tree-data="treeData"
      :default-props="defaultProps"
      :is-switch="isSwitch"
      :default-expand-all="true"
      :default-selected-keys="defaultSelectedKeys"
      :edit="true"
      :icons="['icon-add', 'icon-remove', '']"
      :have-right-menu="true"
      @change-switch="changeSwitch"
      @selected="selectHandler"
      @delete-node="deleteNode"
      @re-name="reNameHandler"
    />
  </div>
  <qx-dialog
    v-model:visible="reNameVisible"
    title="重命名预报单模板"
    width="345px"
    class="create-forecast-temp-dialog"
    @update:visible="reNameVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="formRef"
          class="forecast-temp-form"
          :model="curRightData"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="模板名称" path="name">
            <n-input
              v-model:value="curRightData.name"
              placeholder="请输入"
              clearable
            />
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="reNameVisible = false">取消</qx-button>
        <qx-button class="primary" @click="reName">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
  <qx-dialog
    v-model:visible="dialogVisible"
    title="新建预报单模板"
    width="345px"
    class="create-forecast-temp-dialog"
    @update:visible="dialogVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="formRef"
          class="forecast-temp-form"
          :model="form"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="模板名称" path="name">
            <n-input v-model:value="form.name" placeholder="请输入" clearable />
          </n-form-item>
          <n-form-item label="关联区域" path="forecastType">
            <n-select
              v-model:value="form.forecastType"
              placeholder="请选择"
              clearable
              :options="areaList"
              label-field="name"
              value-field="code"
            ></n-select>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="dialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onSave">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { QxTree } from 'src/components/QxTree'
import type { TreeOption } from 'naive-ui'
import Api from 'src/requests/forecast'
import { useMessage } from 'naive-ui'
import { QxDialog } from 'src/components/QxDialog'
import { QxButton } from 'src/components/QxButton'
import { getFirstLeafNode } from 'src/utils/util'
import { createDiscreteApi } from 'naive-ui'
const { dialog } = createDiscreteApi(['dialog'])
const message = useMessage()
const emits = defineEmits(['select'])

// tab 切换区域 start
const status = ref(0)
const tabList = ref<any[]>([
  {
    label: '禁用',
    id: 1,
    value: 2
  },
  {
    label: '启用',
    id: 2,
    value: 1
  },
  {
    label: '全部',
    id: 3,
    value: 0
  }
])

function filterData(val: any) {
  getForecastTemp()
}

// tab 切换区域 end

// 新建 start
const dialogVisible = ref<boolean>(false)
const areaList = ref<any[]>([])
let loading = ref<boolean>(false)

const form = reactive({
  name: '',
  status: true,
  forecastType: '',
  sort: 1
})

const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入模板名称',
      trigger: 'blur'
    }
  ],
  forecastType: [
    {
      required: true,
      message: '请选择关联区域',
      trigger: 'change'
    }
  ]
})

// 预报单新建
function onSave() {
  Api.saveForecastTemp(form)
    .then(res => {
      message.success('操作成功')
      dialogVisible.value = false
      getForecastTemp()
    })
    .catch((e: any) => {
      let { msg = '' } = e?.response?.data || {}
      message.error(msg || '操作失败')
    })
}
//重命名预报单模板
const reNameVisible = ref(false)
const curRightData = ref<any>({})
function reNameHandler(option: any) {
  curRightData.value = option
  reNameVisible.value = true
}
const treeRef = ref()
function reName() {
  Api.reNameForecastTemp(curRightData.value)
    .then(() => {
      getForecastTemp()
      reNameVisible.value = false
      message.success('重命名成功')
      treeRef.value.hiddenRightMenu()
    })
    .catch((e: any) => {
      let { msg = '' } = e?.response?.data || {}
      message.error(msg || '操作失败')
    })
}
function deleteNode(option: any) {
  curRightData.value = option
  dialog.warning({
    title: '提示',
    content: `是否删除${option.name}模板`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      Api.deleteForecastTemp(option)
        .then(() => {
          message.success('删除成功')
          getForecastTemp()
          treeRef.value.hiddenRightMenu()
        })
        .catch((e: any) => {
          let { msg = '' } = e?.response?.data || {}
          message.error(msg || '操作失败')
        })
    },
    onNegativeClick: () => {
      treeRef.value.hiddenRightMenu()
    }
  })
}
// 获取区域列表
function getAreaList() {
  loading.value = true
  const params = {
    name: ''
  }
  Api.getAreaList(params)
    .then((res: any) => {
      areaList.value = res
    })
    .finally(() => {
      loading.value = false
    })
    .catch((e: any) => {
      let { msg } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
    })
}
// 新建 end

// tree start
const isSwitch = ref<boolean>(true)
const treeData = ref([])
const defaultSelectedKeys = ref<any[]>([])

const defaultProps = ref({
  key: 'id',
  label: 'name',
  children: 'forecastTemplateList'
})

function changeSwitch(val: boolean, option: TreeOption) {
  Api.updateForecastTempStatus(option.id as string, val)
    .then(() => {
      message.success('操作成功')
    })
    .catch((e: any) => {
      let { msg = '' } = e?.response?.data || {}
      message.error(msg || '操作失败')
    })
}

// tree 选中触发
function selectHandler(val: string, option: any) {
  const parents = findParent(treeData.value, option.id, 'forecastTemplateList')
  console.log(parents, 'parents')
  if (parents) {
    option.parentSort = parents?.sort
  }
  const forecastType = option?.forecastType
  if (forecastType) {
    Api.getArea({ areaTypeCode: forecastType })
      .then((res: any) => {
        emits('select', res, option)
      })
      .catch((e: any) => {
        let { msg = '' } = e?.response?.data || {}
        message.error(msg || '获取数据失败')
      })
  } else {
    message.warning('请选择子级进行切换')
  }
}

// 格式化状态 接口参数 全部 = null,启用=true,禁用=false
function getStatus() {
  if (status.value === 0) {
    return { status: null }
  } else if (status.value === 1) {
    return { status: true }
  } else {
    return { status: false }
  }
}
function getForecastTemp() {
  const params = getStatus()
  Api.getForecastTemp(params)
    .then((res: any) => {
      if (res && res.length) {
        res.forEach((item: any, index: number) => {
          item.id = index + 1
        })
        treeData.value = res
        const result = getFirstLeafNode(res, 'forecastTemplateList')
        selectHandler('', result)
        defaultSelectedKeys.value.push(result?.id as string)
      }
    })
    .catch((e: any) => {
      let { msg = '' } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
    })
}

function findParent(tree: any[], id: string, children = 'children') {
  for (let node of tree) {
    if (node.id === id) {
      return node // 如果找到了，返回 null 表示没有父级
    }
    if (node[children] && node[children].length) {
      const parent: any = findParent(node[children], id, children)
      if (parent !== null) {
        return node // 找到了父级节点
      }
    }
  }
  return null // 没有找到
}
// tree end

onMounted(() => {
  getForecastTemp()
  getAreaList()
})
</script>

<style lang="scss">
.forecast-temp-aside {
  width: 360px;
  height: 100%;
  margin-right: 20px;
  background: #fff;
  .aside-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .qx-select {
    padding-bottom: 13px;
  }
  .n-base-selection {
    min-height: auto;
    line-height: 1;
    .n-base-selection-label {
      background-color: transparent;
      height: auto;
    }
  }
  .qx-tree-wrap {
    overflow-y: auto;
  }
}
.create-forecast-temp-dialog {
  .form-container {
    box-sizing: border-box;
    padding: 14px 27px 0;
  }
  .forecast-temp-form {
    padding: 0 !important;
  }
  .btns {
    box-sizing: border-box;
    padding: 10px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
</style>
