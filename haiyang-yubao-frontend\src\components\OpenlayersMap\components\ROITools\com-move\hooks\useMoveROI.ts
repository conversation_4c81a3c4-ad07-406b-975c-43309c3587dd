import { useROIInject } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIInject'
import VectorSource from 'ol/source/Vector'
import { onMounted, onUnmounted, Ref } from 'vue'
import { roiToolBus } from 'src/components/OpenlayersMap/components/ROITools/hooks/types'
import eventBus from 'src/utils/eventBus'
import Translate, { TranslateEvent } from 'ol/interaction/Translate'

import VectorLayer from 'ol/layer/Vector'
import { Polygon, SimpleGeometry } from 'ol/geom'
import { Select } from 'ol/interaction.js'
import {
  ActionType,
  IROIOptions
} from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIOptions'
import {
  roiStyle,
  selectStyle
} from 'src/components/OpenlayersMap/components/ROITools/hooks/useVector'
import { getLandPoint } from 'src/components/OpenlayersMap/components/ROITools/com-draw/hooks/useDrawROI'

/**
 * 移动要素 hook
 */
export function useMoveROI() {
  const vectorSource = useROIInject('vectorSource').injectROI()
  const vectorLayer = useROIInject('vectorLayer').injectROI()
  const currentAction = useROIInject('currentAction').injectROI()
  const roiOptions = useROIInject('roiOptions').injectROI()
  const intersectPointLayer = useROIInject('intersectPointLayer').injectROI()
  const getMap = useROIInject('getMap').injectROI()
  // const vectorSource = useROIInject('vectorSource').injectROI()

  if (
    !vectorSource ||
    !getMap ||
    !vectorLayer ||
    !currentAction ||
    !roiOptions ||
    !intersectPointLayer
  ) {
    throw new Error('注入失败')
  }

  const { select, translate, translateVecSource, translateVecLayer } =
    createTranslate({
      vectorSource,
      roiOptions
    })

  onMounted(() => {
    vectorLayer.setVisible(false)
    intersectPointLayer.setVisible(false)

    eventBus.on(roiToolBus.saveModify, () => {
      vectorSource.clear()
      vectorSource.addFeatures(translateVecSource.getFeatures())
      currentAction.value = ActionType.NONE
      getLandPoint({ intersectPointLayer, vectorSource })
    })
    getMap(map => {
      map.addLayer(translateVecLayer)
      map.addInteraction(select)
      map.addInteraction(translate)
    })
  })

  onUnmounted(() => {
    eventBus.off(roiToolBus.saveModify)
    vectorLayer.setVisible(true)
    intersectPointLayer.setVisible(true)
    getMap(map => {
      map.removeLayer(translateVecLayer)
      map.removeInteraction(select)
      map.removeInteraction(translate)
    })
  })
}

/**
 * 创建移动交互逻辑
 */
function createTranslate(opt: {
  vectorSource: VectorSource<SimpleGeometry>
  roiOptions: Ref<IROIOptions>
}) {
  const translateVecSource = new VectorSource<SimpleGeometry>({
    useSpatialIndex: false
  })
  const translateVecLayer = new VectorLayer({
    source: translateVecSource,
    style: roiStyle({ ...opt })
  })
  translateVecSource.addFeatures(
    opt.vectorSource.getFeatures().map(feature => feature.clone())
  )

  const select = new Select({
    layers: [translateVecLayer],
    style: selectStyle
  })

  const translate = new Translate({
    features: select.getFeatures()
  })

  translate.on('translateend', (e: TranslateEvent) => {
    const dx = e.coordinate[0] - e.startCoordinate[0]
    const dy = e.coordinate[1] - e.startCoordinate[1]
    e.features.forEach(feature => {
      const geometry = feature.getGeometry()
      if (geometry instanceof Polygon) {
        const geomProp = geometry.getProperties()
        const $originPoints: number[][][] = geomProp.$originPoints
        $originPoints[0].map(item => {
          item[0] += dx
          item[1] += dy
        })
        geometry.setProperties({ $originPoints })
      }
    })
  })

  return {
    translate,
    select,
    translateVecSource,
    translateVecLayer
  }
}
