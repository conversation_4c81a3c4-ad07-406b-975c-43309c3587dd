package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.processor.WaveGenerateText;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.data.making.server.dto.SeaWaveAlarmDTO;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;

/**
 * 海浪警报制作表信息(SeaWaveAlarm)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:18
 */
public interface SeaWaveAlarmService extends IService<SeaWaveAlarm> {
    PageResult pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime);

    GenerateTextVO generateText( WaveGenerateText waveAlarmGenerateText);

    SeaWaveAlarm saveInfo(SeaWaveAlarmDTO seaWaveAlarmDTO);

    Boolean checkNumber(String number);

    SeaWaveAlarm selectByNumber(String number);

    void release(Long id);

    SeaWaveAlarm updateInfo(SeaWaveAlarmDTO seaWaveAlarmDTO);

    void updateDisplay(String lastNumber);

    Map<Long, Long> statistic(Date startTime, Date endTime);

    void downloadDoc(HttpServletResponse response, Long id);

    boolean updateByIdObj(SeaWaveAlarm seaWaveAlarm);
}

