<template>
  <div class="content alarm-edit">
    <div class="content-header">
      <h3>模板编辑</h3>
      <div class="btns">
        <qx-button class="qx-button" @click="isEdit = true">编辑</qx-button>
        <qx-button class="primary" @click="onSave">保存</qx-button>
      </div>
    </div>
    <AlarmForm :isEdit="isEdit" ref="alarmRef" :info="info">
      <template #suffix>
        <n-form-item label="模板内容" path="templateContent">
          <n-input
            v-model:value="form.templateContent"
            type="textarea"
            :rows="10"
            :readonly="!isEdit"
          />
        </n-form-item>
      </template>
    </AlarmForm>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { QxButton } from 'src/components/QxButton'
import { AlarmForm } from './index'
import Api from 'src/requests/forecast'
import { useMessage } from 'naive-ui'
import eventBus from 'src/utils/eventBus'

const message = useMessage()
const alarmRef = ref<any>(null)
let form = reactive({ templateContent: '' })

let isEdit = ref(false)
const props = defineProps({
  info: {
    type: Object,
    default: () => ({})
  }
})
function onSave() {
  alarmRef.value?.form.validate((errors: boolean) => {
    if (!errors) {
      let { alarmForm = {} } = alarmRef.value
      let params = JSON.parse(JSON.stringify(alarmForm))
      params.templateContent = form.templateContent
      params.id = props.info.id

      Api.updateAlarmProduct(params)
        .then(res => {
          message.success('操作成功')
          eventBus.emit('refresh')
        })
        .catch(e => {
          let { msg } = e?.response?.data
          message.error(msg || '操作失败')
        })
    }
  })
}

watch(
  () => props.info,
  val => {
    if (val) {
      form.templateContent = val.templateContent
    }
  },
  { immediate: true }
)
</script>

<style lang="scss">
.alarm-edit {
  .form-container {
    box-sizing: border-box;
    padding: 17px 251px;
  }
}
</style>
