package cn.piesat.data.making.server;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.dto.generate.SeaWaveAlarmResultDTO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.service.StationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jogamp.common.util.ArrayHashSet;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.geotools.data.*;
import org.geotools.data.shapefile.ShapefileDataStoreFactory;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.geotools.geojson.GeoJSON;
import org.geotools.geojson.GeoJSONUtil;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.MultiLineString;
import org.locationtech.jts.geom.Point;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;
import org.opengis.feature.type.AttributeDescriptor;
import org.opengis.feature.type.Name;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.io.StringReader;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.util.*;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
//@SpringBootTest
public class DataMakingServerApplicationTests {

    @Autowired
    private StationService stationService;

    @Test
    public void test(){
       /* FileDataStore dataStore = null;
        try {
            // 1. 指定Shapefile路径
            File shpFile = new File("E:\\项目相关\\海南海灾\\业务产品文档\\潮位核定\\20250310矢量\\岸段核定结果20241209.shp");
            System.out.println("文件是否存在：" + shpFile.exists()); // 调试文件路径
            // 2. 创建DataStore并连接文件
            dataStore = FileDataStoreFinder.getDataStore(shpFile);
            dataStore.set

            // 3. 获取要素源（FeatureSource）
            SimpleFeatureSource featureSource = dataStore.getFeatureSource();

            // 4. 获取要素集合
            SimpleFeatureCollection features = featureSource.getFeatures();

            // 5. 遍历要素
            try (SimpleFeatureIterator iterator = features.features()) {
                while (iterator.hasNext()) {
                    SimpleFeature feature = iterator.next();

                    // 打印属性（假设Shapefile有一个名为"name"的字段）
                    String name = (String) feature.getAttribute("name");
                    System.out.println("Name: " + name);

                    // 获取几何对象（如多边形、点等）
                    Geometry geometry = (Geometry) feature.getDefaultGeometry();
                    System.out.println("Geometry: " + geometry);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 6. 关闭DataStore释放资源
            if (dataStore != null) {
                dataStore.dispose();
            }
        }*/
        try {
            // 1. 定义Shapefile路径
            File shpFile = new File("E:\\项目相关\\海南海灾\\业务产品文档\\潮位核定\\20250310矢量\\50个岸段_警戒潮位核定20241123_2000.shp");
            URL shpUrl = shpFile.toURI().toURL();

            // 2. 创建参数Map，指定字符集为GBK
            Map<String, Serializable> params = new HashMap<>();
            params.put(ShapefileDataStoreFactory.URLP.key, shpUrl);
            params.put(ShapefileDataStoreFactory.DBFCHARSET.key, "UTF-8"); // 设置.dbf文件编码

            // 3. 创建 DataStore
            ShapefileDataStoreFactory factory = new ShapefileDataStoreFactory();
            DataStore dataStore = factory.createDataStore(params);

            // 4. 获取要素类型名称（Shapefile 只有一个类型）
            Name typeName = dataStore.getNames().get(0); // 或 dataStore.getSchema().getName()

            // 5. 获取 FeatureSource
            SimpleFeatureSource featureSource = dataStore.getFeatureSource(typeName);
            // 5. 获取所有字段描述符
            List<AttributeDescriptor> attributes = featureSource.getSchema().getAttributeDescriptors();
            // 4. 遍历要素并读取属性
            HashMap<Long, HashMap<String, Object>> map = new HashMap<>();
            //查询所有未匹配海洋站
            LambdaQueryWrapper<Station> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Station::getStationTypeCode,"oceanStation");
            wrapper.isNull(Station::getDatumBlueWarn);
            List<Station> list = stationService.list(wrapper);

            try (SimpleFeatureIterator iterator = featureSource.getFeatures().features()) {
                while (iterator.hasNext()) {
                    SimpleFeature feature = iterator.next();

                    String regionCode = (String)feature.getAttribute("行政地");
                    Integer blue = (Integer)feature.getAttribute("蓝色");
                    Integer yellow = (Integer)feature.getAttribute("黄色");
                    Integer orange = (Integer)feature.getAttribute("橙色");
                    Integer red = (Integer)feature.getAttribute("红色");
                    MultiLineString multiLine = (MultiLineString)feature.getAttribute("the_geom");
                    // 1. 创建缓冲区（容差设为 0.0001 度，约 10 米）
                    Geometry buffer = multiLine.buffer(0.05);

                    if(!CollectionUtils.isEmpty(list)){
                        list.forEach(station->{

                            try {
                                String locationJson = station.getLocationJson();
                                locationJson= locationJson.replace("\"properties\":null", "\"properties\":{}");
                                FeatureJSON json = new FeatureJSON();
                                SimpleFeature simpleFeature = json.readFeature(new StringReader(locationJson));
                                // 处理每个要素
                                Point point = (Point)simpleFeature.getDefaultGeometry();
                                if(buffer.covers(point)){
                                    HashMap<String, Object> datum = map.get(station.getId());
                                    double distance = multiLine.distance(point);
                                    if(Objects.isNull(datum)){
                                        datum = new HashMap<>();
                                        datum.put("dist",distance);
                                        datum.put("blue",blue);
                                        datum.put("yellow",yellow);
                                        datum.put("orange",orange);
                                        datum.put("red",red);
                                        map.put(station.getId(),datum);
                                    }else {
                                        Double dist = (Double)datum.get("dist");
                                        System.out.println("old:" + dist);
                                        System.out.println("new:" + distance);
                                        if(distance < dist){
                                            datum.put("dist",distance);
                                            datum.put("blue",blue);
                                            datum.put("yellow",yellow);
                                            datum.put("orange",orange);
                                            datum.put("red",red);
                                            map.put(station.getId(),datum);
                                        }
                                    }

                                }

                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        });
                    }

                    // 打印所有属性
                    /*for (AttributeDescriptor attr : attributes) {
                        String fieldName = attr.getLocalName();
                        Object value = feature.getAttribute(fieldName);

                        // 特殊处理几何字段（如Geometry）
                        if (value instanceof Geometry) {
                            Geometry geometry = (Geometry) value;
                            System.out.println(fieldName + " (几何类型): " + geometry.getGeometryType());
                            System.out.println(fieldName + " (WKT): " + geometry.toText());
                        } else {
                            System.out.println(fieldName + ": " + value);
                        }
                    }*/

                }

                list.forEach(station -> {
                    HashMap<String, Object> datum = map.get(station.getId());
                    station.setLocationGeo(station.getLocationJson());
                    if(!Objects.isNull(datum)){
                        Integer blue = (Integer)datum.get("blue");
                        Integer yellow =(Integer)datum.get("yellow");
                        Integer orange =(Integer)datum.get("orange");
                        Integer red = (Integer)datum.get("red");
                        station.setDatumYellowWarn(yellow);
                        station.setDatumRedWarn(red);
                        station.setDatumOrangeWarn(orange);
                        station.setDatumBlueWarn(blue);
                    }
                });
                stationService.updateBatchById(list);
                System.out.println(JsonUtil.object2Json(map));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args){
        try {
            Map<String,Object> param = new HashMap<>();
            param.put("time", DateUtil.format(new Date(),"yyyy年MM月dd日HH时"));
            param.put("sourceType",2);
            param.put("forecastStartTime",DateUtil.format(new Date(),"dd日HH时"));
            param.put("forecastEndTime",DateUtil.format(new Date(),"dd日HH时"));
            param.put("typhoonName","111111");
            param.put("areaList",new ArrayList<>());
            param.put("regionList",new ArrayList<>());
            param.put("levelColor","蓝色");
            param.put("levelRoman","VVV");

            //生成内容文字
            StringTemplateLoader stringTemplateLoader = new StringTemplateLoader();
            stringTemplateLoader.putTemplate("alarm_content.ftl","受台风“${typhoonName}”影响，预计${forecastStartTime}到${forecastEndTime}：<#if areaList?? && (areaList?size > 0)><#list areaList as area>${area.name}附近海面有${area.min?string[\"0.##\"]}~${area.max?string[\"0.##\"]}米的<#if area.minZh == area.maxZh>${area.maxZh}浪<#else>${area.minZh}到${area.maxZh}浪。</#if></#list></#if><#if regionList?? && (regionList?size>0)><#list regionList as region>${region.name}近岸海域将出现${region.min?string[\"0.##\"]}~${region.max?string[\"0.##\"]}米的<#if region.minZh == region.maxZh>${region.maxZh}浪<#else>${region.minZh}到${region.maxZh}浪<#if region_has_next>、<#else>，</#if></#if></#list></#if>近岸海域海浪预警级别为${levelColor}。");
            stringTemplateLoader.putTemplate("alarm_sms.ftl","海南省海洋预报台${time}发布海浪${levelColor}（${levelRoman}）预警：受台风“${typhoonName}”影响，预计${forecastStartTime}到${forecastEndTime}：<#if areaList?? && (areaList?size > 0)><#list areaList as area>${area.name}附近海面有${area.min?string[\"0.##\"]}~${area.max?string[\"0.##\"]}米的<#if area.minZh == area.maxZh>${area.maxZh}浪<#else>${area.minZh}到${area.maxZh}浪。</#if></#list></#if><#if regionList?? && (regionList?size>0)><#list regionList as region>${region.name}近岸海域将出现${region.min?string[\"0.##\"]}~${region.max?string[\"0.##\"]}米的<#if region.minZh == region.maxZh>${region.maxZh}浪<#else>${region.minZh}到${region.maxZh}浪<#if region_has_next>、<#else>，</#if></#if></#list></#if>近岸海域海浪预警级别为${levelColor}。请上述海域作业船只提前做好防浪避浪工作，并关注我台的后续预报。");

            Configuration configuration = new Configuration(Configuration.getVersion());
            configuration.setTemplateLoader(stringTemplateLoader);
            Template alarmContentTemplate = configuration.getTemplate("alarm_content.ftl");
            Template alarmSmsTemplate = configuration.getTemplate("alarm_sms.ftl");
            String alarmSms = FreeMarkerTemplateUtils.processTemplateIntoString(alarmSmsTemplate, param);
            String alarmContent = FreeMarkerTemplateUtils.processTemplateIntoString(alarmContentTemplate, param);
            System.out.println(alarmContent);
            System.out.println(alarmSms);
        } catch (IOException | TemplateException e) {
            e.printStackTrace();
        }
    }

}
