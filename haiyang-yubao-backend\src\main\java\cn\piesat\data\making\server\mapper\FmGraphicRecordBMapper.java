package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.FmGraphicRecordBDTO;
import cn.piesat.data.making.server.entity.FmGraphicRecordB;
import cn.piesat.data.making.server.vo.FmGraphicRecordBVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 图形记录表Mapper类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:39
 */
@Mapper(componentModel = "spring")
public interface FmGraphicRecordBMapper {

    FmGraphicRecordBMapper INSTANCE = Mappers.getMapper(FmGraphicRecordBMapper.class);

    /**
     * entity-->vo
     */
    FmGraphicRecordBVO entityToVo(FmGraphicRecordB entity);

    /**
     * dto-->entity
     */
    FmGraphicRecordB dtoToEntity(FmGraphicRecordBDTO dto);

    /**
     * entityList-->voList
     */
    List<FmGraphicRecordBVO> entityListToVoList(List<FmGraphicRecordB> list);

    /**
     * dtoList-->entityList
     */
    List<FmGraphicRecordB> dtoListToEntityList(List<FmGraphicRecordBDTO> dtoList);
}
