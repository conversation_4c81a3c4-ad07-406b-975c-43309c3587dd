package cn.piesat.data.making.server.dto;


import java.io.Serializable;
import java.util.Date;

/**
 * 图形记录表DTO类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:36
 */
public class FmGraphicRecordBDTO implements Serializable {

    private static final long serialVersionUID = 277580051130553875L;

    public interface Save {
    }

    /**
     * id
     */
    private Long id;
    /**
     * 文件路径彩色
     */
    private String fileUrlColorful;
    /**
     * 名称
     */
    private String name;
    /**
     * 预报任务id
     */
    private Long forecastTaskId;
    /**
     * 图形模板id
     */
    private Long graphicTemplateId;
    /**
     * 文件路径
     */
    private String fileUrl;
    /**
     * 是否选中
     */
    private Boolean checked;
    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 数据源
     */
    private String dataSource;
    /**
     * 起报时间
     */
    private Date forecastStartTime;
    /**
     * 图形json
     */
    private String graphicJson;

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Date getForecastStartTime() {
        return forecastStartTime;
    }

    public void setForecastStartTime(Date forecastStartTime) {
        this.forecastStartTime = forecastStartTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getForecastTaskId() {
        return forecastTaskId;
    }

    public void setForecastTaskId(Long forecastTaskId) {
        this.forecastTaskId = forecastTaskId;
    }

    public Long getGraphicTemplateId() {
        return graphicTemplateId;
    }

    public void setGraphicTemplateId(Long graphicTemplateId) {
        this.graphicTemplateId = graphicTemplateId;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getFileUrlColorful() {
        return fileUrlColorful;
    }

    public void setFileUrlColorful(String fileUrlColorful) {
        this.fileUrlColorful = fileUrlColorful;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getGraphicJson() {
        return graphicJson;
    }

    public void setGraphicJson(String graphicJson) {
        this.graphicJson = graphicJson;
    }
}
