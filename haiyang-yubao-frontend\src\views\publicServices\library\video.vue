<template>
  <div class="library-common file-product">
    <div class="content">
      <div class="content-header">
        <h3>产品清单</h3>
      </div>
      <div class="content-filter">
        <n-form
          ref="formRef"
          :model="query"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
        >
          <n-grid :cols="24" :x-gap="12" :y-gap="14">
            <n-form-item-gi :span="5" label="产品名称：" path="title">
              <n-input
                v-model:value="query.title"
                clearable
                placeholder="请输入"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="5" label="上传时间：" path="unit">
              <n-date-picker
                v-model:formatted-value="query.uploadTime"
                type="datetime"
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="9" label="产品类别：" path="unit">
              <n-checkbox
                v-model:checked="checkedAll"
                @update:checked="handleCheckedAll"
                >全部</n-checkbox
              >
              <n-checkbox-group
                v-model:value="query.categoryId"
                class="qx-checkbox-group"
              >
                <template v-for="item in categoryList" :key="item.value">
                  <n-checkbox
                    class="qx-checkbox"
                    :value="item.value"
                    :label="item.label"
                  />
                </template>
              </n-checkbox-group>
            </n-form-item-gi>
            <n-form-item-gi :span="5" class="custom-form">
              <qx-button>重置</qx-button>
              <qx-button class="primary">查询</qx-button>
              <qx-button class="primary" @click="onUpload">上传</qx-button>
              <qx-button class="warning">批量下载</qx-button>
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </div>
    </div>
    <div class="table-container">
      <n-data-table
        v-loading="loading"
        :row-key="rowKey"
        class="qx-table"
        :data="tableData"
        :columns="columns"
        :max-height="550"
      ></n-data-table>
    </div>
  </div>
  <UploadDialog
    :productOptions="categoryList"
    ref="uploadDialog"
    :visible="dialogVisible"
    @close="dialogVisible = false"
    type="video"
  />
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { ref, reactive, h } from 'vue'
import { DataTableColumn } from 'naive-ui'
import './library.scss'
import { UploadDialog } from './index'

interface Query {
  title: string
  uploadTime: string | [string, string] | null
  categoryId: number[]
}

const query = reactive<Query>({
  title: '',
  uploadTime: null,
  categoryId: []
})

let page = ref(1)
let checkedAll = ref(false)
let categoryList = ref([
  {
    label: '科普视频',
    value: 1
  },
  {
    label: '新闻视频',
    value: 2
  }
])

function handleCheckedAll(val: boolean) {
  if (val) {
    let categoryIds: number[] = categoryList.value.map(item => item.value)
    query.categoryId = categoryIds
  } else {
    query.categoryId = []
  }
}

interface RowData {
  name: string
  type: number
  fileType: string
  uploadTime: string
}
const rowKey = (row: any) => row.id
const loading = ref(false)
const tableData = ref<any[]>([
  {
    id: 1,
    name: '2018年中国海洋灾害公报',
    type: 1,
    fileType: 'pdf',
    uploadTime: '2023-01-01 10:00:00'
  }
])
const columns = ref<DataTableColumn<RowData>[]>([
  {
    type: 'selection'
  },
  {
    title: '序号',
    key: 'index',
    render(row: any, index: number) {
      return index + 1
    }
  },
  {
    title: '产品名称',
    key: 'name'
  },
  {
    title: '产品类别',
    key: 'type',
    render(row: any) {
      return getCategoryName(row.type)
    }
  },
  {
    title: '产品格式',
    key: 'fileType'
  },
  {
    title: '上传时间',
    key: 'uploadTime'
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row: any) {
      return [
        h(
          'span',
          {
            size: 'small',
            class: 'operate-btn',
            style: { cursor: 'pointer' },
            onClick: () => onPreview(row)
          },
          { default: () => '预览' }
        ),
        h(
          'span',
          {
            size: 'small',
            class: 'operate-btn del',
            style: { cursor: 'pointer' },
            onClick: () => onDownLoad(row)
          },
          { default: () => '下载' }
        )
      ]
    }
  }
])
function getCategoryName(type: number) {
  let category = categoryList.value.find(item => item.value === type)
  return category?.label
}
function onPreview(row: RowData) {}
function onDownLoad(row: RowData) {}

let dialogVisible = ref(false)
function onUpload() {
  dialogVisible.value = true
}
</script>

