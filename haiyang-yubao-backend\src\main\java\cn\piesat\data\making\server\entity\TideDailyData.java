package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
@Data
@TableName("fm_tide_daily")
public class TideDailyData {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Date tideTime;

    private int height;

    private int type;

    private String stationName;

    private Long stationId;

    private int level;

    private int warnHeight;

    private Date createTime;

    private Date updateTime;
    /**
     * 基准
     */
    private String datum;
}
