package cn.piesat.data.making.server.dto;


import java.io.Serializable;
import java.util.Date;

/**
 * DTO类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:05
 */
public class FmSchedulingTableDTO implements Serializable {

    private static final long serialVersionUID = 452321185724074446L;

    public interface Save {
    }

    private Long id;
    private Date schedulingDate;
    private String userName;
    private Long userId;
    private Integer day;
    private Long schedulingMainId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getSchedulingDate() {
        return schedulingDate;
    }

    public void setSchedulingDate(Date schedulingDate) {
        this.schedulingDate = schedulingDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Long getSchedulingMainId() {
        return schedulingMainId;
    }

    public void setSchedulingMainId(Long schedulingMainId) {
        this.schedulingMainId = schedulingMainId;
    }
}
