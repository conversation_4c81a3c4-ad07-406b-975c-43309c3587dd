package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmGraphicTemplateMainDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateMain;
import cn.piesat.data.making.server.vo.FmGraphicTemplateMainVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-13 09:43:28
 */
public interface FmGraphicTemplateMainService extends IService<FmGraphicTemplateMain> {
    List<FmGraphicTemplateMainVO> getList(FmGraphicTemplateMainDTO dto);
}
