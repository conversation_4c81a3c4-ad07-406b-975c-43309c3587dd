import { computed, ref } from 'vue'
import type { RowData } from './useContentTable'
import { getBoatAdviceLevelByHeight } from 'src/config/waveLevel'

export function useParagraph(opt: {
  areaGetter: () => { name: string; index: number }[]
  contentArr: () => RowData[]
}) {
  const paragraph = ref('')

  const getAreaName = (index: number) => {
    const areaList = opt.areaGetter()
    const find = areaList.find(i => i.index === index)
    return find?.name
  }

  const getWords = (areaNameList: string[], swhInterval: string, langjiInterval: string) => {
    return `${areaNameList.join('、')}附近海面有${swhInterval}米的${langjiInterval}。`
  }

  const cptText = computed(() => {
    const textArr: string[] = []

    const groupData = opt.contentArr().reduce((map, item) => {
      const key = getBoatAdviceLevelByHeight(item.swhInterval)
      const group = map.get(key) || []
      group.push(item)
      map.set(key, group)
      return map
    }, new Map<string, RowData[]>())

    for (const [key, value] of groupData) {
      value.forEach(item => {
        const areaList: string[] = []
        const indexArr = item.index.split(',').map(i => Number(i))
        indexArr.forEach(i => {
          areaList.push(getAreaName(i) || '-')
        })
        const words = getWords(areaList, item.swhInterval, item.langjiInterval)
        textArr.push(words)
      })
      textArr.push(key)
    }

    return textArr.join('')
  })

  return {
    paragraph,
    cptText,
    updateParagraph() {
      paragraph.value = cptText.value
    },
    setParagraph(text: string) {
      paragraph.value = text
    }
  }
}
