<template>
  <div class="library-common img-product">
    <div class="content">
      <div class="content-header">
        <h3>产品清单</h3>
      </div>
      <div class="content-filter">
        <n-form
          ref="formRef"
          :model="query"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
        >
          <n-grid :cols="24" :x-gap="12" :y-gap="14">
            <!-- <n-form-item-gi :span="5" label="产品名称：" path="title">
              <n-input
                v-model:value="query.title"
                clearable
                placeholder="请输入"
              />
            </n-form-item-gi> -->
            <n-form-item-gi :span="8" label="上传时间：" path="unit">
              <n-date-picker
                v-model:formatted-value="query.uploadTime"
                type="datetimerange"
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="9" label="产品类别：" path="unit">
              <n-radio-group
                v-model:value="query.categoryId"
                class="qx-checkbox-group"
                @update:value="val => onChangeProduct(val, 'change')"
              >
                <template v-for="item in categoryList" :key="item.value">
                  <n-radio
                    class="qx-checkbox"
                    :value="item.value"
                    :label="item.label"
                  />
                </template>
              </n-radio-group>
              <template v-if="['europe', 'korea'].includes(query.categoryId)">
                <n-select
                  v-model:value="query.productId"
                  :options="productOptions"
                  clearable
                  placeholder="请选择"
                  style="width: 50%"
                ></n-select>
              </template>
            </n-form-item-gi>
            <n-form-item-gi :span="5" class="custom-form">
              <qx-button>重置</qx-button>
              <qx-button class="primary" @click="onSearch">查询</qx-button>
              <qx-button class="primary" @click="onUpload">上传</qx-button>
              <qx-button class="warning" @click="onDownload"
                >批量下载</qx-button
              >
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </div>
    </div>
    <div class="page-container">
      <div class="page-list">
        <div v-for="item in pageList" :key="item.id" class="page-list-item">
          <div class="page-list-item__img" title="点击查看大图">
            <n-image :src="item.file_path" object-fit="cover" alt="" />
          </div>
          <p>{{ item.file_name }}</p>
        </div>
      </div>
      <n-pagination
        v-model:page="query.pageNum"
        class="qx-pagination"
        :page-count="pageCount"
        @update:page="onPageChange"
      />
    </div>
  </div>
  <UploadDialog
    ref="uploadDialog"
    :product-options="categoryList"
    :visible="dialogVisible"
    type="img"
    @close="dialogVisible = false"
  />
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { ref, reactive, onMounted } from 'vue'
import './library.scss'
import { UploadDialog } from './index'
import { Query, CategoryList } from './types'
import Api from 'src/requests/product'
import { useMessage } from 'naive-ui'
import moment from 'moment'
const message = useMessage()
const query = reactive<Query>({
  title: '',
  uploadTime: [
    moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  ],
  categoryId: 'korea',
  productId: 'P_CURRENT_L4_GIF',
  pageNum: 1,
  pageSize: 10
})

let categoryList = ref<CategoryList[]>([
  {
    label: '国内预报',
    value: 'domestic'
  },
  {
    label: '韩国',
    value: 'korea',
    options: [
      {
        label: '海洋预计天气图海洋循环海流（表层）',
        value: 'P_CURRENT_L4_GIF'
      },
      {
        label: '海风和海浪-亚洲',
        value: 'P_WAVE_L4_GIF'
      },
      {
        label: '最大波浪周期和平均波浪-亚洲',
        value: 'P_WDPR_L4_GIF'
      }
    ]
  },
  {
    label: '欧洲',
    value: 'europe',
    options: [
      {
        label: '有效波高和波向',
        value: 'P_SWH-_L4_WEBP'
      },
      {
        label: '海风和海浪-亚洲',
        value: 'P_TSSH_L4_GIF'
      }
    ]
  }
])

let productOptions = ref<any[]>([])

function onChangeProduct(val: string, type: string) {
  query.productId = ''
  let result = categoryList.value.find((item: any) => item.value === val)
  productOptions.value = result?.options || []
  if (type === 'init') {
    query.productId = productOptions.value[0]?.value
  }
}

function onDownload() {
  console.log('download')
}

let dialogVisible = ref(false)
function onUpload() {
  dialogVisible.value = true
}

let pageList = ref<any[]>([])
let pageCount = ref(0)
// 获取产品列表
function getPageList() {
  pageList.value = []
  pageCount.value = 0
  const params = {
    beginTime: '',
    endTime: '',
    pageSize: 10,
    pageNum: query.pageNum,
    _tz: 'GMT',
    orderBy: 'data_time',
    sort: 'desc'
  }
  if (query.uploadTime?.length) {
    params.beginTime = query.uploadTime[0]
    params.endTime = query.uploadTime[1]
  }

  Api.getProductById(query.productId, params)
    .then((res: any) => {
      if (res) {
        const { pageResult, pages } = res || {}
        pageResult.forEach((item: any) => {
          item.file_path = `${config.fileService}${item.file_path}`
        })
        pageList.value = pageResult
        pageCount.value = pages
      }
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data
      message.error(msg || '获取数据失败')
      console.error(e, 'getProductById===')
    })
}

function onPageChange(page: number) {
  query.pageNum = page
  getPageList()
}

function onSearch() {
  query.pageNum = 1
  getPageList()
}

onMounted(() => {
  // 初始化 默认展示韩国 options第一类数据
  onChangeProduct('korea', 'init')
  getPageList()
})
</script>
<style lang="scss">
.img-product {
  .page-container {
    box-sizing: border-box;
    padding: 0 20px 20px;
    height: calc(100% - 140px);
  }
  .page-list {
    display: flex;
    flex-wrap: wrap;
    height: 90%;
    overflow-y: auto;
  }
  .page-list-item {
    border: 1px solid #c9e2ff;
    width: calc((100% - 40px) / 3);
    height: 260px;
    padding: 15px;
    text-align: center;
    margin-right: 20px;
    margin-bottom: 20px;
    &:nth-child(3n + 3) {
      margin-right: 0;
    }
    p {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      margin-top: 10px;
    }
  }
  .page-list-item__img {
    width: 100%;
    height: 90%;
    .n-image {
      max-height: auto;
      height: 100%;
    }
    img {
      max-height: 100%;
    }
  }
}
</style>
