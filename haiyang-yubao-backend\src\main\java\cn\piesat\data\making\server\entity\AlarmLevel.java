package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 警报等级信息表(AlarmLevel)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:28:09
 */
@TableName("fm_alarm_level_b")
public class AlarmLevel implements Serializable {
    private static final long serialVersionUID = -1;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 等级颜色
     */
    private String levelColor;
    /**
     * 等级罗马数字
     */
    private String levelRoman;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getLevelColor() {
        return levelColor;
    }

    public void setLevelColor(String levelColor) {
        this.levelColor = levelColor;
    }

    public String getLevelRoman() {
        return levelRoman;
    }

    public void setLevelRoman(String levelRoman) {
        this.levelRoman = levelRoman;
    }
}

