import { onMounted, ref } from 'vue'
import ForecastApi from 'src/requests/forecast'
import { DataTableBaseColumn } from 'naive-ui'
import { IForecastAreaItem } from 'src/requests/forecast.type'


export function useAreaTable() {
  const areaColumns = getColumns()
  const areaTableData = ref<IForecastAreaItem[]>([])
  const areaDataLoading = ref(false)

  onMounted(async () => {
    void (await getAreaData())
  })

  async function getAreaData() {
    areaDataLoading.value = true
    try {
      areaTableData.value = await ForecastApi.getArea({ areaTypeCode: 'city' })
      areaTableData.value.sort((a, b) => a.index - b.index)
    } catch (e) {
      console.warn(e)
    } finally {
      areaDataLoading.value = false
    }
  }

  return {
    getAreaData,
    areaColumns,
    areaTableData,
    areaDataLoading
  }
}

export function getColumns(): DataTableBaseColumn<IForecastAreaItem>[] {
  return [
    {
      key: 'index',
      title: '编号'
    },
    {
      key: 'name',
      title: '区域名称'
    }
  ]
}
