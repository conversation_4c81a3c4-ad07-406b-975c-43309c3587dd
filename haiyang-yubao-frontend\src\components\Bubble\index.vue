<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-10-13 15:04:11
 * @LastEditors: xuli<PERSON>e <EMAIL>
 * @LastEditTime: 2024-10-28 17:05:49
 * @FilePath: \hainan-jianzai-web\src\components\Bubble\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="nochart-popup-div">
    <input class="my-input" type="textarea" placeholder="" /> 厘米
  </div>
</template>

<script setup>
import { ref } from 'vue'
const text = ref('')
</script>

<style lang="scss" scoped>
.popup-div {
  width: 400px;
  height: max-content;
  position: relative;
  bottom: 0;
  left: 0;
  z-index: 10;
  background-image: url('@/assets/img/settingBg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 0 auto;
  background: rgba(9, 28, 54, 1);
  border: 1px solid #4cb2ff;
}

.nochart-popup-div {
  width: 150px;
  height: 60px;
  position: relative;
  bottom: 0;
  left: 0;
  z-index: 10;
  margin: 0 auto;
  .my-input {
    width: 90px;
  }
}
</style>
