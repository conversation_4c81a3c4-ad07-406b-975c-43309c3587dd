package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanStationHourWlO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-海浪特征值-原始数据表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanStationHourWlODao extends BaseMapper<OceanStationHourWlO> {

    @Select({"<script>",
            "select * from ocean_station_hour_wl_o where monitoringdate between #{startTime} and #{endTime} " +
                    "and station_num in " +
                    "<foreach collection='stationNumList' item='stationNum' open='(' separator=',' close=')'> " +
                    "#{stationNum}" +
                    "</foreach>",
            "</script>"})
    List<OceanStationHourWlO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    @Select("SELECT MAX(createtime) FROM ocean_station_hour_wl_o")
    LocalDateTime getMaxCreateTime();
}
