<template>
  <div class="label-maintenance d-flex">
    <Aside @select="onSelectHandler" />
    <div class="content">
      <div class="content-header d-flex flex-justify-between flex-align-center">
        <h3>模板编辑</h3>
        <div class="btns">
          <qx-button class="primary" @click="createVisible = true">
            新建
          </qx-button>
        </div>
      </div>
      <div class="main-container">
        <n-data-table
          :bordered="false"
          :single-line="false"
          :columns="columns"
          :data="tableData"
          :pagination="pagination"
        />
      </div>
    </div>
  </div>
  <create-dialog
    v-model:visible="createVisible"
    :info="rowData"
    @save="onSave"
  />
</template>

<script setup lang="ts">
import { Aside, CreateDialog } from './index'
import { ref, reactive, h } from 'vue'
import { QxButton } from 'src/components/QxButton'
import TagApi from 'src/requests/tag'
import { useMessage, createDiscreteApi } from 'naive-ui'
const { dialog } = createDiscreteApi(['dialog'])

const message = useMessage()
let createVisible = ref(false)

const tableData = ref<any>([])
const columns = ref<any[]>([
  {
    title: '序号',
    render(row: any, index: number) {
      return index + 1
    }
  },
  {
    title: '姓名',
    key: 'name'
  },
  {
    title: '联系电话',
    key: 'phone'
  },
  {
    title: '电子邮箱',
    key: 'email'
  },
  {
    title: '操作',
    render(row: any) {
      return [
        h(
          'span',
          {
            size: 'small',
            class: 'operate-btn',
            style: { cursor: 'pointer' },
            onClick: () => onEdit(row)
          },
          { default: () => '编辑' }
        ),
        h(
          'span',
          {
            size: 'small',
            class: 'operate-btn',
            style: { cursor: 'pointer' },
            onClick: () => onDel(row)
          },
          { default: () => '删除' }
        )
      ]
    }
  }
])

const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 0,
  updatePage(page: number) {
    pagination.page = page
    getTableData()
  }
})
let tagId = ref('')
function onSelectHandler(options: any) {
  console.log(options, 'options')
  tagId.value = options.id
  getTableData()
}

function getTableData() {
  const params = {
    tagId: tagId.value,
    pageNum: pagination.page,
    pageSize: pagination.pageSize
  }
  TagApi.getUserPage(params)
    .then((res: any) => {
      if (res) {
        const { pageResult, pages } = res || {}
        tableData.value = pageResult
        pagination.pageCount = pages
      }
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
    })
}

let rowData = ref<any>({})
function onSave(val: any) {
  console.log(val, 'va')
  const params = {
    ...val,
    tagId: tagId.value
  }
  if (rowData.value.id) {
    params.id = rowData.value.id
  }
  console.log(params, 'params')
  TagApi.saveUser(params)
    .then(() => {
      message.success('保存成功')
      createVisible.value = false
      getTableData()
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '保存失败')
    })
}

function onEdit(row: any) {
  rowData.value = row
  createVisible.value = true
}
function onDel(row: any) {
  dialog.warning({
    title: '提示',
    content: `是否删除${row.name}`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      TagApi.delUserById(row.id)
        .then((res: any) => {
          message.success('删除成功')
          getTableData()
        })
        .catch((e: any) => {
          let { msg } = e?.response?.data || {}
          message.error(msg || '删除失败')
        })
    }
  })
}
</script>

<style scoped lang="scss">
.label-maintenance {
  height: 100%;
  .content {
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;
    margin-left: 20px;
    .content-header {
      box-sizing: border-box;
      padding: 9px 20px;
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 23px;
      }
    }
  }
  .main-container {
    box-sizing: border-box;
    padding: 20px;
  }
}
</style>
