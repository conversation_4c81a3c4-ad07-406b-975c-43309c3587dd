package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmGraphicDataSourceDTO;
import cn.piesat.data.making.server.vo.FmGraphicDataSourceVO;
import cn.piesat.data.making.server.service.FmGraphicDataSourceService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据源控制层
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:29
 */
@RestController
@RequestMapping("fmGraphicDataSource")
public class FmGraphicDataSourceController {

    @Resource
    private FmGraphicDataSourceService fmGraphicDataSourceService;

    /**
     * 查询列表
     *
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
    public List<FmGraphicDataSourceVO> getList() {
        return fmGraphicDataSourceService.getList();
    }

}
