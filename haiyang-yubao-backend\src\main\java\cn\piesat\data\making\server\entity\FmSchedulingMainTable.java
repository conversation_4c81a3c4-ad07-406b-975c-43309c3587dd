package cn.piesat.data.making.server.entity;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-09-27 16:36:59
 */
@Data
@Accessors(chain = true)
@TableName("fm_scheduling_main_table")
public class FmSchedulingMainTable implements Serializable {

    private static final long serialVersionUID = -60305856463104801L;


    @TableId("id")
    private Long id;
    @TableField("scheduling_date")
    private Date schedulingDate;
    @TableField("create_user")
    private String createUser;
    @TableField("create_time")
    private Date createTime;
    @TableField("file_path")
    private String filePath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getSchedulingDate() {
        return schedulingDate;
    }

    public void setSchedulingDate(Date schedulingDate) {
        this.schedulingDate = schedulingDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
