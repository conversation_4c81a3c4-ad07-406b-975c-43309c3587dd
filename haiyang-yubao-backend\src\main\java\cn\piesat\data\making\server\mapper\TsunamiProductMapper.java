package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.TsunamiProductDTO;
import cn.piesat.data.making.server.entity.TsunamiProduct;
import cn.piesat.data.making.server.vo.TsunamiProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TsunamiProductMapper {

    TsunamiProductMapper INSTANCE = Mappers.getMapper(TsunamiProductMapper.class);

    /**
     * entity-->vo
     */
    TsunamiProductVO entityToVo(TsunamiProduct entity);

    /**
     * dto-->entity
     */
    TsunamiProduct dtoToEntity(TsunamiProductDTO dto);

    /**
     * entityList-->voList
     */
    List<TsunamiProductVO> entityListToVoList(List<TsunamiProduct> list);

    /**
     * dtoList-->entityList
     */
    List<TsunamiProduct> dtoListToEntityList(List<TsunamiProductDTO> dtoList);
}
