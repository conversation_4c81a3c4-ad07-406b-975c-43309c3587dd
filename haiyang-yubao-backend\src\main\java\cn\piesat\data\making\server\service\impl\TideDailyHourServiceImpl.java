package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.TideDailyHourDao;
import cn.piesat.data.making.server.entity.TideDailyHourData;
import cn.piesat.data.making.server.service.TideDailyHourService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TideDailyHourServiceImpl extends ServiceImpl<TideDailyHourDao, TideDailyHourData>
        implements TideDailyHourService {
}