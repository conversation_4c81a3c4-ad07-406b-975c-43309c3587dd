package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.SeaWaveAlarmDTO;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface SeaWaveAlarmMapper {
    SeaWaveAlarmMapper INSTANCE = Mappers.getMapper(SeaWaveAlarmMapper.class);

    SeaWaveAlarm toEntity(SeaWaveAlarmDTO seaWaveAlarmDTO);
}
