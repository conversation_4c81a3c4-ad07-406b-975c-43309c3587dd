import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)

const ProductPublish: any = {
  /**
   * 查询预报产品发布结果分页
   * @param mode
   * @returns
   */
  getForecastResult(params: any) {
    return axiosInstance({
      url: `/forecastProductRecord/releaseResult/page`,
      method: 'GET',
      params
    })
  },

  /**
   * 根据任务id、产品名称、状态查询预报产品列表
   * @param id
   * @returns
   */
  getForecastResultInfo(id: string, params: any) {
    return axiosInstance({
      url: `/forecastProductRecord/releaseResult/${id}`,
      method: 'GET',
      params
    })
  }
}

export default ProductPublish
