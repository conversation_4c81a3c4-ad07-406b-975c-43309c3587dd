<!--
 * @Author: xuli<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-11 11:07:22
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-03-13 18:04:50
 * @FilePath: \hainan-jianzai-web\src\components\QxMenu\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="qx-menu">
    <qx-menu-item :menu-data="props.options" @node-click="handleNodeClick" />
  </div>
</template>
<script lang="ts">
export default {
  inheritAttrs: false
}
</script>

<script setup lang="ts">
import { MenuType } from './types'
import { QxMenuItem } from './index'

const props = defineProps({
  options: {
    type: Array<MenuType>,
    default: () => []
  }
})

const emit = defineEmits(['nodeClick'])
function handleNodeClick(item: any) {
  emit('nodeClick', item)
}
</script>

<style lang="scss">
.qx-menu {
  width: 100%;
  overflow-y: auto;
  height: calc(100% - 50px);
  .qx-sub-menu {
    & > .qx-menu-item__label {
      box-sizing: border-box;
      padding: 11px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 16px;
      }
    }
    .qx-sub-menu{
      margin-left: 20px;
    }
  }
  .qx-menu-item {
    margin-bottom: 2px;

    i.icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
    & > .qx-menu-item__label {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 10px;
      cursor: pointer;
      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 600;
        font-size: 14px;
        color: #222222;
        line-height: 16px;
      }
      &.active,
      &:hover {
        background: #e8f0fa;
        span {
          color: #1c81f8 !important;
        }
      }
    }
  }
}
</style>
