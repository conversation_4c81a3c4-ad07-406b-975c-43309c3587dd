/**
 * base64 格式的 png 解析
 */
export class Base64PNGFile {
  constructor(private _base64Str: string) {}

  toBlob() {
    if (this._base64Str.startsWith('data:image')) {
      this._base64Str = this._base64Str.split(',')[1]
    }

    const byteCharacters = atob(this._base64Str)
    const byteArrays = []

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512)
      const byteNumbers = new Array(slice.length)
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      byteArrays.push(byteArray)
    }

    return new Blob(byteArrays, { type: 'image/png' })
  }

  download(filename: string) {
    const blob = this.toBlob()
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.click()
  }
}
