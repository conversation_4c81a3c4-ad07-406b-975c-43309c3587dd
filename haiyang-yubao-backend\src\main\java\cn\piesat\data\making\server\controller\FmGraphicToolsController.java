package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.vo.FmGraphicToolsVO;
import cn.piesat.data.making.server.service.FmGraphicToolsService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;

import javax.annotation.Resource;
import java.util.List;

/**
 * 图形工具控制层
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:39
 */
@RestController
@RequestMapping("fmGraphicTools")
public class FmGraphicToolsController {

    @Resource
    private FmGraphicToolsService fmGraphicToolsService;

//    /**
//     * 查询分页
//     *
//     * @param id
//     * @param pageNum  页数
//     * @param pageSize 条数
//     * @return
//     */
//    @GetMapping("/page")
//    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
//    public PageResult<FmGraphicToolsVO> getPage(@RequestParam(required = false) Long id,
//                                                @RequestParam(defaultValue = "1") Integer pageNum,
//                                                @RequestParam(defaultValue = "10") Integer pageSize) {
//        FmGraphicToolsDTO dto = new FmGraphicToolsDTO();
//        dto.setId(id);
//        PageParam pageParam = new PageParam();
//        pageParam.setPageNum(pageNum);
//        pageParam.setPageSize(pageSize);
//        return fmGraphicToolsService.getPage(dto, pageParam);
//    }

    /**
     * 查询列表
     *
     * @param type
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
    public List<FmGraphicToolsVO> getList(@RequestParam(required = false) String type) {
        return fmGraphicToolsService.getList(type);
    }

//    /**
//     * 根据id查询数据
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/info/{id}")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
//    public FmGraphicToolsVO getById(@PathVariable Long id) {
//        return fmGraphicToolsService.getById(id);
//    }
//
//    /**
//     * 保存数据
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/save")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.INSERT)
//    public void save(@Validated(value = {FmGraphicToolsDTO.Save.class}) @RequestBody FmGraphicToolsDTO dto) {
//        fmGraphicToolsService.save(dto);
//    }
//
//    /**
//     * 根据id删除数据
//     *
//     * @param id
//     * @return
//     */
//    @DeleteMapping("/delete/{id}")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.DELETE)
//    public void deleteById(@PathVariable("id") Long id) {
//        fmGraphicToolsService.deleteById(id);
//    }
//
//    /**
//     * 根据idList批量删除数据
//     *
//     * @param idList
//     * @return
//     */
//    @DeleteMapping("/delete")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.DELETE)
//    public void deleteByIdList(@RequestBody List<Long> idList) {
//        fmGraphicToolsService.deleteByIdList(idList);
//    }
}
