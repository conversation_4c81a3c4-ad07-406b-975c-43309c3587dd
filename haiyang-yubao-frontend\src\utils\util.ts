/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-03-01 10:51:17
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-14 15:47:52
 * @Description: 一些通用方法
 * Copyright (c) 2022 by piesat, All Rights Reserved.
 */

import type { TreeOption } from 'naive-ui'

/**
 * 身份证号脱敏
 * @param idCard
 * @returns
 */
export function desensitizedIdNo(idCard: string) {
  if (idCard && idCard.length >= 10) {
    return idCard.replace(/^(.{8})(?:\d+)(.{4})$/, '$1******$2')
  }
  return ''
}

function loadJS(id: string, src: string) {
  return new Promise<void>((resolve, reject) => {
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.id = id
    script.src = src
    document.body.appendChild(script)
    script.onload = () => {
      resolve()
    }
    script.onerror = e => {
      reject(e)
      const ele = document.getElementById(id)
      ele?.remove()
    }
  })
}

export async function checkJS(id: string, src: string) {
  const ele = document.getElementById(id)
  if (!ele) {
    await loadJS(id, src)
  }
}

// 格式化时间
export function formatData(time: string | number) {
  // 年月日
  const date = new Date(time)
  const y = date.getFullYear()
  let m: string | number = date.getMonth() + 1
  m = m < 10 ? `0${String(m)}` : m
  let d: string | number = date.getDate()
  d = d < 10 ? `0${String(d)}` : d
  return `${String(y)}-${String(m)}-${String(d)}`
}

export function download(url: string, fileName: string) {
  const aLink = document.createElement('a')
  const evt = document.createEvent('HTMLEvents')
  evt.initEvent('click', true, true)
  aLink.download = fileName
  aLink.target = '_blank'
  aLink.href = url
  aLink.dispatchEvent(
    new MouseEvent('click', { bubbles: true, cancelable: true, view: window })
  )
}

export const findPropByName = (
  data: any[],
  name: string,
  prop?: string
): Array<any> | null => {
  // 遍历参数列表
  for (let i = 0; i < data.length; i++) {
    // 如果当前参数的id匹配子级的name，则找到了父级参数
    if (data[i] && data[i].name === name) {
      return data[i]
    }

    // 如果当前参数有子级参数，则递归调用findParentParameter函数继续查找
    if (data[i] && data[i].children && data[i].children.length > 0) {
      const parentParameter = findPropByName(data[i].children, name)
      // 如果找到了父级参数，则返回该参数
      if (parentParameter) {
        return parentParameter
      }
    }
  }

  // 如果未找到符合条件的父级参数，则返回null或者其他指定的默认值
  return null
}

// 获取第一个叶子节点
export function getFirstLeafNode(
  tree: any[],
  props = 'children'
): TreeOption | null {
  if (tree) {
    for (const node of tree) {
      if (node[props] && node[props].length > 0) {
        return node[props][0] // 返回第一个子节点
      }
      // 如果当前节点没有子节点，递归检查其子节点
      const result = getFirstLeafNode(node[props], props)
      if (result) {
        return result
      }
    }
  }
  return null // 如果没有找到符合条件的节点，返回 null
}
// 导出一个函数，用于查找数组中的最大值和最小值
export function findMaxMin(arr: any) {
  // 检查数组是否为空
  if (arr.length === 0) {
    throw new Error('数组不能为空')
  }

  // 初始化最大值和最小值为数组的第一个元素
  let maxVal = arr[0]
  let minVal = arr[0]

  // 遍历数组，更新最大值和最小值
  for (let i = 1; i < arr.length; i++) {
    if (arr[i] > maxVal) {
      maxVal = arr[i]
    } else if (arr[i] < minVal) {
      minVal = arr[i]
    }
  }

  // 返回一个对象，包含最大值和最小值
  return { max: maxVal, min: minVal }
}

// 获取第二小
export function getSecondSmallest(arr: any) {
  // 使用 Set 去除重复的值，然后转换回数组
  const uniqueArr = [...new Set(arr)]

  // 确保数组至少有两个不同的元素
  if (uniqueArr.length < 2) {
    throw new Error('数组中没有第二小的元素')
  }

  // 对数组进行排序
  uniqueArr.sort((a: any, b: any) => a - b)

  // 返回排序后的第二个元素
  return uniqueArr[1]
}

// 导出一个函数，用于找到数组中的第二大的数
export function getSecondMax(arr: any) {
  // 如果数组长度小于2，则无法找到第二大的数，返回null
  if (arr.length < 2) {
    return null // 数组长度小于2时，无法找到第二大的数
  }

  // 初始化两个变量，分别表示数组中的最大值和第二大的值
  let largest = -Infinity
  let secondLargest = -Infinity

  // 遍历数组
  for (let i = 0; i < arr.length; i++) {
    // 如果当前元素大于最大值，则更新最大值和第二大的值
    if (arr[i] > largest) {
      secondLargest = largest
      largest = arr[i]
    // 如果当前元素大于第二大的值，且不等于最大值，则更新第二大的值
    } else if (arr[i] > secondLargest && arr[i] !== largest) {
      secondLargest = arr[i]
    }
  }

  // 如果第二大的值仍为初始值，则返回null，否则返回第二大的值
  return secondLargest === -Infinity ? null : secondLargest
}

/**
 * 明确字符串方向
 * @param text
 * @param arr
 */
export function explicitStringDirection(text: string, arr = ['6', '9']) {
  const find = arr.find(i => {
    return text.indexOf(i) !== -1
  })
  return find ? `${text}↑` : text
}

/**
 * 生成随机 ID
 */
export function generateID() {
  return 'id-' + Math.random().toString(36).substring(2, 9)
}

/**
 * 生成一个指定长度的随机数字字符串
 * @param length 字符串长度，默认为10
 * @returns 随机数字字符串
 */
export function randomNumericID(length = 10): string {
  let result = ''
  const characters = '0123456789'
  const charactersLength = characters.length

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charactersLength)
    result += characters.charAt(randomIndex)
  }

  return result
}

export async function blobToBase64(blob: Blob) {
  const fileReader = new FileReader()
  return new Promise((resolve, reject) => {
    fileReader.readAsDataURL(blob)
    fileReader.onload = () => resolve(fileReader.result)
    fileReader.onerror = reject
  })
}

/**
 * 对象数组去重
 * @param arr
 * @param cb
 */
export function unionObjectArray<T extends object>(arr: T[], cb: () => keyof T) {
  const newArr: T[] = []
  const key = cb()
  arr.forEach((element: T) => {
    const find = newArr.find(i => i[key] === element[key])
    if (!find) {
      newArr.push(element)
    }
  })
  return newArr
}
