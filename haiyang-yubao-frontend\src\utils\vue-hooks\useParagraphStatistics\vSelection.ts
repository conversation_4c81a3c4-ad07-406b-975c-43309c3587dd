import type { ObjectDirective, DirectiveBinding } from 'vue'

const vSelection: ObjectDirective = {
  mounted(
    el: HTMLInputElement | HTMLTextAreaElement,
    binding: DirectiveBinding
  ) {
    el.addEventListener('selectionchange', e => {
      console.log(el)
      const start = el.selectionStart == null ? el.value.length : el.selectionStart
      const end = el.selectionEnd == null ? el.value.length : el.selectionEnd
      binding.value = el.value.substring(start, end)
    })
  }
}

export default vSelection
