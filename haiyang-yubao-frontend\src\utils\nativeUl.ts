import {
  // create naive ui
  create,
  // component
  NTooltip,
  NButton,
  NInput,
  NSelect,
  NForm,
  NFormItem,
  NIcon,
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NPagination,
  NSpace,
  NDropdown,
  NMenu,
  NModal,
  NBreadcrumb,
  NBreadcrumbItem,
  NRow,
  NCol,
  NFormItemGi,
  NGi,
  NRadio,
  NDatePicker,
  NUpload,
  NTabs,
  NCarousel,
  NTabPane,
  NRate,
  NProgress,
  NRadioGroup,
  NCard,
  NTree,
  NDataTable,
  NGrid,
  NGridItem,
  NCalendar,
  NBadge,
  NTimeline,
  NTimelineItem,
  NLayout,
  NLayoutContent,
  NLayoutSider,
  NDivider,
  NSwitch,
  NInputGroup,
  NCheckboxGroup,
  NCheckbox,
  NInputNumber,
  NTreeSelect,
  NSlider,
  NColorPicker,
  NUploadDragger,
  NUploadFileList,
  NImage,
  NSpin,
  NPopconfirm,
  NRadioButton,
  NDrawer,
  NDrawerContent,
  NPopover
} from 'naive-ui'

const naive = create({
  components: [
    NRadioButton,
    NPopconfirm,
    NSpin,
    NImage,
    NSlider,
    NColorPicker,
    NTooltip,
    NButton,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NIcon,
    NConfigProvider,
    NMessageProvider,
    NDialogProvider,
    NPagination,
    NSpace,
    NDropdown,
    NMenu,
    NModal,
    NGrid,
    NRow,
    NCol,
    NFormItemGi,
    NGi,
    NRadio,
    NDatePicker,
    NUpload,
    NCarousel,
    NTabs,
    NTabPane,
    NDataTable,
    NGrid,
    NGridItem,
    NCard,
    NBreadcrumb,
    NBreadcrumbItem,
    NRate,
    NProgress,
    NRadioGroup,
    NSpace,
    NCard,
    NTree,
    NCalendar,
    NBadge,
    NTimeline,
    NTimelineItem,
    NLayout,
    NLayoutContent,
    NLayoutSider,
    NDivider,
    NSwitch,
    NInputGroup,
    NCheckboxGroup,
    NCheckbox,
    NInputNumber,
    NTreeSelect,
    NUploadDragger,
    NUploadFileList,
    NDrawer,
    NDrawerContent,
    NPopover
  ]
})

export {naive};
