package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.BuoyStationDao;
import cn.piesat.data.making.server.dto.BuoyStationDataDTO;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.BuoyStation;
import cn.piesat.data.making.server.entity.OceanLargeDeepseaMooringbuoyO;
import cn.piesat.data.making.server.entity.OceanSmallShallowseauoyO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.BuoyStationService;
import cn.piesat.data.making.server.service.OceanLargeDeepseaMooringbuoyOService;
import cn.piesat.data.making.server.service.OceanSmallShallowseauoyOService;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.vo.BuoyStationDataVO;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.StationVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 浮标站表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BuoyStationServiceImpl extends ServiceImpl<BuoyStationDao, BuoyStation> implements BuoyStationService {

    @Resource
    private BuoyStationDao buoyStationDao;
    @Resource
    private StationService stationService;
    @Resource
    private OceanLargeDeepseaMooringbuoyOService oceanLargeDeepseaMooringbuoyOService;
    @Resource
    private OceanSmallShallowseauoyOService oceanSmallShallowseauoyOService;

    @Override
    public List<StationInfo> getList(String name) {
        return buoyStationDao.getList(name);
    }

    @Override
    public List<BuoyStationDataVO> getDataList(BuoyStationDataDTO dto) {
        List<BuoyStationDataVO> resultList = new ArrayList<BuoyStationDataVO>();
        String type = dto.getType();
        List<String> buoyStationCodeList = dto.getBuoyStationCodeList();
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();

        //站点信息表
        StationDTO stationDTO = new StationDTO();
        stationDTO.setStationTypeCode("buoyStation");
        List<StationVO> stationList = stationService.getList(stationDTO);
        Map<String, StationVO> stationMap = stationList.stream().collect(Collectors.toMap(StationVO::getRelationStation, Function.identity(),
                (key1, key2) -> key2));

        if (type == null || "3m".equals(type) || "10m".equals(type)) {
            LambdaQueryWrapper<OceanLargeDeepseaMooringbuoyO> largeWrapper = new LambdaQueryWrapper<>();
            largeWrapper.between(OceanLargeDeepseaMooringbuoyO::getMonitoringtime, startTime, endTime);
            //10m综合浮标[MF20000 MF20001] 3m综合浮标[MF20002 MF20003 MF20004 MF20005 MF20006 MF20007 MF20008]
            List<String> threeList = new ArrayList<>(Arrays.asList("MF20002", "MF20003", "MF20004", "MF20005", "MF20006", "MF20007", "MF20008"));
            List<String> tenList = new ArrayList<>(Arrays.asList("MF20000", "MF20001"));
            if ("3m".equals(type)) {
                largeWrapper.in(OceanLargeDeepseaMooringbuoyO::getBuoyinfoId, threeList);
            } else if ("10m".equals(type)) {
                largeWrapper.in(OceanLargeDeepseaMooringbuoyO::getBuoyinfoId, tenList);
            } else {
                threeList.addAll(tenList);
                largeWrapper.in(OceanLargeDeepseaMooringbuoyO::getBuoyinfoId, threeList);
            }
            if (!CollectionUtils.isEmpty(buoyStationCodeList)) {
                largeWrapper.in(OceanLargeDeepseaMooringbuoyO::getBuoyinfoId, buoyStationCodeList);
            }
            List<OceanLargeDeepseaMooringbuoyO> largeList = oceanLargeDeepseaMooringbuoyOService.list(largeWrapper);
            largeList.stream().forEach(data -> {
                BuoyStationDataVO vo = new BuoyStationDataVO();
                vo.setBuoyStationCode(data.getBuoyinfoId());
                vo.setTime(data.getMonitoringtime());
                vo.setBuoydataWs(data.getBuoydataWs());
                vo.setBuoydataWd(data.getBuoydataWd());
                vo.setBuoydataWsm(data.getBuoydataWsm());
                vo.setBuoydataWdm(data.getBuoydataWdm());
                vo.setBuoydataWsa(data.getBuoydataWsa());
                vo.setBuoydataWda(data.getBuoydataWda());
                vo.setBuoydataWsh(data.getBuoydataWsh());
                vo.setBuoydataWdh(data.getBuoydataWdh());
                vo.setBuoydataBg(data.getBuoydataBg());
                vo.setBuoydataBx(data.getBuoydataBx());
                vo.setBuoydataZq(data.getBuoydataZq());
                vo.setBuoydataYbg(data.getBuoydataYbg());
                vo.setBuoydataYzq(data.getBuoydataYzq());
                vo.setBuoydataZbg(data.getBuoydataZbg());
                vo.setBuoydataZzq(data.getBuoydataZzq());
                vo.setBuoydataAt(data.getBuoydataAt());
                vo.setBuoydataBp(data.getBuoydataBp());
                vo.setBuoydataHu(data.getBuoydataHu());
                vo.setBuoydataWt(data.getBuoydataWt());
                vo.setBuoydataSl(data.getBuoydataSl());
                vo.setBuoydataNjd(data.getBuoydataNjd());
                vo.setBuoydataCs(data.getBuoydataCs());
                vo.setBuoydataCd(data.getBuoydataCd());
                StationVO station = stationMap.get(data.getBuoyinfoId());
                vo.setBuoyStationName(station.getName());
                vo.setBuoyStationLocationJson(station.getLocationJson());
                resultList.add(vo);
            });
        }
        if (type == null || type.equals("waveSpectrum")) {
            LambdaQueryWrapper<OceanSmallShallowseauoyO> smallWrapper = new LambdaQueryWrapper<>();
            smallWrapper.in(OceanSmallShallowseauoyO::getBuoyinfoId, buoyStationCodeList);
            smallWrapper.between(OceanSmallShallowseauoyO::getMonitoringtime, startTime, endTime);
            List<OceanSmallShallowseauoyO> smallList = oceanSmallShallowseauoyOService.list(smallWrapper);
            smallList.stream().forEach(data -> {
                BuoyStationDataVO vo = new BuoyStationDataVO();
                vo.setBuoyStationCode(data.getBuoyinfoId());
                vo.setTime(data.getMonitoringtime());
                vo.setBuoydataWs(data.getBuoydataWs());
                vo.setBuoydataWd(data.getBuoydataWd());
                vo.setBuoydataBg(data.getBuoydataBg());
                vo.setBuoydataBx(data.getBuoydataBx());
                vo.setBuoydataZq(data.getBuoydataZq());
                vo.setBuoydataYbg(data.getBuoydataYbg());
                vo.setBuoydataYzq(data.getBuoydataYzq());
                vo.setBuoydataZbg(data.getBuoydataZbg());
                vo.setBuoydataZzq(data.getBuoydataZzq());
                vo.setBuoydataAt(data.getBuoydataAt());
                vo.setBuoydataBp(data.getBuoydataBp());
                vo.setBuoydataHu(data.getBuoydataHu());
                vo.setBuoydataWt(data.getBuoydataWt());
                vo.setBuoydataSl(data.getBuoydataSl());
                vo.setPuPbg(data.getPuPbg());
                vo.setPuPfzq(data.getPuPfzq());
                vo.setYlpuPbg(data.getYlpuPbg());
                vo.setYlpuPfzq(data.getYlpuPfzq());
                StationVO station = stationMap.get(data.getBuoyinfoId());
                vo.setBuoyStationName(station.getName());
                vo.setBuoyStationLocationJson(station.getLocationJson());
                resultList.add(vo);
            });
        }
//        List<BuoyStationDataVO> list = resultList.stream().collect(Collectors.collectingAndThen(
//                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(tc -> tc.getBuoyStationCode()))), ArrayList::new));

        Set<Map.Entry<Date, String>> set = new HashSet<>();
        List<BuoyStationDataVO> list = resultList.stream()
                .filter(tc -> set.add(new AbstractMap.SimpleEntry<>(tc.getTime(), tc.getBuoyStationCode())))
                .collect(Collectors.toList());
        list.sort(Comparator.comparing(BuoyStationDataVO::getBuoyStationName).thenComparing(BuoyStationDataVO::getTime)
        );
        return list;
    }

    @Override
    @SneakyThrows
    public FillMapVO getGeoRangeDataList(BuoyStationDataDTO dto) {
        FillMapVO fillMap = new FillMapVO();
        //查询范围内的浮标站
        String geoRange = null;
        if (StringUtils.isNotBlank(dto.getGeoRange())) {
            geoRange = GeoToolsUtil.geojsonToGeo(dto.getGeoRange());
        }
        List<Station> stationList = stationService.getListByRange(geoRange);
        if (!CollectionUtils.isEmpty(stationList)) {
            //浮标站数据
            List<String> codeList =
                    stationList.stream().filter(s -> s.getRelationStation() != null && s.getStationTypeCode().equals("buoyStation")).map(Station::getRelationStation).collect(Collectors.toList());
            dto.setBuoyStationCodeList(codeList);
            List<BuoyStationDataVO> dataList = this.getDataList(dto);
            //按照数据时间对浮标站数据进行分组
            Map<Date, List<BuoyStationDataVO>> dataMap = dataList.stream().collect(Collectors.groupingBy(BuoyStationDataVO::getTime));
            //数据时间列表
            List<Date> timeList = dataMap.keySet().stream().collect(Collectors.toList()).stream().sorted().collect(Collectors.toList());
            fillMap.setTimeList(timeList);
            fillMap.setTimeAndDataMap(dataMap);
        }
        return fillMap;
    }
}