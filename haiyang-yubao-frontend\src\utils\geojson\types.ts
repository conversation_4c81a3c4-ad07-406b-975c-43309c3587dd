/**
 * json 支持的数据类型
 */
export type json =
  | string
  | number
  | boolean
  | null
  | Array<json>
  | { [k: string]: json }

export type Feature =
  | IPointFeature
  | IMultiPoint
  | ILineString
  | IMultiLineString
  | IPolygon
  | IMultiPolygon

/**
 * GeoJSON 接口
 */
export interface IGeoJSON {
  type: 'FeatureCollection'
  features: Array<Feature>
}

/**
 * 要素接口
 */
export interface IFeature {
  type: 'Feature'
  geometry: {
    type: string
    coordinates: Array<
      | number
      | Array<number>
      | Array<Array<number>>
      | Array<Array<Array<number>>>
    >
  }
  properties?: {
    [k: string]: json
  }
}

/**
 * 点要素接口
 */
export interface IPointFeature extends IFeature {
  geometry: {
    type: 'Point'
    coordinates: Array<number>
  }
}

/**
 * 多点要素接口
 */
export interface IMultiPoint extends IFeature {
  geometry: {
    type: 'MultiPoint'
    coordinates: Array<Array<number>>
  }
}

/**
 * 线要素接口
 */
export interface ILineString extends IFeature {
  geometry: {
    type: 'LineString'
    coordinates: Array<Array<number>>
  }
}

/**
 * 多线要素接口
 */
export interface IMultiLineString extends IFeature {
  geometry: {
    type: 'MultiLineString'
    coordinates: Array<Array<Array<number>>>
  }
}

/**
 * 面要素接口
 */
export interface IPolygon extends IFeature {
  geometry: {
    type: 'Polygon'
    coordinates: Array<Array<Array<number>>>
  }
}

/**
 * 多面要素接口
 */
export interface IMultiPolygon extends IFeature {
  geometry: {
    type: 'MultiPolygon'
    coordinates: Array<Array<Array<Array<number>>>>
  }
}
