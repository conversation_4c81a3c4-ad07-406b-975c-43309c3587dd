<template>
  <div class="content product-edit">
    <div class="content-header">
      <h3>模板编辑</h3>
      <div class="btns">
        <qx-button class="qx-button" @click="editType = true">编辑</qx-button>
        <qx-button class="primary" @click="onSave">保存</qx-button>
      </div>
    </div>
    <div class="content-filter">
      <Form :info="props.info" ref="formRef" />
    </div>
    <div class="content-edit-container">
      <template v-if="fileType.includes('txt')|| fileType.includes('xml')">
        <n-input
          placeholder="请输入"
          v-model:value="fileContent"
          type="textarea"
          size="small"
          :autosize="{
            minRows: 6,
            maxRows: 8
          }"
        />

      </template>
      <template v-else>
        <!-- <iframe :src="fileUrl" width="100%" height="100%" frameborder="0"/> -->
        <OfficeEditor
          v-if="wordUrl"
          v-loading="loading"
          :url="wordUrl"
          :callbackUrl="props.callbackUrl"
          @close="onClose"
          :editType="editType"
        ></OfficeEditor>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { OfficeEditor } from 'src/components/OfficeEditor'
import { ref, watch } from 'vue'
import { Form } from './index'
import { useMessage } from 'naive-ui'
import Api from 'src/requests/forecast'
import eventBus from 'src/utils/eventBus'

const message = useMessage()
const formRef = ref<any>(null)
let editType = ref(false)
let loading = ref(true)
const wordUrl = ref('')
let fileType = ref('')
let fileContent = ref('')

const props = defineProps({
  info: {
    type: Object,
    default: () => ({})
  },
  callbackUrl: {
    type: String
  }
})

function onClose() {}

function onSave() {
  formRef.value?.form.validate((errors: boolean) => {
    if (!errors) {
      const { dialogForm = {} } = formRef.value
      const params = JSON.parse(JSON.stringify(dialogForm))
      params.relationTemplateCode = params.relationTemplateCode.join(',')
      params.id = props.info.id
      params.productId = props.info.productId
      if(params.fileType.includes('txt') || params.fileType.includes('xml')){
        params.fileContent = fileContent.value
      }

      Api.saveProductTemp(params)
        .then((res: any) => {
          message.success('保存成功')
          eventBus.emit('saveTemp',res)
        })
        .catch(e => {
          let { msg } = e?.response?.data
          message.error(msg || '操作失败')
        })
    }
  })
}

function readFile(url:string) {
    fetch(url)
      .then((response) => response.text())
      .then((data) => {
        fileContent.value = data
      })
      .catch(() => {
        message.warning("暂无数据");
      });
  }

  let fileUrl = ref("")
watch(
  () => props.info,
  val => {
    console.log(props.callbackUrl,'val---')
    if (JSON.stringify(val) !== '{}') {
      loading.value = false
      
      fileType.value = val.fileUrl.substring(val.fileUrl.lastIndexOf('.') + 1)
      if(fileType.value.includes('txt') || fileType.value.includes('xml')){
        readFile(config.onlyOfficeServerUrl + val?.fileUrl)
      }else if(fileType.value.includes('doc')){
        wordUrl.value = config.onlyOfficeServerUrl + val?.fileUrl
        // fileUrl.value = config.kkFileUrl +decodeURIComponent(wordUrl.value)
      }
      
    }
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style lang="scss">
.product-edit {
  display: flex;
  flex-direction: column;
  .content-edit-container {
    flex: 1;
    box-sizing: border-box;
    padding: 6px 32px;
    .n-input--textarea{
      height: 100%;
    }
  }
  .content-filter {
    box-sizing: border-box;
    padding: 20px 27px 6px;
    background: rgba(217, 217, 217, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.1);
    .n-form {
      display: flex;
      flex-wrap: wrap;
      .n-form-item {
        width: 50%;
      }
    }
  }
}
</style>
