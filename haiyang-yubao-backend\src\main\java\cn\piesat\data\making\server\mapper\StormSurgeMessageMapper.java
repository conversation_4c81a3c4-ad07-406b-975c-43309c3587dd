package cn.piesat.data.making.server.mapper;


import cn.piesat.data.making.server.dto.StormSurgeMessageDTO;
import cn.piesat.data.making.server.entity.StormSurgeMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface StormSurgeMessageMapper {
    StormSurgeMessageMapper INSTANCE = Mappers.getMapper(StormSurgeMessageMapper.class);

    StormSurgeMessage toEntity(StormSurgeMessageDTO stormSurgeMessageDTO);
}
