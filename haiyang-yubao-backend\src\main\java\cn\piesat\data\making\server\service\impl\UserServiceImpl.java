package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.UserDao;
import cn.piesat.data.making.server.dto.UserDTO;
import cn.piesat.data.making.server.entity.User;
import cn.piesat.data.making.server.mapper.UserMapper;
import cn.piesat.data.making.server.service.UserService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.UserVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserDao, User>
        implements UserService {

    @Resource
    private UserDao userDao;

    @Override
    public PageResult<UserVO> getPage(UserDTO dto, PageParam pageParam) {
        Page<User> page = this.page(new Page<>(pageParam.getPageNum(), pageParam.getPageSize()), createQueryWrapper(dto));
        List<UserVO> voList = UserMapper.INSTANCE.entityListToVoList(page.getRecords());
        return new PageResult(voList, pageParam.getPageNum(), pageParam.getPageSize(), page.getTotal());
    }

    @Override
    public List<UserVO> getList(UserDTO dto) {
        List<User> list = this.list(createQueryWrapper(dto));
        return UserMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public UserVO getInfoById(Long id) {
        return UserMapper.INSTANCE.entityToVo(this.getById(id));
    }

    @Override
    public void save(UserDTO dto) {
        if (dto.getId() == null) {
            this.save(UserMapper.INSTANCE.dtoToEntity(dto));
        } else {
            this.updateById(UserMapper.INSTANCE.dtoToEntity(dto));
        }
    }

    @Override
    public void deleteById(Long id) {
        baseMapper.deleteById(id);
    }

    private LambdaQueryWrapper<User> createQueryWrapper(UserDTO dto) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (dto.getId() != null) {
            queryWrapper.eq(User::getId, dto.getId());
        }
        if (dto.getTagId() != null) {
            queryWrapper.eq(User::getTagId, dto.getTagId());
        }
        return queryWrapper.orderByAsc(User::getCreateTime);
    }
}





