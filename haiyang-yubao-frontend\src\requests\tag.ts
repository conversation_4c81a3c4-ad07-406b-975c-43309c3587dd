/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-26 14:06:15
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-26 14:26:40
 * @FilePath: /hainan-jianzai-web/src/requests/labelMaintenance.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import getAxios from '../utils/axios'
import { ITagItem } from 'src/requests/tag.type'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)

// 获取标签列表
function getTagList() {
  return axiosInstance({
    url: '/tag/list',
    method: 'GET'
  })
}

/**
 * 保存标签
 * @param data
 * @returns
 */
function saveTag(data: any) {
  return axiosInstance({
    url: '/tag/save',
    method: 'POST',
    data
  })
}

// 删除标签
function delTagById(id: string) {
  return axiosInstance({
    url: `/tag/delete/${id}`,
    method: 'DELETE'
  })
}

// 根据用户表id查询详情
// 根据用户id获取用户信息
function getUserInfoById(id: string) {
  return axiosInstance({
    url: `/user/info/${id}`,
    method: 'GET'
  })
}

/**
 * 获取用户列表
 */
function getUserList(params: {tagId: string}) {
  return axiosInstance<ITagItem[]>({
    url: '/user/list',
    method: 'GET',
    params
  })
}

/**
 * 获取签发用户列表
 */
function getSignUserList() {
  return getUserList({ tagId: '1904727438635282434' })
}

/**
 * 获取拟稿人列表
 */
function getMakeUserList() {
  return getUserList({ tagId: '1904782290649661442' })
}

/**
 * 保存用户
 * @param data
 * @returns
 */
function saveUser(data: any) {
  return axiosInstance({
    url: '/user/save',
    method: 'POST',
    data
  })
}

/**
 * 根据id删除用户
 * @param id
 * @returns
 */
function delUserById(id: string) {
  return axiosInstance({
    url: `/user/delete/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取用户分页
 * @param params
 * @returns
 */
function getUserPage(params: any) {
  return axiosInstance({
    url: '/user/page',
    method: 'GET',
    params
  })
}

export default {
  getTagList,
  saveTag,
  delTagById,
  getUserInfoById,
  getUserList,
  saveUser,
  delUserById,
  getUserPage,
  getSignUserList,
  getMakeUserList
}
