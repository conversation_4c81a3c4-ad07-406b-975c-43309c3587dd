package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.FmGraphicDataSource;
import cn.piesat.data.making.server.dto.FmGraphicDataSourceDTO;
import cn.piesat.data.making.server.vo.FmGraphicDataSourceVO;
import cn.piesat.data.making.server.dao.FmGraphicDataSourceDao;
import cn.piesat.data.making.server.service.FmGraphicDataSourceService;
import cn.piesat.data.making.server.mapper.FmGraphicDataSourceMapper;
import cn.piesat.common.utils.PageResult;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:28
 */
@Service
@Slf4j
public class FmGraphicDataSourceServiceImpl extends ServiceImpl<FmGraphicDataSourceDao, FmGraphicDataSource> implements FmGraphicDataSourceService {

    @Resource
    private FmGraphicDataSourceDao fmGraphicDataSourceDao;

    @Override
    public List<FmGraphicDataSourceVO> getList() {
        List<FmGraphicDataSource> fmGraphicDataSources = fmGraphicDataSourceDao.selectAll();
        return FmGraphicDataSourceMapper.INSTANCE.entityListToVoList(fmGraphicDataSources);
    }


}
