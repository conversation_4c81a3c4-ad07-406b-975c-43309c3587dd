package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationHourVbO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-能见度-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationHourVbOService extends IService<OceanStationHourVbO> {

    List<OceanStationHourVbO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




