package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.service.ForecastTaskService;
import cn.piesat.data.making.server.vo.ForecastTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 预报任务表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forecastTask")
@Slf4j
public class ForecastTaskController {

    @Resource
    private ForecastTaskService forecastTaskService;

    /**
     * 根据状态查询预报任务列表
     *
     * @param status 状态
     * @return
     */
    @GetMapping("/list/{status}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报任务管理", operateType = OperateType.SELECT)
    public List<ForecastTaskVO> getList(@PathVariable("status") Integer status) {
        return forecastTaskService.getList(status);
    }

    /**
     * 查询预报任务列表的状态统计信息
     *
     * @return
     */
    @GetMapping("/info")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报任务管理", operateType = OperateType.SELECT)
    public Map<String, Long> getInfo() {
        return forecastTaskService.getInfo();
    }

    /**
     * 定时创建预报任务
     *
     * @return
     */
    @GetMapping("/schedule")
    @Scheduled(cron = "${schedule.cron.createForecastTask}")
    public void createForecastTask() {
        new Thread(() -> {
            Long curTime = System.currentTimeMillis();
            log.info("=======================开始执行任务============================");
            forecastTaskService.createForecastTask();
            log.info("=======================结束执行任务============================");
            Long difTime = (System.currentTimeMillis()-curTime)/1000;
            log.info("=======================执行任务耗时:"+difTime+"============================");
        }).start();
    }
}

