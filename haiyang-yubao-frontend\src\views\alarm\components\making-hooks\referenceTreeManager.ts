import { IRegionTree } from 'src/requests/alarm.type'

/**
 * 参考站树结构管理
 */
export class ReferenceTreeManager {
  // 市县级根节点条件
  static cityCondition = (i: IRegionTree) => i.code === 'oceanStation'

  constructor(protected _stationTreeList: IRegionTree[]) {}

  /**
   * 根据回调获取节点
   * @param cb
   * @param stationTreeList
   */
  getTreeNode(
    cb: (item: IRegionTree) => boolean,
    stationTreeList: IRegionTree[] | null = this._stationTreeList
  ): IRegionTree | null {
    if (stationTreeList === null) {
      return null
    }
    let find =
      stationTreeList?.find(i => {
        return cb(i)
      }) || null
    if (!find) {
      for (let i = 0; i < stationTreeList.length; i++) {
        const element = stationTreeList[i]
        find = this.getTreeNode(cb, element.childList)
        if (find) {
          break
        }
      }
    }
    return find
  }

  /**
   * 获取市县级根节点
   */
  getCityRootNode(): IRegionTree {
    return this.getTreeNode(ReferenceTreeManager.cityCondition)!
  }
}
