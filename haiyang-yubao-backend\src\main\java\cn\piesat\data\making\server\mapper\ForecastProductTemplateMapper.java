package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastProductTemplateDTO;
import cn.piesat.data.making.server.entity.ForecastProductTemplate;
import cn.piesat.data.making.server.vo.ForecastProductTemplateVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ForecastProductTemplateMapper {

    ForecastProductTemplateMapper INSTANCE = Mappers.getMapper(ForecastProductTemplateMapper.class);

    /**
     * entity-->vo
     */
    ForecastProductTemplateVO entityToVo(ForecastProductTemplate entity);

    /**
     * dto-->entity
     */
    ForecastProductTemplate dtoToEntity(ForecastProductTemplateDTO dto);

    /**
     * entityList-->voList
     */
    List<ForecastProductTemplateVO> entityListToVoList(List<ForecastProductTemplate> list);

    /**
     * dtoList-->entityList
     */
    List<ForecastProductTemplate> dtoListToEntityList(List<ForecastProductTemplateDTO> dtoList);
}
