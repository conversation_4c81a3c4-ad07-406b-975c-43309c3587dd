package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.piesat.data.making.server.entity.FmTyphoonB;
import cn.piesat.data.making.server.dto.FmTyphoonBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonForecast;
import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.mapper.FmTyphoonBMapper;
import cn.piesat.data.making.server.service.FmTyphoonForecastService;
import cn.piesat.data.making.server.service.FmTyphoonRealBService;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.utils.HttpClientUtil;
import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import cn.piesat.data.making.server.dao.FmTyphoonBDao;
import cn.piesat.data.making.server.service.FmTyphoonBService;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.vo.FmTyphoonScoreVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import cn.piesat.data.making.server.utils.page.PageParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 台风信息服务实现类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:21
 */
@Service
@Slf4j
public class FmTyphoonBServiceImpl extends ServiceImpl<FmTyphoonBDao, FmTyphoonB> implements FmTyphoonBService {

    @Resource
    private FmTyphoonBDao fmTyphoonBDao;

    @Resource
    FmTyphoonRealBService fmTyphoonRealBService;
    @Resource
    FmTyphoonForecastService fmTyphoonForecastService;

    private ExecutorService executorService = Executors.newFixedThreadPool(50);


    @Override
    public PageResult<FmTyphoonBVO> getPage(FmTyphoonBDTO dto, PageParam pageParam) {
        return null;
    }

    @Override
    public List<FmTyphoonBVO> getList(FmTyphoonBDTO dto) {
        return null;
    }

    @Override
    public FmTyphoonBVO getById(Long id) {
        return null;
    }

    @Override
    public void save(FmTyphoonBDTO dto) {

    }

    @Override
    public void saveList(List<FmTyphoonBDTO> dtoList) {

    }

    @Override
    public void deleteById(Long id) {

    }

    @Override
    public void deleteByIdList(List<Long> idList) {

    }

    @Override
    public FmTyphoonB getInfo(String typhoonNo) {
        LambdaQueryWrapper<FmTyphoonB> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmTyphoonB::getTfbh,typhoonNo);
        return this.getOne(wrapper,false);
    }

    @Override
    public List<FmTyphoonScoreVO> getSimilarTyphoonList(String code, int startYear, int points, int distance, int score) throws Exception {
        LambdaQueryWrapper<FmTyphoonB> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmTyphoonB::getTfbh,code);
        FmTyphoonB fmTyphoonB = this.getOne(queryWrapper);
        if(fmTyphoonB == null) {
            throw new BusinessException(HttpStatus.BAD_REQUEST,"查询的台风"+code+"不存在");
        }

        List<FmTyphoonB> historyTyphoons = this.getHistoryTyphoons(startYear,code);
        if(historyTyphoons == null || historyTyphoons.isEmpty()) return null;

        List<FmTyphoonScoreVO> similarTyphoons = this.findSimilarTyphoons(fmTyphoonB,historyTyphoons,points,distance,score);
//        List<FmTyphoonBVO> similarTyphoonVos = FmTyphoonBMapper.INSTANCE.entityListToVoList(similarTyphoons);
        return similarTyphoons;
    }

    @Override
    public String syncTyphoon(Integer startYear, Integer endYear) {
        try {
            List<FmTyphoonB> fmTyphoonBList = new ArrayList<>();
            List typhoonInfoList = getTyphoonInfo(startYear, endYear);
            /*LambdaQueryWrapper<FmTyphoonB> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.gt(FmTyphoonB::getYear,startYear);
            queryWrapper.lt(FmTyphoonB::getYear,endYear);*/
            List<FmTyphoonB> list = this.list();
            Map<String, FmTyphoonB> collect = new HashMap<>();
            if (!CollectionUtils.isEmpty(list)) {
                collect = list.stream().collect(Collectors.toMap(FmTyphoonB::getTfbh, v -> v));
            }
            //根据台风编号获取台风点数据
            for (Object object : typhoonInfoList) {
                JSONObject typhoon = JSONUtil.parseObj(JSONUtil.toJsonStr(object));

                String tfid = typhoon.getStr("tfid");
                if (tfid.equals("2501")) continue;
                FmTyphoonB fmTyphoonB = new FmTyphoonB();
                fmTyphoonB.setYear(tfid.substring(0,4));
                fmTyphoonB.setName(typhoon.getStr("name"));
                fmTyphoonB.setTfbh(tfid);
                fmTyphoonB.setIdent(tfid);
                fmTyphoonB.setEname(typhoon.getStr("enname"));
                fmTyphoonB.setStatus(typhoon.getInt("isactive"));
                if (!collect.containsKey(tfid)) {
                    fmTyphoonBList.add(fmTyphoonB);
                }
//                log.info("fmTyphoonB:{}",fmTyphoonB);
                //请求台风点信息
                getTyphoonInfoDetail(tfid);
            }
//            getTyphoonInfoDetail("202423");
            this.saveOrUpdateBatch(fmTyphoonBList);
        } catch (Exception e) {
            log.error("请求台风数据出现异常：",e);
        }
        //String response = HttpClientUtil.post(url, "", appKey, appParam, appSign);
        return "请求台风数据结束";
    }

    private List getTyphoonInfo(Integer startYear, Integer endYear) throws IOException {
        //请求台风信息
        String url = "https://10.132.108.150:8089/service/api/hydzzhzhyysfxt_typhoon";
        String appKey = "39362a905da356dc651b03240807661e9cb43ebe";
        String sign = "b01c08c028b541b799964dd781f22e97";
        JSONObject param = new JSONObject();
        param.set("startYear","'"+startYear+"'");
        param.set("endYear","'"+endYear+"'");
        JSONObject reqParam = new JSONObject();
        reqParam.set("param",param);
        String reqJson = JSONUtil.toJsonStr(reqParam);
        String appParam = Base64.encode(reqJson);
        String join = String.join("&", appKey, sign, appParam);
        String appSign = MD5.create().digestHex(join);
        log.debug("url:{},过滤条件:{}", url, reqParam);
        String response = HttpClientUtil.post(url, "", appKey, appParam, appSign);
        String jsonString = response.trim();
        TypeReference<OceanStationServiceImpl.Wrapper> typeRef = new TypeReference<OceanStationServiceImpl.Wrapper>() {
        };
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        OceanStationServiceImpl.Wrapper wrapper = mapper.readValue(jsonString, typeRef);
        if (wrapper.getCode() == 0) {
            return wrapper.getData().getResultList();
        } else {
            return null;
        }
    }

    private void getTyphoonInfoDetail(String tfid) throws IOException {
        //请求台风信息
        String url = "https://10.132.108.150:8089/service/api/hydzzhzhyysfxt_typhoon_info";
        String appKey = "39362a905da356dc651b03240807661e9cb43ebe";
        String sign = "b01c08c028b541b799964dd781f22e97";
        JSONObject param = new JSONObject();
        param.set("tfid","'"+tfid+"'");
        JSONObject reqParam = new JSONObject();
        reqParam.set("param",param);
        String reqJson = JSONUtil.toJsonStr(reqParam);
        String appParam = Base64.encode(reqJson);
        String join = String.join("&", appKey, sign, appParam);
        String appSign = MD5.create().digestHex(join);
        log.info("url:{},过滤条件:{},appKey:{},appParam:{},appSign:{},", url, reqParam,appKey, appParam, appSign);
        String response = HttpClientUtil.post(url, "", appKey, appParam, appSign);
        String jsonString = response.trim();
        TypeReference<OceanStationServiceImpl.Wrapper> typeRef = new TypeReference<OceanStationServiceImpl.Wrapper>() {
        };
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        OceanStationServiceImpl.Wrapper wrapper = mapper.readValue(jsonString, typeRef);

        if (wrapper.getCode() != 0) {
            log.info("url:{},过滤条件:{},appKey:{},appParam:{},appSign:{},请求数据失败", url, reqParam,appKey, appParam, appSign);
            return;
        }
        List<FmTyphoonRealB> fmTyphoonRealBList = new ArrayList<>();
        List<FmTyphoonForecast> fmTyphoonForecastList = new ArrayList<>();
        List resultList = wrapper.getData().getResultList();

        List<FmTyphoonRealB> byTfbh = fmTyphoonRealBService.getByTfbh(tfid);
        //Map<Date, FmTyphoonRealB> collect = byTfbh.stream().collect(Collectors.toMap(FmTyphoonRealB::getTime, v -> v));
        Map<Date, FmTyphoonRealB> collect = new HashMap<>();
        if(byTfbh!=null&&byTfbh.size()>0){
            for(FmTyphoonRealB fmTyphoonRealB: byTfbh){
                collect.put(fmTyphoonRealB.getTime(),fmTyphoonRealB);
            }
        }

//        long dbTime = byTfbh.getTime().getTime();
        for (Object object : resultList) {
            JSONObject typhoon = JSONUtil.parseObj(JSONUtil.toJsonStr(object));
            String infoStr = typhoon.getStr("info");
            if (StringUtils.isBlank(infoStr)) {
                log.info("获取到的数据info数据为空:{}",infoStr);
                break;
            }
            JSONObject info = JSONUtil.parseObj(infoStr);
            //JSONObject points = JSONUtil.parseObj(info.getStr("points"));
            List points = JSONUtil.toList(info.getStr("points"), List.class);
            for (Object po : points) {
                String pos = JSONUtil.toJsonStr(po);
                //log.info("sssssssssssssssssss:{}",pos);
                JSONObject point = JSONUtil.parseObj(JSONUtil.toJsonStr(po).substring(1,pos.length()-1));
                Date time = point.getDate("time");
                //DateUtil.
                if (collect.containsKey(time)) continue;
                FmTyphoonRealB fmTyphoonRealB = new FmTyphoonRealB();
                fmTyphoonRealB.setTfbh(tfid);
                fmTyphoonRealB.setTime(point.getDate("time"));
                fmTyphoonRealB.setLng(point.getDouble("lng"));
                fmTyphoonRealB.setLat(point.getDouble("lat"));
                fmTyphoonRealB.setStrong(point.getStr("strong"));
                fmTyphoonRealB.setPower(point.getInt("power"));
                fmTyphoonRealB.setSpeed(point.getInt("speed"));
                fmTyphoonRealB.setPressure(point.getInt("pressure"));
                fmTyphoonRealB.setMoveSpeed(point.getInt("movespeed"));
                fmTyphoonRealB.setMoveDir(point.getStr("movedirection"));
                fmTyphoonRealB.setRadius7(point.getInt("radius7"));
                fmTyphoonRealB.setRadius10(point.getInt("radius10"));
                fmTyphoonRealB.setRadius12(point.getInt("radius12"));
//                log.info("FmTyphoonRealB:{}",fmTyphoonRealB);
                fmTyphoonRealBList.add(fmTyphoonRealB);
                //预报点数据
                List forecast = point.get("forecast", List.class);
                for (Object fore : forecast) {
                    JSONObject fObject = (JSONObject)fore;
                    FmTyphoonForecast fmTyphoonForecast = new FmTyphoonForecast();
                    fmTyphoonForecast.setTm(fObject.getStr("tm"));
                    fmTyphoonForecast.setTfbh(tfid);
                    fmTyphoonForecast.setTime(point.getDate("time"));
                    fmTyphoonForecast.setPointsJson(fObject.getStr("forecastpoints"));
                    fmTyphoonForecastList.add(fmTyphoonForecast);
//                    log.info("FmTyphoonForecast:{}",fmTyphoonForecast);
                }
            }
        }

        //入库实时数据
        fmTyphoonRealBService.saveOrUpdateBatch(fmTyphoonRealBList);
        log.info("台风实时点数据入库条数：{}",fmTyphoonRealBList.size());
        //入库预报数据
        fmTyphoonForecastService.saveOrUpdateBatch(fmTyphoonForecastList);
        log.info("台风预报点数据入库条数：{}",fmTyphoonForecastList.size());
    }

    /**
     * 获取
     * @param startYear
     * @param sourceCode
     * @return
     */
    private List<FmTyphoonB> getHistoryTyphoons(int startYear, String sourceCode){
        Calendar calendar = Calendar.getInstance();
        LambdaQueryWrapper<FmTyphoonB> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(FmTyphoonB::getTfbh,String.valueOf(startYear));
        queryWrapper.le(FmTyphoonB::getTfbh,calendar.get(Calendar.YEAR)+"999");
        queryWrapper.ne(FmTyphoonB::getTfbh,sourceCode);
        return this.list(queryWrapper);

    }

    private List<FmTyphoonScoreVO> findSimilarTyphoons(FmTyphoonB typhoon,List<FmTyphoonB> historyTyphoons,
                                                       int points,int distance,int score) throws ExecutionException, InterruptedException {
        //获取当前台风路径信息
        List<FmTyphoonRealB> fmTyphoonRealBList = fmTyphoonRealBService.getByTfbh(typhoon.getTfbh());

        List<Future> futures = new ArrayList<>();
        historyTyphoons.forEach(historyTyphoon->{

            TyphoonSimilarMatchTask similarMatchTask = new TyphoonSimilarMatchTask(fmTyphoonRealBList,historyTyphoon,score,distance,points);
            Future<FmTyphoonScoreVO> future = executorService.submit(similarMatchTask);
            futures.add(future);
        });

        List<FmTyphoonScoreVO> result = new ArrayList<>();
        for(Future<FmTyphoonScoreVO> future:futures){
            FmTyphoonScoreVO typhoonScoreVO = future.get();
            Boolean similar = typhoonScoreVO.getSimilar();
            if(similar) result.add(typhoonScoreVO);

        }
        return result;

    }


    private class TyphoonSimilarMatchTask implements Callable<FmTyphoonScoreVO> {

        private int minScore;

        private int distance;

        private int points;

        private List<FmTyphoonRealB> curTyphoonPath;

        private FmTyphoonB matchTyphoon;

        public TyphoonSimilarMatchTask(List<FmTyphoonRealB> curTyphoonPath,
                                       FmTyphoonB matchTyphoon,
                                       int score,
                                       int distance,
                                       int points){
            this.minScore = score;
            this.distance = distance;
            this.points = points;
            this.curTyphoonPath = curTyphoonPath;
            this.matchTyphoon = matchTyphoon;

        }
        @Override
        public FmTyphoonScoreVO call() throws Exception {
            FmTyphoonScoreVO fmTyphoonScoreVO = new FmTyphoonScoreVO();
            fmTyphoonScoreVO.setFmTyphoon(matchTyphoon);
            List<FmTyphoonRealB> matchTyphoonPath = fmTyphoonRealBService.getByTfbh(matchTyphoon.getTfbh());
            long score = computeSimilarScore(curTyphoonPath,matchTyphoonPath,points,distance);
            log.debug("{} similar typhoon score: {}",matchTyphoon.getName(),score);
            fmTyphoonScoreVO.setScore(score);
            if(score>=minScore){
                fmTyphoonScoreVO.setSimilar(Boolean.TRUE);
            }
            return fmTyphoonScoreVO;
        }
    }

    /**
     * 计算台风路径相似度分值
     * @param typhoonPath1
     * @param typhoonPath2
     * @param points
     * @param d
     * @return
     */
    private long computeSimilarScore(List<FmTyphoonRealB> typhoonPath1,List<FmTyphoonRealB> typhoonPath2,int points,int d){
        long score = 0;
        int size =0;
        List<Long> scores = new ArrayList<>();
        if(typhoonPath1 == null || typhoonPath1.size() ==0 || typhoonPath2 == null || typhoonPath2.size() == 0){
            return score;
        }
        for(FmTyphoonRealB p1:typhoonPath1){
            double distance = d;
            for(FmTyphoonRealB p2:typhoonPath2){
                double dis = GeoToolsUtil.getDistance(p1.getLat(),p1.getLng(),p2.getLat(),p2.getLng());
                if(distance>dis){
                    distance = dis;
                }
            }
            if(distance<d){
                size++;
            }
            log.debug("similar typhoon distance: {}",distance);
            long sc =Math.round((1- Math.pow( distance/d,2))*100);
            scores.add(sc);

        }
        double sum=0;
        log.debug("similar points:{}",size);
        if(size >=points) {
            for(double sc:scores)
            {
                sum=sum+sc;
            }
            score=Math.round(sum/scores.size());;
        }
        return score;


    }
}
