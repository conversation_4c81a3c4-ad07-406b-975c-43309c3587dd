package cn.piesat.data.making.server.fegin;

import cn.piesat.data.making.server.dto.generate.ManualExecutionDTO;
import cn.piesat.webconfig.response.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "schedule-ncclip-exec",url = "${schedule.alg.server-url}")
public interface AlgorithmExecClient {

    @PostMapping("/api/external/manualExecution")
    Boolean manualExecution(@RequestBody @Validated ManualExecutionDTO manualExecutionDTO);
}
