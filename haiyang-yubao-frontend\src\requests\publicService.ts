/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-28 10:10:00
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-10 16:56:28
 * @FilePath: /hainan-jianzai-web/src/requests/publicService.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { multiply } from 'lodash'
import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)
const PublicService = {
  // 获取专项预报分页
  getForecastPage(params: any) {
    return axiosInstance({
      url: '/forecast/pageList',
      method: 'get',
      params
    })
  },
  /**
   * 保存专项预报数据
   * @param data
   * @returns
   */
  saveInfoOfForecast(data: any) {
    return axiosInstance({
      url: '/forecast/saveInfo',
      method: 'post',
      data
    })
  },
  /**
   * 修改专项预报数据
   * @param data
   * @returns
   */
  updateInfoOfForecast(data: any) {
    return axiosInstance({
      url: '/forecast/updateInfo',
      method: 'post',
      data
    })
  },
  /**
   * 通过Id修改专项预报状态
   * @param id
   * @param status
   * @returns
   */
  updateStatusById(id: string, status: number) {
    return axiosInstance({
      url: `/forecast/updateStatus/${id}/${status}`,
      method: 'GET'
    })
  },
  /**
   * 通过Id获取专项预报详情
   * @param id
   * @returns
   */
  getForecastInfoById(id: string) {
    return axiosInstance({
      url: `/forecast/info/${id}`,
      method: 'GET'
    })
  },
  /**
   * 获取海啸产品分页
   * @param params
   * @returns
   */
  getTsunamiProductPage(params: any) {
    return axiosInstance({
      url: '/tsunamiProduct/page',
      method: 'get',
      params
    })
  },
  /**
   * 获取海啸产品列表
   * @param params
   * @returns
   */
  getTsunamiProductList(params: any) {
    return axiosInstance({
      url: '/tsunamiProduct/list',
      method: 'get',
      params
    })
  },
  /**
   * 下载海啸产品单文件
   * @param id
   * @returns
   */
  downloadTsunamiProduct(id: string) {
    return axiosInstance({
      url: `/tsunamiProduct/download/${id}`,
      method: 'get',
      responseType: 'blob'
    })
  },
  /**
   * 批量下载
   * @param ids
   * @returns
   */
  multipleDownloadTsunamiProduct(params: any) {
    return axiosInstance({
      url: `/tsunamiProduct/batch/download`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取海洋设施类型(图层叠加列表)
   */
  getOceanFacilityList() {
    return axiosInstance({
      url: '/oceanFacility/typeList',
      method: 'get'
    })
  },

  /**
   * 根据海洋设施类型查询海洋设施列表
   * @param type
   * @returns
   */
  getOceanFacilityListbyType(params: any) {
    return axiosInstance({
      url: `/oceanFacility/list`,
      method: 'GET',
      params
    })
  }
}
export default PublicService
