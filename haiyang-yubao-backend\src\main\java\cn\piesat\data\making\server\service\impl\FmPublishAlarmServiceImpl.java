package cn.piesat.data.making.server.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.piesat.data.making.server.dao.FmPublishAlarmDao;
import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3.xlink.Simple;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class FmPublishAlarmServiceImpl extends ServiceImpl<FmPublishAlarmDao, FmPublishAlarm> implements FmPublishAlarmService {

    @Resource
    private FmPublishAlarmDao fmPublishAlarmDao;

    @Resource
    private FmGraphicRecordBService fmGraphicRecordBService;

    @Autowired
    private FmTyphoonCompletionBService typhoonCompletionBService;
    @Autowired
    private FmTyphoonRealBService typhoonRealBService;
    @Autowired
    private FmTyphoonBService fmTyphoonBService;

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

    @Autowired
    private StormSurgeAlarmService stormSurgeAlarmServiceImpl;

    /**
     * 推送海浪警报数据
     * @param seaWaveAlarm
     */
    @Override
    public void publishAlarm(SeaWaveAlarm seaWaveAlarm) {
        //SeaWaveAlarm转换为FmPublishAlarm
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy'年'MM'月'dd'日'HH'时'");
        FmPublishAlarm fmPublishAlarm = new FmPublishAlarm();
        Long alarmLevel = seaWaveAlarm.getAlarmLevel();
        String level = "I";
        String color = "红色";
        int warningType = 2;
        if (alarmLevel == 1) {
            level = "I";
            color = "红色";
            warningType = 2;
        } else if (alarmLevel == 2) {
            level = "II";
            color = "橙色";
            warningType = 2;
        } else if (alarmLevel == 3) {
            level = "III";
            warningType = 2;
            color = "黄色";
        } else if (alarmLevel == 4) {
            level = "IV";
            warningType = 2;
            color = "蓝色";
        } else if (alarmLevel == 5) {
            level = "null";
            color = "null";
            warningType = 4;
        }
        fmPublishAlarm.setTitle(seaWaveAlarm.getTitle());
        fmPublishAlarm.setCode(seaWaveAlarm.getNumber());
        fmPublishAlarm.setLevel(level);
        fmPublishAlarm.setColor(color);


        List<String> contentStr = assemblyContent(seaWaveAlarm, color);


        fmPublishAlarm.setContent(contentStr.toString());
        fmPublishAlarm.setImg(seaWaveAlarm.getAlarmImages());
        fmPublishAlarm.setImgTitle("附：南海海浪预报图");
        fmPublishAlarm.setAlarmType("wave");
        fmPublishAlarm.setWarningType(warningType);
        fmPublishAlarm.setTimePublish(seaWaveAlarm.getReleaseTime());
        fmPublishAlarm.setTime(sdf.format(seaWaveAlarm.getReleaseTime()));
        fmPublishAlarmDao.insert(fmPublishAlarm);

        //在这里增加发送海浪警报邮件
        //add by wangxin

        String wordFileName = seaWaveAlarm.getWordFilePath().substring(seaWaveAlarm.getWordFilePath().lastIndexOf("/")+1);
        fmPushResultServiceImpl.addFmPushResList("","SeaWaveAlarm",seaWaveAlarm.getAlarmContent(),seaWaveAlarm.getWordFilePath(),wordFileName,"/seaWaveAlarm/downloadDoc/"+seaWaveAlarm.getId()+".docx");
    }

    /**
     * 拼接海浪content
     * @param seaWaveAlarm
     * @param color
     * @return
     */
    private List<String> assemblyContent(SeaWaveAlarm seaWaveAlarm,String color){
        List<String> contentStr = new ArrayList<String>();
        String typhoonDesc = "";
        contentStr.add("\"海南省海洋预报台根据《海南省海洋灾害应急预案》发布" + seaWaveAlarm.getTitle()  +"。\"");
        if (seaWaveAlarm.getSourceType() == 2) {//台风
        //查询台风信息
            FmTyphoonB typhoon = fmTyphoonBService.getInfo(seaWaveAlarm.getTyphoonNo());
            //查询台风路径信息
            FmTyphoonRealB typhoonReal = typhoonRealBService.getInfo(seaWaveAlarm.getTyphoonNo(), seaWaveAlarm.getTyphoonTime());
            //查询台风预计位置信息
            FmTyphoonCompletionB typhoonCompletionB = typhoonCompletionBService.getInfo(seaWaveAlarm.getTyphoonNo(), seaWaveAlarm.getTyphoonTime());
            /**
             * "2501号台风“蝴蝶”（热带风暴级）12日08时中心位于17.1°N，110.3°E，中心气压为990百帕，中心附近最大风力8级（20米/秒），
             * 七级大风半径260km。预计12小时内，该系统将以每小时8公里左右的速度向西北方向移动。",
             */
            if (typhoonCompletionB != null) {
                typhoonDesc = typhoon.getTfbh() + "号台风\"" + typhoon.getName() + "\"(" + typhoonReal.getStrong() + ")" + typhoonReal.getTime().getDay() + "日" + typhoonReal.getTime().getHours()
                        + "中心位于" + typhoonReal.getLat() + "°N，" + typhoonReal.getLng() + "°E，中心气压为" + typhoonReal.getPressure() + "百帕，中心附近最大风力" + typhoonReal.getPower() + "级（" +typhoonReal.getSpeed()
                        + "米/秒），七级大风半径" + typhoonReal.getRadius7() + "km。预计12小时内，" + typhoonCompletionB.getCompletion() + "。";
            } else {
                typhoonDesc = typhoon.getTfbh() + "号台风\"" + typhoon.getName() + "\"(" + typhoonReal.getStrong() + ")" + typhoonReal.getTime().getDay() + "日" + typhoonReal.getTime().getHours()
                        + "中心位于" + typhoonReal.getLat() + "°N，" + typhoonReal.getLng() + "°E，中心气压为" + typhoonReal.getPressure() + "百帕，中心附近最大风力" + typhoonReal.getPower() + "级（" +typhoonReal.getSpeed()
                        + "米/秒），七级大风半径" + typhoonReal.getRadius7() + "km。";
            }


            contentStr.add("\""+typhoonDesc+"\"");
        }
        contentStr.add("\""+seaWaveAlarm.getAlarmContent()+"\"");
        contentStr.add("\""+seaWaveAlarm.getCityAlarmContent() + seaWaveAlarm.getDefenseGuide()+"\"");
        return contentStr;
    }

    /**
     * 推送风暴潮警报数据
     * @param stormSurgeAlarm
     */
    @Override
    public void publishAlarm(StormSurgeAlarm stormSurgeAlarm) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy'年'MM'月'dd'日'HH'时'");
        FmPublishAlarm fmPublishAlarm = new FmPublishAlarm();
        fmPublishAlarm.setTimePublish(stormSurgeAlarm.getReleaseTime());
        fmPublishAlarm.setTitle(stormSurgeAlarm.getTitle());
        fmPublishAlarm.setCode(stormSurgeAlarm.getNumber());
        Integer alarmLevel = stormSurgeAlarm.getAlarmLevel();

        String level = "I";
        String color = "红色";
        int warningType = 2;
        if (alarmLevel == 1) {
            level = "I";
            color = "红色";
            warningType = 2;
        } else if (alarmLevel == 2) {
            level = "II";
            color = "橙色";
            warningType = 2;
        } else if (alarmLevel == 3) {
            level = "III";
            warningType = 2;
            color = "黄色";
        } else if (alarmLevel == 4) {
            level = "IV";
            warningType = 2;
            color = "蓝色";
        } else if (alarmLevel == 5) {
            level = "null";
            color = "null";
            warningType = 4;
        }
        fmPublishAlarm.setLevel(level);
        List<String> strings = assemblyStormContent(stormSurgeAlarm, color);
        fmPublishAlarm.setContent(strings.toString());
        fmPublishAlarm.setWarningType(warningType);
        fmPublishAlarm.setAlarmType("storm");
        fmPublishAlarm.setTime(sdf.format(stormSurgeAlarm.getReleaseTime()));
        fmPublishAlarm.setColor(color);
        String tableStr = assembylTable(stormSurgeAlarm);
        fmPublishAlarm.setTable(tableStr);
        fmPublishAlarmDao.insert(fmPublishAlarm);

        //在这里增加发送风暴潮警报邮件
        //add by wangxin
        String wordFileName = stormSurgeAlarm.getWordFilePath().substring(stormSurgeAlarm.getWordFilePath().lastIndexOf("/")+1);
        fmPushResultServiceImpl.addFmPushResList("","StormSurgeAlarm",stormSurgeAlarm.getAlarmContent(),stormSurgeAlarm.getWordFilePath(),wordFileName,"/stormSurgeAlarm/downloadDoc/"+stormSurgeAlarm.getId()+".docx");
    }

    /**
     * 拼接风暴潮content
     * @param stormSurgeAlarm
     * @param color
     * @return
     */
    private List<String> assemblyStormContent(StormSurgeAlarm stormSurgeAlarm,String color){
        List<String> contentStr = new ArrayList<String>();
        String typhoonDesc = "";
        contentStr.add("海南省海洋预报台根据《海南省海洋灾害应急预案》发布" + stormSurgeAlarm.getTitle() + "（" + color + "）。");
        if (stormSurgeAlarm.getSourceType() == 2) {//台风
            //查询台风信息
            FmTyphoonB typhoon = fmTyphoonBService.getInfo(stormSurgeAlarm.getTyphoonNo());
            //查询台风路径信息
            FmTyphoonRealB typhoonReal = typhoonRealBService.getInfo(stormSurgeAlarm.getTyphoonNo(), stormSurgeAlarm.getTyphoonTime());
            //查询台风预计位置信息
            FmTyphoonCompletionB typhoonCompletionB = typhoonCompletionBService.getInfo(stormSurgeAlarm.getTyphoonNo(), stormSurgeAlarm.getTyphoonTime());

            /**
             * "2501号台风“蝴蝶”（热带风暴级）12日08时中心位于17.1°N，110.3°E，中心气压为990百帕，中心附近最大风力8级（20米/秒），
             * 七级大风半径260km。预计12小时内，该系统将以每小时8公里左右的速度向西北方向移动。",
             */
            if (typhoonCompletionB != null) {
                typhoonDesc = typhoon.getTfbh() + "号台风\"" + typhoon.getName() + "\"(" + typhoonReal.getStrong() + ")" + typhoonReal.getTime().getDay() + "日" + typhoonReal.getTime().getHours()
                        + "中心位于" + typhoonReal.getLat() + "°N，" + typhoonReal.getLng() + "°E，中心气压为" + typhoonReal.getPressure() + "百帕，中心附近最大风力" + typhoonReal.getPower() + "级（" +typhoonReal.getSpeed()
                        + "米/秒），七级大风半径" + typhoonReal.getRadius7() + "km。预计12小时内，" + typhoonCompletionB.getCompletion() + "。";
            } else {
                typhoonDesc = typhoon.getTfbh() + "号台风\"" + typhoon.getName() + "\"(" + typhoonReal.getStrong() + ")" + typhoonReal.getTime().getDay() + "日" + typhoonReal.getTime().getHours()
                        + "中心位于" + typhoonReal.getLat() + "°N，" + typhoonReal.getLng() + "°E，中心气压为" + typhoonReal.getPressure() + "百帕，中心附近最大风力" + typhoonReal.getPower() + "级（" +typhoonReal.getSpeed()
                        + "米/秒），七级大风半径" + typhoonReal.getRadius7() + "km。";
            }
            contentStr.add(typhoonDesc);
        }
        contentStr.add(stormSurgeAlarm.getAlarmContent());
        contentStr.add(stormSurgeAlarm.getCityAlarmContent() + stormSurgeAlarm.getDefenseGuide());
        return contentStr;
    }

    /**
     * 拼接风暴潮table
     * @param stormSurgeAlarm
     * @return
     */
    private String assembylTable(StormSurgeAlarm stormSurgeAlarm){
        String stationWarning = stormSurgeAlarm.getStationWarning();
        //[{"stationName":"三亚","tideTime":1752022200000,"height":123,"warnHeight":0,"levelDesc":"无","regionName":"三亚市","date":"09日","tide":"08:50"},{"stationName":"海口（秀英）","tideTime":1752056580000,"height":145,"warnHeight":0,"levelDesc":"无","regionName":"海口市","date":"09日","tide":"18:23"}]
        JSONArray stationArray = JSONUtil.parseArray(stationWarning);
        JSONArray headerArray = new JSONArray();
        JSONObject header1 = new JSONObject();
        header1.put("title","影响区域");
        header1.put("code","key_1");
        headerArray.add(header1);

        JSONObject header2 = new JSONObject();
        header2.put("title","参考站");
        header2.put("code","key_2");
        headerArray.add(header2);

        JSONObject header3 = new JSONObject();
        header3.put("title","日期");
        header3.put("code","key_3");
        headerArray.add(header3);

        JSONObject header4 = new JSONObject();
        header4.put("title","时间");
        header4.put("code","key_4");
        headerArray.add(header4);

        JSONObject header5 = new JSONObject();
        header5.put("title","高潮位(cm)");
        header5.put("code","key_5");
        headerArray.add(header5);

        JSONObject header6 = new JSONObject();
        header6.put("title","警戒潮位*(cm)");
        header6.put("code","key_6");
        headerArray.add(header6);

        JSONObject header7 = new JSONObject();
        header7.put("title","预警级别");
        header7.put("code","key_1");
        headerArray.add(header7);

        JSONArray bodyArray = new JSONArray();
        for (int i = 0; i < stationArray.size(); i++) {
            JSONObject stationObj = stationArray.getJSONObject(i);
            JSONObject bodyObj = new JSONObject();
            bodyObj.put("key_1",stationObj.getStr("regionName"));
            bodyObj.put("key_2",stationObj.getStr("stationName"));
            bodyObj.put("key_3",stationObj.getStr("date"));
            bodyObj.put("key_4",stationObj.getStr("tide"));
            bodyObj.put("key_5",String.valueOf(stationObj.getInt("height")));
            bodyObj.put("key_6",String.valueOf(stationObj.getInt("warnHeight")));
            Integer level = stationObj.getInt("level");
            String color = "红色";
            if (level == 1) {
                color = "红色";
            } else if (level == 2) {
                color = "橙色";
            } else if (level == 3) {
                color = "黄色";
            } else if (level == 4) {
                color = "蓝色";
            } else if (level == 5) {
                color = "灰色";
            }
            bodyObj.put("key_7",color);
            bodyArray.add(bodyObj);
        }
        String headerStr = headerArray.toString();
        String bodyStr = bodyArray.toString();
        String tableStr = "/'header/':" + headerStr+",/'body/':"+bodyStr;
        return tableStr;
    }
}
