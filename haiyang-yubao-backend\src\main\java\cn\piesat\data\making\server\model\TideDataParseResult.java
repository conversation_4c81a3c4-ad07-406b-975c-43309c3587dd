package cn.piesat.data.making.server.model;

import cn.piesat.data.making.server.entity.TideDailyData;
import cn.piesat.data.making.server.entity.TideDailyHourData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class TideDataParseResult {

    /**
     * 每日逐日潮高数据列表
     */
    List<TideDailyHourData> tideDailyHourDataListList = new ArrayList<>();

    List<TideDailyData> tideDailyDataList = new ArrayList<>();

}
