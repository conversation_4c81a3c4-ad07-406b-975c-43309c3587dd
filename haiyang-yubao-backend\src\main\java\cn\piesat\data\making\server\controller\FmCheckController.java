package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.FmGraphicRecordBDTO;
import cn.piesat.data.making.server.service.FmCheckService;
import cn.piesat.data.making.server.vo.FmGraphicRecordBVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 质检数据控制层
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:30
 */
@RestController
@RequestMapping("checkData")
public class FmCheckController {

    @Resource
    private FmCheckService fmCheckService;
    /**
     * 根据预报任务Id查询列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     *
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "图形记录管理", operateType = OperateType.SELECT)
    public List<FmGraphicRecordBVO> getList(@RequestParam(required = false) Date startTime,
                                            @RequestParam(required = false) Date endTime,
                                            @RequestParam(required = false) String type) {
        fmCheckService.selectByProductIdAndTimeRange(type,startTime,endTime);
        return null;
    }

}
