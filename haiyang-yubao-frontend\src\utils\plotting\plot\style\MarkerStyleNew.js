/*
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-17 17:55:05
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-02-05 15:32:50
 * @FilePath: \hainan-jianzai-web\src\utils\plotting\plot\style\MarkerStyleNew.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import FTStyle from './Style'
import Style from 'ol/style/Style'
import Icon from 'ol/style/Icon'
import Text from 'ol/style/Text'
import img from 'src/assets/images/marker-begin.png'
import img1 from 'src/assets/images/weather.png'
import Circle from 'ol/style/Circle.js'
import Fill from 'ol/style/Fill.js'
import Stroke from 'ol/style/Stroke.js'
class MarkerStyle extends FTStyle {
  /**
   * @class MarkerStyle
   * @classdesc 点类样式
   * @extends {FTStyle}
   * <AUTHOR>
   * @constructs
   */
  constructor(img, rotation = 0, obj = null) {
    super()
    this.src = ''
    this.offset = [0, 0]
    this.swh = ''
    this.waveCycle = ''
    this.rotation = rotation
    this.type = ''
    if (obj) {
      this.swh = obj.swh
      this.waveCycle = obj.waveCycle
      this.type = '主波向'
    }
    this._style = {
      image: {
        // --ol.Image 的全部属性
        icon: {
          src: img,
          offset: this.offset,
          size: [30, 30],
          opacity: 1,
          scale: 1,
          anchor: [0.5, 0.5],
          rotation: rotation
        }
      }
    }
  }
  parse() {
    let image = null

    if (this._style.image) {
      if (this._style.image.icon) {
        image = new Icon(this._style.image.icon)
      }
    }
    const aa = new Style({
      image: image
    })
    if (this.type === '主波向') {
      // 右侧波周期
      const textStyle1 = new Style({
        text: new Text({
          text: this.waveCycle,
          offsetX: Math.cos(this.rotation) * 16,
          offsetY: Math.sin(this.rotation) * 16,
          font: 'bold 10px Calibri,sans-serif',
          fill: new Fill({
            color: 'black'
          }),
          stroke: new Stroke({
            color: 'white',
            width: 1
          })
        })
      })
      // 左侧有效波高
      const textStyle2 = new Style({
        text: new Text({
          text: this.swh,
          offsetX: Math.cos(this.rotation) * -16,
          offsetY: Math.sin(this.rotation) * -16,
          font: 'bold 10px Calibri,sans-serif',
          fill: new Fill({
            color: 'black'
          }),
          stroke: new Stroke({
            color: 'white',
            width: 1
          })
        })
      })
      return [aa, textStyle1, textStyle2]
    } else {
      return aa
    }
  }
}
export default MarkerStyle
