package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanSmallShallowseauoyRtDao;
import cn.piesat.data.making.server.entity.OceanSmallShallowseauoyRt;
import cn.piesat.data.making.server.service.OceanSmallShallowseauoyRtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 浅海小型浮标数据-实时数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanSmallShallowseauoyRtServiceImpl extends ServiceImpl<OceanSmallShallowseauoyRtDao, OceanSmallShallowseauoyRt>
        implements OceanSmallShallowseauoyRtService {
}





