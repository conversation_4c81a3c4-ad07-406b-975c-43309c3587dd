package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.vo.FmPublicProductTemplateVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicProductTemplateServiceTest {

    @Autowired
    private FmPublicProductTemplateService fmPublicProductTemplateServiceImpl;

    @Test
    public void testGetById(){
        FmPublicProductTemplateVO vo = fmPublicProductTemplateServiceImpl.getById(1944425734357504002L);

        Assert.assertNotNull(vo);
    }

    @Test
    public void testGetByTemplateCode(){
        FmPublicProductTemplateVO vo = fmPublicProductTemplateServiceImpl.getByTemplateCode("TWDCXX");

        Assert.assertNotNull(vo);
    }

    @Test
    public void testSave(){
        FmPublicProductTemplateVO vo = new FmPublicProductTemplateVO();
        vo.setPublicType("1");

        fmPublicProductTemplateServiceImpl.save(vo);
    }

    @Test
    public void testUpdate(){
        FmPublicProductTemplateVO vo = new FmPublicProductTemplateVO();
        vo.setId(1945044893109858305L);
        vo.setPublicType("2");
        vo.setStatus(true);

        fmPublicProductTemplateServiceImpl.update(vo);
    }

    @Test
    public void testGetListByParam(){
        FmPublicProductTemplateVO vo = new FmPublicProductTemplateVO();
        vo.setStatus(true);
        vo.setPublicType("1");
        List<FmPublicProductTemplateVO> resList =fmPublicProductTemplateServiceImpl.getListByParam(vo);

        Assert.assertNotNull(resList);
    }
}
