package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.vo.ForecastProductRecordVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class ForecastProductRecordServiceTest {

    @Resource
    private ForecastProductRecordService forecastProductRecordServiceImpl;


    @Test
    public void testSave(){
        List<ForecastProductRecordVO> list = forecastProductRecordServiceImpl.save();

        Assert.assertNotNull(list);
    }

    @Test
    public void testDownload(){
        forecastProductRecordServiceImpl.download(null);
    }

    @Test
    public void testRelease(){
        forecastProductRecordServiceImpl.release();
    }
}
