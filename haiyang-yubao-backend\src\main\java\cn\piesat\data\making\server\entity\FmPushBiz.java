package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

@TableName("fm_push_biz_b")
public class FmPushBiz implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("biz_name")
    private String bizName;

    @TableField("push_channel")
    private String pushChannel;

    @TableField("push_freq")
    private String pushFreq;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getPushChannel() {
        return pushChannel;
    }

    public void setPushChannel(String pushChannel) {
        this.pushChannel = pushChannel;
    }

    public String getPushFreq() {
        return pushFreq;
    }

    public void setPushFreq(String pushFreq) {
        this.pushFreq = pushFreq;
    }
}
