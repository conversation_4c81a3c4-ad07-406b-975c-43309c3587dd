package cn.piesat.data.making.server.dto;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 图形模板表DTO类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:39
 */
public class FmGraphicTemplateBDTO implements Serializable {

    private static final long serialVersionUID = 716347357957571329L;

    public interface Save {
    }

    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 上纬度
     */
    private Double leftLongitude;
    /**
     * 下纬度
     */
    private Double leftLatitude;
    /**
     * 左经度
     */
    private Double rightLongitude;
    /**
     * 右经度
     */
    private Double rightLatitude;
    /**
     * 标签
     */
    private String tag;
    /**
     * 图片路径
     */
    private String imageUrl;
    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * M模版类型
     */
    private String templateType;

    private Long parebtId;
    /**
     * 标签
     */
    private List<FmGraphicTmplateSignDTO> sign;
    /**
     * 工具
     */
    private List<FmGraphicTemplateToolsDTO> tools;

    /**
     * 下级模版
     */
    private List<FmGraphicTemplateBDTO> fmGraphicTemplateBDTOS;

    public Long getParebtId() {
        return parebtId;
    }

    public void setParebtId(Long parebtId) {
        this.parebtId = parebtId;
    }

    public List<FmGraphicTemplateBDTO> getFmGraphicTemplateBDTOS() {
        return fmGraphicTemplateBDTOS;
    }

    public void setFmGraphicTemplateBDTOS(List<FmGraphicTemplateBDTO> fmGraphicTemplateBDTOS) {
        this.fmGraphicTemplateBDTOS = fmGraphicTemplateBDTOS;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Double getLeftLongitude() {
        return leftLongitude;
    }

    public void setLeftLongitude(Double leftLongitude) {
        this.leftLongitude = leftLongitude;
    }

    public Double getLeftLatitude() {
        return leftLatitude;
    }

    public void setLeftLatitude(Double leftLatitude) {
        this.leftLatitude = leftLatitude;
    }

    public Double getRightLongitude() {
        return rightLongitude;
    }

    public void setRightLongitude(Double rightLongitude) {
        this.rightLongitude = rightLongitude;
    }

    public Double getRightLatitude() {
        return rightLatitude;
    }

    public void setRightLatitude(Double rightLatitude) {
        this.rightLatitude = rightLatitude;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<FmGraphicTmplateSignDTO> getSign() {
        return sign;
    }

    public void setSign(List<FmGraphicTmplateSignDTO> sign) {
        this.sign = sign;
    }

    public List<FmGraphicTemplateToolsDTO> getTools() {
        return tools;
    }

    public void setTools(List<FmGraphicTemplateToolsDTO> tools) {
        this.tools = tools;
    }
}
