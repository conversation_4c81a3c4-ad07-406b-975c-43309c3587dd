@mixin keyframes($animationName) {
  @keyframes #{$animationName} {
    @content;
  }
}

@mixin BG($url) {
  background-image: url($url);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

@mixin centerX() {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

@mixin centerY() {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

@mixin center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin textOver() {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin scrollBase() {
  overflow-x: hidden;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 120px;
  }
  &::-webkit-scrollbar-thumb {
    background: #017b7a;
    border-radius: 3px;
  }
}

@mixin scrollHiddenBase() {
  overflow-x: hidden;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}
