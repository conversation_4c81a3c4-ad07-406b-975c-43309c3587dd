package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmGraphicTemplateToolsDao;
import cn.piesat.data.making.server.dto.FmGraphicTemplateToolsDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateTools;
import cn.piesat.data.making.server.entity.FmGraphicTools;
import cn.piesat.data.making.server.dao.FmGraphicToolsDao;
import cn.piesat.data.making.server.mapper.FmGraphicTemplateToolsMapper;
import cn.piesat.data.making.server.mapper.FmGraphicToolsMapper;
import cn.piesat.data.making.server.service.FmGraphicToolsService;
import cn.piesat.data.making.server.vo.FmGraphicToolsVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:39
 */
@Service
@Slf4j
public class FmGraphicToolsServiceImpl extends ServiceImpl<FmGraphicToolsDao, FmGraphicTools> implements FmGraphicToolsService {

    @Resource
    private FmGraphicToolsDao fmGraphicToolsDao;
    @Resource
    private FmGraphicTemplateToolsDao fmGraphicTemplateToolsDao;

    @Override
    public List<FmGraphicToolsVO> getList(String type) {
        return FmGraphicToolsMapper.INSTANCE.entityListToVoList(fmGraphicToolsDao.selectAll());
    }

    @Override
    public void saveByTemplateId(List<FmGraphicTemplateToolsDTO> list, Long templateId) {
        if(fmGraphicTemplateToolsDao.countByTemplateId(templateId) > 0){
            fmGraphicTemplateToolsDao.deleteByTemplateId(templateId);
        }
        for (FmGraphicTemplateTools fmGraphicTemplateTools : FmGraphicTemplateToolsMapper.INSTANCE.dtoListToEntityList(list)) {
            fmGraphicTemplateTools.setTemplateId(templateId);
            fmGraphicTemplateToolsDao.insert(fmGraphicTemplateTools);
        }
    }
}
