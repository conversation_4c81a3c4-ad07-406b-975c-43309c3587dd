package cn.piesat.data.making.server.utils;

import okhttp3.*;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

public class HttpClientUtil {

//    private static final OkHttpClient client = new OkHttpClient();
    private static  OkHttpClient client;
    //@PostConstruct
    private static void init(){
        TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        SSLContext sslContext = null;
        try {
            sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        } catch (Exception e) {
            //log.error("Install the all-trusting trust manager error:{}", e.getMessage());
            System.out.println("Install the all-trusting trust manager error:" + e.getMessage());
        }

        client = new OkHttpClient.Builder()
                .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                .hostnameVerifier((hostname, session) -> true)
                .build();
    }

    /**
     * 发送POST请求
     *
     * @param url      请求URL
     * @param jsonBody 请求体（JSON格式）
     * @param appKey   AppKey
     * @param appParam AppParam
     * @param appSign  AppSign
     * @return 响应结果
     * @throws IOException 如果请求失败
     */
    public static String post(String url, String jsonBody, String appKey, String appParam, String appSign) throws IOException {
        if (null == client) {
            init();
        }
        // 创建请求体
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, JSON);

        // 创建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("appkey", appKey)
                .addHeader("appParam", appParam)
                .addHeader("appSign", appSign)
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body().string();
        }
    }

    public static String md5(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(str.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}