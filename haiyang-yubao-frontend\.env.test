###
 # @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 # @Date: 2024-10-11 11:07:21
 # @LastEditors: 樊海玲 <EMAIL>
 # @LastEditTime: 2025-04-23 10:55:45
 # @FilePath: \hainan-jianzai-web\.env.development
 # @Description:
 #
 # Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
###
VITE_Base_Url='/'
VITE_Busi_Url=''

VITE_Host_Url='https://*************:80'

### security-passport-server
VITE_AUTH_BASE_URL='/api/auth'
VITE_AUTH_BASE_API_URL='https://*************:80/api/auth'

### sys-api-server
VITE_SYS_BASE_URL='/api/basic'
VITE_SYS_BASE_API_URL='https://*************:80/api/basic'

### sys-admin-api
VITE_SYS_ADMIN_URL='/api/sysadmin'
VITE_SYS_ADMIN_API_URL='https://*************:80/api/sysadmin'

### data-scrap-api
VITE_DATA_SCRAP_BASE_URL="/api/dataScrap"
VITE_DATA_SCRAP_BASE_API_URL='https://*************:80/api/dataScrap'

### product-push-api
VITE_PRODUCT_PUSH_BASE_URL="/api/push"
VITE_PRODUCT_PUSH_BASE_API_URL='https://*************:80/api/push'

### security-ucenter-server
VITE_PERS_BASE_URL="/api/pers"
VITE_PERS_BASE_API_URL="https://*************:80/api/pers"

VITE_MONITOR_BASE_URL="/api/monitor"
VITE_MONITOR_BASE_API_URL="https://*************:80/api/monitor"

### schedule-api-server
VITE_DISPATCH_BASE_URL='/api/schedule'
VITE_DISPATCH_BASE_API_URL='https://*************:80/api/schedule'

###product-push-api
VITE_BASE_PRODUCT_URL = '/api/push'
VITE_BASE_PRODUCT_API_URL = "https://120.46.50.11:80/push/api/push"

###data-scrap-mail
VITE_BASE_EMAIL_URL = '/api/mail'
VITE_BASE_EMAIL_API_URL = 'https://*************:80/api/mail'

VITE_FORECAST_BASE_URL = '/api/data'
# VITE_FORECAST_BASE_API_URL='http://10.2.27.58:8080'
VITE_FORECAST_BASE_API_URL='https://*************:80/api/data'

VITE_PRODUCT_BASE_URL = '/api/product'
VITE_PRODUCT_BASE_API_URL='https://*************:80/api/product'

VITE_MICROBLOG_URL='/api/weibo'
VITE_MICROBLOG_API_RUL='https://*************:80/api/weibo'

VITE_TIF_BASE_API_URL='https://10.2.25.87:80'


VITE_ETTP_SERVICE_BASE_API ='/api/meteo_ettp'
VITE_ETTP_SERVICE_BASE_API_URL='https://*************:80/api/meteo_ettp'

VITE_ETTP_BASE_API ='/api/ettp'
VITE_ETTP_BASE_API_URL='https://*************:80/api/ettp'

