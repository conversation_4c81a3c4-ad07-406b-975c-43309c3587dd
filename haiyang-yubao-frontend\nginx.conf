server {
    listen      80;
    server_name  localhost;
    root /var/www/production;
    client_max_body_size 100m;

    location / {
        try_files $uri $uri/ @router;
        # index index.html index.htm;
    }
    #对应上面的@router，主要原因是路由的路径资源并不是一个真实的路径，所以无法找到具体的文件
    #因此需要rewrite到index.html中，然后交给路由在处理请求资源
    location @router {
        rewrite ^.*$ /index.html last;
    }
  location /api/auth {
	 proxy_pass http://**********:26081/;
  }
  location /api/basic {
	 proxy_pass http://**********:23080/;
  }
  location /api/sysadmin {
	 proxy_pass http://**********:25080/;
  }
  location /api/dataScrap {
         proxy_pass http://**********:28080/;
  }
  location /api/pers {
         proxy_pass http://**********:2608/;
  }
 location /api/schedule {
         proxy_pass http://**********:38080/;
  }
  location /api/push {
         proxy_pass http://************/push/api/push/;
  }
 location /api/mail {
         proxy_pass http://************/push/api/mail/;
  }
  location /api/data {
         proxy_pass http://**********:38081/;
  }
  location /api/product {
         proxy_pass http://**********:38081/;
  }



}
