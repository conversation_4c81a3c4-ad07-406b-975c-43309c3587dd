2025-09-24 15:49:51.417 [main] DEBUG c.p.webconfig.filter.jsonparam.JsonParameterFilter - Filter 'jsonParameterFilter' configured for use
2025-09-24 15:50:29.227 [http-nio-8080-exec-2] DEBUG c.p.d.m.server.dao.ForecastTemplateDao.selectList - ==>  Preparing: SELECT id,code,name,forecast_type,status,sort,create_user_id,create_user,create_time,update_user_id,update_user,update_time FROM fm_forecast_template_b WHERE (status = ?) ORDER BY sort ASC,create_time ASC
2025-09-24 15:50:29.446 [http-nio-8080-exec-2] DEBUG c.p.d.m.server.dao.ForecastTemplateDao.selectList - ==> Parameters: true(Boolean)
2025-09-24 15:50:29.505 [http-nio-8080-exec-2] DEBUG c.p.d.m.server.dao.ForecastTemplateDao.selectList - <==      Total: 6
2025-09-24 15:50:29.516 [http-nio-8080-exec-2] DEBUG c.p.data.making.server.dao.AreaTypeDao.selectList - ==>  Preparing: SELECT id,code,name,sort,create_user_id,create_user,create_time,update_user_id,update_user,update_time FROM fm_area_type_b ORDER BY sort ASC
2025-09-24 15:50:29.518 [http-nio-8080-exec-2] DEBUG c.p.data.making.server.dao.AreaTypeDao.selectList - ==> Parameters: 
2025-09-24 15:50:29.559 [http-nio-8080-exec-2] DEBUG c.p.data.making.server.dao.AreaTypeDao.selectList - <==      Total: 10
2025-09-24 15:50:29.643 [http-nio-8080-exec-2] DEBUG c.p.webconfig.web.interceptor.TimeZoneInterceptor - remove request Timezone...
2025-09-24 15:51:00.021 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:51:00.023 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:51:00.062 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:52:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:52:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:52:00.072 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:53:00.002 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:53:00.003 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:53:00.044 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:54:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:54:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:54:00.087 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:55:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:55:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:55:03.750 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:56:00.002 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:56:00.002 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:56:00.263 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:57:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:57:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:57:00.052 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:58:00.007 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:58:00.007 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:58:01.008 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 15:59:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 15:59:00.007 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 15:59:00.048 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:00:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:00:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:00:00.118 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:01:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:01:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:01:00.051 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:02:00.009 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:02:00.009 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:02:00.054 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:03:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:03:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:03:00.061 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:04:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:04:00.014 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:04:15.876 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:05:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:05:00.007 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:05:00.048 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:06:00.008 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:06:00.009 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:06:00.048 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:07:00.002 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:07:00.003 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:07:00.053 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:08:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:08:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:08:01.468 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:09:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:09:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:24:07.361 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:24:07.373 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:24:07.552 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:25:00.008 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:25:00.008 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:25:00.049 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:26:00.015 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:26:00.016 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:26:00.058 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:27:00.005 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:27:00.005 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:27:00.047 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:28:00.016 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:28:00.016 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:28:00.058 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:29:00.014 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:29:00.015 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:29:00.056 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:30:00.004 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:30:00.005 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:30:00.044 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:31:00.016 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:31:00.016 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:31:01.347 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:32:00.014 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:32:00.015 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:32:00.661 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:33:00.002 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:33:00.002 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:33:00.066 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:34:00.009 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:34:00.009 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:34:00.054 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:35:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:35:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:35:00.055 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:36:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:36:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:36:03.437 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:37:00.014 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:37:00.014 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:37:00.064 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:38:00.003 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:38:00.004 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:38:00.047 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:39:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:39:00.012 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:39:00.058 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:40:00.010 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:40:00.011 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:40:00.050 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:41:00.008 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:41:00.008 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:41:00.046 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:42:00.004 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:42:00.004 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:42:00.265 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:43:00.003 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:43:00.003 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:43:00.042 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:44:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:44:00.006 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:44:00.048 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
2025-09-24 16:45:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==>  Preparing: SELECT id,biz_id,biz_code,biz_name,rec_id,rec_name,push_channel,push_channel_id,push_content,push_msg,push_time,push_flag FROM fm_push_result_b WHERE (push_flag <> ?)
2025-09-24 16:45:00.013 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - ==> Parameters: 1(String)
2025-09-24 16:45:00.052 [scheduling-1] DEBUG c.p.d.making.server.dao.FmPushResultDao.selectList - <==      Total: 0
