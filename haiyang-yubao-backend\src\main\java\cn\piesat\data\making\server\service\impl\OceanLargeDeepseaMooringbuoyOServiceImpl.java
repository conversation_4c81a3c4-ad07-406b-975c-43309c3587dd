package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanLargeDeepseaMooringbuoyODao;
import cn.piesat.data.making.server.entity.OceanLargeDeepseaMooringbuoyO;
import cn.piesat.data.making.server.service.OceanLargeDeepseaMooringbuoyOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 深海大型锚系浮标原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanLargeDeepseaMooringbuoyOServiceImpl extends ServiceImpl<OceanLargeDeepseaMooringbuoyODao, OceanLargeDeepseaMooringbuoyO>
        implements OceanLargeDeepseaMooringbuoyOService {

    @Resource
    private OceanLargeDeepseaMooringbuoyODao oceanLargeDeepseaMooringbuoyODao;

    @Override
    public List<OceanLargeDeepseaMooringbuoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanLargeDeepseaMooringbuoyODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime(String buoyInfoId) {
        return oceanLargeDeepseaMooringbuoyODao.getMaxCreateTime(buoyInfoId);
    }
}





