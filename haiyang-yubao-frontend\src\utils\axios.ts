/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-24 09:53:52
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-27 13:24:50
 * @FilePath: \hainan-jianzai-web\src\utils\axios.ts
 * @Description: axios封装
 * Copyright (c) 2022 by piesat, All Rights Reserved.
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { AuthService } from '../service/authService'
import { get } from '../utils/ioc'

type AxiosInstanceWithData = {
  <T = any>(config: AxiosRequestConfig): Promise<T>
} & AxiosInstance


export default function getAxios(baseURL: string): AxiosInstanceWithData {
  const settings: AxiosRequestConfig = {
    baseURL
  }
  const Axios = axios.create(settings)
  Axios.defaults.timeout = 3 * 60 * 1000 * 10
  //  请求拦截器
  Axios.interceptors.request.use(config => {
    const authInfo = get<AuthService>('auth').getAuth()
    if (authInfo?.token) {
      config.headers.Authorization = `Bearer ${authInfo.token}`
    }
    return config
  })
  // 响应拦截器
  Axios.interceptors.response.use(
    (response: AxiosResponse) => {
      // 存下最近一次操作的时间
      localStorage.setItem('lastRequestMoment', new Date().getTime().toString())
      const contentDisposition = response.headers['content-disposition']
      // 统一打印 token 无效/未传递 等异常
      // 并 reject

      if (response.status == 206 || response.config.responseType == 'blob') {
        if (contentDisposition) {
          return {
            data: response.data,
            fileName: contentDisposition.split(';')[1].split('=')[1]
          }
        }
        return response.data
      }
      if (response.status === 200 && response.data.success) {
        return response.data.data
      } else {
        return Promise.reject(response)
      }
    },
    async (error: any) => {
      // 服务器异常
      if (error.response) {
        const status = error.response.status
        const statusText = error.response.statusText
        console.error(status, statusText)
        // 登录过期，跳转到登录页
        if (error.response.status === 401) {
          location.href = `${config.loginUrl}`
        }
      }
      // eslint-disable-next-line promise/no-promise-in-callback
      return await Promise.reject(error)
    }
  )
  return Axios
}
