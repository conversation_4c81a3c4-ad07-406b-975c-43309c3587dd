package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.StormSurgeMessageDTO;
import cn.piesat.data.making.server.entity.StormSurgeMessage;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;

/**
 * 风暴潮消息制作(StormSurgeMessageB)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:35
 */
public interface StormSurgeMessageService extends IService<StormSurgeMessage> {
    PageResult<StormSurgeMessage> pageList(Integer pageNum, Integer pageSize, Date startTime, Date endTime);

    void checkNumber(String number);

    StormSurgeMessage selectByNumber(String number);

    void release(StormSurgeMessageDTO stormSurgeMessageDTO);

    StormSurgeMessage saveInfo(StormSurgeMessageDTO stormSurgeMessageDTO);

    StormSurgeMessage updateInfo(StormSurgeMessageDTO stormSurgeMessageDTO);
}

