package cn.piesat.data.making.server.dto.generate;

import javax.validation.constraints.NotEmpty;

public class ManualExecutionDTO {
    @NotEmpty(message = "执行脚本路径不能为空！")
    private String execScriptPath;
    @NotEmpty(message = "执行参数文件不能为空！")
    private String execJsonFilePath;

    public String getExecScriptPath() {
        return execScriptPath;
    }

    public void setExecScriptPath(String execScriptPath) {
        this.execScriptPath = execScriptPath;
    }

    public String getExecJsonFilePath() {
        return execJsonFilePath;
    }

    public void setExecJsonFilePath(String execJsonFilePath) {
        this.execJsonFilePath = execJsonFilePath;
    }
}
