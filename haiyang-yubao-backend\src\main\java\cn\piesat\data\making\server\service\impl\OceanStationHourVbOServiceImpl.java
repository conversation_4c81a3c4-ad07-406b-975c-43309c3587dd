package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourVbODao;
import cn.piesat.data.making.server.entity.OceanStationHourVbO;
import cn.piesat.data.making.server.service.OceanStationHourVbOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-能见度-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourVbOServiceImpl extends ServiceImpl<OceanStationHourVbODao, OceanStationHourVbO>
        implements OceanStationHourVbOService {

    @Resource
    private OceanStationHourVbODao oceanStationHourVbODao;

    @Override
    public List<OceanStationHourVbO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourVbODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourVbODao.getMaxCreateTime();
    }
}





