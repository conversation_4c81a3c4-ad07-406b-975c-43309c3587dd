package cn.piesat.data.making.server.entity;

import cn.piesat.data.making.server.config.PgGeometryTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 站点表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_station_b")
public class Station implements Serializable {

    private static final long serialVersionUID = -92250756508290229L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     **/
    @TableField("code")
    private String code;
    /**
     * 名称
     **/
    @TableField("name")
    private String name;
    /**
     * 站点类型编码
     **/
    @TableField("station_type_code")
    private String stationTypeCode;
    /**
     * 行政区编码
     **/
    @TableField("region_code")
    private String regionCode;
    /**
     * 位置
     **/
    @TableField(value = "location_geo",typeHandler = PgGeometryTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private String locationGeo;
    /**
     * 位置
     **/
    @TableField("location_json")
    private String locationJson;
    /**
     * 创建人id
     **/
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建人
     **/
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人id
     **/
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;
    /**
     * 更新人
     **/
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 85基面-黄色警戒值
     **/
    @TableField("datum_yellow_warn")
    private Integer datumYellowWarn;
    /**
     * 85基面-蓝色警戒值
     **/
    @TableField("datum_blue_warn")
    private Integer datumBlueWarn;
    /**
     * 85基面-红色警戒值
     **/
    @TableField("datum_red_warn")
    private Integer datumRedWarn;
    /**
     * 85基面-橙色警戒值
     **/
    @TableField("datum_orange_warn")
    private Integer datumOrangeWarn;
    /**
     * 潮汐表基面-黄色警戒值
     **/
    @TableField("tide_datum_yellow_warn")
    private Integer tideDatumYellowWarn;
    /**
     * 潮汐表基面-蓝色警戒值
     **/
    @TableField("tide_datum_blue_warn")
    private Integer tideDatumBlueWarn;
    /**
     * 潮汐表基面-红色警戒值
     **/
    @TableField("tide_datum_red_warn")
    private Integer tideDatumRedWarn;
    /**
     * 潮汐表基面-橙色警戒值
     **/
    @TableField("tide_datum_orange_warn")
    private Integer tideDatumOrangeWarn;
    /**
     * 水尺零点-黄色警戒值
     **/
    @TableField("gauge_zero_yellow_warn")
    private Integer gaugeZeroYellowWarn;
    /**
     * 水尺零点-蓝色警戒值
     **/
    @TableField("gauge_zero_blue_warn")
    private Integer gaugeZeroBlueWarn;
    /**
     * 水尺零点-红色警戒值
     **/
    @TableField("gauge_zero_red_warn")
    private Integer gaugeZeroRedWarn;
    /**
     * 水尺零点-橙色警戒值
     **/
    @TableField("gauge_zero_orange_warn")
    private Integer gaugeZeroOrangeWarn;
    /**
     * 类型：潮位tide 实况观测liveObservation 3米浮标站3m 10米浮标站10m 波浪谱浮标站waveSpectrum
     **/
    @TableField("type")
    private String type;
    /**
     * 关联站点
     **/
    @TableField("relation_station")
    private String relationStation;
    /**
     * 是否启用
     **/
    @TableField("enable")
    private Boolean enable;
}



