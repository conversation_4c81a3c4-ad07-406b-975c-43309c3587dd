import { MaybeRefOrGetter } from '@vueuse/core'
import { IParagraphTableData } from './useParagraph.type'
import { toValue } from 'vue'

export function useParagraph(tableData: MaybeRefOrGetter<IParagraphTableData[]>) {
  return {
    getParagraph: () => getParagraph(tableData)
  }
}

function getParagraph(tableData: MaybeRefOrGetter<IParagraphTableData[]>): string {
  let result = ''
  const tableDataValue = toValue(tableData)
  const waveLevelSet = new Set()
  tableDataValue.forEach(item => waveLevelSet.add(item.langjiMean))
  waveLevelSet.forEach(waveLevel => {
    const filter = tableDataValue.filter(item => item.langjiMean === waveLevel)
    if (filter.length !== 0) {
      if (result !== '') {
        result += ','
      }
      result += `${filter
        .map(_ => _.name)
        .join('、')}海域有${filter[0].langjiMean}`
    }
  })
  return result
}
