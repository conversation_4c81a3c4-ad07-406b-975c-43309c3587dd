import type { IForecastProductItem } from './types'
import { DataTableBaseColumn } from 'naive-ui'
import { unionObjectArray } from 'src/utils/util'


/**
 * 获取基础列
 * @param data
 */
export function getColumns(data: IForecastProductItem[]): DataTableBaseColumn[] {
  const columns: DataTableBaseColumn[] = data.map(item => {
    return {
      title: item.columnName,
      key: item.columnCode
    }
  })
  columns.unshift({
    title: '名称',
    key: 'name'
  })
  return unionObjectArray(columns, () => 'key')
}

export function getTableData(
  data: IForecastProductItem[]
): Record<keyof IForecastProductItem, string>[] {
  const rows: Record<string, string>[] = []
  const rowKey = Array.from(new Set(data.map(item => item.areaCode)))
  rowKey.forEach(key => {
    const row: Record<string, string> = {}
    const oneRowDataArr = data.filter(item => item.areaCode === key)
    oneRowDataArr.forEach(item => {
      row[item.columnCode] = item.value
    })
    row.name = oneRowDataArr[0].areaName
    rows.push(row)
  })
  return rows
}

export function toShitData(
  tableData: Record<string, any>[],
  originalShitData: IForecastProductItem[]
): void {
  tableData.forEach(row => {
    Object.keys(row).forEach(tableColumn => {
      // 行名
      if (tableColumn === 'name' || tableColumn === 'areaCode') {
        const findArr = originalShitData.filter(shit => {
          return shit.areaName === row[tableColumn]
        })
        findArr.forEach(shit => {
          shit.value = row[shit.columnCode]
        })
      } else if(tableColumn === '$names') {
        const findArr = originalShitData.filter(shit => {
          return row[tableColumn].includes(shit.areaName)
        })
        findArr.forEach(shit => {
          shit.value = row[shit.columnCode]
        })
      }
    })
  })
}
