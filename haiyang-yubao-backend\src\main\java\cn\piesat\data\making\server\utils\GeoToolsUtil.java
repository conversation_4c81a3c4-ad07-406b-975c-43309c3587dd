package cn.piesat.data.making.server.utils;

import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.feature.simple.SimpleFeatureBuilder;
import org.geotools.feature.simple.SimpleFeatureTypeBuilder;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.index.strtree.STRtree;
import org.locationtech.jts.operation.overlay.snap.GeometrySnapper;
import org.opengis.feature.Feature;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;

import java.io.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class GeoToolsUtil {

    private static final double EARTH_RADIUS = 6378.137;//地球半径,单位千米

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    public static String arrToGeojson(double[][] coordinates) throws IOException {
//        // 二维的经纬度数组
//        double[][] coordinates = new double[][]{
//                {102.0, 0.0},
//                {103.0, 1.0},
//                {104.0, 0.0},
//                {105.0, 1.0},
//                {102.0, 0.0}
//        };

        // 创建GeometryFactory
        GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();

        // 将二维数组转换为Coordinate数组
        Coordinate[] coords = new Coordinate[coordinates.length];
        for (int i = 0; i < coordinates.length; i++) {
            coords[i] = new Coordinate(coordinates[i][0], coordinates[i][1]);
        }
        Geometry geometry;
        if (coordinates.length == 1) {
            // 创建Point
            geometry = geometryFactory.createPoint(coords[0]);
        } else {
            // 使用Coordinate数组创建LinearRing
            LinearRing linearRing = geometryFactory.createLinearRing(coords);
            // 创建Polygon
            geometry = geometryFactory.createPolygon(linearRing, null);
        }

        // 创建一个简单特征类型
        SimpleFeatureTypeBuilder builder = new SimpleFeatureTypeBuilder();
        builder.setName("geometry_feature");
        builder.add("the_geom", geometry.getClass());
        SimpleFeatureType type = builder.buildFeatureType();
        // 创建一个SimpleFeatureBuilder
        SimpleFeatureBuilder featureBuilder = new SimpleFeatureBuilder(type);
        // 创建一个SimpleFeature
        SimpleFeature feature = featureBuilder.buildFeature(null);
        // 设置几何属性
        feature.setDefaultGeometry(geometry);

        // 将Geometry对象转换为GeoJSON
        StringWriter writer = new StringWriter();
        FeatureJSON featureJSON = new FeatureJSON();
        featureJSON.writeFeature(feature, writer);
        return writer.toString();
//        JSONObject jsonObject = new JSONObject(writer.toString());
//        return jsonObject.get("geometry").toString();
    }

    public static String geojsonToGeo(String geojson) throws IOException {
//        String geojson = "{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[110.4,20.2]},\"properties\":{}," +
//                "\"id\":\"fid-4780e600_1920422c0bb_-8000\"}";

        // 创建FeatureJSON解析器
        FeatureJSON featureJSON = new FeatureJSON();

        // 解析GeoJSON字符串为Feature对象
        boolean properties = geojson.contains("\"properties\":null");
        if (properties) {
            geojson = geojson.replace("\"properties\":null", "\"properties\":{}");
        }
        Feature feature = featureJSON.readFeature(new StringReader(geojson));

        // 获取Geometry对象
        Geometry geometry = (Geometry) feature.getDefaultGeometryProperty().getValue();

        // 将Geometry对象转换为16进制字符串
        return geometry.toText();
    }


    /**
     * @param lat1 第一个纬度
     * @param lng1 第一个经度
     * @param lat2 第二个纬度
     * @param lng2 第二个经度
     * @return 两个经纬度的距离
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);

        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000) / 10000;
        return s;

    }

    public static SimpleFeatureCollection getFeatureCollection(File file) throws Exception {
        FeatureJSON featureJSON = new FeatureJSON();
        return (SimpleFeatureCollection) featureJSON.readFeatureCollection(new FileInputStream(file));
    }

    public static SimpleFeatureCollection getFeatureCollection(String geoJsonString) throws Exception {
        FeatureJSON featureJSON = new FeatureJSON();
        return (SimpleFeatureCollection) featureJSON.readFeatureCollection(new StringReader(geoJsonString));
    }

    public static List<Map<String, Object>> getIntersection(SimpleFeatureCollection featureCollection1,
                                                            SimpleFeatureCollection featureCollection2,
                                                            String attribute) throws Exception {

        // 使用 LinkedHashSet 存储结果，保持插入顺序并去重
        Set<Map<String, Object>> results = Collections.synchronizedSet(new LinkedHashSet<>());

        // 构建空间索引 (STRtree)
        STRtree spatialIndex = new STRtree();
        try (SimpleFeatureIterator iterator = featureCollection2.features()) {
            while (iterator.hasNext()) {
                SimpleFeature feature = iterator.next();
                Geometry geometry = (Geometry) feature.getDefaultGeometry();
                if (geometry != null) {
                    spatialIndex.insert(geometry.getEnvelopeInternal(), feature);
                }
            }
        }
        spatialIndex.build(); // 确保构建索引

        // 创建线程池
        int numThreads = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(numThreads);

        // 提交任务
        List<Future<?>> futures = new ArrayList<>(); // 使用通配符 ?
        try (SimpleFeatureIterator featureIterator = featureCollection1.features()) {
            while (featureIterator.hasNext()) {
                SimpleFeature feature1 = featureIterator.next();
                Geometry geometry1 = (Geometry) feature1.getDefaultGeometry();
                if (geometry1 == null) continue;

                // 获取属性值
                Object value = attribute != null ? feature1.getAttribute(attribute) : null;

                // 提交任务到线程池
                futures.add(executorService.submit(() -> {
                    // 查询索引中的候选几何对象
                    List<SimpleFeature> candidates = spatialIndex.query(geometry1.getEnvelopeInternal());
                    for (SimpleFeature candidate : candidates) {
                        Geometry candidateGeometry = (Geometry) candidate.getDefaultGeometry();

                        if (candidateGeometry == null) {
                            continue;
                        }

                        // 提取边线
                        Geometry boundary1 = extractBoundary(geometry1);
                        Geometry boundary2 = extractBoundary(candidateGeometry);
                        if (boundary1 == null || boundary2 == null) continue;

                        // 检查边线是否相交
                        if (boundary1.intersects(boundary2)) {
                            Geometry intersection = safeIntersection(boundary1, boundary2);
                            if (!intersection.isEmpty()) {
                                extractIntersectionPoints(intersection, value, attribute, results);
                            }
                        }
                    }
                }));
            }
        }

        // 等待所有任务完成 (不需要收集 Future 的返回值)
        for (Future<?> future : futures) {
            future.get();
        }

        // 关闭线程池
        executorService.shutdown();

        return new ArrayList<>(results); // 返回 List
    }

    /**
     * 根据类型提取交叉点
     *
     * @param intersection 路口
     * @param value        值
     * @param attribute    属性
     * @param results      结果
     */
    private static void extractIntersectionPoints(Geometry intersection, Object value, String attribute, Set<Map<String, Object>> results) {
        if (intersection instanceof Point) {
            addPointToResults((Point) intersection, value, attribute, results);
        } else if (intersection instanceof MultiPoint) {
            MultiPoint multiPoint = (MultiPoint) intersection;
            for (int i = 0; i < multiPoint.getNumGeometries(); i++) {
                addPointToResults((Point) multiPoint.getGeometryN(i), value, attribute, results);
            }
        } else if (intersection instanceof LineString) {
            // 如果交集是线，通常你只需要线的端点作为交点
            LineString line = (LineString) intersection;
            addPointToResults(line.getStartPoint(), value, attribute, results);
            addPointToResults(line.getEndPoint(), value, attribute, results);
        } else if (intersection instanceof MultiLineString) {
            MultiLineString multiLine = (MultiLineString) intersection;
            for (int i = 0; i < multiLine.getNumGeometries(); i++) {
                LineString line = (LineString) multiLine.getGeometryN(i);
                addPointToResults(line.getStartPoint(), value, attribute, results);
                addPointToResults(line.getEndPoint(), value, attribute, results);
            }
        } else if (intersection instanceof Polygon || intersection instanceof MultiPolygon) {
            // 如果交集是面，通常不需要面的所有顶点，而是需要交集的边界线（再提取边界线的端点）
            // 这里简化处理，只获取外环的顶点
            Geometry boundary = intersection.getBoundary(); // 获取边界
            extractIntersectionPoints(boundary, value, attribute, results); // 递归处理边界
        } else if (intersection instanceof GeometryCollection) {
            GeometryCollection geometryCollection = (GeometryCollection) intersection;
            for (int i = 0; i < geometryCollection.getNumGeometries(); i++) {
                extractIntersectionPoints(geometryCollection.getGeometryN(i), value, attribute, results); // 递归处理
            }
        }
    }

    /**
     * 添加点转结果
     *
     * @param point     点
     * @param value     值
     * @param attribute 属性
     * @param results   结果
     */
    private static void addPointToResults(Point point, Object value, String attribute, Set<Map<String, Object>> results) {
        // 使用 Double.toString() 来避免浮点数精度问题导致的重复
        Map<String, Object> result = new HashMap<>();
        result.put("coordinate", new double[]{point.getX(), point.getY()});
        if (attribute != null) {
            result.put(attribute, value);
        }
        results.add(result);
    }


    /**
     * 提取边界
     *
     * @param geometry 几何
     * @return {@link Geometry }
     */
    private static Geometry extractBoundary(Geometry geometry) {
        if (geometry instanceof Polygon || geometry instanceof MultiPolygon) {
            return geometry.getBoundary();
        } else if (geometry instanceof LineString || geometry instanceof MultiLineString) {
            return geometry;
        } else {
            return null; // 不处理点或其他类型
        }
    }

    /**
     * 安全判断交点
     *
     * @param geom1 geom1
     * @param geom2 geom2
     * @return {@link Geometry }
     */
    private static Geometry safeIntersection(Geometry geom1, Geometry geom2) {
        try {
            // 尝试普通交集计算
            return geom1.intersection(geom2);
        } catch (TopologyException e) {
            // 使用GeometrySnapper捕捉几何体
            try {
                // 调整精度
                double snapTolerance = 1e-6;
                Geometry[] snappedGeoms = GeometrySnapper.snap(geom1, geom2, snapTolerance);
                Geometry snappedGeom1 = snappedGeoms[0];
                Geometry snappedGeom2 = snappedGeoms[1];
                return snappedGeom1.intersection(snappedGeom2);
            } catch (Exception ex) {
                // 若仍失败，返回空几何
                return geom1.getFactory().createGeometryCollection();
            }
        }
    }

    public static void main(String[] args) throws Exception {

        File testFile = new File("E:\\系统资源\\Desktop\\1.geojson");
        File allFile = new File("E:\\系统资源\\Desktop\\globalNationalBoundary.geojson");
        SimpleFeatureCollection featureCollection1 = getFeatureCollection(testFile);
        SimpleFeatureCollection featureCollection2 = getFeatureCollection(allFile);
        List<Map<String, Object>> results = getIntersection(featureCollection1, featureCollection2, "value");
        // 打印结果
        System.out.println("Intersection Points:" + results.size());
        for (Map<String, Object> result : results) {
            System.out.println("Coordinate: " + result.get("coordinate") + ", Value: " + result.get("value"));
        }
    }
}