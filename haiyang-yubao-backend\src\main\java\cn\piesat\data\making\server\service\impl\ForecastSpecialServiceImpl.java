package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.ForecastSpecialDao;
import cn.piesat.data.making.server.dto.ForecastSpecialDTO;
import cn.piesat.data.making.server.dto.generate.*;
import cn.piesat.data.making.server.entity.ForecastSpecial;
import cn.piesat.data.making.server.fegin.AlgorithmExecClient;
import cn.piesat.data.making.server.mapper.ForecastSpecialMapper;
import cn.piesat.data.making.server.service.ForecastSpecialService;
import cn.piesat.data.making.server.service.GenerateProductService;
import cn.piesat.data.making.server.utils.GenerateFileUtil;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.webconfig.exception.BusinessException;
import com.alibaba.druid.sql.visitor.functions.Char;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.Tables;
import com.deepoove.poi.data.style.BorderStyle;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("ForecastSpecialService")
public class ForecastSpecialServiceImpl extends ServiceImpl<ForecastSpecialDao, ForecastSpecial> implements ForecastSpecialService {
    public Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Value("${schedule.alg.script-path}")
    private String scriptPath;
    @Value("${piesat.make.forecast-special-template}")
    private String forecastSpecialTemplate;
    @Autowired
    private AlgorithmExecClient algorithmExecClient;
    @Autowired
    private GenerateProductService generateProductService;

    @Override
    public PageResult pageList(ForecastSpecialDTO dto) {
        LambdaQueryWrapper<ForecastSpecial> wrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(dto.getProductName())) {
            wrapper.like(ForecastSpecial::getProductName, dto.getProductName());
        }
        if (Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime())) {
            wrapper.between(ForecastSpecial::getStartTime, dto.getStartTime(), dto.getEndTime());
        }
        wrapper.orderByDesc(ForecastSpecial::getCreateTime);
        Page<ForecastSpecial> page = this.page(new Page<>(dto.getPageNum(), dto.getPageSize()), wrapper);
        return new PageResult<>(page.getRecords(), dto.getPageNum(), dto.getPageSize(), page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInfo(ForecastSpecialDTO dto) {
        ForecastSpecial forecastSpecial = ForecastSpecialMapper.INSTANCE.toForecastSpecial(dto);
        this.save(forecastSpecial);
    }

    @Override
    public void updateInfo(ForecastSpecialDTO dto) {
        ForecastSpecial forecastSpecial = ForecastSpecialMapper.INSTANCE.toForecastSpecialModify(dto);
        this.updateById(forecastSpecial);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        LambdaUpdateWrapper<ForecastSpecial> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ForecastSpecial::getStatus,status);
        wrapper.eq(ForecastSpecial::getId,id);
        this.update(wrapper);
        //调用算法逻辑
        ForecastSpecial byId = this.getById(id);
        this.makeFile(byId);
        //调用结束后更新状态 并写入报告路径
        byId.setStatus(2);
        this.updateById(byId);
    }

    @Override
    public ForecastSpecialDTO info(Long id) {
        ForecastSpecial byId = this.getById(id);
        return ForecastSpecialMapper.INSTANCE.toForecastSpecialDTO(byId);
    }

    private void makeFile(ForecastSpecial forecastSpecial){

        try {
            Date date = new Date();

            //获取areaGeoJson数据
            String areaJsonFilePath = String.format(CommonConstant.GEOJSON_FILE_PATH, baseOutputPath, forecastSpecial.getId());
            FileUtil.writeUtf8String(forecastSpecial.getForecastArea(),areaJsonFilePath);
            //生成预报时间段文件
            ForecastTimeDTO forecastTimeDTO = new ForecastTimeDTO(forecastSpecial.getForecastTimeS().getTime(), forecastSpecial.getForecastTimeE().getTime());
            String forecastTimeJson = JsonUtil.object2Json(forecastTimeDTO);
            String forecastTimeFilePath = String.format(CommonConstant.FORECAST_FILE_PATH, baseOutputPath, forecastSpecial.getId());
            FileUtil.writeUtf8String(forecastTimeJson,forecastTimeFilePath);
            //获取逐天还是逐时
            String timeType = forecastSpecial.getTimeType();
            //获取执行结果
            HashMap<String, List<ForecastSpecialAreaTableDTO>> map = new HashMap<>();

            String forecastNcPath = forecastSpecial.getForecastNcPath();
            List<ForecastSpecialElementDTO> forecastSpecialElementDTOS = JsonUtil.json2ObjectList(forecastNcPath, new TypeReference<List<ForecastSpecialElementDTO>>() {
            });
            forecastSpecialElementDTOS.forEach(element->{
                String[] ncFile = element.getNcPath().split(",");
                List<ForecastSpecialElementDTO.Product> products = element.getProduct();
                for (String ncPath : ncFile) {
                    try {
                        List<AlgorithmResultDTO.Result> results = GenerateFileUtil.generateExecJson(baseOutputPath, date, ncPath, areaJsonFilePath, forecastTimeFilePath, algorithmExecClient, scriptPath);
                        if (CollectionUtils.isEmpty(results)) {
                            logger.error("算法计算异常产出结果为空");
                            continue;
                        }
                        //处理result按照产品进行分组
                        products.forEach(product -> {
                            List<SeaWaveAlarmResultDTO> waveAlarmResultDTOS = new ArrayList<>();
                            List<AlgorithmResultDTO.Result> resultList = results.stream().filter(result -> product.getProductId().equals(result.getProductIdENG())).collect(Collectors.toList());
                            for (AlgorithmResultDTO.Result result : resultList) {//解析算法输出结果
                                String filePath = result.getFilePath();
                                String resultJson = FileUtil.readUtf8String(filePath);
                                String[] strings = StrUtil.split(FileUtil.getName(filePath), "_");
                                if (strings != null) {
                                    String forecastTimeStr = strings[5];
                                    DateTime forecastTime = DateUtil.parse(forecastTimeStr, "yyyyMMdd'T'HHmmss");
                                    //判断产品文件时间是否在预报时间范围内
                                    if (DateUtil.isIn(forecastTime, forecastSpecial.getForecastTimeS(), forecastSpecial.getForecastTimeE())) {
                                        try {
                                            List<SeaWaveAlarmResultDTO> seaWaveAlarmResultDTOS = JsonUtil.json2ObjectList(resultJson, new TypeReference<List<SeaWaveAlarmResultDTO>>() {
                                            });
                                            seaWaveAlarmResultDTOS.forEach(seaWaveAlarmResultDTO -> seaWaveAlarmResultDTO.setForecastTime(forecastTime));
                                            waveAlarmResultDTOS.addAll(seaWaveAlarmResultDTOS);
                                        } catch (IOException e) {
                                            logger.error("解析算法查出文件异常", e);
                                        }
                                    }
                                }
                            }
                            logger.info("waveAlarmResultDTOS条数有：{}",waveAlarmResultDTOS.size());
                            //判断是逐天还是逐时
                            if("D".equals(timeType)) {
                                // 按区域和日期分组，生成扁平化结果列表
                                List<ForecastSpecialAreaTableDTO> tableDTOList = new ArrayList<>(waveAlarmResultDTOS.stream()
                                        .collect(Collectors.groupingBy(
                                                item -> item.getName() + "_" + item.getForecastTime().toDateStr(),  // 复合键分组
                                                Collectors.collectingAndThen(
                                                        Collectors.toList(),
                                                        list -> {
                                                            double overallMin = list.stream()
                                                                    .mapToDouble(SeaWaveAlarmResultDTO::getMin)
                                                                    .min()
                                                                    .orElse(Double.NaN);

                                                            double overallMax = list.stream()
                                                                    .mapToDouble(SeaWaveAlarmResultDTO::getMax)
                                                                    .max()
                                                                    .orElse(Double.NaN);

                                                            SeaWaveAlarmResultDTO firstItem = list.get(0);
                                                            //风速转风级
                                                            if (product.getProductId().equals("GRID_GRID-WIND_WINDSPEED_GEOJSON") ||
                                                                    product.getProductId().equals("FORECAST_WIND_WINDSPEED_GEOJSON")) {
                                                                return new ForecastSpecialAreaTableDTO(DateUtil.format(firstItem.getForecastTime(), "MM月dd日"), firstItem.getName(), String.format("%s-%s", this.windSpeedLevel(NumberUtil.round(overallMin, 1)), this.windSpeedLevel(NumberUtil.round(overallMax, 1))));
                                                            }
                                                            String waveLevel = this.waveLevel(overallMax);
                                                            String waveLevels[] = waveLevel.split("-");
                                                            return new ForecastSpecialAreaTableDTO(DateUtil.format(firstItem.getForecastTime(), "MM月dd日"), firstItem.getName(), String.format("%s-%s", NumberUtil.round(waveLevels[0], 1), NumberUtil.round(waveLevels[1], 1)));
                                                        }
                                                )
                                        ))
                                        .values());
                                map.put(product.getProductName(),tableDTOList);
                            }else{
                                //风速转风级
                                if (product.getProductId().equals("GRID_GRID-WIND_WINDSPEED_GEOJSON") ||
                                        product.getProductId().equals("FORECAST_WIND_WINDSPEED_GEOJSON")) {
                                    logger.info("提取风速数据");
                                    List<ForecastSpecialAreaTableDTO> tableDTOList = waveAlarmResultDTOS.stream().map(dto -> new ForecastSpecialAreaTableDTO(DateUtil.format(dto.getForecastTime(), "MM月dd日HH时"), dto.getName(), String.format("%s-%s", this.windSpeedLevel(NumberUtil.round(dto.getMin(), 1)), this.windSpeedLevel(NumberUtil.round(dto.getMax(), 1))))).collect(Collectors.toList());
                                    map.put(product.getProductName(), tableDTOList);
                                } else {

                                    List<ForecastSpecialAreaTableDTO> tableDTOList = waveAlarmResultDTOS.stream().map(dto -> new ForecastSpecialAreaTableDTO(DateUtil.format(dto.getForecastTime(), "MM月dd日HH时"), dto.getName(), String.format("%s-%s", NumberUtil.round(this.waveLevel(dto.getMax()).split("-")[0], 1), NumberUtil.round(this.waveLevel(dto.getMax()).split("-")[1], 1)))).collect(Collectors.toList());
                                    map.put(product.getProductName(), tableDTOList);
                                }
                            }
                        });
                    } catch (Exception e ) {
                        logger.error("执行算法异常",e);
                    }
                }
            });

            logger.info("组装数据map.keySet()-----------------------{}",map.keySet());
            logger.info("组装数据map-----------------------{}",map);
            ForecastSpecialWordDTO wordDTO = new ForecastSpecialWordDTO();
            wordDTO.setElementName(forecastSpecial.getElementName());
            wordDTO.setProductName(forecastSpecial.getProductName());
            wordDTO.setTableList(this.handleTable(map));
            wordDTO.setReleaseTime(DateUtil.format(forecastSpecial.getCreateTime(),"yyyy年MM月dd日HH时"));
            wordDTO.setWaveAnnotation(forecastSpecial.getWaveAnnotation());
            wordDTO.setSuggestion(forecastSpecial.getSuggestion());
            wordDTO.setRanchName(forecastSpecial.getRanchName());
            wordDTO.setRanchValue(forecastSpecial.getRanchValue());
            wordDTO.setImages(GenerateFileUtil.generateUrlPicture(forecastSpecial.getImagePath()));
            logger.info("ForecastSpecialWordDTO-----------------------------:{}",wordDTO);
            //拼接产出文件地址
            Date releaseTime = forecastSpecial.getCreateTime();
            String wordFilePath = String.format(CommonConstant.FORECAST_SPECIAL_WORD_FILE_NAME,baseOutputPath,DateUtil.year(releaseTime), DateUtil.format(releaseTime, "yyyyMMdd"), DateUtil.format(releaseTime, "yyyyMMddHHmmss"), DateUtil.format(new Date(), "yyyyMMddHHmmss"));
            generateProductService.generateWord(wordFilePath,forecastSpecialTemplate, wordDTO);

            forecastSpecial.setReportPath(wordFilePath);
        } catch (IOException e) {
            logger.error("解析要素失败",e);
        }
    }

    private TableRenderData handleTable(Map<String, List<ForecastSpecialAreaTableDTO>> tableMap){
        List<String> allColumns = new LinkedList<>(Arrays.asList("日期", "名称"));
        allColumns.addAll(tableMap.keySet());
        logger.info("allColumns.addAll(tableMap.keySet());---------------------{}",allColumns);

        RowRenderData header = Rows.of(allColumns.toArray(new String[0])).center().textBold().textFontFamily("宋体").textFontSize(14).create();
        // 3. 创建表格
        TableRenderData tableRenderData = Tables.ofA4MediumWidth()
                .addRow(header)
                .border(BorderStyle.DEFAULT) // 设置边框
                .create();

        Map<String, Map<String, ForecastSpecialAreaTableDTO>> groupedData = new TreeMap<>();
        tableMap.forEach((columnName, dtos) -> {
            dtos.forEach(dto -> {
                String key = dto.getTime() + "_" + dto.getArea();
                groupedData.computeIfAbsent(key, k -> new HashMap<>())
                        .put(columnName, dto);
            });
        });

        // 5. 按日期和区域生成行
        groupedData.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
            String[] parts = entry.getKey().split("_");
            String time = parts[0];
            String area = parts[1];
            Map<String, ForecastSpecialAreaTableDTO> columnData = entry.getValue();

            // 创建行数据
            List<String> rowData = new ArrayList<>();
            for (String colName : allColumns) {
                if ("日期".equals(colName)) {
                    rowData.add(time);
                } else if ("名称".equals(colName)) {
                    rowData.add(area);
                } else {
                    // 动态列的值
                    ForecastSpecialAreaTableDTO dto = columnData.get(colName);
                    rowData.add(dto != null ? invalidValue(dto.getValue()) : ""); // 无数据则填空
                    logger.info("rowData.add(dto != null ? dto.getValue() : \"\");---------------------:{}",dto.getValue());
                }
            }

            // 添加行到表格
            tableRenderData.addRow(Rows.of(rowData.toArray(new String[0])).center().textFontFamily("宋体").textFontSize(12).create());
        });
        logger.info("tableRenderData-------------------------:{}",tableRenderData);
        return tableRenderData;
    }

    private String invalidValue(String value) {
        return value.contains("-999.0") ? value.replace("-999.0","0.1") : value;
    }

    private String waveLevel(Double val){
        String level = "0.2-0.5";
        if (val >= 0 && val <= 0.5) {
            level = "0.2-0.5";
        } else if (val >= 0.6 && val <= 0.8) {
            level = "0.3-0.8";
        } else if (val >= 0.9 && val <= 1.2) {
            level = "0.7-1.2";
        } else if (val >= 1.3 && val <= 1.5) {
            level = "1.0-1.5";
        } else if (val >= 1.6 && val <= 1.8) {
            level = "1.3-1.8";
        } else if (val >= 1.9 && val <= 2.3) {
            level = "1.8-2.3";
        } else if (val >= 2.4 && val <= 2.5) {
            level = "2.0-2.5";
        } else if (val >= 2.6 && val <= 2.8) {
            level = "2.0-2.8";
        } else if (val >= 2.9 && val <= 3.0) {
            level = "2.5-3.0";
        } else if (val >= 3.1 && val <= 3.5) {
            level = "2.5-3.5";
        } else if (val >= 3.6 && val <= 3.8) {
            level = "3.0-3.8";
        } else if (val >= 3.9 && val <= 4.0) {
            level = "3.0-4.0";
        } else if (val >= 4.1 && val <= 4.5) {
            level = "3.5-4.5";
        } else if (val >= 4.6 && val <= 4.8) {
            level = "4.0-4.8";
        } else if (val >= 4.9 && val <= 5.0) {
            level = "4.0-5.0";
        } else if (val >= 5.1 && val <= 5.5) {
            level = "4.5-5.5";
        } else if (val >= 5.6 && val <= 6.0) {
            level = "5.0-6.0";
        } else if (val >= 6.1 && val <= 6.5) {
            level = "5.5-6.5";
        } else if (val >= 6.6 && val <= 7.0) {
            level = "5.0-7.0";
        } else if (val >= 7.1 && val <= 7.5) {
            level = "5.5-7.5";
        } else if (val >= 7.6 && val <= 8.0) {
            level = "6.0-8.0";
        } else if (val >= 8.1 && val <= 8.5) {
            level = "6.5-8.5";
        } else if (val >= 8.6 && val <= 9.0) {
            level = "7.0-9.0";
        } else if (val >= 9.1 && val <= 10.0) {
            level = "7.0-10.0";
        } else if (val >= 10.1 && val <= 11.0) {
            level = "8.0-11.0";
        } else if (val >= 11.1 && val <= 12.0) {
            level = "9.0-12.0";
        } else if (val >= 12.1 && val <= 13.0) {
            level = "10.0-13.0";
        } else if (val >= 13.1 && val <= 14.0) {
            level = "11.0-14.0";
        }
        return level;
    }

    private String windSpeedLevel(BigDecimal value){
        String levelValue = "0";
        double val = value.doubleValue();
        //NumberUtil.
        if (val > 0.3 && val < 1.5) {
            levelValue = "1";
        } else if (val > 1.6 && val < 3.3) {
            levelValue = "2";
        } else if (val > 3.4 && val < 5.4) {
            levelValue = "3";
        } else if (val > 5.5 && val < 7.9) {
            levelValue = "4";
        } else if (val > 8.0 && val < 10.7) {
            levelValue = "5";
        } else if (val > 10.8 && val < 13.8) {
            levelValue = "6";
        } else if (val > 13.9 && val < 17.1) {
            levelValue = "7";
        } else if (val > 17.2 && val < 20.7) {
            levelValue = "8";
        } else if (val > 20.8 && val < 24.4) {
            levelValue = "9";
        } else if (val > 24.5 && val < 28.4) {
            levelValue = "10";
        } else if (val > 28.5 && val < 32.6) {
            levelValue = "11";
        } else if (val > 32.7 && val < 36.9) {
            levelValue = "12";
        } else if (val > 37.0 && val < 41.4) {
            levelValue = "13";
        } else if (val > 41.5 && val < 46.1) {
            levelValue = "14";
        } else if (val > 46.2 && val < 50.9) {
            levelValue = "15";
        } else if (val > 51.0 && val < 56.0) {
            levelValue = "16";
        } else if (val > 56.1) {
            levelValue = "17";
        }
        return levelValue;
    }

    public static void main(String[] args) {

        try {
            ForecastSpecialWordDTO wordDTO = new ForecastSpecialWordDTO();
            wordDTO.setElementName("测试");
            wordDTO.setProductName("测试");
            wordDTO.setTableList(new TableRenderData());
            wordDTO.setReleaseTime(DateUtil.format(new Date(),"yyyy年MM月dd日HH时"));
//            wordDTO.setWaveAnnotation("注：预报波高为有效波高");
            wordDTO.setWaveAnnotation("");
            wordDTO.setSuggestion("防御措施建议：密切关注预警信息，检查与加固网箱框架，检查与加固锚泊系统，检查与加固网衣系统。");
            wordDTO.setRanchName("【牧场】");
            wordDTO.setRanchValue("牧场【123456】");
            wordDTO.setImages(GenerateFileUtil.generateUrlPicture("file:///C:\\Users\\<USER>\\Desktop\\e343a4fc5d736f18096e0521dffb2acd.gif"));
            String filePath = "C:\\Users\\<USER>\\Desktop\\"+DateUtil.format(new Date(), "yyyyMMddHHmmss")+".docx";
            GenerateFileUtil.createFile(filePath);
            Configure config = Configure.builder().useSpringEL().build();
            XWPFTemplate.compile("C:\\Users\\<USER>\\Desktop\\专项预报模板.docx",config).render(wordDTO).writeAndClose(new FileOutputStream(filePath));
        } catch (Exception e) {
            //logger.error("创建word失败",e);
            System.out.println(e);
            throw new BusinessException("创建word文件失败");
        }
    }

}
