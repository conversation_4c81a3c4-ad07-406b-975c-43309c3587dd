<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-15 10:02:17
 * @LastEditors: xulijie <EMAIL>
 * @LastEditTime: 2025-03-26 10:29:04
 * @FilePath: \hainan-jianzai-web\src\components\OpenlayersMap\components\LQGJ.vue
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="btns">
    <qx-button
      class="edit-btn"
      :class="[ActionType.DRAW === currentAction ? 'active' : '']"
      @click="toggleAction(ActionType.DRAW)"
      >{{ ActionType.DRAW === currentAction ? '结束' : '绘制' }}
    </qx-button>
    <qx-button
      class="edit-btn"
      :class="[ActionType.MODIFY === currentAction ? 'active' : '']"
      @click="toggleAction(ActionType.MODIFY)"
      >{{ ActionType.MODIFY === currentAction ? '放弃' : '编辑' }}
    </qx-button>
    <qx-button
      class="edit-btn"
      :class="[ActionType.MOVE === currentAction ? 'active' : '']"
      @click="toggleAction(ActionType.MOVE)"
      >{{ ActionType.MOVE === currentAction ? '放弃' : '移动' }}
    </qx-button>
    <qx-button
      class="edit-btn"
      :class="[ActionType.DELETE === currentAction ? 'active' : '']"
      @click="toggleAction(ActionType.DELETE)"
      >{{ ActionType.DELETE === currentAction ? '放弃' : '删除' }}
    </qx-button>
    <qx-button
      class="edit-btn"
      :class="[ActionType.CLEAR === currentAction ? 'active' : '']"
      @click="toggleAction(ActionType.CLEAR)"
      >{{ ActionType.CLEAR === currentAction ? '放弃' : '清空' }}
    </qx-button>
  </div>
  <div class="tools-container">
<!--    <div class="query-item">-->
<!--      <div class="query-title">不透明度：</div>-->
<!--      <n-slider-->
<!--        v-model:value="roiOptions.opacity"-->
<!--        :default-value="100"-->
<!--        :step="1"-->
<!--        :format-tooltip="formatTooltip"-->
<!--        class="my-slider"-->
<!--      />-->
<!--      <n-input-number-->
<!--        v-model:value="roiOptions.opacity"-->
<!--        class="small-input"-->
<!--        :show-button="false"-->
<!--      >-->
<!--        <template #suffix> %</template>-->
<!--      </n-input-number>-->
<!--    </div>-->

    <div class="query-item">
      <div class="query-title">描边线宽：</div>
      <n-select
        v-model:value="roiOptions.strokeWidth"
        class="query-info"
        :options="defaultStrokeWidths()"
      />
    </div>
<!--    <div class="query-item">-->
<!--      <div class="query-title">描边颜色：</div>-->
<!--      <div class="color-picker">-->
<!--        <div-->
<!--          v-for="item in colors"-->
<!--          :key="item.id"-->
<!--          class="color-item"-->
<!--          :style="{ background: item.value }"-->
<!--          :class="[-->
<!--            item.haveBorder ? 'have-border' : '',-->
<!--            item.id === 'transparent' ? 'transparent-div' : '',-->
<!--            roiOptions.strokeColor === item.value ? 'active-color' : ''-->
<!--          ]"-->
<!--          @click="roiOptions.strokeColor = item.value"-->
<!--        ></div>-->
<!--        <div-->
<!--          class="color-item"-->
<!--          :style="{ background: '#000000' }"-->
<!--          :class="roiOptions.strokeColor === '#000000' ? 'active-color' : ''"-->
<!--          @click="roiOptions.strokeColor = '#000000'"-->
<!--        ></div>-->
<!--        &lt;!&ndash; <n-color-picker-->
<!--          v-model:value="borderColorPick"-->
<!--          class="color-item"-->
<!--          :show-alpha="false"-->
<!--          :actions="['confirm']"-->
<!--          :modes="['hex']"-->
<!--          @update:value="borderColor = borderColorPick"-->
<!--        >-->
<!--        </n-color-picker> &ndash;&gt;-->
<!--      </div>-->
<!--    </div>-->

<!--    <div class="query-item">-->
<!--      <div class="query-title">填充颜色：</div>-->
<!--      <div class="color-picker">-->
<!--        <div-->
<!--          v-for="item in colors"-->
<!--          :key="item.id"-->
<!--          class="color-item"-->
<!--          :style="{ background: item.value }"-->
<!--          :class="[-->
<!--            item.haveBorder ? 'have-border' : '',-->
<!--            item.id === 'transparent' ? 'transparent-div' : '',-->
<!--            roiOptions.fillColor === item.value ? 'active-color' : ''-->
<!--          ]"-->
<!--          @click="roiOptions.fillColor = item.value"-->
<!--        ></div>-->
<!--        &lt;!&ndash; <n-color-picker-->
<!--          v-model:value="fillColorPicker"-->
<!--          :show-alpha="false"-->
<!--          :actions="['confirm']"-->
<!--          class="color-item"-->
<!--          :modes="['hex']"-->
<!--          @update:value="fillColor = fillColorPicker"-->
<!--        >-->
<!--        </n-color-picker> &ndash;&gt;-->
<!--      </div>-->
<!--    </div>-->
    <div class="query-item">
      <div class="query-title">落区值：</div>
      <n-input
        v-model:value="roiOptions.value"
        placeholder="请输入落区值"
        class="query-info"
      />
    </div>
  </div>
  <div class="query-bottom">
    <qx-button
      v-show="
        [ActionType.MODIFY, ActionType.MOVE, ActionType.DELETE].includes(
          currentAction
        )
      "
      class="my-btn"
      @click="saveModifyClicked"
      >保存修改
    </qx-button>
    <component
      :is="actionTypeToComponentMap[currentAction]"
      :current-action="currentAction"
      @close="currentAction = ActionType.NONE"
    ></component>
  </div>
</template>

<script setup lang="ts">
import { ActionType, useROIOptions } from './hooks/useROIOptions'
import { QxButton } from 'src/components/QxButton'
import { useFillColors } from './hooks/useColors'
import { useVector } from './hooks/useVector'
import eventBus from 'src/utils/eventBus'
import { roiToolBus } from 'src/components/OpenlayersMap/components/ROITools/hooks/types'

const {
  roiOptions,
  defaultStrokeWidths,
  formatTooltip,
  currentAction,
  actionTypeToComponentMap,
  toggleAction
} = useROIOptions()

const { colors } = useFillColors({
  roiOptions
})
const { vectorLayer, vectorSource } = useVector({ currentAction, roiOptions })

function saveModifyClicked() {
  eventBus.emit(roiToolBus.saveModify)
}
</script>

<style lang="scss" scoped>
.btns {
  display: flex;
  justify-content: space-between;

  .edit-btn {
    width: 60px;
    height: 32px;
    background: #1c81f8;
    border-radius: 4px 4px 4px 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-right: 0; */
    padding: 0px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 16px;

    &:nth-last-child(1) {
      margin-right: 0;
    }

    &.active {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
  }
}

.tools-container {
  // width: 346px;
  height: 110px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 12px 0px;
  padding: 0px 12px;

  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 0px;

    .query-title {
      white-space: nowrap;
      width: 70px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }

    .query-info {
      width: 346px;
    }

    .small-input {
      width: 63px;
      height: 32px;
      margin-left: 8px;
    }

    .my-slider {
      width: 178px;
    }

    .color-picker {
      display: flex;

      .color-item {
        width: 20px;
        height: 20px;
        margin-right: 5px;

        &.active-color {
          border: 3px solid #333;
          padding: 1px;
        }
      }

      .have-border {
        border-radius: 2px 2px 2px 2px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }

      .transparent-div {
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 7px;
          left: 0;
          right: 0;
          height: 2px; /* 斜线的高度 */
          background-color: rgba(0, 0, 0, 0.1); /* 斜线的颜色 */
          transform: rotate(-45deg); /* 斜线的角度 */
        }
      }
    }
  }
}

.my-btn {
  width: 140px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}

.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
