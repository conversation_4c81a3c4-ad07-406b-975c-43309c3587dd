import { method } from 'lodash'
import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_FORECAST_BASE_URL
const axiosInstance = getAxios(baseUrl)

const Scheduling = {
  /**
   * 获取排班列表
   * @returns 
   */
  getSchedulingList(params: any) {
    return axiosInstance({
      url: "/fmSchedulingTable/list",
      method: "GET",
      params
    })
  },

  /**
   * 排班管理 保存
   * @param data 
   * @returns 
   */
  saveScheduling(data: any) {
    return axiosInstance({
      url: "/fmSchedulingTable/saveAll",
      method: "POST",
      data
    })
  },

  /**
   * 导入排版信息
   * @param data 
   * @returns 
   */
  uploadScheduling(data: any) {
    return axiosInstance({
      url: "/fmSchedulingTable/upload",
      method: "POST",
      data
    })
  },

  /**
   * 导出排班信息
   * @returns 
   */
  downloadScheduling(params:any) {
    return axiosInstance({
      url: "/fmSchedulingTable/download",
      method: "GET",
      responseType: 'blob',
      params
    })
  }
}


export default Scheduling