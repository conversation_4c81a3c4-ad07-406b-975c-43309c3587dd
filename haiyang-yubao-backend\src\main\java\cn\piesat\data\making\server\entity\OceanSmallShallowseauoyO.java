package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 浅海小型浮标数据-原始数据（文件编码标识BL）实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_small_shallowsea_buoy_o")
public class OceanSmallShallowseauoyO implements Serializable {

    private static final long serialVersionUID = 841669034846604412L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 浮标站ID
     **/
    @JsonProperty("BuoyInfo_Id")
    @TableField("buoyinfo_id")
    private String buoyinfoId;
    /**
     * 浮标类型
     **/
    @JsonProperty("BuoyInfo_Type")
    @TableField("buoyinfo_type")
    private String buoyinfoType;
    /**
     * 浮标名称
     **/
    @JsonProperty("BuoyInfo_Name")
    @TableField("buoyinfo_name")
    private String buoyinfoName;
    /**
     * 浮标编号
     **/
    @JsonProperty("BuoyInfo_No")
    @TableField("buoyinfo_no")
    private String buoyinfoNo;
    /**
     * 浮标种类
     **/
    @JsonProperty("BuoyInfo_Kind")
    @TableField("buoyinfo_kind")
    private String buoyinfoKind;
    /**
     * 经度
     **/
    @JsonProperty("longitude")
    @TableField("longitude")
    private String longitude;
    /**
     * 经度(标准格式)
     **/
    @JsonProperty("longitude_standard")
    @TableField("longitude_standard")
    private String longitudeStandard;
    /**
     * 经度质控符
     **/
    @JsonProperty("longitude_Qc2")
    @TableField("longitude_qc2")
    private Integer longitudeQc2;
    /**
     * 纬度
     **/
    @JsonProperty("latitude")
    @TableField("latitude")
    private String latitude;
    /**
     * 纬度(标准格式)
     **/
    @JsonProperty("latitude_standard")
    @TableField("latitude_standard")
    private String latitudeStandard;
    /**
     * 纬度质控符
     **/
    @JsonProperty("latitude_Qc2")
    @TableField("latitude_qc2")
    private Integer latitudeQc2;
    /**
     * 采样率
     **/
    @JsonProperty("CommInfo_Sample")
    @TableField("comminfo_sample")
    private String comminfoSample;
    /**
     * 监测时间（原始未处理）
     **/
    @JsonProperty("MonitoringTimeString")
    @TableField("monitoringtimestring")
    private String monitoringtimestring;
    /**
     * 监测时间质控符
     **/
    @JsonProperty("MonitoringTimeString_Qc2")
    @TableField("monitoringtimestring_qc2")
    private Integer monitoringtimestringQc2;
    /**
     * 监测时间
     **/
    @JsonProperty("MonitoringTime")
    @TableField("monitoringtime")
    private Date monitoringtime;
    /**
     * 监测时间质控符
     **/
    @JsonProperty("MonitoringTime_Qc2")
    @TableField("monitoringtime_qc2")
    private Integer monitoringtimeQc2;
    /**
     * 波浪传感器状态 1：正常；0：异常
     **/
    @JsonProperty("RunningStatus_status")
    @TableField("runningstatus_status")
    private String runningstatusStatus;
    /**
     * 波浪传感器状态质控符
     **/
    @JsonProperty("RunningStatus_status_Qc2")
    @TableField("runningstatus_status_qc2")
    private Integer runningstatusStatusQc2;
    /**
     * 内部温度
     **/
    @JsonProperty("RunningStatus_InTem")
    @TableField("runningstatus_intem")
    private String runningstatusIntem;
    /**
     * 内部温度质控符
     **/
    @JsonProperty("RunningStatus_InTem_Qc2")
    @TableField("runningstatus_intem_qc2")
    private Integer runningstatusIntemQc2;
    /**
     * 系统未发送数据条数
     **/
    @JsonProperty("RunningStatus_NoSend")
    @TableField("runningstatus_nosend")
    private String runningstatusNosend;
    /**
     * 系统未发送数据条数压质控符
     **/
    @JsonProperty("RunningStatus_NoSend_Qc2")
    @TableField("runningstatus_nosend_qc2")
    private Integer runningstatusNosendQc2;
    /**
     * 工作模式
     **/
    @JsonProperty("RunningStatus_Mode")
    @TableField("runningstatus_mode")
    private String runningstatusMode;
    /**
     * 工作模式质控符
     **/
    @JsonProperty("RunningStatus_Mode_Qc2")
    @TableField("runningstatus_mode_qc2")
    private Integer runningstatusModeQc2;
    /**
     * 浮标序列号
     **/
    @JsonProperty("RunningStatus_ID")
    @TableField("runningstatus_id")
    private String runningstatusId;
    /**
     * 浮标序列号质控符
     **/
    @JsonProperty("RunningStatus_ID_Qc2")
    @TableField("runningstatus_id_qc2")
    private Integer runningstatusIdQc2;
    /**
     * 风速（预留）
     **/
    @JsonProperty("BuoyData_WS")
    @TableField("buoydata_ws")
    private String buoydataWs;
    /**
     * 风速质控符
     **/
    @JsonProperty("BuoyData_WS_Qc2")
    @TableField("buoydata_ws_qc2")
    private Integer buoydataWsQc2;
    /**
     * 风向（预留）
     **/
    @JsonProperty("BuoyData_WD")
    @TableField("buoydata_wd")
    private String buoydataWd;
    /**
     * 风向质控符
     **/
    @JsonProperty("BuoyData_WD_Qc2")
    @TableField("buoydata_wd_qc2")
    private Integer buoydataWdQc2;
    /**
     * 气温（预留）
     **/
    @JsonProperty("BuoyData_AT")
    @TableField("buoydata_at")
    private String buoydataAt;
    /**
     * 气温质控符
     **/
    @JsonProperty("BuoyData_AT_Qc2")
    @TableField("buoydata_at_qc2")
    private Integer buoydataAtQc2;
    /**
     * 气压（预留）
     **/
    @JsonProperty("BuoyData_BP")
    @TableField("buoydata_bp")
    private String buoydataBp;
    /**
     * 气压质控符
     **/
    @JsonProperty("BuoyData_BP_Qc2")
    @TableField("buoydata_bp_qc2")
    private Integer buoydataBpQc2;
    /**
     * 相对湿度（预留）
     **/
    @JsonProperty("BuoyData_HU")
    @TableField("buoydata_hu")
    private String buoydataHu;
    /**
     * 相对湿度质控符
     **/
    @JsonProperty("BuoyData_HU_Qc2")
    @TableField("buoydata_hu_qc2")
    private Integer buoydataHuQc2;
    /**
     * 表层水温（预留）
     **/
    @JsonProperty("BuoyData_WT")
    @TableField("buoydata_wt")
    private String buoydataWt;
    /**
     * 表层水温质控符
     **/
    @JsonProperty("BuoyData_WT_Qc2")
    @TableField("buoydata_wt_qc2")
    private Integer buoydataWtQc2;
    /**
     * 表层盐度（预留）
     **/
    @JsonProperty("BuoyData_SL")
    @TableField("buoydata_sl")
    private String buoydataSl;
    /**
     * 表层盐度质控符
     **/
    @JsonProperty("BuoyData_SL_Qc2")
    @TableField("buoydata_sl_qc2")
    private Integer buoydataSlQc2;
    /**
     * 平均波高
     **/
    @JsonProperty("BuoyData_BG")
    @TableField("buoydata_bg")
    private String buoydataBg;
    /**
     * 平均波高质控符
     **/
    @JsonProperty("BuoyData_BG_Qc2")
    @TableField("buoydata_bg_qc2")
    private Integer buoydataBgQc2;
    /**
     * 波向
     **/
    @JsonProperty("BuoyData_BX")
    @TableField("buoydata_bx")
    private String buoydataBx;
    /**
     * 波向质控符
     **/
    @JsonProperty("BuoyData_BX_Qc2")
    @TableField("buoydata_bx_qc2")
    private Integer buoydataBxQc2;
    /**
     * 平均波周期
     **/
    @JsonProperty("BuoyData_ZQ")
    @TableField("buoydata_zq")
    private String buoydataZq;
    /**
     * 平均波周期质控符
     **/
    @JsonProperty("BuoyData_ZQ_Qc2")
    @TableField("buoydata_zq_qc2")
    private Integer buoydataZqQc2;
    /**
     * 有效波高
     **/
    @JsonProperty("BuoyData_YBG")
    @TableField("buoydata_ybg")
    private String buoydataYbg;
    /**
     * 有效波高质控符
     **/
    @JsonProperty("BuoyData_YBG_Qc2")
    @TableField("buoydata_ybg_qc2")
    private Integer buoydataYbgQc2;
    /**
     * 有效波周期
     **/
    @JsonProperty("BuoyData_YZQ")
    @TableField("buoydata_yzq")
    private String buoydataYzq;
    /**
     * 有效波周期质控符
     **/
    @JsonProperty("BuoyData_YZQ_Qc2")
    @TableField("buoydata_yzq_qc2")
    private Integer buoydataYzqQc2;
    /**
     * 十分之一波高
     **/
    @JsonProperty("BuoyData_TenthBG")
    @TableField("buoydata_tenthbg")
    private String buoydataTenthbg;
    /**
     * 十分之一波高质控符
     **/
    @JsonProperty("BuoyData_TenthBG_Qc2")
    @TableField("buoydata_tenthbg_qc2")
    private Integer buoydataTenthbgQc2;
    /**
     * 十分之一波周期
     **/
    @JsonProperty("BuoyData_TenthZQ")
    @TableField("buoydata_tenthzq")
    private String buoydataTenthzq;
    /**
     * 十分之一波周期质控符
     **/
    @JsonProperty("BuoyData_TenthZQ_Qc2")
    @TableField("buoydata_tenthzq_qc2")
    private Integer buoydataTenthzqQc2;
    /**
     * 最大波高
     **/
    @JsonProperty("BuoyData_ZBG")
    @TableField("buoydata_zbg")
    private String buoydataZbg;
    /**
     * 最大波高质控符
     **/
    @JsonProperty("BuoyData_ZBG_Qc2")
    @TableField("buoydata_zbg_qc2")
    private Integer buoydataZbgQc2;
    /**
     * 最大波周期
     **/
    @JsonProperty("BuoyData_ZZQ")
    @TableField("buoydata_zzq")
    private String buoydataZzq;
    /**
     * 最大波周期质控符
     **/
    @JsonProperty("BuoyData_ZZQ_Qc2")
    @TableField("buoydata_zzq_qc2")
    private Integer buoydataZzqQc2;
    /**
     * 波数
     **/
    @JsonProperty("BuoyData_BS")
    @TableField("buoydata_bs")
    private String buoydataBs;
    /**
     * 波数质控符
     **/
    @JsonProperty("BuoyData_BS_Qc2")
    @TableField("buoydata_bs_qc2")
    private Integer buoydataBsQc2;
    /**
     * 谱有效波高
     **/
    @JsonProperty("Pu_PBG")
    @TableField("pu_pbg")
    private String puPbg;
    /**
     * 谱有效波高质控符
     **/
    @JsonProperty("Pu_PBG_Qc2")
    @TableField("pu_pbg_qc2")
    private Integer puPbgQc2;
    /**
     * 谱峰周期
     **/
    @JsonProperty("Pu_PFZQ")
    @TableField("pu_pfzq")
    private String puPfzq;
    /**
     * 谱峰周期质控符
     **/
    @JsonProperty("Pu_PFZQ_Qc2")
    @TableField("pu_pfzq_qc2")
    private Integer puPfzqQc2;
    /**
     * 谱平均周期
     **/
    @JsonProperty("Pu_PPJZQ")
    @TableField("pu_ppjzq")
    private String puPpjzq;
    /**
     * 谱平均周期质控符
     **/
    @JsonProperty("Pu_PPJZQ_Qc2")
    @TableField("pu_ppjzq_qc2")
    private Integer puPpjzqQc2;
    /**
     * 谱峰波向
     **/
    @JsonProperty("Pu_PFBX")
    @TableField("pu_pfbx")
    private String puPfbx;
    /**
     * 谱峰波向质控符
     **/
    @JsonProperty("Pu_PFBX_Qc2")
    @TableField("pu_pfbx_qc2")
    private Integer puPfbxQc2;
    /**
     * 谱平均波向
     **/
    @JsonProperty("Pu_PPJBX")
    @TableField("pu_ppjbx")
    private String puPpjbx;
    /**
     * 谱平均波向质控符
     **/
    @JsonProperty("Pu_PPJBX_Qc2")
    @TableField("pu_ppjbx_qc2")
    private Integer puPpjbxQc2;
    /**
     * 风浪谱有效波高
     **/
    @JsonProperty("FLPu_PBG")
    @TableField("flpu_pbg")
    private String flpuPbg;
    /**
     * 风浪谱有效波高质控符
     **/
    @JsonProperty("FLPu_PBG_Qc2")
    @TableField("flpu_pbg_qc2")
    private Integer flpuPbgQc2;
    /**
     * 风浪谱峰周期
     **/
    @JsonProperty("FLPu_PFZQ")
    @TableField("flpu_pfzq")
    private String flpuPfzq;
    /**
     * 风浪谱峰周期质控符
     **/
    @JsonProperty("FLPu_PFZQ_Qc2")
    @TableField("flpu_pfzq_qc2")
    private Integer flpuPfzqQc2;
    /**
     * 风浪谱平均周期
     **/
    @JsonProperty("FLPu_PPJZQ")
    @TableField("flpu_ppjzq")
    private String flpuPpjzq;
    /**
     * 风浪谱平均周期质控符
     **/
    @JsonProperty("FLPu_PPJZQ_Qc2")
    @TableField("flpu_ppjzq_qc2")
    private Integer flpuPpjzqQc2;
    /**
     * 风浪谱峰波向
     **/
    @JsonProperty("FLPu_PFBX")
    @TableField("flpu_pfbx")
    private String flpuPfbx;
    /**
     * 风浪谱峰波向质控符
     **/
    @JsonProperty("FLPu_PFBX_Qc2")
    @TableField("flpu_pfbx_qc2")
    private Integer flpuPfbxQc2;
    /**
     * 风浪谱平均波向
     **/
    @JsonProperty("FLPu_PPJBX")
    @TableField("flpu_ppjbx")
    private String flpuPpjbx;
    /**
     * 风浪谱平均波向质控符
     **/
    @JsonProperty("FLPu_PPJBX_Qc2")
    @TableField("flpu_ppjbx_qc2")
    private Integer flpuPpjbxQc2;
    /**
     * 涌浪谱有效波高
     **/
    @JsonProperty("YLPu_PBG")
    @TableField("ylpu_pbg")
    private String ylpuPbg;
    /**
     * 涌浪谱有效波高质控符
     **/
    @JsonProperty("YLPu_PBG_Qc2")
    @TableField("ylpu_pbg_qc2")
    private Integer ylpuPbgQc2;
    /**
     * 涌浪谱峰周期
     **/
    @JsonProperty("YLPu_PFZQ")
    @TableField("ylpu_pfzq")
    private String ylpuPfzq;
    /**
     * 涌浪谱峰周期质控符
     **/
    @JsonProperty("YLPu_PFZQ_Qc2")
    @TableField("ylpu_pfzq_qc2")
    private Integer ylpuPfzqQc2;
    /**
     * 涌浪谱平均周期
     **/
    @JsonProperty("YLPu_PPJZQ")
    @TableField("ylpu_ppjzq")
    private String ylpuPpjzq;
    /**
     * 涌浪谱平均周期质控符
     **/
    @JsonProperty("YLPu_PPJZQ_Qc2")
    @TableField("ylpu_ppjzq_qc2")
    private Integer ylpuPpjzqQc2;
    /**
     * 涌浪谱峰波向
     **/
    @JsonProperty("YLPu_PFBX")
    @TableField("ylpu_pfbx")
    private String ylpuPfbx;
    /**
     * 涌浪谱峰波向质控符
     **/
    @JsonProperty("YLPu_PFBX_Qc2")
    @TableField("ylpu_pfbx_qc2")
    private Integer ylpuPfbxQc2;
    /**
     * 涌浪谱平均波向
     **/
    @JsonProperty("YLPu_PPJBX")
    @TableField("ylpu_ppjbx")
    private String ylpuPpjbx;
    /**
     * 涌浪谱平均波向质控符
     **/
    @JsonProperty("YLPu_PPJBX_Qc2")
    @TableField("ylpu_ppjbx_qc2")
    private Integer ylpuPpjbxQc2;
    /**
     * 反演风速 m/s
     **/
    @JsonProperty("FY_WS")
    @TableField("fy_ws")
    private String fyWs;
    /**
     * 反演风速质控符
     **/
    @JsonProperty("FY_WS_Qc2")
    @TableField("fy_ws_qc2")
    private Integer fyWsQc2;
    /**
     * 反演风速 m/s
     **/
    @JsonProperty("FY_WD")
    @TableField("fy_wd")
    private String fyWd;
    /**
     * 反演风速质控符
     **/
    @JsonProperty("FY_WD_Qc2")
    @TableField("fy_wd_qc2")
    private Integer fyWdQc2;
    /**
     * 一维能量谱
     **/
    @JsonProperty("NLPu")
    @TableField("nlpu")
    private String nlpu;
    /**
     * 一维能量谱质控符
     **/
    @JsonProperty("NLPu_Qc2")
    @TableField("nlpu_qc2")
    private Integer nlpuQc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    private Date createtime;
    /**
     * 名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
    /**
     * 经度
     **/
    @JsonProperty("HY_LONGITUDE")
    @TableField("hy_longitude")
    private String hyLongitude;
    /**
     * 纬度
     **/
    @JsonProperty("HY_LATITUDE")
    @TableField("hy_latitude")
    private String hyLatitude;
}



