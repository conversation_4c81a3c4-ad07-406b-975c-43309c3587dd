package cn.piesat.data.making.server.dto;

import cn.piesat.data.making.server.entity.AlarmProductTemplate;

import java.util.List;

public class AlarmProductTemplateDTO {
    /**
     * 模板类型名称
     */
    private String templateTypeName;
    /**
     * 模板类型 1 海浪警报 2海浪消息  3风暴潮警报 4风暴潮消息
     */
    private Integer templateType;

    private List<AlarmProductTemplate> templateList;

    public String getTemplateTypeName() {
        return templateTypeName;
    }

    public void setTemplateTypeName(String templateTypeName) {
        this.templateTypeName = templateTypeName;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public List<AlarmProductTemplate> getTemplateList() {
        return templateList;
    }

    public void setTemplateList(List<AlarmProductTemplate> templateList) {
        this.templateList = templateList;
    }

    public AlarmProductTemplateDTO() {

    }

    public AlarmProductTemplateDTO(String templateTypeName, Integer templateType) {
        this.templateTypeName = templateTypeName;
        this.templateType = templateType;
    }

    public AlarmProductTemplateDTO(String templateTypeName, Integer templateType, List<AlarmProductTemplate> templateList) {
        this.templateTypeName = templateTypeName;
        this.templateType = templateType;
        this.templateList = templateList;
    }
}
