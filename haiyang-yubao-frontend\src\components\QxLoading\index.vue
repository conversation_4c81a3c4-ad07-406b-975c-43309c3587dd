<template>
  <div class="qx-loading">
    <img class="loader"  src="src/assets/images/common/loading.gif"/>
  </div>
</template>

<script setup lang="ts" name="loading">
import { ref, onMounted } from 'vue'

const show = ref(false)

const showLoading = () => {
  show.value = true
  document.body.style.overflow = 'hidden'
  document.addEventListener('touchmove', () => {}, true)
}
const hideLoading = () => {
  show.value = false
  var mo = function (e:Event) {
    e.preventDefault()
  }
  document.body.style.overflow = ''
  document.removeEventListener('touchmove', mo, true)
}

onMounted(() => {})

defineExpose({
  show,
  showLoading,
  hideLoading
})
</script>

<style scoped>
.qx-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(255, 255, 255, .9);
  display: flex;
  align-items: center;
  justify-content: center;
}
.tips {
  font-family: 'Open Sans';
  color: #52b852;
  font-size: 1rem;
  width: 100%;
  text-align: center;
  position: absolute;
  top: 55%;
  line-height: 30px;
}
/* .loader {
  width: 20px;
  aspect-ratio: 0.577;
  clip-path: polygon(0 0, 100% 100%, 0 100%, 100% 0);
  position: relative;
  animation: l19 2s infinite linear;
  overflow: hidden;
  position: relative;
  left: 50%;
  top: 45%;
  margin: 0 0 0 -25px;
}
.loader:before {
  content: '';
  position: absolute;
  inset: -150% -150%;
  background: repeating-conic-gradient(
    from 30deg,
    #ffabab 0 60deg,
    #abe4ff 0 120deg,
    #ff7373 0 180deg
  );
  animation: inherit;
  animation-direction: reverse;
} */

.loader{
  width: 40px;
  height: 40px;
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
</style>