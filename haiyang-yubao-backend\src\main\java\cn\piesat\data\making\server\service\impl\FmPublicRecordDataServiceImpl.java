package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmPublicRecordDataDao;
import cn.piesat.data.making.server.entity.FmPublicRecordData;
import cn.piesat.data.making.server.mapper.FmPublicRecordDataMapper;
import cn.piesat.data.making.server.service.FmPublicRecordDataService;
import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class FmPublicRecordDataServiceImpl extends ServiceImpl<FmPublicRecordDataDao, FmPublicRecordData> implements FmPublicRecordDataService {

    @Autowired
    private FmPublicRecordDataMapper fmPublicRecordDataMapperImpl;

    @Override
    public List<FmPublicRecordDataVO> getListByRecordId(Long resultId) {
        LambdaQueryWrapper<FmPublicRecordData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicRecordData::getRecordId, resultId);
        List<FmPublicRecordData> resList= this.list(queryWrapper);
        if(!CollectionUtils.isEmpty(resList)){
            return fmPublicRecordDataMapperImpl.entityListToVoList(resList);
        }
        return null;
    }

    @Override
    public void saveList(List<FmPublicRecordDataVO> list) {
        if(!CollectionUtils.isEmpty(list)){
            List<FmPublicRecordData> entityList = fmPublicRecordDataMapperImpl.voListToEntityList(list);
            this.saveOrUpdateBatch(entityList);
        }
    }

    public void updateList(List<FmPublicRecordDataVO> list){
        if(!CollectionUtils.isEmpty(list)){
            List<FmPublicRecordData> entityList = fmPublicRecordDataMapperImpl.voListToEntityList(list);
            this.saveOrUpdateBatch(entityList);
        }
    }

    @Override
    public void delete(Long id) {
        this.removeById(id);
    }

    @Override
    public void deleteAll(Long resultId) {
        LambdaQueryWrapper<FmPublicRecordData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicRecordData::getRecordId, resultId);

        this.remove(queryWrapper);
    }
}
