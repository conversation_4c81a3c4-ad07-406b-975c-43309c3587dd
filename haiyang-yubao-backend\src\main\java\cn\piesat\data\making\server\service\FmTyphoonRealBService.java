package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonRealB;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 台风实时数据信息服务接口
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:23
 */
public interface FmTyphoonRealBService extends IService<FmTyphoonRealB>{

        /**
         * 根据参数查询分页
         */
        PageResult<FmTyphoonRealBVO> getPage(FmTyphoonRealBDTO dto,PageParam pageParam);

        /**
         * 根据参数查询列表
         */
        List<FmTyphoonRealBVO> getList(FmTyphoonRealBDTO dto);

        /**
         * 根据id查询数据
         */
        FmTyphoonRealBVO getById(Long id);

        /**
         * 保存数据
         */
        void save(FmTyphoonRealBDTO dto);

        /**
         * 批量保存数据
         */
        void saveList(List<FmTyphoonRealBDTO> dtoList);

        /**
         * 根据id删除数据
         */
        void deleteById(Long id);

        /**
         * 根据idList批量删除数据
         */
        void deleteByIdList(List<Long> idList);

         List<FmTyphoonRealB> getByTfbh(String typhoonNo);

        FmTyphoonRealB getInfo(String typhoonNo, Date typhoonTime);
}
