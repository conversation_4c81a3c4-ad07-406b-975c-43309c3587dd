package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.RegionDTO;
import cn.piesat.data.making.server.entity.Region;
import cn.piesat.data.making.server.vo.RegionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RegionMapper {

    RegionMapper INSTANCE = Mappers.getMapper(RegionMapper.class);

    /**
     * entity-->vo
     */
    RegionVO entityToVo(Region entity);

    /**
     * dto-->entity
     */
    Region dtoToEntity(RegionDTO dto);

    /**
     * entityList-->voList
     */
    List<RegionVO> entityListToVoList(List<Region> list);

    /**
     * dtoList-->entityList
     */
    List<Region> dtoListToEntityList(List<RegionDTO> dtoList);
}
