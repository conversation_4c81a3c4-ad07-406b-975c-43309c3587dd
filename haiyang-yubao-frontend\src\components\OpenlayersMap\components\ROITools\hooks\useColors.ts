import type { IROIOptions } from './useROIOptions'
import { computed, Ref } from 'vue'

export interface IColorItem {
  label: string
  value: string
  id: string
  haveBorder: boolean
}

export function defaultColors(): IColorItem[] {
  return [
    {
      label: '黑色',
      value: '#000',
      id: 'black',
      haveBorder: false
    },
    {
      label: '红色',
      value: '#F90102',
      id: 'red',
      haveBorder: false
    },
    {
      label: '橘色',
      value: '#FE7B0E',
      id: 'orange',
      haveBorder: false
    },
    {
      label: '黄色',
      value: '#FFF300',
      id: 'yellow',
      haveBorder: false
    },
    {
      label: '蓝色',
      value: '#0083FD',
      id: 'blue',
      haveBorder: false
    },
    // {
    //   label: '透明',
    //   value: 'rgba(0,0,0,0)',
    //   id: 'transparent',
    //   haveBorder: true
    // },
    {
      label: '白色',
      value: '#ffffff',
      id: 'white',
      haveBorder: true
    }
  ]
}

export function useFillColors(options: { roiOptions: Ref<IROIOptions> }) {
  const { roiOptions } = options
  const colors: IColorItem[] = defaultColors()
  return {
    colors,
    cptIsStrokeColorActive: computed(() => {
      return roiOptions.value.strokeColor === '#000000' ? 'active-color' : ''
    }),
    cptIsFillColorActive: computed(() => {
      return roiOptions.value.fillColor === '#000000' ? 'active-color' : ''
    })
  }
}
