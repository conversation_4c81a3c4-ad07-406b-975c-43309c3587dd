package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.AlarmDefenseGuideDTO;
import cn.piesat.data.making.server.entity.AlarmDefenseGuide;
import cn.piesat.data.making.server.entity.AlarmGuideLevel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AlarmDefenseGuideMapper {
    AlarmDefenseGuideMapper INSTANCE = Mappers.getMapper(AlarmDefenseGuideMapper.class);

    AlarmDefenseGuide toEntity(AlarmDefenseGuideDTO guideDTO);

    @Mapping(source = "guideLevels",target = "guideLevelList")
    AlarmDefenseGuideDTO toDTO(AlarmDefenseGuide guide, List<AlarmGuideLevel> guideLevels);
}
