<template>
  <div class="tools-container">
    <div class="query-item">
      <div class="query-title">预报图：</div>
      <n-select
        v-model:value="modelValue"
        class="query-info"
        :options="options"
        @update:value="handleUpdateValue"
      />
    </div>
  </div>
  <div class="query-bottom">
    <qx-button class="my-btn" @click="addLayer">添加预报图</qx-button>
    <qx-button class="my-btn" @click="clearLayer">清空</qx-button>
  </div>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { ref, onMounted, inject, onUnmounted } from 'vue'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import GeoJSON from 'ol/format/GeoJSON.js'
import { Fill, Stroke, Style, Text } from 'ol/style.js'
import ForecastApi from 'src/requests/forecast'
import { useMessage } from 'naive-ui'
import ToolApi from 'src/requests/toolRequest'
import Point from 'ol/geom/Point.js'
import Feature from 'ol/Feature.js'
import EventBus from 'src/utils/eventBus'
import { IForecastImageItem } from 'src/requests/forecast.type'
import eventBus from 'src/utils/eventBus'
import { waveHeightLineList } from 'src/config/waveHeightLine'

const message = useMessage()
const getMap = inject<(map: any) => void>('getMap')
const modelValue = ref('')
const options = ref(<any>[])
const vectorSource = new VectorSource()
const vectorLayer = new VectorLayer({
  zIndex: 6,
  source: vectorSource
})
const countryStyle = new Style({
  stroke: new Stroke({
    color: 'black',
    width: 2
  }),
  fill: new Fill({
    color: 'rgba(255, 255, 255, 0.6)'
  }),
  text: new Text({
    font: '16px Calibri,sans-serif',
    fill: new Fill({
      color: '#000'
    }),
    stroke: new Stroke({
      color: '#fff',
      width: 3
    }),
    placement: 'line'
  })
})
const style = [countryStyle]

function lineStyles(feature: any) {
  const label = feature.get('value') + ''
  countryStyle?.getText()?.setText(label)
  let color = 'rgba(84, 221, 196, 1)'
  const labelNumber = Number(label)
  const find = waveHeightLineList.find(i => i.condition(labelNumber))
  if (find) {
    color = find.color
  }
  // if (Number(label) <= 2.5) {
  //   color = 'rgba(84, 221, 196, 1)'
  // } else if (Number(label) > 2.5 && Number(label) <= 3) {
  //   color = 'rgba(1, 2, 247, 1)'
  // } else if (Number(label) > 3 && Number(label) <= 4) {
  //   color = 'rgba(253, 242, 5, 1)'
  // } else if (Number(label) > 4 && Number(label) <= 6) {
  //   color = 'rgba(253, 125, 21, 1)'
  // } else if (Number(label) > 6 && Number(label) <= 9) {
  //   color = 'rgba(247, 5, 1, 1)'
  // } else if (Number(label) > 9 && Number(label) <= 14) {
  //   color = 'rgba(119, 8, 4, 1)'
  // } else if (Number(label) > 14) {
  //   color = '#770804'
  // }
  countryStyle?.getStroke()?.setColor(color)
  countryStyle?.getFill()?.setColor(color)
  return style
}

const layerJson = ref(<any>'')
const curId = ref(<any>'')

function handleUpdateValue(value: string, option: any) {
  curId.value = value
}

function addLayer() {
  layerJson.value = ''
  ForecastApi.getYBTInfo({
    id: curId.value
  })
    .then((res: any) => {
      if (res && res.graphicJson) {
        layerJson.value = res.graphicJson
        vectorSource.clear()
        pointSource.refresh()
        // features.push(new Feature(new LineString(lstring)))
        const featuresJson = new GeoJSON().readFeatures(
          JSON.parse(layerJson.value)
        )
        if (featuresJson.length === 0) {
          message.warning('加载成功, 但落区数量为 0')
        }
        featuresJson.forEach((item: any) => {
          item.setStyle(lineStyles)
          vectorSource.addFeature(item)
          item.setProperties({
            properties: { value: item.values_.value }
          })
        })
        getLandPoint()
      } else {
        message.warning('暂无数据')
      }
    })
    .catch(err => {
      console.warn(err)
      layerJson.value = ''
    })
}

function clearLayer() {
  vectorSource.clear()
}

const forecastTmp = ref(<any>null)

function getForecast() {
  ForecastApi.getTemplateByParent({
    productType: 1,
    status: true,
    templateType: 'YBTZZ'
  })
    .then((res: any) => {
      forecastTmp.value = res[0]
      getforecastList()
    })
    .catch(() => {})
}

function getforecastList() {
  ForecastApi.getYBTList({
    graphicTemplateId: forecastTmp.value.id,
    name: 'wave'
  })
    .then((res: any) => {
      options.value = []
      res.forEach((item: any) => {
        if (item.name.includes('wave')) {
          options.value.push({
            label: item.name,
            value: item.id,
            geojson: item.graphicJson
          })
        }
      })
    })
    .catch(() => {})
}

// 陆地标记
const pointSource = new VectorSource()
const pointLayer = new VectorLayer({
  source: pointSource,
  declutter: true, //防止覆盖
  zIndex: 8
})

function addLandPoint(points: any) {
  pointSource.refresh()
  points.forEach((item: any) => {
    const feature = new Feature(new Point(item.coordinate))
    const iconStyle = new Style({
      text: new Text({
        font: '16px Calibri,sans-serif',
        text: item.value,
        fill: new Fill({
          color: '#000'
        }),
        stroke: new Stroke({
          color: '#fff',
          width: 3
        })
      })
    })
    feature.setStyle(iconStyle)
    pointSource.addFeature(feature)
  })
}

function getLandPoint() {
  pointSource.refresh()
  const features: any = []
  vectorSource.forEachFeature((item: any) => {
    features.push(item)
  })
  const geojsonFormat = new GeoJSON()
  const geojsonObject = geojsonFormat.writeFeatures(features)

  // 现在 geojsonObject 是一个 GeoJSON 对象，你可以将其转换为字符串或者直接使用
  const graphicJson = JSON.stringify(geojsonObject)
  // 获取与陆地展示的点
  const formData = new FormData()
  formData.append('geoJsonString', graphicJson)
  ToolApi.getLandPoint(formData)
    .then(res => {
      addLandPoint(res)
    })
    .catch(() => {})
}

onMounted(() => {
  getForecast()
  if (getMap) {
    getMap((map: any) => {
      map.addLayer(vectorLayer)
      map.addLayer(pointLayer)
      vectorLayer.setProperties({
        layerType: '预报图'
      })
    })
  }
})
onUnmounted(() => {
  if (getMap) {
    getMap((map: any) => {
      map.removeLayer(vectorLayer)
      map.removeLayer(pointLayer)
    })
  }
})

onMounted(() => {
  EventBus.on('forecastImageClicked', (imageInfo: IForecastImageItem) => {
    console.log('预报图收到信号')
    const timer = setInterval(() => {
      if (options.value.length > 0) {
        clearInterval(timer)
        const find = options.value.find(
          (i: { label: string; id: string; geojson: string }) =>
            i.label === imageInfo.name
        )
        if (find) {
          handleUpdateValue(find.value, options.value)
          addLayer()
        } else {
          message.warning('暂无预报图')
        }
      }
    }, 500)
  })
})
onUnmounted(() => {
  eventBus.off('forecastImageClicked')
})
</script>

<style lang="scss" scoped>
.tools-container {
  // width: 346px;
  height: 100px;
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 12px 0px;
  padding: 0px 12px;

  .query-item {
    display: flex;
    align-items: center;
    margin: 7px 0px;

    .query-title {
      white-space: nowrap;
      width: 70px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: #222222;
      line-height: 16px;
    }

    .query-info {
      width: 346px;
    }

    .small-input {
      width: 63px;
      height: 32px;
      margin-left: 8px;
    }

    .my-slider {
      width: 178px;
    }
  }
}

.my-btn {
  width: 140px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}

.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
