package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmPublicProductRecordDao;
import cn.piesat.data.making.server.entity.FmPublicProductRecord;
import cn.piesat.data.making.server.entity.FmPublicProductTemplate;
import cn.piesat.data.making.server.entity.ForecastProductRecord;
import cn.piesat.data.making.server.mapper.FmPublicProductRecordMapper;
import cn.piesat.data.making.server.service.FmPublicProductRecordService;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.vo.FmPublicProductRecordVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FmPublicProductRecordServiceImpl extends ServiceImpl<FmPublicProductRecordDao, FmPublicProductRecord> implements FmPublicProductRecordService {

    @Autowired
    private FmPublicProductRecordMapper fmPublicProductRecordMapperImpl;

    @Override
    public void save(FmPublicProductRecordVO vo) {
        FmPublicProductRecord fmPublicProductRecord = fmPublicProductRecordMapperImpl.voToEntity(vo);
        this.save(fmPublicProductRecord);
    }

    @Override
    public void update(FmPublicProductRecordVO vo) {
        FmPublicProductRecord fmPublicProductRecord = fmPublicProductRecordMapperImpl.voToEntity(vo);
        this.updateById(fmPublicProductRecord);
    }

    public FmPublicProductRecordVO findById(Long id){
        FmPublicProductRecord fmPublicProductRecord = this.getById(id);
        if(fmPublicProductRecord!=null){
            return fmPublicProductRecordMapperImpl.entityToVo(fmPublicProductRecord);
        }
        return null;
    }

    @Override
    public FmPublicProductRecordVO findByRecordId(Long recordId) {
        LambdaQueryWrapper<FmPublicProductRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicProductRecord::getRecordId, recordId);
        FmPublicProductRecord FmPublicProductRecord = this.getOne(queryWrapper);

        if(FmPublicProductRecord!=null){
            return fmPublicProductRecordMapperImpl.entityToVo(FmPublicProductRecord);
        }
        return null;
    }


    @Override
    public void download(HttpServletResponse response,Long Id) {
        FmPublicProductRecord fmPublicProductRecord = this.getById(Id);
        //FileUtil.zipFiles(filePathList, "预报产品.zip", response);
        FileUtil.downloadFile(fmPublicProductRecord.getFileUrl(),fmPublicProductRecord.getId()+".docx",response);
    }
}
