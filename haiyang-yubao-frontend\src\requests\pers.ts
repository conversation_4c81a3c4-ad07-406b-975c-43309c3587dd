import getAxios from '../utils/axios'

const baseUrl = import.meta.env.VITE_PERS_BASE_URL
const axiosInstance = getAxios(baseUrl)

const getPersList = () => {
  return axiosInstance({
    method: 'GET',
    url: '/api/login_user/pers'
  })
}

const getUserInfo = () => {
  return axiosInstance({
    url: '/api/login_user/info',
    method: 'GET'
  })
}

/**
 * 获取用户列表
 * @param params
 * @returns
 */
const getUserList = (params: any) => {
  return axiosInstance({
    url: '/api/user/list',
    method: 'GET',
    params
  })
}

export default {
  getPersList,
  getUserInfo,
  getUserList
}
