<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-05 15:55:08
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-10 15:14:46
 * @FilePath: \hainan-jianzai-web\src\components\ElementStatistics\components\Re-analysis.vue
 * @Description: 再分析数据
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="query-item">
    <div class="query-title">海洋要素：</div>
    <div class="query-info">
      <div
        class="radio-btn"
        :class="{ active: element === 'HMFC_TIF' }"
        @click="element = 'HMFC_TIF'"
      >
        海面风场
      </div>
      <div
        class="radio-btn"
        :class="{ active: element === 'HL_TIF' }"
        @click="element = 'HL_TIF'"
      >
        海浪
      </div>
      <div
        class="radio-btn"
        :class="{ active: element === 'HW_TIF' }"
        @click="element = 'HW_TIF'"
      >
        海温
      </div>
    </div>
  </div>
  <div class="query-item">
    <div class="query-title">时间范围：</div>
    <div class="query-info">
      <n-date-picker
        v-model:formatted-value="range"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetimerange"
        size="small"
        clearable
        :default-time="['00:00:00', '23:59:59']"
      />
    </div>
  </div>
  <!-- <div class="query-item">
    <div class="query-title">空间范围：</div>
    <div class="query-info">
      <n-input
        v-model:value="locationJson"
        placeholder="请输入"
        clearable
        size="small"
        @update:value="updateFeature"
      />

      <Scan class="icon-scan" @click="drawHandler" />
    </div>
  </div> -->
  <div class="center-btn">
    <qx-button
      class="query-btn"
      :class="{ active: curQueryType === 'HN_ZFX_KJCX_' }"
      @click="queryInfo('HN_ZFX_KJCX_')"
      >空间查询</qx-button
    >
    <qx-button
      class="query-btn"
      :class="{ active: curQueryType === 'HN_ZFX_JPFX_' }"
      @click="queryInfo('HN_ZFX_JPFX_')"
      >距平分析</qx-button
    >
    <qx-button
      class="query-btn"
      :class="{ active: curQueryType === 'HN_ZFX_TZFX_' }"
      @click="queryInfo('HN_ZFX_TZFX_')"
      >特征统计</qx-button
    >
  </div>

  <n-divider style="margin-top: 10px; margin-bottom: 10px" />
  <n-data-table
    :bordered="true"
    :single-line="true"
    :columns="columns"
    :data="tableData"
    :pagination="paginationReactive"
    pagination-behavior-on-filter="first"
    :row-props="rowProps"
    :paginate-single-page="false"
    size="small"
  />
  <div class="alis-mouse-position">
    <div class="legend">
      <div class="text">
        {{
          element === 'HMFC_TIF'
            ? '海面风场'
            : element === 'HL_TIF'
            ? '海浪'
            : '海温'
        }}
      </div>
      <div class="color-ramp">
        <span v-for="item in colorRamp" :key="item">{{ item.toFixed(1) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  inject,
  onMounted,
  onBeforeUnmount,
  onUnmounted,
  reactive
} from 'vue'
import { Scan } from '@vicons/ionicons5'
import { Modify, Select } from 'ol/interaction.js'
import { Circle, Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { GeoJSON } from 'ol/format'
import { Draw } from 'ol/interaction'
import { useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import { hexToRgbs } from 'src/utils/hexToRgb.js'
import { WebGLTile } from 'ol/layer'
import GeoTIFFSource from 'ol/source/GeoTIFF'
import ToolApi from 'src/requests/toolRequest'
import moment from 'moment'

import * as GeoTIFF from 'geotiff'
const message = useMessage()
const element = ref('HMFC_TIF') // 海洋要素
const getMap = inject('getMap')
// const range = ref([
//   moment().format('YYYY-MM-DD 00:00:00'),
//   moment().format('YYYY-MM-DD 23:59:59')
// ]) //时间范围2024-11-28 00:00:00']) //时间范围
const range = ref(['2024-01-01 00:00:00', '2024-11-04 23:59:59'])
// const locationJson = ref('') //空间范围
const curQueryType = ref('') // 当前查询类型
/** 空间范围修改功能 */
const source = new VectorSource()
const layer = new VectorLayer({
  source: source,
  zIndex: 8
})
function updateFeature(value) {
  source.clear()
  if (value.trim() !== '') {
    const feature = new GeoJSON().readFeatures(value)
    feature[0].setStyle(
      new Style({
        fill: new Fill({
          color: 'rgba(0, 0, 255, 0.1)'
        }),
        stroke: new Stroke({
          color: '#ff0000'
        })
      })
    )
    source.addFeature(feature[0])
  }
}
// let draw = null
// function drawHandler() {
//   getMap(map => {
//     if (draw) {
//       map.removeInteraction(draw)
//     }
//     draw = new Draw({
//       type: 'Polygon', //绘制的几何图形的几何类型
//       source
//     })
//     map.addInteraction(draw)

//     draw.on('drawstart', drawstart)
//     draw.on('drawend', drawend)
//   })
// }
// function drawstart(evt) {
//   source.refresh()

//   message.warning('开始绘制,双击击结束')
// }
// function drawend(event) {
//   getMap(map => {
//     const json = new GeoJSON().writeFeature(event.feature)
//     locationJson.value = json
//     map.removeInteraction(draw)
//   })
// }
/**查询 */
function queryInfo(str) {
  let params = {
    beginTime: range.value[0],
    endTime: range.value[1],
    orderBy: 'data_time',
    sort: 'desc'
  }
  curQueryType.value = str
  if (str === 'HN_ZFX_TZFX_') {
    params = {}
  }
  ToolApi.getReAnalysis(str + element.value, params)
    .then(res => {
      tableData.value = res
    })
    .catch(() => {
      tableData.value = []
    })
}
/** 表格部分 */
const columns = ref(createColumns())
const tableData = ref([])
function createColumns() {
  return [
    {
      title: '序号',
      width: 50,
      render(row, rowIndex) {
        return rowIndex + 1
      }
    },
    {
      title: '数据名称',
      key: 'file_name',
      ellipsis: {
        tooltip: {
          maxWidth: 300
        },
        lineClamp: 1
      }
    },
    {
      title: '数据时间',
      key: 'data_time',
      ellipsis: {
        tooltip: {
          maxWidth: 300
        },
        lineClamp: 1
      }
    }
  ]
}
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSlot: 5,
  size: 'small',
  onChange: page => {
    paginationReactive.page = page
  }
})
const currentRowIndex = ref(-1)
function rowProps(row, rowIndex) {
  return {
    style: { cursor: 'pointer' },
    class: currentRowIndex.value === rowIndex ? 'row-active' : '',
    onClick() {
      onRowClick(row, rowIndex)
    }
  }
}
const jet = [
  '#00007F',
  '#000091',
  '#0000A3',
  '#0000B6',
  '#0000C8',
  '#0000DA',
  '#0000EC',
  '#0000FE',
  '#0000FF',
  '#0010FF',
  '#0020FF',
  '#0030FF',
  '#0040FF',
  '#0050FF',
  '#0060FF',
  '#0070FF',
  '#0084FF',
  '#0094FF',
  '#00A4FF',
  '#00B4FF',
  '#00C4FF',
  '#00D4FF',
  '#00E4F7',
  '#0CF4EA',
  '#18FFDD',
  '#25FFD0',
  '#32FFC3',
  '#3FFFB7',
  '#4CFFAA',
  '#59FF9D',
  '#66FF90',
  '#73FF83',
  '#83FF73',
  '#90FF66',
  '#9DFF59',
  '#AAFF4C',
  '#B7FF3F',
  '#C3FF32',
  '#D0FF25',
  '#DDFF18',
  '#EAFF0C',
  '#F7F400',
  '#FFE500',
  '#FFD700',
  '#FFC800',
  '#FFB900',
  '#FFAA00',
  '#FF9B00',
  '#FF8900',
  '#FF7A00',
  '#FF6B00',
  '#FF5C00',
  '#FF4D00',
  '#FF3F00',
  '#FF3000',
  '#FF2100',
  '#FE1200',
  '#EC0300',
  '#DA0000',
  '#C80000',
  '#B60000',
  '#A30000',
  '#910000',
  '#7F0000'
]
const colorRamp = ref([]) // colorBar数字部分
/** 点击表格数据展示对应的图层 */
let elementLayer = new WebGLTile()
async function onRowClick(row, rowIndex) {
  currentRowIndex.value = rowIndex
  const url = config.onlyOfficeServerUrl + row.file_path
  const tif = await GeoTIFF.fromUrl(url)
  // const tif = await GeoTIFF.fromUrl("/lp/data/W_NAFP_C_ECMF_20231009054242_P_C1D10090000100900001.10u.tif");

  const tifImage = await tif.getImage()
  const rasters = await tifImage.readRasters()
  // console.log(tif, tifImage, rasters)
  let max = Math.max(...rasters[0])
  const min = Math.min(...rasters[0])

  if (max == 65535) {
    max = secondMax(Array.from(rasters[0]))
  }
  const arr = divideRangeIntoParts(min, max, 64)
  colorRamp.value = divideRangeIntoParts(min, max, 5)
  const colorArr = []
  arr.forEach((item, index) => {
    colorArr.push({
      color: jet[index],
      label: item,
      opacity: '1.0',
      quantity: item
    })
  })
  const colorData = {
    colorAttr: colorArr,
    invalidValue: '65535',
    renderType: 'ramp'
  }
  console.log(colorData)
  // const colorData = colors[element.value]

  createLayer(url, colorData)
}
function secondMax(arr) {
  if (arr.length < 2) {
    return null // 数组长度小于2时，无法找到第二大的数
  }

  let largest = -Infinity
  let secondLargest = -Infinity

  for (let i = 0; i < arr.length; i++) {
    if (arr[i] > largest) {
      secondLargest = largest
      largest = arr[i]
    } else if (arr[i] > secondLargest && arr[i] !== largest) {
      secondLargest = arr[i]
    }
  }

  return secondLargest === -Infinity ? null : secondLargest
}
/** 图层处理 */
// 获取tif图层样式
function getTifStyle(colorData) {
  let obj = {}
  if (colorData) {
    const { colorAttr, invalidValue, renderType } = colorData
    const color = []
    const opacity = []
    const quantity = []
    colorAttr.forEach(item => {
      color.push(item.color)
      opacity.push(item.opacity - 0)
      quantity.push(item.quantity - 0)
    })
    obj = hexToRgbs(color, quantity, opacity, renderType, invalidValue)
    return obj
  } else {
    return obj
  }
}
function createLayer(url, colorData) {
  const sourceOptions = {
    url: url,
    max: null,
    min: null,
    bands: ['1'],
    nodata: colorData.invalidValue
  }
  const source = new GeoTIFFSource({
    sources: [sourceOptions],
    normalize: false, // 归一化
    interpolate: false, // 插值
    wrapX: false,
    crossOrigin: 'anonymous'
  })
  elementLayer.setSource(source)
  const styleOptions = getTifStyle(colorData)
  elementLayer.setStyle({
    color: styleOptions
  })
  // getMap(map => {
  //   map.addLayer(elementLayer)
  // })
}
// 获取xml的最大值最小值属性
function getMinMaxFromXML(xmlString) {
  // 创建一个新的DOMParser实例
  let parser = new DOMParser()
  // 解析XML字符串
  let xmlDoc = parser.parseFromString(xmlString, 'text/xml')

  // 获取所有的Item元素
  let items = xmlDoc.getElementsByTagName('Item')
  let min = null
  let max = null

  // 遍历所有Item元素
  for (let i = 0; i < items.length; i++) {
    let item = items[i]
    // 检查Item元素的name属性
    if (item.getAttribute('name') === 'min') {
      min = parseFloat(item.textContent)
    } else if (item.getAttribute('name') === 'max') {
      max = parseFloat(item.textContent)
    }
  }

  return { min, max }
}
// 将数组平均分成几份
function divideRangeIntoParts(min, max, parts) {
  if (max <= min) {
    throw new Error('max must be greater than min')
  }

  let range = max - min
  let step = range / (parts - 1) // parts - 1 是为了确保包含 max 值
  let result = []

  for (let i = 0; i < parts; i++) {
    let value = min + i * step
    result.push(value)
  }

  return result
}
onMounted(() => {
  getMap(map => {
    map.addLayer(layer)
    map.addLayer(elementLayer)
  })
})
onUnmounted(() => {
  getMap(map => {
    if (elementLayer) {
      map.removeLayer(elementLayer)
    }
    map.removeLayer(layer)
    // if (draw) {
    //   map.removeInteraction(draw)
    // }
  })
})
</script>

<style lang="scss" scoped>
.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;
  .query-title {
    white-space: nowrap;
    width: 70px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }
  .query-info {
    width: 346px;
    display: flex;
    align-items: center;
    .radio-btn {
      background-color: rgb(231, 241, 253);
      padding: 5px 8px;
      margin: 0px 5px;
      border-radius: 4px;
      color: #222222;
      &:hover {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
      &.active {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
    }
    .icon-scan {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
::v-deep .row-active {
  background: #e8f0fa;
  .n-data-table-td {
    background: transparent;
  }
}
.center-btn {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .query-btn {
    background: #1c81f8;
    color: #fff;
    &:hover {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
    &.active {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
  }
}
::v-deep .qx-button {
  padding: 0px 8px;
}
.alis-mouse-position {
  position: fixed;
  z-index: 800;
  right: 20px;
  bottom: 18px;
  width: 370px;
  // padding: 15px;
  color: #fff;
  background: #eff4fc;
  border-radius: 4px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  .legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 15px;
    width: 100%;
    height: 100%;
    .text {
      white-space: nowrap;
      margin-right: 8px;
    }
    .color-ramp {
      flex: 1;
      position: relative;
      height: 15px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 12px;
      padding: 0px 6px;
      background: linear-gradient(
        90deg,
        #00007f,
        #000093,
        #0000a9,
        #0000c1,
        #0000d7,
        #0000ed,
        #0000ff,
        #0009ff,
        #001eff,
        #0032ff,
        #0045ff,
        #005aff,
        #006eff,
        #0083ff,
        #0096ff,
        #00aaff,
        #00bfff,
        #00d2ff,
        #01e7f4,
        #11fae4,
        #20ffd5,
        #31ffc3,
        #41ffb4,
        #52ffa3,
        #62ff93,
        #72ff83,
        #82ff73,
        #92ff63,
        #a1ff54,
        #b2ff42,
        #c2ff33,
        #d4ff21,
        #e3ff12,
        #f3f902,
        #ffe500,
        #ffd300,
        #ffbf00,
        #ffae00,
        #ff9b00,
        #ff8800,
        #ff7600,
        #ff6300,
        #ff5000,
        #ff3f00,
        #ff2b00,
        #ff1a00,
        #ef0600,
        #d90000,
        #c30000,
        #ab0000,
        #950000,
        #7f0000
      );
    }
  }
}
</style>
