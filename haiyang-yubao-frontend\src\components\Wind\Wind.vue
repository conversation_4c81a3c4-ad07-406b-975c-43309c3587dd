<template>
  <div></div>
</template>

<script>
import 'ol/ol.css'
import { Vector as VectorSource } from 'ol/source'
import { Tile as TileLayer, Vector as VectorLayer } from 'ol/layer'
import { Style, Icon, Fill, Text } from 'ol/style'
import { View, Map as OlMap } from 'ol'
import { Point } from 'ol/geom'
import Feature from 'ol/Feature'
import GeoTIFF from 'ol/source/GeoTIFF'
import WebGLTileLayer from 'ol/layer/WebGLTile'
import blue from './blueWind.json'
import { GeoJsonFormatFeilds } from 'src/utils/styleFeilds.js'
import { containsXY } from 'ol/extent'
import { unByKey } from 'ol/Observable'
import windPNG from 'src/assets/images/windy.png'
import { transform } from 'ol/proj'

export default {
  name: 'WindLayers',
  props: {
    /** @type {OlMap} */
    map: Object,
    name: String
  },
  data() {
    return {
      data: [8, 4, 4, 4, 4, 2, 1, 0.5, 0.5, 0.25, 0.125],
      data2: [
        12800000, 6400000, 3200000, 1600000, 800000, 400000, 200000, 100000,
        100000, 100000
      ],
      /** @type {WebGLTileLayer | null} */
      geotifLayer: null,
      drawVector: null,
      addSpinner: null,
      removeSpinner: null,
      handle1: null,
      handle2: null,
      handle3: null
    }
  },
  watch: {},
  mounted() {
    this.addSpinner = () => {}
    this.removeSpinner = () => {}
  },
  beforeUnmount() {
    this.removeLayers()
    unByKey(this.addSpinner)
    unByKey(this.removeSpinner)
    this.map.getTargetElement().classList.remove('spinner')
    if (this.handle1) {
      unByKey(this.handle1)
    }
    if (this.handle2) {
      unByKey(this.handle2)
    }
    if (this.handle3) {
      unByKey(this.handle3)
    }
  },
  methods: {
    removeLayers() {
      if (this.geotifLayer) {
        this.map.removeLayer(this.geotifLayer)
        this.map.removeLayer(this.drawVector)
      }
    },
    createWindyLayer(arr) {
      if (this.handle1) {
        unByKey(this.handle1)
      }
      if (this.handle2) {
        unByKey(this.handle2)
      }
      if (this.handle3) {
        unByKey(this.handle3)
      }
      const drawSource = new VectorSource({ wrapX: false })
      this.drawVector = new VectorLayer({
        source: drawSource,
        zIndex: 199,
        className: 'productWind'
      })
      const source = new GeoTIFF({
        sources: [
          {
            url: arr
            // max: 25,
            // min: 0,
            // bands: ['0']
            // nodata: -999,
          }
        ],
        normalize: false
      })
      this.geotifLayer = new WebGLTileLayer({
        source: source,
        zIndex: 100,
        className: 'productWind',
        style: {
          color: [
            'interpolate',
            ['linear'],
            ['band', 0],
            0,
            [0, 0, 0, 0],
            25,
            [50, 21, 67, 0]
          ]
        }
      })
      this.geotifLayer.setProperties({ name: this.name })
      let xyArray = []
      let extent0
      let extent1
      const _this = this
      const extentXZ = [105, 5.98, 126.019999999999996, 26]
      let extent = this.map.getView().calculateExtent(this.map.getSize())
      // this.map.getView().setCenter([100, 35])
      const extent4326 = [extent[0], extent[1]]
      extent0 = Math.floor(extent4326[0])
      extent1 = Math.floor(extent4326[1])
      this.map.addLayer(this.drawVector)
      this.map.addLayer(this.geotifLayer)

      let isFinally = true
      let zoom = 1

      let value = _this.data[zoom]
      /** @type {OlMap} */
      const map = this.map
      this.handle1 = map.on(['rendercomplete', 'loadend'], () => {
        console.log('rendercomplete/loadend')
        if (isFinally) {
          zoom = this.map.getView().getZoom()
          if (zoom > _this.data.length - 1) {
            value = _this.data[_this.data.length - 1]
          } else {
            value = _this.data[zoom]
          }

          extent = this.map.getView().calculateExtent(this.map.getSize())
          const extent4326_1 = [extent[0], extent[1]]
          extent0 = Math.floor(extent4326_1[0] - (extent4326_1[0] % value))
          extent1 = Math.floor(extent4326_1[1] - (extent4326_1[1] % value))
          drawWind()
          isFinally = false
        }
      })

      this.handle2 = this.map.on('moveend', () => {
        console.log('moveend')
        this.map.getView().setConstrainResolution(true)
        zoom = this.map.getView().getZoom()
        if (zoom > _this.data.length - 1) {
          value = _this.data[_this.data.length - 1]
        } else {
          value = _this.data[zoom]
        }
        extent = this.map.getView().calculateExtent(this.map.getSize())
        const extent4326_2 = [extent[0], extent[1]]
        extent0 = Math.floor(extent4326_2[0] - (extent4326_2[0] % value))
        extent1 = Math.floor(extent4326_2[1] - (extent4326_2[1] % value))
        drawWind()
      })

      // this.handle3 = this.geotifLayer.on("postrender", () => {
      //   zoom = this.map.getView().getZoom();
      //   value = _this.data[zoom];
      //   extent = this.map.getView().calculateExtent(this.map.getSize());
      //   const extent4326_3 = toLonLat([extent[0], extent[1]]);
      //   extent0 = Math.floor(extent4326_3[0] - (extent4326_3[0] % value));
      //   extent1 = Math.floor(extent4326_3[1] - (extent4326_3[1] % value));
      //   // drawWind();
      // });

      const drawWind = () => {
        xyArray = []
        console.log('drawWind', value)
        const extent4326_4 = [extent[2], extent[3]]
        for (let x = extent0; x < extent4326_4[0]; x += value) {
          for (let y = extent1; y < extent4326_4[1]; y += value) {
            if (containsXY(extentXZ, x, y)) {
              xyArray.push([x, y])
            }
          }
        }
        let pixelLonlat
        let drawPoint
        let feature
        const features = []
        const tmpFeatures = []
        for (const index in xyArray) {
          const coordinate = xyArray[index]
          pixelLonlat = this.map.getPixelFromCoordinate(coordinate)
          const data = _this.geotifLayer.getData(pixelLonlat)
          if (data != null) {
            // const WX = Math.atan2(windData1, windData0);
            const WX = data[0]
            drawPoint = new Point(coordinate)
            feature = new Feature(drawPoint)
            if (!isNaN(data[0]) && data[0] !== 0) {
              const wf = WX
              const FieldObj = GeoJsonFormatFeilds.AutomaticStationFeilds
              const iconJson = blue
              let offsetData = null
              let iconrotate = 0
              const anchor = [0.5, 0.5]
              const unit = 'fraction'
              iconrotate = (wf * Math.PI) / 180
              let key
              key = FieldObj.iconName + '00'
              offsetData = iconJson[key]

              if (offsetData) {
                const pointStyle = function (feature) {
                  return new Style({
                    // fill: new Fill({
                    //   color: 'red'
                    // }),
                    // text: new Text({
                    //   text: `r-${data[0].toFixed(1)}`,
                    // })
                    image: new Icon({
                      anchor: anchor,
                      offset: [offsetData.x, offsetData.y],
                      anchorXUnits: unit,
                      anchorYUnits: unit,
                      size: [offsetData.height, offsetData.width],
                      scale: FieldObj.size,
                      rotateWithView: true,
                      rotation: iconrotate,
                      src: windPNG
                    })
                  })
                }
                feature.setStyle(pointStyle)
                features.push(feature)
              }
            }
          }
        }
        _this.drawVector.getSource().clear()
        _this.drawVector.getSource().addFeatures(features)
      }
      return this.geotifLayer
    }
  }
}
</script>

<style></style>
