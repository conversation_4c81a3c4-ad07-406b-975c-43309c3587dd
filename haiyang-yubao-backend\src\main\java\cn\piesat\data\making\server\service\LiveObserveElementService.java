package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.LiveObserveElement;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 实况观测要素表服务接口
 *
 * <AUTHOR>
 */
public interface LiveObserveElementService extends IService<LiveObserveElement> {

    /**
     * 查询列表
     */
    List<LiveObserveElement> getList(LiveObserveElement dto);
}




