package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.entity.Area;
import cn.piesat.data.making.server.vo.AreaVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AreaMapper {

    AreaMapper INSTANCE = Mappers.getMapper(AreaMapper.class);

    /**
     * entity-->vo
     */
    AreaVO entityToVo(Area entity);

    /**
     * dto-->entity
     */
    Area dtoToEntity(AreaDTO dto);

    /**
     * entityList-->voList
     */
    List<AreaVO> entityListToVoList(List<Area> list);

    /**
     * dtoList-->entityList
     */
    List<Area> dtoListToEntityList(List<AreaDTO> dtoList);
}
