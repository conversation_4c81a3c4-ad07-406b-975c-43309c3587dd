package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmSchedulingMainTableDTO;
import cn.piesat.data.making.server.vo.FmSchedulingMainTableVO;
import cn.piesat.data.making.server.service.FmSchedulingMainTableService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 排班祝表控制层
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:01
 */
@RestController
@RequestMapping("fmSchedulingMainTable")
public class FmSchedulingMainTableController {

    @Resource
    private FmSchedulingMainTableService fmSchedulingMainTableService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.SELECT)
    public PageResult<FmSchedulingMainTableVO> getPage(@RequestParam(required = false) Long id,
                                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        FmSchedulingMainTableDTO dto = new FmSchedulingMainTableDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmSchedulingMainTableService.getPage(dto, pageParam);
    }

    /**
     * 查询列表
     *
     * @param id
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.SELECT)
    public List<FmSchedulingMainTableVO> getList(@RequestParam(required = false) Long id) {
        FmSchedulingMainTableDTO dto = new FmSchedulingMainTableDTO();
        dto.setId(id);
        return fmSchedulingMainTableService.getList(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.SELECT)
    public FmSchedulingMainTableVO getById(@PathVariable Long id) {
        return null;
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {FmSchedulingMainTableDTO.Save.class}) @RequestBody FmSchedulingMainTableDTO dto) {
        fmSchedulingMainTableService.save(dto);
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmSchedulingMainTableService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "排班管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmSchedulingMainTableService.deleteByIdList(idList);
    }
}
