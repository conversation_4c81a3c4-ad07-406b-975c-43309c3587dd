import { MaybeRefOrGetter, onMounted, onUnmounted, toValue } from 'vue'
import eventBus from 'src/utils/eventBus'
import { FormType, showStationDialog } from 'src/components/ElementStatistics/components/types'
import { IRealtimeStationItem } from 'src/requests/toolRequest.type'

export function useMultiVectorSelect(opt: {
  form: MaybeRefOrGetter<FormType>
}) {
  onMounted(() => {
    eventBus.on(showStationDialog, (arr: IRealtimeStationItem[]) => {
      const formValue = toValue(opt.form)
      formValue.method = 2
      const idArr = formValue.oceanStationCodeList
      idArr.length = 0
      idArr.push(...arr.map(i => i.code))
    })
  })

  onUnmounted(() => {
    eventBus.off(showStationDialog)
  })
}
