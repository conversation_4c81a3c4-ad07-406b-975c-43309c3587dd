package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.generate.StormSurgeMessageWordDTO;
import cn.piesat.data.making.server.dto.generate.WaveMessageWordDTO;
import cn.piesat.data.making.server.entity.SeaWaveMessage;
import cn.piesat.data.making.server.entity.StormSurgeMessage;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface StormSurgeMessageWordMapper {
    StormSurgeMessageWordMapper INSTANCE = Mappers.getMapper(StormSurgeMessageWordMapper.class);

    @Mapping(target = "time", source = "stormSurgeMessage.releaseTime", dateFormat = "yyyy年MM月dd日HH时")
    StormSurgeMessageWordDTO toDTO(StormSurgeMessage stormSurgeMessage, UserInfoDTO userInfo, String fax);
}
