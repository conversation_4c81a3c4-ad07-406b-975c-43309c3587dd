package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.FmPublicProductTemplateDao;
import cn.piesat.data.making.server.entity.FmPublicProductTemplate;
import cn.piesat.data.making.server.entity.FmPublicRecord;
import cn.piesat.data.making.server.mapper.FmPublicProductTemplateMapper;
import cn.piesat.data.making.server.service.FmPublicProductTemplateService;
import cn.piesat.data.making.server.vo.FmPublicProductTemplateVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
public class FmPublicProductTemplateServiceImpl extends ServiceImpl<FmPublicProductTemplateDao, FmPublicProductTemplate> implements FmPublicProductTemplateService {

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;

    @Autowired
    private FmPublicProductTemplateDao fmPublicProductTemplateDaoImpl;

    @Autowired
    private FmPublicProductTemplateMapper fmPublicProductTemplateMapperImpl;

    @Override
    public FmPublicProductTemplateVO getById(Long id) {
        FmPublicProductTemplate obj = fmPublicProductTemplateDaoImpl.selectById(id);
        return fmPublicProductTemplateMapperImpl.entityToVo(obj);
    }

    @Override
    public FmPublicProductTemplateVO getByTemplateCode(String code) {
        LambdaQueryWrapper<FmPublicProductTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicProductTemplate::getTemplateCode, code);
        queryWrapper.eq(FmPublicProductTemplate::getStatus,true);
        queryWrapper.last("limit 1");

        FmPublicProductTemplate fmPublicProductTemplate = this.getOne(queryWrapper);
        return fmPublicProductTemplateMapperImpl.entityToVo(fmPublicProductTemplate);
    }

    @Override
    public void save(FmPublicProductTemplateVO vo) {
        FmPublicProductTemplate entity = fmPublicProductTemplateMapperImpl.voToEntity(vo);

        this.save(entity);
    }

    @Override
    public void update(FmPublicProductTemplateVO vo) {
        FmPublicProductTemplate entity = fmPublicProductTemplateMapperImpl.voToEntity(vo);

        this.updateById(entity);
    }

    @Override
    public List<FmPublicProductTemplateVO> getListByParam(FmPublicProductTemplateVO vo) {
        FmPublicProductTemplate entity = fmPublicProductTemplateMapperImpl.voToEntity(vo);

        LambdaQueryWrapper<FmPublicProductTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmPublicProductTemplate::getPublicType, vo.getPublicType());
        queryWrapper.eq(FmPublicProductTemplate::getStatus, entity.getStatus());
        queryWrapper.orderByAsc(FmPublicProductTemplate::getSort);

        List<FmPublicProductTemplate> list = this.list(queryWrapper);
        if(!CollectionUtils.isEmpty(list)){
            List<FmPublicProductTemplateVO> voList = fmPublicProductTemplateMapperImpl.entityListToVoList(list);
            return voList;
        }
        return null;
    }
}
