package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.vo.StationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StationMapper {

    StationMapper INSTANCE = Mappers.getMapper(StationMapper.class);

    /**
     * entity-->vo
     */
    StationVO entityToVo(Station entity);

    /**
     * dto-->entity
     */
    Station dtoToEntity(StationDTO dto);

    /**
     * entityList-->voList
     */
    List<StationVO> entityListToVoList(List<Station> list);

    /**
     * dtoList-->entityList
     */
    List<Station> dtoListToEntityList(List<StationDTO> dtoList);
}
