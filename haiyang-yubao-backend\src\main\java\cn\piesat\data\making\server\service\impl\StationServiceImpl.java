package cn.piesat.data.making.server.service.impl;

import cn.hutool.json.JSONObject;
import cn.piesat.data.making.server.common.CodeGenerator;
import cn.piesat.data.making.server.dao.BuoyStationDao;
import cn.piesat.data.making.server.dao.OceanStationDao;
import cn.piesat.data.making.server.dao.StationDao;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.Station;
import cn.piesat.data.making.server.mapper.StationMapper;
import cn.piesat.data.making.server.model.AreaStationInfo;
import cn.piesat.data.making.server.model.StationImport;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.RegionService;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.utils.ExcelUtil;
import cn.piesat.data.making.server.utils.GeoToolsUtil;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.StationVO;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 站点表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class StationServiceImpl extends ServiceImpl<StationDao, Station>
        implements StationService {

    @Resource
    private StationDao stationDao;
    @Resource
    private RegionService regionService;
    @Resource
    private OceanStationDao oceanStationDao;
    @Resource
    private BuoyStationDao buoyStationDao;

    @Override
    public PageResult<StationVO> getPage(StationDTO dto, PageParam pageParam) {
        Page<Station> page = this.page(new Page<>(pageParam.getPageNum(), pageParam.getPageSize()), createQueryWrapper(dto));
        List<StationVO> voList = StationMapper.INSTANCE.entityListToVoList(page.getRecords());
        return new PageResult(voList, pageParam.getPageNum(), pageParam.getPageSize(), page.getTotal());
    }

    @Override
    public List<StationVO> getList(StationDTO dto) {
        List<Station> list = this.list(createQueryWrapper(dto));
        return StationMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public Long save(StationDTO dto) {
        //查询同名站点
        StationDTO stationDTO = new StationDTO();
        stationDTO.setName(dto.getName());
        stationDTO.setStationTypeCode(dto.getStationTypeCode());
        stationDTO.setType(dto.getType());
        List<StationVO> voList = this.getList(stationDTO);
        if (StringUtils.isBlank(dto.getCode())) {
            dto.setCode(CodeGenerator.getPinYinHeadChar(dto.getName()));
        }
        dto.setLocationGeo(dto.getLocationJson());
        Station entity = StationMapper.INSTANCE.dtoToEntity(dto);
        if (dto.getId() == null) {
            if (!CollectionUtils.isEmpty(voList)) {
                throw new BusinessException("该站点已经存在！");
            }
            this.save(entity);
        } else {
            List<StationVO> list = voList.stream().filter(obj -> !obj.getId().equals(dto.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                throw new BusinessException("该站点已经存在！");
            }
            this.updateById(entity);
        }
        return entity.getId();
    }

    @Override
    public void deleteById(Long id) {
        stationDao.deleteById(id);
    }

    @Override
    @SneakyThrows
    public void upload(MultipartFile file) {
        ArrayList<Station> list = new ArrayList<>();
        List<StationImport> importList = ExcelUtil.readData(file.getInputStream(), 0, StationImport.class);
        importList.stream().forEach(s -> {
            Station station = new Station();
            station.setCode(s.getCode());
            station.setRegionCode(regionService.getCodeByName(s.getRegionCode()));
            station.setName(s.getName());
            station.setStationTypeCode("buoyStation");
            station.setType("10m");
            String json = "{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[" + s.getLng() + "," + s.getLat() + "]},\"properties" +
                    "\":null}";
            station.setLocationJson(json);
            station.setLocationGeo(json);
            list.add(station);
        });
        this.saveBatch(list);
    }

    @Override
    public List<Station> getListByRange(String geoRange) {
        return stationDao.getListByRange(geoRange);
    }

    @Override
    public void syncRelationStation(String stationTypeCode) {
        LambdaQueryWrapper<Station> wrapper = new LambdaQueryWrapper<>();
        if (stationTypeCode.equals("oceanStation")) {
            List<StationInfo> oceanList = oceanStationDao.getList(null);
            Map<String, String> oceanMap = oceanList.stream().collect(Collectors.toMap(StationInfo::getName, StationInfo::getCode, (key1, key2) -> key2));
            wrapper.eq(Station::getStationTypeCode, "oceanStation");
            List<Station> stationList = this.list(wrapper);
            stationList.forEach(station -> {
                station.setLocationGeo(station.getLocationJson());
                station.setRelationStation(oceanMap.get(station.getName()));
            });
            this.updateBatchById(stationList);
        }
        if (stationTypeCode.equals("buoyStation")) {
            List<StationInfo> buoyList = buoyStationDao.getList(null);
            Map<String, String> buoyMap = buoyList.stream().collect(Collectors.toMap(StationInfo::getName, StationInfo::getCode, (key1, key2) -> key2));
            wrapper.eq(Station::getStationTypeCode, "buoyStation");
            List<Station> stationList = this.list(wrapper);
            stationList.forEach(station -> {
                station.setLocationGeo(station.getLocationJson());
                station.setRelationStation(buoyMap.get(station.getName()));
            });
            this.updateBatchById(stationList);
        }
    }

    /**
     * 根据名称更新位置
     */
    private void updateLocationByName(Station entity) {
        stationDao.updateLocationByName(entity.getCode(), entity.getName(), entity.getLocationGeo(), entity.getLocationJson());
    }

    /**
     * 初始化站点数据
     */
    private void initStationData() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            URL resource = classLoader.getResource("initData/area.json");
            List<AreaStationInfo> data = mapper.readValue(new File(resource.getFile()), mapper.getTypeFactory().constructCollectionType(List.class,
                    AreaStationInfo.class));
            data.forEach(o -> {
                Station obj = new Station();
                obj.setName(o.getName());
                obj.setCode(CodeGenerator.getPinYinHeadChar(o.getName()));
                try {
                    //locationJson {"type":"Feature","geometry":{"type":"Point","coordinates":[110.4,20.2]},"properties":{},
                    // "id":"fid-4780e600_1920422c0bb_-8000"}
                    String locationJson = GeoToolsUtil.arrToGeojson(o.getRing());
                    obj.setLocationJson(locationJson);
                    //locationGeo {"type":"Point","coordinates":[110.4,20.2]}
                    JSONObject jsonObject = new JSONObject(locationJson);
                    obj.setLocationGeo(jsonObject.get("geometry").toString());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                this.updateLocationByName(obj);
            });
        } catch (Exception e) {
            log.error("站点位置信息初始化失败！");
        }
    }

    private LambdaQueryWrapper<Station> createQueryWrapper(StationDTO dto) {
        LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            queryWrapper.like(Station::getName, dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getStationTypeCode())) {
            queryWrapper.eq(Station::getStationTypeCode, dto.getStationTypeCode());
        }
        if (StringUtils.isNotBlank(dto.getType())) {
            //queryWrapper.eq(Station::getType, dto.getType());
            queryWrapper.in(Station::getType,Arrays.asList(dto.getType().split(",")));
        }
        if (dto.getEnable() != null) {
            queryWrapper.eq(Station::getEnable, dto.getEnable());
        }
        if (dto.getIsAstronomicalTide() != null && dto.getIsAstronomicalTide() == true) {
            List<String> list = Arrays.asList("hk（xy）", "ql", "ba", "wc", "sy", "ygh", "df（bs）", "pq", "gb", "mc", "hw");
            queryWrapper.in(Station::getCode, list);
        }
        return queryWrapper.orderByAsc(Station::getCreateTime);
    }

    public List<StationVO> getListByStationTypeCode(StationDTO dto){
        List<Station> list = this.list(createQueryWrapper(dto));
        return StationMapper.INSTANCE.entityListToVoList(list);
    }
}





