<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.piesat.data.making.server.dao.AreaDao">


<select id="selectGeoJsonByIds" resultType="java.lang.String">
SELECT
    row_to_json(fc) AS geojson
FROM (
         SELECT
             'FeatureCollection' AS type,
             array_to_json(array_agg(f)) AS features
         FROM (
                  SELECT
                      'Feature' AS type,
                      ST_AsGeoJSON(location_geo)::json AS geometry,
                      row_to_json((SELECT l FROM (SELECT id, name) AS l)) AS properties
                  FROM
                      fm_area_b
                  <where>
                    <if test="areaIds != null and areaIds.size() != 0">
                       id in (<foreach collection="areaIds" item="item" separator=",">#{item}</foreach>)
                    </if>
                    and location_json is not null
                  </where>
              ) AS f
     ) AS fc;
</select>

</mapper>

