package cn.piesat.data.making.server.utils.result;

import java.io.Serializable;

/**
 * 返回报文实体
 *
 * <AUTHOR> peihe.che
 * @version 1.0
 * @date 2022/01/12 15:30
 */
public class Result<T> implements Serializable {

    /**
     * 接口调用成功标识   true：调用成功   false：调用失败
     */
    private boolean success;
    /**
     * 接口返回Code，暂时无用
     */
    private String code;
    /**
     * 调用接口返回提示
     */
    private String message;
    /**
     * 调用接口返回的数据
     */
    private T data;


    public Result(boolean success, String code, String msg, T data) {
        this.success = success;
        this.code = code;
        this.message = msg;
        this.data = data;
    }

    public static Result fail(String msg) {
        return fail("", msg, null);
    }

    public static Result fail(String code, String msg) {
        return fail(code, msg, null);
    }

    public static <T> Result<T> fail(String code, String msg, T data) {
        return new Result(false, code, msg, data);
    }

    public static Result success() {
        return success("", null, null);
    }

    public static <T> Result<T> success(T data) {
        return success("", null, data);
    }

    public static <T> Result<T> success(String code, String msg, T data) {
        return new Result(true, code, msg, data);
    }


    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}