package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.vo.FmTideDialyVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmTideDialyServiceTest {

    @Autowired
    private FmTideDialyService fmTideDialyServiceImpl;

    @Test
    public void testQueryListByParam(){
        FmTideDialyVO fmTideDialyVO = new FmTideDialyVO();
        fmTideDialyVO.setYearMonth("2025-01");

        List<FmTideDialyVO> list = fmTideDialyServiceImpl.queryListByParam(fmTideDialyVO);
        Assert.assertNotNull(list);
    }
}
