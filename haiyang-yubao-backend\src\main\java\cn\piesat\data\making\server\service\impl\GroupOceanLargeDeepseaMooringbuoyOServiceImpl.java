package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.GroupOceanLargeDeepseaMooringbuoyODao;
import cn.piesat.data.making.server.entity.GroupOceanLargeDeepseaMooringbuoyO;
import cn.piesat.data.making.server.service.GroupOceanLargeDeepseaMooringbuoyOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 深海大型锚系浮标原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GroupOceanLargeDeepseaMooringbuoyOServiceImpl extends ServiceImpl<GroupOceanLargeDeepseaMooringbuoyODao, GroupOceanLargeDeepseaMooringbuoyO>
        implements GroupOceanLargeDeepseaMooringbuoyOService {

    @Resource
    private GroupOceanLargeDeepseaMooringbuoyODao groupOceanLargeDeepseaMooringbuoyODao;

    @Override
    public List<GroupOceanLargeDeepseaMooringbuoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return groupOceanLargeDeepseaMooringbuoyODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return groupOceanLargeDeepseaMooringbuoyODao.getMaxCreateTime();
    }

    @Override
    public List<String> getBuoyIdList() {
        return groupOceanLargeDeepseaMooringbuoyODao.getBuoyIdList();
    }
}





