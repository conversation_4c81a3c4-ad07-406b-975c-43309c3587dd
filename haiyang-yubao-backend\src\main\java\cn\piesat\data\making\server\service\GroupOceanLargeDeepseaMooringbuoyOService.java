package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.GroupOceanLargeDeepseaMooringbuoyO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 深海大型锚系浮标原始数据服务接口
 *
 * <AUTHOR>
 */
public interface GroupOceanLargeDeepseaMooringbuoyOService extends IService<GroupOceanLargeDeepseaMooringbuoyO> {

    List<GroupOceanLargeDeepseaMooringbuoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();

    List<String> getBuoyIdList();
}




