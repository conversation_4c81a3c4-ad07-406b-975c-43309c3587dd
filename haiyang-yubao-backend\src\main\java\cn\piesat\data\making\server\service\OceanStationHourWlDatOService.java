package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.entity.OceanStationHourWlDatO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-历时潮汐dat-原始数据服务接口
 *
 * <AUTHOR>
 */
public interface OceanStationHourWlDatOService extends IService<OceanStationHourWlDatO> {

    List<OceanStationHourWlDatO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    LocalDateTime getMaxCreateTime();
}




