package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:36
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_sign")
public class FmGraphicSign implements Serializable {

    private static final long serialVersionUID = 302485865665306358L;


    @TableField("id")
    private Long id;
    @TableField("type")
    private String type;
    @TableField("name")
    private String name;
    @TableField("context")
    private String context;
    @TableField("position")
    private String position;
    @TableField("template_id")
    private Long templateId;
    @TableField("disabled")
    private boolean disabled;

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
