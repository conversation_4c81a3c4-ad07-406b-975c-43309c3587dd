package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;

/**
 * 台风实时数据信息实体类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
@Data
@Accessors(chain = true)
@TableName("fm_typhoon_real_b")
public class FmTyphoonRealB implements Serializable {

    private static final long serialVersionUID = 222652474414751065L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 台风编号
     */
    @TableField("tfbh")
    private String tfbh;
    /**
     * 数据时间
     */
    @TableField("time")
    private Date time;
    /**
     * 经度
     */
    @TableField("lng")
    private Double lng;
    /**
     * 纬度
     */
    @TableField("lat")
    private Double lat;
    /**
     * 强度
     */
    @TableField("strong")
    private String strong;
    /**
     * 等级
     */
    @TableField("power")
    private Integer power;
    /**
     * 中心速度
     */
    @TableField("speed")
    private Integer speed;
    /**
     * 移动方向
     */
    @TableField("move_dir")
    private String moveDir;
    /**
     * 移动速度
     */
    @TableField("move_speed")
    private Integer moveSpeed;
    /**
     * 中心气压
     */
    @TableField("pressure")
    private Integer pressure;
    /**
     * 七级大风半径
     */
    @TableField("radius7")
    private Integer radius7;
    /**
     * 十级大风半径
     */
    @TableField("radius10")
    private Integer radius10;
    /**
     * 十二级大风半径
     */
    @TableField("radius12")
    private Integer radius12;
    /**
     * 七级大风四边形
     */
    @TableField("radius7_quad")
    private String radius7Quad;
    /**
     * 十级大风四边形
     */
    @TableField("radius10_quad")
    private String radius10Quad;
    /**
     * 十二级大风四边形
     */
    @TableField("radius12_quad")
    private String radius12Quad;

    public String getRadius7Quad() {
        return radius7Quad;
    }

    public void setRadius7Quad(String radius7Quad) {
        this.radius7Quad = radius7Quad;
    }

    public String getRadius10Quad() {
        return radius10Quad;
    }

    public void setRadius10Quad(String radius10Quad) {
        this.radius10Quad = radius10Quad;
    }

    public String getRadius12Quad() {
        return radius12Quad;
    }

    public void setRadius12Quad(String radius12Quad) {
        this.radius12Quad = radius12Quad;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public Integer getPower() {
        return power;
    }

    public void setPower(Integer power) {
        this.power = power;
    }

    public Integer getSpeed() {
        return speed;
    }

    public void setSpeed(Integer speed) {
        this.speed = speed;
    }

    public String getMoveDir() {
        return moveDir;
    }

    public void setMoveDir(String moveDir) {
        this.moveDir = moveDir;
    }

    public Integer getMoveSpeed() {
        return moveSpeed;
    }

    public void setMoveSpeed(Integer moveSpeed) {
        this.moveSpeed = moveSpeed;
    }

    public Integer getPressure() {
        return pressure;
    }

    public void setPressure(Integer pressure) {
        this.pressure = pressure;
    }

    public Integer getRadius7() {
        return radius7;
    }

    public void setRadius7(Integer radius7) {
        this.radius7 = radius7;
    }

    public Integer getRadius10() {
        return radius10;
    }

    public void setRadius10(Integer radius10) {
        this.radius10 = radius10;
    }

    public Integer getRadius12() {
        return radius12;
    }

    public void setRadius12(Integer radius12) {
        this.radius12 = radius12;
    }
}
