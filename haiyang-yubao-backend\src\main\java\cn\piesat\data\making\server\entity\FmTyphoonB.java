package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 台风信息实体类
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:19
 */
@Data
@Accessors(chain = true)
@TableName("fm_typhoon_b")
public class FmTyphoonB implements Serializable {

    private static final long serialVersionUID = -93981499255982305L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 台风编号
     */
    @TableField("tfbh")
    private String tfbh;
    /**
     * 台风唯一编码
     */
    @TableField("ident")
    private String ident;
    /**
     * 台风中文名
     */
    @TableField("name")
    private String name;
    /**
     * 台风英文名
     */
    @TableField("ename")
    private String ename;
    /**
     * 台风状态 1运行中 0已结束
     */
    @TableField("status")
    private Integer status;

    @TableField("year")
    private String year;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfbh() {
        return tfbh;
    }

    public void setTfbh(String tfbh) {
        this.tfbh = tfbh;
    }

    public String getIdent() {
        return ident;
    }

    public void setIdent(String ident) {
        this.ident = ident;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }
}
