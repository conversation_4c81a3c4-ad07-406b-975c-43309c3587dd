<template>
  <qx-dialog
    v-if="visible"
    title="图层叠加"
    :visible="visible"
    width="400px"
    class="layer-choose-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <n-form
        ref="formRef"
        :model="form"
        label-placement="left"
        label-width="auto"
        required-mark-placement="left"
        class="qx-form"
      >
        <n-form-item label="海上工程" path="">
          <n-radio-group
            v-model:value="form.elementName"
            name="radiogroup"
            @update:value="onSelectLayer"
          >
            <n-space>
              <n-radio
                v-for="item in layerOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
                >{{ item.name }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="设备名称" path="">
          <n-select
            v-model:value="form.devices"
            :options="oceanFacilityTypes"
            label-field="code"
            value-field="id"
            multiple
            :max-tag-count="3"
            clearable
          />
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btn-group text-right">
        <qx-button class="cancel" @click="onCancel">取消</qx-button>
        <qx-button class="primary" @click="onCreate">确定</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>
<script lang="ts" setup>
import { QxDialog } from 'src/components/QxDialog'
import { ref, reactive, onMounted, watch, inject } from 'vue'
import PublicService from 'src/requests/publicService'
import { useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import eventBus from 'src/utils/eventBus'
import { unionObjectArray } from 'src/utils/util'

const message = useMessage()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})
const areaList = inject<AreaType[]>('areaList')

const emit = defineEmits(['update:visible'])
const onClose = () => {
  emit('update:visible', false)
}
const formRef = ref()

interface FormType {
  elementName: string
  devices: string[]
}

const form = reactive<FormType>({
  elementName: '',
  devices: []
})

const layerOptions = ref<any[]>([])

function getOceanFacilityList() {
  PublicService.getOceanFacilityList()
    .then((res: any) => {
      let result: any = []
      res.forEach((item: any) => {
        let obj = {
          name: item,
          code: item
        }
        result.push(obj)
      })
      layerOptions.value = result
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg || '获取失败')
    })
}

interface FacilityType {
  id: string
  code: string
}

const oceanFacilityTypes = ref<FacilityType[]>([])

function onSelectLayer(type: string) {
  PublicService.getOceanFacilityListbyType({ type })
    .then((res: any) => {
      if (oceanFacilityTypes.value.length > 0) {
        oceanFacilityTypes.value = oceanFacilityTypes.value.filter(item =>
          form.devices.includes(item.id)
        )
      }
      oceanFacilityTypes.value.push(...res)
      oceanFacilityTypes.value = unionObjectArray<FacilityType>(
        oceanFacilityTypes.value,
        () => 'id'
      )
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || e?.data || {}
      message.error(msg || '获取失败')
    })
}

function onCreate() {
  let devices: any = []
  oceanFacilityTypes.value.forEach((item: FacilityType) => {
    if (form.devices.includes(item.id)) {
      devices.push(item)
    }
  })
  eventBus.emit('addDeviceLayer', devices)
  onClose()
}

type AreaType = {
  name?: string
  graphicJson?: string
  id: string
  isPoint?: boolean
}

watch(
  () => props.visible,
  val => {
    if (val) {
      const arr: string[] = []
      areaList?.forEach(item => {
        arr.push(item.id.toString())
      })
      form.devices = form.devices.filter(item => {
        return arr.includes(item)
      })
    }
  }
)

function onCancel() {
  const arr: string[] = []
  areaList?.forEach(item => {
    arr.push(item.id.toString())
  })
  form.devices = form.devices.filter(item => {
    return arr.includes(item)
  })
  onClose()
}

onMounted(() => {
  getOceanFacilityList()
})
</script>
<style lang="scss" scoped>
.layer-choose-dialog {
  .qx-form {
    box-sizing: border-box;
    padding: 20px 20px 0;
    height: auto;
  }

  .btn-group {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 20px;
    text-align: right;
  }
}
</style>
