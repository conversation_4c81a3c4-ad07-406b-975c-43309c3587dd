<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-07 09:51:08
 * @LastEditors: xuli<PERSON><PERSON> xuli<PERSON><EMAIL>
 * @LastEditTime: 2025-03-26 14:59:08
 * @FilePath: \hainan-jianzai-web\src\views\analysis\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 预报分析 -->
  <div ref="parentEl" class="analysis">
    <base-map :show-tool="false" @ready="onMapReady">
      <MapTypes
        @change-particle="onChangeParticle"
        @current-type-changed="val => (currentType = val)"
      />
    </base-map>
    <WebGLFlowField
      v-show="isShowParticle"
      v-if="currentType !== 'seaWind'"
      :map="map"
      :width="flowFieldWidth"
      :height="flowFieldHeight"
    />
    <WebGLFlowFieldFast
      v-else
      v-show="isShowParticle"
      :map="map"
      :width="flowFieldWidth"
      :height="flowFieldHeight"
    />
  </div>
</template>

<script setup lang="ts">
import { BaseMap } from 'src/components/OpenlayersMap'
import { MapTypes, WebGLFlowField } from './components/index'
import { ref, provide, onMounted, nextTick } from 'vue'
import WebGLFlowFieldFast from 'src/views/analysis/components/webGLFlowFieldFast.vue'
import WindLayers from 'src/components/Wind/Wind.vue'

const map = ref(null)
let flowFieldWidth = ref(0)
let flowFieldHeight = ref(0)
let parentEl = ref()
const currentType = ref('seaWind')
let isShowParticle = ref(true)
function onMapReady(val: any) {
  map.value = val
  if (val) {
    val.getAllLayers().forEach((layer: any) => {
      const property = layer.getProperties()
      if (property && property.name === '海南陆地') {
        layer.setOpacity(0)
      }
    })
    val.getView().setZoom(6)
    val.getView().setCenter([115.0, 15.38])
  }
}
provide('map', map)

function onChangeParticle(val: boolean) {
  isShowParticle.value = !isShowParticle.value
}

onMounted(() => {
  nextTick(() => {
    flowFieldWidth.value = parentEl?.value?.clientWidth as number
    flowFieldHeight.value = parentEl?.value?.clientHeight as number
  })
})
</script>

<style scoped>
.analysis {
  position: relative;
}
</style>
