package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import cn.piesat.data.making.server.model.AlertUpdateInfo;
import cn.piesat.data.making.server.service.AlertStormSurgeXmlService;
import cn.piesat.data.making.server.service.AlertUploadService;
import cn.piesat.data.making.server.service.StormSurgeAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Map;

@Service
@Slf4j
public class AlertStormSurgeXmlServiceImpl implements AlertStormSurgeXmlService {

    private String[] chineseName = {"零","一","二","三","四","五","六"};

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ssXXX");

    private DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    private DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private StormSurgeAlarmService stormSurgeAlarmServiceImpl;

    @Autowired
    private AlertUploadService alertUploadServiceImpl;

    @Value("${piesat.base-output-path}")
    private String baseOutPutPath;

    @Value("${piesat.alertXmlAuthorize}")
    private String alertXmlAuthorize;

    @Override
    public void sender(String xmlPath, StormSurgeAlarm stormSurgeAlarm) {
        try{
            Document document = initDocument(xmlPath);

            document.setXmlStandalone(true);

            XPathFactory xPathFactory = XPathFactory.newInstance();
            XPath xPath = xPathFactory.newXPath();

            String timeStr = LocalDateTime.now().format(dtf);

            String id = "46000045000100_"+timeStr;

            //预警编号
            setXmlValueByXPath(document,xPath,"/alert/identifier",id);

            //发送单位名称
            setXmlValueByXPath(document,xPath,"/alert/sender","海南省海洋预报台");

            //发送单位编码
            setXmlValueByXPath(document,xPath,"/alert/senderCode","46000045000100");

            //发送时间
            setXmlValueByXPath(document,xPath,"/alert/sendTime",dtf2.format(LocalDateTime.now())+"+08:00");

            //过期时间
            setXmlValueByXPath(document,xPath,"/alert/expirationTime",dtf2.format(LocalDateTime.now().plusHours(1L))+"+08:00");

            //发送状态 默认为Actual
            setXmlValueByXPath(document,xPath,"/alert/status","Actual");

            //发送信息类型
            if(StringUtils.isNotBlank(stormSurgeAlarm.getLastIdentifier())){
                setXmlValueByXPath(document,xPath,"/alert/msgType","Update");
            }else{
                setXmlValueByXPath(document,xPath,"/alert/msgType","Alert");
            }
            //发送范围
            setXmlValueByXPath(document,xPath,"/alert/scope","Public");

            //解除
            if(StringUtils.isNotBlank(stormSurgeAlarm.getLastIdentifier())&&stormSurgeAlarm.getAlarmLevel()!=null&&stormSurgeAlarm.getAlarmLevel()==5){
                setXmlValueByXPath(document,xPath,"/alert/references",stormSurgeAlarm.getLastIdentifier());
            }

            //事件类别
            setXmlValueByXPath(document,xPath,"/alert/info/eventType","风暴潮");

            //级别
            setXmlValueByXPath(document,xPath,"/alert/info/severity",chineseName[stormSurgeAlarm.getAlarmLevel().intValue()]+"级");

            //生效时间
            setXmlValueByXPath(document,xPath,"/alert/info/effective",sdf.format(stormSurgeAlarm.getReleaseTime()));

            //时效时间
            //setXmlValueByXPath(document,xPath,"/alert/info/expires",sdf.format(stormSurgeAlarm.getReleaseTime()));

            //发布人
            setXmlValueByXPath(document,xPath,"/alert/info/senderName",stormSurgeAlarm.getSignUserName());

            //标题
            setXmlValueByXPath(document,xPath,"/alert/info/headline",stormSurgeAlarm.getSummarize());

            //内容
            setXmlValueByXPath(document,xPath,"/alert/info/description",stormSurgeAlarm.getSummarize()+":"+stormSurgeAlarm.getAlarmContent()+stormSurgeAlarm.getDefenseGuide());

            //防范措施
            setXmlValueByXPath(document,xPath,"/alert/info/instruction",stormSurgeAlarm.getDefenseGuide());

            //影响范围
            setXmlValueByXPath(document,xPath,"/alert/info/area/areaDesc","海南省");

            //影响区域编码
            setXmlValueByXPath(document,xPath,"/alert/info/area/geocode","460000");

//            if(seaWaveAlarm!=null&&seaWaveAlarm.getAlarmArea()!=null) {
//                setXmlValueByXPath(document, xPath, "/alert/info/area/polygon", seaWaveAlarm.getAlarmArea());
//            }

            //输出xml文本
            String output = outPutXmlStr(document);

            System.out.println("修改后的XML内容:");
            System.out.println(output);

            stormSurgeAlarm.setLastIdentifier(id);

            stormSurgeAlarmServiceImpl.updateByIdObj(stormSurgeAlarm);

            AlertUpdateInfo alertUpdateInfo = new AlertUpdateInfo();

            String fileName = LocalDateTime.now().format(dtf)+".xml";
            String filePath = baseOutPutPath+File.separator+LocalDateTime.now().getYear()+File.separator+LocalDateTime.now().getDayOfYear()+File.separator;

            alertUpdateInfo.setFilePath(filePath+fileName);
            alertUpdateInfo.setFileName(fileName);

            File outputFile = new File(filePath+fileName);

            FileUtils.writeStringToFile(outputFile,output,"UTF-8");

            alertUpdateInfo.setFile(outputFile);

            alertUpdateInfo.setUnit("HY");
            alertUpdateInfo.setProductType("1");
            alertUpdateInfo.setAuthorize(alertXmlAuthorize);
            alertUpdateInfo.setRequestId(LocalDateTime.now().format(dtf));

            //发送到国突接口
            Map<String,Object> ret = alertUploadServiceImpl.sendMsg(alertUpdateInfo);
            log.info("发送国突接口反馈信息：",ret);

        }catch(Exception e){
            e.printStackTrace();
        }
    }

    @NotNull
    private String outPutXmlStr(Document document) throws TransformerException {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();

        transformer.setOutputProperty(OutputKeys.DOCTYPE_PUBLIC,"yes");
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(document), new StreamResult(writer));
        return writer.getBuffer().toString();
    }

    private void setXmlValueByXPath(Document document,XPath xPath,String xPathExpre,String val) throws XPathExpressionException {
        Node node = (Node) xPath.evaluate(xPathExpre, document, XPathConstants.NODE);
        node.setTextContent(val);
    }

    private Document initDocument(String xmlPath) throws ParserConfigurationException, SAXException, IOException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(new File(xmlPath));
    }
}
