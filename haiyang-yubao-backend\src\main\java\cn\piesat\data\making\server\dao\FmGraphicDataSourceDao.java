package cn.piesat.data.making.server.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;
import cn.piesat.data.making.server.entity.FmGraphicDataSource;

import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:26
 */
public interface FmGraphicDataSourceDao extends BaseMapper<FmGraphicDataSource> {
    @Select("SELECT * FROM fm_graphic_data_source")
    List<FmGraphicDataSource> selectAll();

}
