package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站整点-表层海水盐度-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_hour_sl_o")
public class OceanStationHourSlO implements Serializable {

    private static final long serialVersionUID = 236380272337690733L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 监测日期
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 21点测值
     **/
    @JsonProperty("measured_value_21")
    @TableField("measured_value_21")
    private String measuredValue21;
    /**
     * 21点测值质控符
     **/
    @JsonProperty("measured_value_21_Qc2")
    @TableField("measured_value_21_qc2")
    private Integer measuredValue21Qc2;
    /**
     * 22点测值
     **/
    @JsonProperty("measured_value_22")
    @TableField("measured_value_22")
    private String measuredValue22;
    /**
     * 22点测值质控符
     **/
    @JsonProperty("measured_value_22_Qc2")
    @TableField("measured_value_22_qc2")
    private Integer measuredValue22Qc2;
    /**
     * 23点测值
     **/
    @JsonProperty("measured_value_23")
    @TableField("measured_value_23")
    private String measuredValue23;
    /**
     * 23点测值质控符
     **/
    @JsonProperty("measured_value_23_Qc2")
    @TableField("measured_value_23_qc2")
    private Integer measuredValue23Qc2;
    /**
     * 00点测值
     **/
    @JsonProperty("measured_value_00")
    @TableField("measured_value_00")
    private String measuredValue00;
    /**
     * 00点测值质控符
     **/
    @JsonProperty("measured_value_00_Qc2")
    @TableField("measured_value_00_qc2")
    private Integer measuredValue00Qc2;
    /**
     * 01点测值
     **/
    @JsonProperty("measured_value_01")
    @TableField("measured_value_01")
    private String measuredValue01;
    /**
     * 01点测值质控符
     **/
    @JsonProperty("measured_value_01_Qc2")
    @TableField("measured_value_01_qc2")
    private Integer measuredValue01Qc2;
    /**
     * 02点测值
     **/
    @JsonProperty("measured_value_02")
    @TableField("measured_value_02")
    private String measuredValue02;
    /**
     * 02点测值质控符
     **/
    @JsonProperty("measured_value_02_Qc2")
    @TableField("measured_value_02_qc2")
    private Integer measuredValue02Qc2;
    /**
     * 03点测值
     **/
    @JsonProperty("measured_value_03")
    @TableField("measured_value_03")
    private String measuredValue03;
    /**
     * 03点测值质控符
     **/
    @JsonProperty("measured_value_03_Qc2")
    @TableField("measured_value_03_qc2")
    private Integer measuredValue03Qc2;
    /**
     * 04点测值
     **/
    @JsonProperty("measured_value_04")
    @TableField("measured_value_04")
    private String measuredValue04;
    /**
     * 04点测值质控符
     **/
    @JsonProperty("measured_value_04_Qc2")
    @TableField("measured_value_04_qc2")
    private Integer measuredValue04Qc2;
    /**
     * 05点测值
     **/
    @JsonProperty("measured_value_05")
    @TableField("measured_value_05")
    private String measuredValue05;
    /**
     * 05点测值质控符
     **/
    @JsonProperty("measured_value_05_Qc2")
    @TableField("measured_value_05_qc2")
    private Integer measuredValue05Qc2;
    /**
     * 06点测值
     **/
    @JsonProperty("measured_value_06")
    @TableField("measured_value_06")
    private String measuredValue06;
    /**
     * 06点测值质控符
     **/
    @JsonProperty("measured_value_06_Qc2")
    @TableField("measured_value_06_qc2")
    private Integer measuredValue06Qc2;
    /**
     * 07点测值
     **/
    @JsonProperty("measured_value_07")
    @TableField("measured_value_07")
    private String measuredValue07;
    /**
     * 07点测值质控符
     **/
    @JsonProperty("measured_value_07_Qc2")
    @TableField("measured_value_07_qc2")
    private Integer measuredValue07Qc2;
    /**
     * 08点测值
     **/
    @JsonProperty("measured_value_08")
    @TableField("measured_value_08")
    private String measuredValue08;
    /**
     * 08点测值质控符
     **/
    @JsonProperty("measured_value_08_Qc2")
    @TableField("measured_value_08_qc2")
    private Integer measuredValue08Qc2;
    /**
     * 09点测值
     **/
    @JsonProperty("measured_value_09")
    @TableField("measured_value_09")
    private String measuredValue09;
    /**
     * 09点测值质控符
     **/
    @JsonProperty("measured_value_09_Qc2")
    @TableField("measured_value_09_qc2")
    private Integer measuredValue09Qc2;
    /**
     * 10点测值
     **/
    @JsonProperty("measured_value_10")
    @TableField("measured_value_10")
    private String measuredValue10;
    /**
     * 10点测值质控符
     **/
    @JsonProperty("measured_value_10_Qc2")
    @TableField("measured_value_10_qc2")
    private Integer measuredValue10Qc2;
    /**
     * 11点测值
     **/
    @JsonProperty("measured_value_11")
    @TableField("measured_value_11")
    private String measuredValue11;
    /**
     * 11点测值质控符
     **/
    @JsonProperty("measured_value_11_Qc2")
    @TableField("measured_value_11_qc2")
    private Integer measuredValue11Qc2;
    /**
     * 12点测值
     **/
    @JsonProperty("measured_value_12")
    @TableField("measured_value_12")
    private String measuredValue12;
    /**
     * 12点测值质控符
     **/
    @JsonProperty("measured_value_12_Qc2")
    @TableField("measured_value_12_qc2")
    private Integer measuredValue12Qc2;
    /**
     * 13点测值
     **/
    @JsonProperty("measured_value_13")
    @TableField("measured_value_13")
    private String measuredValue13;
    /**
     * 13点测值质控符
     **/
    @JsonProperty("measured_value_13_Qc2")
    @TableField("measured_value_13_qc2")
    private Integer measuredValue13Qc2;
    /**
     * 14点测值
     **/
    @JsonProperty("measured_value_14")
    @TableField("measured_value_14")
    private String measuredValue14;
    /**
     * 14点测值质控符
     **/
    @JsonProperty("measured_value_14_Qc2")
    @TableField("measured_value_14_qc2")
    private Integer measuredValue14Qc2;
    /**
     * 15点测值
     **/
    @JsonProperty("measured_value_15")
    @TableField("measured_value_15")
    private String measuredValue15;
    /**
     * 15点测值质控符
     **/
    @JsonProperty("measured_value_15_Qc2")
    @TableField("measured_value_15_qc2")
    private Integer measuredValue15Qc2;
    /**
     * 16点测值
     **/
    @JsonProperty("measured_value_16")
    @TableField("measured_value_16")
    private String measuredValue16;
    /**
     * 16点测值质控符
     **/
    @JsonProperty("measured_value_16_Qc2")
    @TableField("measured_value_16_qc2")
    private Integer measuredValue16Qc2;
    /**
     * 17点测值
     **/
    @JsonProperty("measured_value_17")
    @TableField("measured_value_17")
    private String measuredValue17;
    /**
     * 17点测值质控符
     **/
    @JsonProperty("measured_value_17_Qc2")
    @TableField("measured_value_17_qc2")
    private Integer measuredValue17Qc2;
    /**
     * 18点测值
     **/
    @JsonProperty("measured_value_18")
    @TableField("measured_value_18")
    private String measuredValue18;
    /**
     * 18点测值质控符
     **/
    @JsonProperty("measured_value_18_Qc2")
    @TableField("measured_value_18_qc2")
    private Integer measuredValue18Qc2;
    /**
     * 19点测值
     **/
    @JsonProperty("measured_value_19")
    @TableField("measured_value_19")
    private String measuredValue19;
    /**
     * 19点测值质控符
     **/
    @JsonProperty("measured_value_19_Qc2")
    @TableField("measured_value_19_qc2")
    private Integer measuredValue19Qc2;
    /**
     * 20点测值
     **/
    @JsonProperty("measured_value_20")
    @TableField("measured_value_20")
    private String measuredValue20;
    /**
     * 20点测值质控符
     **/
    @JsonProperty("measured_value_20_Qc2")
    @TableField("measured_value_20_qc2")
    private Integer measuredValue20Qc2;
    /**
     * 日最高值
     **/
    @JsonProperty("day_max_value")
    @TableField("day_max_value")
    private String dayMaxValue;
    /**
     * 日最高值质控符
     **/
    @JsonProperty("day_max_value_Qc2")
    @TableField("day_max_value_qc2")
    private Integer dayMaxValueQc2;
    /**
     * 日最低值
     **/
    @JsonProperty("day_min_value")
    @TableField("day_min_value")
    private String dayMinValue;
    /**
     * 日最低值质控符
     **/
    @JsonProperty("day_min_value_Qc2")
    @TableField("day_min_value_qc2")
    private Integer dayMinValueQc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
}



