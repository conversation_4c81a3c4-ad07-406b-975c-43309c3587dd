<template>
  <div class="card">
    <div class="card-header">
      <div class="card-title">{{ props.title }}</div>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="scss">
.card {
  .card-header {
    height: 58.92px;
    background: url('src/assets/images/common/card-header.png') no-repeat;
    background-size: 100% calc(100% - 10px);
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dfdfdf;
    justify-content: space-between;
    padding: 0px 20px;
  }
  .card-title {
    font-weight: bold;
    font-size: 18px;
    color: #222222;
    margin-left: 10px;
    position: relative;

    &::before {
      content: '';
      width: 4px;
      height: 15px;
      background: rgba(86, 123, 255, 1);
      position: absolute;
      left: -10px;
    }
  }
  .card-content {
    box-sizing: border-box;
    padding: 15px 20px 10px;
  }
}
</style>
