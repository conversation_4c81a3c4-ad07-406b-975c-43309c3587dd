package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.FmPublishForecastDao;
import cn.piesat.data.making.server.dao.ForecastRecordDao;
import cn.piesat.data.making.server.dao.ForecastTaskDao;
import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.dto.ForecastRecordDTO;
import cn.piesat.data.making.server.entity.*;
import cn.piesat.data.making.server.enums.ForecastTaskStatus;
import cn.piesat.data.making.server.enums.ValueHandle;
import cn.piesat.data.making.server.mapper.ForecastRecordMapper;
import cn.piesat.data.making.server.service.*;
import cn.piesat.data.making.server.vo.AreaVO;
import cn.piesat.data.making.server.vo.ElementVO;
import cn.piesat.data.making.server.vo.ForecastRecordVO;
import cn.piesat.data.making.server.vo.ForecastTemplateVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预报记录表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastRecordServiceImpl extends ServiceImpl<ForecastRecordDao, ForecastRecord> implements ForecastRecordService {

    @Resource
    private ForecastTaskDao forecastTaskDao;
    @Resource
    private ForecastRecordDetailService forecastRecordDetailService;
    @Resource
    private FmForecastProductDataService fmForecastProductDataService;
    @Resource
    private ForecastTemplateService forecastTemplateService;
    @Resource
    private AreaService areaService;
    @Resource
    private ElementService elementService;
    @Resource
    private ForecastTaskService forecastTaskService;
//    @Resource
//    private FmPublishForecastDao fmPublishForecastDao;
    @Resource
    private FmPublishForecastService fmPublishForecastService;

    //区域关系映射
    static Map<String, List<String>> areaRelationMap;

    static {
        areaRelationMap = new HashMap<>();
        areaRelationMap.put("hkhy", Arrays.asList("hkxhadjq", "hkjrhtyc"));
        areaRelationMap.put("wchy", Arrays.asList("wcglwyc"));
        areaRelationMap.put("syhy", Arrays.asList("sytyhjdjq", "syhtwyc", "sywzzdyc", "syylwyc", "syddhyc", "sysywyc"));
        areaRelationMap.put("xsyxdhy", Arrays.asList("xszsdhy"));
        areaRelationMap.put("nsysjhy", Arrays.asList("nsmjjhy"));
    }

    @Override
    public List<ForecastRecordVO> getList() {
        List<ForecastRecord> list = this.list(createQueryWrapper());
        List<ForecastRecordVO> voList = ForecastRecordMapper.INSTANCE.entityListToVoList(list);
        voList.stream().forEach(vo -> {
            List<ForecastRecordDetail> detailList = forecastRecordDetailService.getList(vo.getId());
            vo.setDetailList(detailList);
        });
        return voList;
    }

    @Override
    public void save(ForecastRecordDTO dto) {
        //页面保存的区域
        List<FmForecastProductData> list = new ArrayList<>();
        //页面保存的区域包含指定的大区域的集合
        List<FmForecastProductData> bigList = new ArrayList<>();
        //大区域编码
        List<String> keyList = areaRelationMap.keySet().stream().collect(Collectors.toList());

        dto.getDetailList().stream().forEach(detail -> {
            FmForecastProductData data = new FmForecastProductData();
            Long id = detail.getId();
            if (id == null) {
                return;
            }
            data.setId(id);
            data.setAreaCode(detail.getAreaCode());
            data.setElementCode(detail.getElementCode());
            String value = detail.getValue();
            if (ValueHandle.MIN.getValue().equals(detail.getElementValueHandle())) {
                data.setModifyMinValue(value);
            }
            if (ValueHandle.MAX.getValue().equals(detail.getElementValueHandle())) {
                data.setModifyMaxValue(value);
            }
            if (ValueHandle.MEAN.getValue().equals(detail.getElementValueHandle())) {
                data.setModifyAveValue(value);
            }
            list.add(data);
            //将页面保存的区域中的大区域放到dList中
            if (keyList.contains(detail.getAreaCode())) {
                bigList.add(data);
            }
        });
        //修改大区域预报数据时同步更新小区域预报数据
        //查询当天所有的预报数据
        List<ForecastTask> curTaskList = forecastTaskService.getList();
        List<ForecastRecordDetail> curDetailList = new ArrayList<>();
        curTaskList.stream().forEach(curTask -> curDetailList.addAll(this.getForecastRecordByTemplateAndData(curTask, dto.getDataSource(),
                dto.getReportTime()).getDetailList()));
        //遍历大区域集合
        //bigList 海口海域 浪高、海口海域 水温、海口海域 高潮时...
        bigList.stream().forEach(big -> {
            //smallList 海口西海岸 海口假日海滩
            List<String> smallList = areaRelationMap.get(big.getName());
            if (smallList != null) {
                //samll 海口西海岸
                smallList.stream().forEach(samll -> {
                    //detailList 海口西海岸 浪高的min max mean共3条数据
                    List<ForecastRecordDetail> detailList =
                            curDetailList.stream().filter(detail -> detail.getAreaCode().equals(samll) && detail.getElementCode().equals(big.getElementCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(detailList)) {
                        log.debug("ElementCode: {}, AreaCode: {}", big.getElementCode(), samll);
                        return;
                    }
                    //预报数据
                    FmForecastProductData data = new FmForecastProductData();
                    Long id = detailList.get(0).getId();
                    if (id == null) {
                        return;
                    }
                    data.setId(id);
                    detailList.stream().forEach(detail -> {
                        if (ValueHandle.MIN.getValue().equals(detail.getElementValueHandle())) {
                            data.setModifyMinValue(big.getModifyMinValue());
                        }
                        if (ValueHandle.MAX.getValue().equals(detail.getElementValueHandle())) {
                            data.setModifyMaxValue(big.getModifyMaxValue());
                        }
                        if (ValueHandle.MEAN.getValue().equals(detail.getElementValueHandle())) {
                            data.setModifyAveValue(big.getModifyAveValue());
                        }
                    });
                    list.add(data);
                });
            }
        });
        fmForecastProductDataService.updateModifyValue(list);
        //修改预报任务的保存时间为当前时间
        ForecastTask forecastTask = new ForecastTask();
        forecastTask.setId(dto.getForecastTaskId());
        forecastTask.setSaveTime(new Date());
        forecastTaskDao.updateById(forecastTask);
    }

    @Override
    public void submit(ForecastRecordDTO dto) {
        //修改预报任务的状态为已制作
        ForecastTask forecastTask = new ForecastTask();
        forecastTask.setId(dto.getForecastTaskId());
        forecastTask.setStatus(ForecastTaskStatus.MAKED.getValue());
        forecastTask.setSubmitTime(new Date());
        forecastTask.setForecastContent(dto.getForecastContent());
        forecastTaskDao.updateById(forecastTask);
        //创建预报记录及明细
        ForecastRecord forecastRecord = ForecastRecordMapper.INSTANCE.dtoToEntity(dto);
        this.save(forecastRecord);
        List<ForecastRecordDetail> detailList = dto.getDetailList();
        detailList.forEach(detail -> detail.setForecastRecordId(forecastRecord.getId()));
        forecastRecordDetailService.saveBatch(detailList);
    }

    @Override
    public ForecastRecordVO getInfoByTaskId(Long taskId, String dataSource, Date reportTime) {
        ForecastTask task = forecastTaskDao.selectById(taskId);
        //任务状态为已制作就使用预报记录；反之使用预报模板
        ForecastRecordVO record;
        if (ForecastTaskStatus.WAIT_MAKE.getValue().equals(task.getStatus())) {
            record = this.getForecastRecordByTemplateAndData(task,dataSource, reportTime);
        } else {
            record = this.getByTaskId(taskId, dataSource);
        }
        return record;
    }

    @Override
    public ForecastRecordVO getInfoByTaskId(Long taskId, Date reportTime) {
        ForecastTask task = forecastTaskDao.selectById(taskId);
        //任务状态为已制作就使用预报记录；反之使用预报模板
        ForecastRecordVO record;
        if (ForecastTaskStatus.WAIT_MAKE.getValue().equals(task.getStatus())) {
            record = this.getForecastRecordByTemplateAndData(task, reportTime);
        } else {
            record = this.getByTaskId(taskId);
        }
        return record;
    }

    @Override
    public ForecastRecordVO loadPre(Long taskId, String dataSource, Date reportTime) {
        //任务信息
        ForecastTask task = forecastTaskDao.selectById(taskId);
        //根据任务的模板id、预报类型、创建时间查询上一期任务
        LambdaQueryWrapper<ForecastTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForecastTask::getTemplateId, task.getTemplateId());
        queryWrapper.eq(ForecastTask::getForecastType, task.getForecastType());
        LocalDate localDate = task.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        queryWrapper.eq(ForecastTask::getStartTime, localDate.minusDays(1L));
        List<ForecastTask> list = forecastTaskDao.selectList(queryWrapper);
        ForecastRecordVO record;
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            record = this.getByTaskId(list.get(0).getId(), dataSource);
        }
        return record;
    }

    @Override
    public ForecastRecordVO reset(Long taskId, String dataSource, Date reportTime) {
        ForecastTask task = forecastTaskDao.selectById(taskId);
        return this.getForecastRecordByTemplateAndData(task, dataSource, reportTime);
    }

    @Override
    public ForecastRecordVO syncDataToPublish(Long taskId, String dataSource, Date reportTime) {
        ForecastTask task = forecastTaskDao.selectById(taskId);
        //任务状态为已制作就使用预报记录；反之使用预报模板
        ForecastRecordVO record;
        if (ForecastTaskStatus.WAIT_MAKE.getValue().equals(task.getStatus())) {
            record = this.getForecastRecordByTemplateAndData(task, dataSource, reportTime);
        } else {
            record = this.getByTaskId(taskId, dataSource);
        }
        List<ForecastRecordDetail> recordDetails = record.getDetailList();
        Map<String, List<ForecastRecordDetail>> collect = recordDetails.stream().collect(Collectors.groupingBy(ForecastRecordDetail::getAreaCode));
        List<FmPublishForecast> newList = new ArrayList<>();
        for(Map.Entry<String,List<ForecastRecordDetail>> entry : collect.entrySet()) {
            String key = entry.getKey();
            List<ForecastRecordDetail> value = entry.getValue();
            Map<String,String> map = new HashMap<>();
            FmPublishForecast fmPublishForecast = new FmPublishForecast();
            fmPublishForecast.setTimeRange("今天夜间到明天白天");
            fmPublishForecast.setAreaTypeCode(record.getForecastType());
            fmPublishForecast.setTimePublish(record.getReportTime());
            fmPublishForecast.setProductType(task.getName());
            for (ForecastRecordDetail detail:value) {
                if (detail.getColumnCode().equals("swhMean")) {//浪高
                    fmPublishForecast.setWave(detail.getValue());
                } else if (detail.getColumnCode().equals("swhInterval")) {
                    fmPublishForecast.setWave(detail.getValue());
                }
                if (detail.getColumnCode().equals("sstMean")) {//海温
                    fmPublishForecast.setTemperature(detail.getValue());
                }
                if (detail.getColumnCode().equals("langjiMean")) {//浪级
                    fmPublishForecast.setLevel(detail.getValue());
                } else if (detail.getColumnCode().equals("langjiInterval")) {
                    fmPublishForecast.setLevel(detail.getValue());
                }
                if (detail.getColumnCode().equals("mwpMean")) { //波周期
                    fmPublishForecast.setWaveCycle(detail.getValue());
                }
                if (detail.getColumnCode().equals("swimMean")) {//游泳适宜度
                    fmPublishForecast.setSwimming(detail.getValue());
                }


                fmPublishForecast.setStationName(detail.getAreaName());

            }
            newList.add(fmPublishForecast);
        }
        fmPublishForecastService.saveBatch(newList);
        return record;
    }

    /**
     * 根据预报任务id查询预报记录
     **/
    private ForecastRecordVO getByTaskId(Long taskId, String dataSource) {
        ForecastRecord record = this.getOne(new QueryWrapper<ForecastRecord>().lambda().eq(ForecastRecord::getForecastTaskId, taskId)
                .eq(ForecastRecord::getDataSource, dataSource));
        if (record == null) {
            return null;
        }
        List<ForecastRecordDetail> detailList = forecastRecordDetailService.getList(record.getId());
        ForecastRecordVO vo = ForecastRecordMapper.INSTANCE.entityToVo(record);
        vo.setDetailList(detailList);
        return vo;
    }

    /**
     * 根据预报任务id查询预报记录
     **/
    private ForecastRecordVO getByTaskId(Long taskId) {
        ForecastRecord record = this.getOne(new QueryWrapper<ForecastRecord>().lambda().eq(ForecastRecord::getForecastTaskId, taskId));
        if (record == null) {
            return null;
        }
        List<ForecastRecordDetail> detailList = forecastRecordDetailService.getList(record.getId());
        ForecastRecordVO vo = ForecastRecordMapper.INSTANCE.entityToVo(record);
        vo.setDetailList(detailList);
        return vo;
    }

    /**
     * 根据数据源、时间范围查询预报数据
     **/
    private Map<String, FmForecastProductData> getForecastDataList(String dataSource, Date reportTime) {
        LocalDate forecastDate = LocalDate.now();
        LocalTime start = LocalTime.of(00, 00, 00);
        LocalTime end = LocalTime.of(23, 59, 59);
        Date startTime = Date.from(LocalDateTime.of(forecastDate, start).atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(LocalDateTime.of(forecastDate, end).atZone(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapper<FmForecastProductData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmForecastProductData::getDataSource, dataSource);
        wrapper.eq(FmForecastProductData::getStartTime, reportTime);
        wrapper.between(FmForecastProductData::getForecastTime, startTime, endTime);
        List<FmForecastProductData> list = fmForecastProductDataService.list(wrapper);
        Map<String, FmForecastProductData> map = new HashMap<>();
        for (FmForecastProductData info : list) {
            String key = info.getAreaId() + "-" + info.getElementCode();
            map.put(key, info);
        }
        return map;
    }

    /**
     * 根据数据源、时间范围查询预报数据
     **/
    private Map<String, FmForecastProductData> getForecastDataList(Date reportTime) {
        LocalDate forecastDate = LocalDate.now();
        LocalTime start = LocalTime.of(00, 00, 00);
        LocalTime end = LocalTime.of(23, 59, 59);
        Date startTime = Date.from(LocalDateTime.of(forecastDate, start).atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(LocalDateTime.of(forecastDate, end).atZone(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapper<FmForecastProductData> wrapper = new LambdaQueryWrapper<>();
        //wrapper.eq(FmForecastProductData::getDataSource, dataSource);
        wrapper.eq(FmForecastProductData::getStartTime, reportTime);
        wrapper.between(FmForecastProductData::getForecastTime, startTime, endTime);
        List<FmForecastProductData> list = fmForecastProductDataService.list(wrapper);
        Map<String, FmForecastProductData> map = new HashMap<>();
        for (FmForecastProductData info : list) {
            String key = info.getAreaId() + "-" + info.getElementCode();
            map.put(key, info);
        }
        return map;
    }

    /**
     * 封装预报模板及数据
     **/
    private ForecastRecordVO getForecastRecordByTemplateAndData(ForecastTask task, String dataSource, Date reportTime) {
        ForecastRecordVO record = new ForecastRecordVO();
        record.setForecastTaskId(task.getId());
        record.setForecastTemplateId(task.getTemplateId());
        record.setForecastType(task.getForecastType());
        record.setDataSource(dataSource);
        record.setReportTime(reportTime);

        //区域信息
        AreaDTO area = new AreaDTO();
        area.setAreaTypeCode(task.getForecastType());
        List<AreaVO> areaList = areaService.getList(area);
        Map<String, String> areaMap = areaList.stream().collect(Collectors.toMap(AreaVO::getCode, AreaVO::getName, (key1, key2) -> key2));

        //查询预报数据
        Map<String, FmForecastProductData> dataMap = this.getForecastDataList(record.getDataSource(), reportTime);

        List<ForecastRecordDetail> detailList = new ArrayList<>();
        //预报模板
        ForecastTemplateVO template = forecastTemplateService.getInfoById(task.getTemplateId());
        record.setForecastTemplateCode(template.getCode());
        List<ForecastTemplateColumn> columnList =
                template.getColumnList().stream().filter(column -> Boolean.TRUE.equals(column.getDisplay())).collect(Collectors.toList());
        List<ForecastTemplateRow> rowList = template.getRowList();
        //要素信息
        List<ElementVO> elementList = elementService.getList();
        Map<String, String> elementMap = elementList.stream().collect(Collectors.toMap(ElementVO::getCode, ElementVO::getName, (key1, key2) -> key2));

        for (ForecastTemplateRow row : rowList) {
            for (ForecastTemplateColumn column : columnList) {
                ForecastRecordDetail detail = new ForecastRecordDetail();
                detail.setAreaId(row.getAreaId());
                detail.setAreaCode(row.getAreaCode());
                detail.setAreaName(areaMap.get(row.getAreaCode()));
                detail.setElementCode(column.getElementCode());
                detail.setElementName(elementMap.get(column.getElementCode()));
                detail.setColumnCode(column.getColumnCode());
                detail.setColumnName(column.getColumnName());
                detail.setElementValueHandle(column.getValueHandle());
                detail.setElementDisplay(column.getDisplay());
                FmForecastProductData data = dataMap.get(detail.getAreaId() + "-" + detail.getElementCode());
                if (data != null) {
                    detail.setId(data.getId());
                    String min = data.getModifyMinValue() == null ? data.getMinValue() : data.getModifyMinValue();
                    String max = data.getModifyMaxValue() == null ? data.getMaxValue() : data.getModifyMaxValue();
                    //String mean = data.getModifyAveValue() == null ? data.getAveValue() : data.getModifyAveValue();
                    String mean = data.getAveValue();
                    if (min != null && !min.contains(":")) {
                        if (!containsNoChinese(min)) {
                            detail.setValue(min);
                            detailList.add(detail);
                            continue;
                        }
                        min = new BigDecimal(min).setScale(1, RoundingMode.HALF_UP).toString();
                    }
                    if (max != null && !max.contains(":")) {
                        if (!containsNoChinese(max)) {
                            detail.setValue(max);
                            detailList.add(detail);
                            continue;
                        }
                        max = new BigDecimal(max).setScale(1, RoundingMode.HALF_UP).toString();
                    }
                    if (mean != null && !mean.contains(":")) {
                        log.info("data.getId()---:{},mean---:{}",data.getId(),mean);
                        if (!containsNoChinese(mean)) {
                            detail.setValue(mean);
                            detailList.add(detail);
                            continue;
                        }
                        mean = new BigDecimal(mean).setScale(1, RoundingMode.HALF_UP).toString();
                    }
                    if (ValueHandle.MIN.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevel(Double.valueOf(min)));
                        } else {
                            detail.setValue(min);
                        }
                    }
                    if (ValueHandle.MAX.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevel(Double.valueOf(max)));
                        } else {
                            detail.setValue(max);
                        }
                    }
                    if (ValueHandle.MEAN.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevel(Double.valueOf(mean)));
                        } else {
                            detail.setValue(mean);
                        }
                    }
                    if (ValueHandle.INTERVAL.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevelRange(Double.valueOf(min), Double.valueOf(max)));
                        } else {
                            detail.setValue(min + "~" + max);
                        }
                    }
                }
                detailList.add(detail);
            }
        }
        record.setDetailList(detailList);
        return record;
    }

    /**
     * 封装预报模板及数据
     **/
    private ForecastRecordVO getForecastRecordByTemplateAndData(ForecastTask task, Date reportTime) {
        ForecastRecordVO record = new ForecastRecordVO();
        record.setForecastTaskId(task.getId());
        record.setForecastTemplateId(task.getTemplateId());
        record.setForecastType(task.getForecastType());
        //record.setDataSource(dataSource);
        record.setReportTime(reportTime);

        //区域信息
        AreaDTO area = new AreaDTO();
        area.setAreaTypeCode(task.getForecastType());
        List<AreaVO> areaList = areaService.getList(area);
        Map<String, String> areaMap = areaList.stream().collect(Collectors.toMap(AreaVO::getCode, AreaVO::getName, (key1, key2) -> key2));

        //查询预报数据
        Map<String, FmForecastProductData> dataMap = this.getForecastDataList(reportTime);

        List<ForecastRecordDetail> detailList = new ArrayList<>();
        //预报模板
        ForecastTemplateVO template = forecastTemplateService.getInfoById(task.getTemplateId());
        record.setForecastTemplateCode(template.getCode());
        List<ForecastTemplateColumn> columnList =
                template.getColumnList().stream().filter(column -> Boolean.TRUE.equals(column.getDisplay())).collect(Collectors.toList());
        List<ForecastTemplateRow> rowList = template.getRowList();
        //要素信息
        List<ElementVO> elementList = elementService.getList();
        Map<String, String> elementMap = elementList.stream().collect(Collectors.toMap(ElementVO::getCode, ElementVO::getName, (key1, key2) -> key2));

        for (ForecastTemplateRow row : rowList) {
            for (ForecastTemplateColumn column : columnList) {
                ForecastRecordDetail detail = new ForecastRecordDetail();
                detail.setAreaId(row.getAreaId());
                detail.setAreaCode(row.getAreaCode());
                detail.setAreaName(areaMap.get(row.getAreaCode()));
                detail.setElementCode(column.getElementCode());
                detail.setElementName(elementMap.get(column.getElementCode()));
                detail.setColumnCode(column.getColumnCode());
                detail.setColumnName(column.getColumnName());
                detail.setElementValueHandle(column.getValueHandle());
                detail.setElementDisplay(column.getDisplay());
                FmForecastProductData data = dataMap.get(detail.getAreaId() + "-" + detail.getElementCode());
                if (data != null) {
                    detail.setId(data.getId());
                    String min = data.getModifyMinValue() == null ? data.getMinValue() : data.getModifyMinValue();
                    String max = data.getModifyMaxValue() == null ? data.getMaxValue() : data.getModifyMaxValue();
                    //String mean = data.getModifyAveValue() == null ? data.getAveValue() : data.getModifyAveValue();
                    String mean = data.getAveValue();
                    if (min != null && !min.contains(":")) {
                        if (!containsNoChinese(min)) {
                            detail.setValue(min);
                            detailList.add(detail);
                            continue;
                        }
                        min = new BigDecimal(min).setScale(1, RoundingMode.HALF_UP).toString();
                    }
                    if (max != null && !max.contains(":")) {
                        if (!containsNoChinese(max)) {
                            detail.setValue(max);
                            detailList.add(detail);
                            continue;
                        }
                        max = new BigDecimal(max).setScale(1, RoundingMode.HALF_UP).toString();
                    }
                    if (mean != null && !mean.contains(":")) {
                        log.info("data.getId()---:{},mean---:{}",data.getId(),mean);
                        if (!containsNoChinese(mean)) {
                            detail.setValue(mean);
                            detailList.add(detail);
                            continue;
                        }
                        mean = new BigDecimal(mean).setScale(1, RoundingMode.HALF_UP).toString();
                    }
                    if (ValueHandle.MIN.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevel(Double.valueOf(min)));
                        } else {
                            detail.setValue(min);
                        }
                    }
                    if (ValueHandle.MAX.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevel(Double.valueOf(max)));
                        } else {
                            detail.setValue(max);
                        }
                    }
                    if (ValueHandle.MEAN.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevel(Double.valueOf(mean)));
                        } else {
                            detail.setValue(mean);
                        }
                    }
                    if (ValueHandle.INTERVAL.getValue().equals(detail.getElementValueHandle())) {
                        if (detail.getColumnCode().startsWith("langji")) {
                            detail.setValue(this.getWaveLevelRange(Double.valueOf(min), Double.valueOf(max)));
                        } else {
                            detail.setValue(min + "~" + max);
                        }
                    }
                }
                detailList.add(detail);
            }
        }
        record.setDetailList(detailList);
        return record;
    }

    private LambdaQueryWrapper<ForecastRecord> createQueryWrapper() {
        LambdaQueryWrapper<ForecastRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(ForecastRecord::getCreateTime, LocalDate.now().atTime(LocalTime.MIN));
        queryWrapper.le(ForecastRecord::getCreateTime, LocalDate.now().atTime(LocalTime.MAX));
        return queryWrapper.orderByAsc(ForecastRecord::getCreateTime);
    }

    /**
     * 根据浪高返回浪级
     * 微浪<0.1米
     * 0.1=<小浪<0.5米
     * 0.5=<轻浪<1.25米
     * 1.25=<中浪<2.5米 2.5=<大浪<4.0米
     * 4.0=<巨浪<6.0米
     * 6.0=<狂浪<9.0米 9.0=<狂涛<14.0米
     * 怒涛>=14.0米
     **/
    private String getWaveLevel(Double waveHeight) {
        if (waveHeight == null) {
            return null;
        }
        if (waveHeight < 0.1) {
            return "微浪";
        } else if (waveHeight >= 0.1 && waveHeight < 0.5) {
            return "小浪";
        } else if (waveHeight >= 0.5 && waveHeight < 1.25) {
            return "轻浪";
        } else if (waveHeight >= 1.25 && waveHeight < 2.5) {
            return "中浪";
        } else if (waveHeight >= 2.5 && waveHeight < 4.0) {
            return "大浪";
        } else if (waveHeight >= 4.0 && waveHeight < 6.0) {
            return "巨浪";
        } else if (waveHeight >= 6.0 && waveHeight < 9.0) {
            return "狂浪";
        } else if (waveHeight >= 9.0 && waveHeight < 14.0) {
            return "狂涛";
        } else {
            return "怒涛";
        }
    }

    /**
     * 根据浪高的最小值和最大值返回浪级变化的范围
     **/
    private String getWaveLevelRange(Double minWaveHeight, Double maxWaveHeight) {
        String minLevel = getWaveLevel(minWaveHeight);
        String maxLevel = getWaveLevel(maxWaveHeight);
        //处理相同浪级的情况
        if (minLevel != null && minLevel.equals(maxLevel)) {
            return minLevel;
        }
        //处理一般情况
        return minLevel + "到" + maxLevel;
    }

    private static boolean containsNoChinese(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }

        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                return false;
            }
        }
        return true;
    }

    private static boolean isChineseCharacter(char c){
        return (c>='\u4E00' && c <= '\u9FFF') || (c>='\u3400' && c <= '\u4DBF');
    }

    public static void main(String[] args) {
        System.out.println(containsNoChinese("aa"));
    }
}





