import { useROIInject } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIInject'
import { Modify } from 'ol/interaction'
import { inject, onMounted, onUnmounted, Ref } from 'vue'
import VectorSource from 'ol/source/Vector'
import {
  type IGetMapFunction,
  roiToolBus
} from 'src/components/OpenlayersMap/components/ROITools/hooks/types'
import { ActionType } from 'src/components/OpenlayersMap/components/ROITools/hooks/useROIOptions'
import { useMessage } from 'naive-ui'
import Polygon from 'ol/geom/Polygon'

import { type FeatureLike } from 'ol/Feature'
import { smoothPolygon } from 'src/utils/smoothPolygon'
import { Circle, Stroke, Style } from 'ol/style'
import MultiPoint from 'ol/geom/MultiPoint'
import VectorLayer from 'ol/layer/Vector'
import { Geometry, SimpleGeometry } from 'ol/geom'
import eventBus from 'src/utils/eventBus'
import { Feature } from 'ol'
import Collection from 'ol/Collection.js'
import { getLandPoint } from 'src/components/OpenlayersMap/components/ROITools/com-draw/hooks/useDrawROI'

interface IModifyObj {
  modify: Modify | undefined
  modifyVecSource: VectorSource
  modifyVecLayer: VectorLayer<VectorSource>
}

/**
 * modify hook
 */
export function useModifyROI() {
  const getMapInject = inject<IGetMapFunction>('getMap')
  const roiOptions = useROIInject('roiOptions').injectROI()
  const vectorSource = useROIInject('vectorSource').injectROI()
  const vectorLayer = useROIInject('vectorLayer').injectROI()
  const currentAction = useROIInject('currentAction').injectROI()
  const intersectPointLayer = useROIInject('intersectPointLayer').injectROI()
  if (
    !getMapInject ||
    !roiOptions ||
    !vectorSource ||
    !currentAction ||
    !vectorLayer ||
    !intersectPointLayer
  ) {
    throw new Error('注入失败')
  }

  const { modifyVecSource, modifyVecLayer, saveModify } = createModifyVector({
    vectorSource: vectorSource,
    vectorLayer: vectorLayer
  })

  let modify: Modify | undefined

  onMounted(() => {
    vectorLayer.setVisible(false)
    intersectPointLayer.setVisible(false)
    modify = createModify({
      currentAction,
      vectorSource,
      vectorLayer,
      modifyVecSource,
      modifyVecLayer
    })
    eventBus.on(roiToolBus.saveModify, () => {
      saveModify()
      currentAction.value = ActionType.NONE
      getLandPoint({ intersectPointLayer, vectorSource })
    })
    getMapInject(map => {
      modify && map.addInteraction(modify)
      map.addLayer(modifyVecLayer)
    })
  })

  onUnmounted(() => {
    eventBus.off(roiToolBus.saveModify)
    vectorLayer.setVisible(true)
    intersectPointLayer.setVisible(true)
    getMapInject(map => {
      modify && map.removeInteraction(modify)
      map.removeLayer(modifyVecLayer)
    })
  })
  return {
    modify,
    currentAction,
    saveModify
  }
}

/**
 * 初始化绘制
 * @param opt
 */
function createModify(opt: {
  currentAction: Ref<ActionType>
  vectorSource: VectorSource
  vectorLayer: VectorLayer<VectorSource>
  modifyVecSource: VectorSource
  modifyVecLayer: VectorLayer<VectorSource>
}): Modify | undefined {
  return new Modify({
    source: opt.modifyVecSource
  })
}

/**
 * 创建 modify vector
 * @param opt
 */
function createModifyVector(opt: {
  vectorSource: VectorSource
  vectorLayer: VectorLayer<VectorSource>
}) {
  const modifyVecSource = new VectorSource<SimpleGeometry>({
    useSpatialIndex: false
  })
  const modifyVecLayer = new VectorLayer({
    source: modifyVecSource,
    style: modifyStyle
  })

  opt.vectorSource.getFeatures().forEach(item => {
    const properties = item.getProperties()
    const geom = item.getGeometry()
    const geomProp = geom?.getProperties()
    let feature: Feature | null = null
    if (geomProp?.$originPoints) {
      // 忽略平滑产生的额外的点
      const polygon = new Polygon(geomProp.$originPoints)
      polygon.setProperties(geomProp)
      feature = new Feature(polygon)
    } else if (properties.points) {
      feature = new Feature(new Polygon([properties.points]))
    } else {
      const lstring = (geom as any).getCoordinates()
      feature = new Feature(new Polygon(lstring))
    }

    for (const key in properties) {
      if (key != 'geometry') {
        feature.set(key, properties[key])
      }
    }
    modifyVecSource.addFeature(feature as Feature<SimpleGeometry>)
  })

  function saveModify() {
    modifyVecSource.getFeatures().forEach(feature => {
      const geometry = feature.getGeometry()
      // 更新编辑后的原始点坐标
      geometry &&
        geometry.setProperties({ $originPoints: geometry.getCoordinates() })
      if (geometry instanceof Polygon) {
        const coordinates = geometry.getCoordinates()
        const points = coordinates[0].map(item => {
          return {
            x: item[0],
            y: item[1]
          }
        })
        const smoothPolygonResult = smoothPolygon(points)
        geometry.setCoordinates([smoothPolygonResult.map(i => [i.x, i.y])])
      }
    })
    opt.vectorSource.clear()
    opt.vectorSource.addFeatures(modifyVecSource.getFeatures())
    modifyVecSource.clear()
  }

  return {
    modifyVecSource,
    modifyVecLayer,
    saveModify
  }
}

/**
 * modify style
 * @param f
 */
function modifyStyle(f: FeatureLike) {
  const geom = f.getGeometry()
  console.log('isPolygon', isPolygon(geom))
  if (!isPolygon(geom)) {
    if (geom instanceof Geometry) {
      return [
        new Style({
          stroke: new Stroke({ color: 'blue', width: 1 }),
          geometry: geom
        })
      ]
    }
    return []
  }
  const geomProp = geom.getProperties()

  const label = f.get('value') + ''
  const editPoint = geom.getCoordinates()
  const pointGeometry: any[] = []
  let geometry = null
  const properties = f.getProperties()
  const lstring = geom.getCoordinates()
  const points: any[] = [] // 原始点
  lstring?.forEach(item => {
    points.push(item)
  })
  geometry = new Polygon(points)
  editPoint?.forEach(item => {
    const downsampledArray = downsampleArray(item, 1)
    downsampledArray.forEach(item1 => {
      pointGeometry.push(item1)
    })
  })
  const pointObjArr = points[0].map((p: any) => {
    return { x: p[0], y: p[1] }
  })
  const smoothPoints = smoothPolygon(pointObjArr)
  geometry.setCoordinates([smoothPoints.map(i => [i.x, i.y])])

  return [
    new Style({
      stroke: new Stroke({ color: 'red', width: 1 }),
      geometry: geometry
    }),
    new Style({
      image: new Circle({
        stroke: new Stroke({ color: 'red', width: 4 }),
        radius: 2
      }),
      geometry: new MultiPoint(pointGeometry)
    })
  ]
}

/**
 * 判断是否是 Polygon
 * @param geom
 */
function isPolygon(geom: Geometry | unknown): geom is Polygon {
  return geom instanceof Polygon
}

/**
 * 几何简化
 * @param arr
 * @param factor
 */
function downsampleArray<T>(arr: T[], factor: number): T[] {
  if (factor <= 0) {
    throw new Error('Factor must be greater than 0')
  }

  const result: T[] = []
  arr.forEach((element, index) => {
    if (index % factor === 0) {
      result.push(element)
    }
  })

  return result
}
