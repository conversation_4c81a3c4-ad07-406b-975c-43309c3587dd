package cn.piesat.data.making.server.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.ArrayList;
import java.util.List;

public class ExcelListener<T> extends AnalysisEventListener<T> {

    private List<T> list;

    public List<T> getList() {
        return list;
    }

    public ExcelListener() {
        this.list = new ArrayList<>();
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        // 获取行号
        Integer rowIndex = context.readRowHolder().getRowIndex();
        // 操作数据值
        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
