package cn.piesat.data.making.server.vo;

import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预报记录表VO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastRecordVO implements Serializable {

    private static final long serialVersionUID = -98496782012196150L;

    /**
     * id
     **/
    private Long id;
    /**
     * 预报任务id
     **/
    private Long forecastTaskId;
    /**
     * 预报模板id
     **/
    private Long forecastTemplateId;
    /**
     * 预报模板编码
     **/
    private String forecastTemplateCode;
    /**
     * 预报类型
     **/
    private String forecastType;
    /**
     * 起报时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;
    /**
     * 数据源
     **/
    private String dataSource;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 预报记录明细列表
     **/
    private List<ForecastRecordDetail> detailList;

    /**
     * 预报内容
     */
    private String forecastContent;
}



