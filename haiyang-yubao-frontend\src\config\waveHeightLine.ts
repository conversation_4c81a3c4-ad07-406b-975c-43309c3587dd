/**
 * 浪高等值线
 */
export interface IWaveHeightLine {
  condition: (val: number) => boolean
  name: string
  color: string
}

export const waveHeightLineList: IWaveHeightLine[] = [
  {
    color: '#54ddc4',
    // condition: val => val < 2.5,
    // name: '2.5米'
    condition: val => val < 2,
    name: '2.0米'
  },
  {
    color: '#ffd970',
    // condition: val => val >= 2.5 && val < 3,
    condition: val => val >= 2 && val < 3,
    name: '3.0米'
  },
  {
    color: '#fead19',
    condition: val => val <= 3 && val < 4,
    name: '4.0米'
  },
  {
    color: '#fe6418',
    condition: val => val <= 4 && val < 6,
    name: '6.0米'
  },
  {
    color: '#d40205',
    condition: val => val <= 6 && val < 9,
    name: '9.0米'
  },
  {
    color: '#750806',
    condition: val => val <= 9 && val < 14,
    name: '14.0米'
  }
]
