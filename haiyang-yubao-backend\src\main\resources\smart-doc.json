{
  // 服务器地址, 导出postman建议设置成http://{{server}}方便直接在postman直接设置环境变量
  "serverUrl": "http://localhost:8080",
  // 是否开启严格模式
  "isStrict": false,
  // 是否将文档合并到一个文件中，一般推荐为true
  "allInOne": true,
  // 指定文档的输出路径。
  "outPath": "./src/main/resources/static/doc",
  // 是否覆盖旧的文件，主要用于mardown文件覆盖
  "coverOld": true,
  // 基于highlight.js的代码高亮设置
  "style":"xt256",
  "createDebugPage": true,
  // smart-doc支持创建可以测试的html页面，仅在AllInOne模式中起作用
  "md5EncryptedHtmlName": false,
  // 推送torna配置接口服务地址
  "debugEnvUrl":"http://127.0.0.1:8080",
  // torna平台地址，填写自己的私有化部署地址
  "openUrl": "http://qhl-develop.natapp1.cc/torna/api",
  // torna平台appToken
  "appToken": "3f61740da91e49f485629e62fa305d61",
  "responseBodyAdvice":{ //自smart-doc 1.9.8起，非必须项，ResponseBodyAdvice统一返回设置(不要随便配置根据项目的技术来配置)，可用ignoreResponseBodyAdvice tag来忽略
    "className":"cn.piesat.webconfig.response.R" //通用响应体
  }
}