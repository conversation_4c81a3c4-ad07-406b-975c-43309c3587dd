<template>
  <qx-dialog
    :visible="visible"
    title="新建用户"
    width="500px"
    class="create-user-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        required-mark-placement="left"
        class="qx-form"
      >
        <n-form-item label="姓名" path="name">
          <n-input v-model:value="form.name" placeholder="请输入" clearable />
        </n-form-item>
        <n-form-item label="联系电话" path="phone">
          <n-input
            v-model:value="form.phone"
            placeholder="请输入"
            clearable
            :input-props="{ type: 'tel' }"
          />
        </n-form-item>
        <n-form-item label="电子邮箱" path="email">
          <n-input v-model:value="form.email" placeholder="请输入" clearable />
        </n-form-item>
      </n-form>
    </template>
    <template #suffix>
      <div class="btn-group">
        <qx-button class="primary" @click="onSave">保存</qx-button>
        <qx-button class="cancel" @click="onClose">取消</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>
<script lang="ts" setup>
import { QxDialog } from 'src/components/QxDialog'
import { reactive, watch, ref } from 'vue'
import type { FormItemRule } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:visible', 'save'])
const formRef = ref()
const form = reactive({
  name: '',
  phone: '',
  email: ''
})

const rules = {
  name: {
    required: true,
    message: '请输入姓名',
    trigger: 'blur'
  }
  // email: {
  //   validator(rule: FormItemRule, value: string) {
  //     if (
  //       !/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
  //         value
  //       )
  //     ) {
  //       return new Error('格式不正确')
  //     }
  //     return true
  //   },
  //   trigger: 'blur'
  // }
}
watch(
  () => props.info,
  val => {
    form.name = val.name
    form.phone = val.phone
    form.email = val.email
  },
  {
    immediate: true
  }
)

function onClose() {
  emit('update:visible', false)
}
function onSave() {
  formRef.value.validate((valid: boolean) => {
    if (!valid) {
      emit('save', form)
      form.name = ''
      form.phone = ''
      form.email = ''
    }
  })
}
</script>
<style scoped lang="scss">
@mixin box-sizing {
  box-sizing: border-box;
  padding: 20px;
}
.create-user-dialog {
  @include box-sizing;
  .n-form {
    @include box-sizing;
    padding-bottom: 0;
  }
  .btn-group {
    @include box-sizing;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: right;
  }
}
</style>
