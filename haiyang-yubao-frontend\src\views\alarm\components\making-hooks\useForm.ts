import type { IAlarmLevelItem } from './useAlarmLevel.type'
import { toValue, type MaybeRefOrGetter, inject } from 'vue'
import { useBridge } from 'src/utils/vue-hooks/useBridge/useBridge'
import { bridgeKey, type IBridgeData } from '../../alarmMakingHooks/bridge.type'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'

export function useForm() {
  const bridge = inject<IBridge<IBridgeData>>(bridgeKey)!

  return {
    getSummary: (
    ) => getSummary(bridge)
  }
}

function getSummary(
  bridge: IBridge<IBridgeData>
) {
  const collect = bridge.collect()
  const model = collect.model
  if (!model) {
    return ''
  }
  return `海南省海洋预报台根据《海南省风暴潮、海浪和海啸灾害应急预案》发布${
    model.title
  }`
}
