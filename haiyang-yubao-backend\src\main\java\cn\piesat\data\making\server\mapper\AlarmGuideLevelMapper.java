package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.AlarmDefenseGuideDTO;
import cn.piesat.data.making.server.entity.AlarmDefenseGuide;
import cn.piesat.data.making.server.entity.AlarmGuideLevel;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface AlarmGuideLevelMapper {
    AlarmGuideLevelMapper INSTANCE = Mappers.getMapper(AlarmGuideLevelMapper.class);

    List<AlarmGuideLevel> toEntity(List<AlarmDefenseGuideDTO.GuideLevelDTO> guideLevelList);

   // List<AlarmGuideLevel> toEntity(List<AlarmDefenseGuideDTO.GuideLevelDTO> guideLevelList, AlarmDefenseGuide guide);

    List<AlarmDefenseGuideDTO.GuideLevelDTO> toDTO(List<AlarmGuideLevel> guideLevels);

    @Mapping(source = "guide.id",target = "guideId")
    @Mapping(source = "guideLevelDTO.id",target = "id")
    @Mapping(source = "guideLevelDTO.levelId",target = "levelId")
    @Mapping(source = "guideLevelDTO.guideContent",target = "guideContent")
    AlarmGuideLevel toEntity(AlarmDefenseGuideDTO.GuideLevelDTO guideLevelDTO, AlarmDefenseGuide guide);

    @Mapping(source = "sourceList", target = "targetList")
    default List<AlarmGuideLevel> toEntity(List<AlarmDefenseGuideDTO.GuideLevelDTO> guideLevelList, AlarmDefenseGuide guide) {
        if (guideLevelList == null) {
            return null;
        }
        return guideLevelList.stream()
                .map(dto ->toEntity(dto,guide))
                .collect(Collectors.toList());
    }
}
