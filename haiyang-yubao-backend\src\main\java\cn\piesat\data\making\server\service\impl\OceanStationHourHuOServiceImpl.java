package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourHuODao;
import cn.piesat.data.making.server.entity.OceanStationHourHuO;
import cn.piesat.data.making.server.service.OceanStationHourHuOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-相对湿度-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourHuOServiceImpl extends ServiceImpl<OceanStationHourHuODao, OceanStationHourHuO>
        implements OceanStationHourHuOService {

    @Resource
    private OceanStationHourHuODao oceanStationHourHuODao;

    @Override
    public List<OceanStationHourHuO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourHuODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourHuODao.getMaxCreateTime();
    }
}





