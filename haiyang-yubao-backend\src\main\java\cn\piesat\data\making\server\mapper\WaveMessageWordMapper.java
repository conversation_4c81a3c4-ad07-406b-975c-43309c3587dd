package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.generate.WaveMessageWordDTO;
import cn.piesat.data.making.server.entity.SeaWaveMessage;
import cn.piesat.data.making.server.vo.UserVO;
import cn.piesat.security.ucenter.base.dto.UserInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface WaveMessageWordMapper {
    WaveMessageWordMapper INSTANCE = Mappers.getMapper(WaveMessageWordMapper.class);

    @Mapping(target = "time", source = "seaWaveMessage.releaseTime", dateFormat = "yyyy年MM月dd日HH时")
    WaveMessageWordDTO toDTO(SeaWaveMessage seaWaveMessage, UserInfoDTO userInfo, String fax);

    @Mapping(target = "time", source = "seaWaveMessage.releaseTime", dateFormat = "yyyy年MM月dd日HH时")
    @Mapping(target = "eMail", source = "userVO.email")
    WaveMessageWordDTO toDTO(SeaWaveMessage seaWaveMessage, UserVO userVO, String fax);
}
