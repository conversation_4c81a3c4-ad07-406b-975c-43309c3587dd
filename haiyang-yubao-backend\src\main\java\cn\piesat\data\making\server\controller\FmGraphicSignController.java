package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.vo.FmGraphicSignVO;
import cn.piesat.data.making.server.service.FmGraphicSignService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;

import javax.annotation.Resource;
import java.util.List;

/**
 * 图形标签控制层
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:38
 */
@RestController
@RequestMapping("fmGraphicSign")
public class FmGraphicSignController {

    @Resource
    private FmGraphicSignService fmGraphicSignService;

//    /**
//     * 查询分页
//     *
//     * @param id
//     * @param pageNum  页数
//     * @param pageSize 条数
//     * @return
//     */
//    @GetMapping("/page")
//    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
//    public PageResult<FmGraphicSignVO> getPage(@RequestParam(required = false) Long id,
//                                               @RequestParam(defaultValue = "1") Integer pageNum,
//                                               @RequestParam(defaultValue = "10") Integer pageSize) {
//        FmGraphicSignDTO dto = new FmGraphicSignDTO();
//        dto.setId(id);
//        PageParam pageParam = new PageParam();
//        pageParam.setPageNum(pageNum);
//        pageParam.setPageSize(pageSize);
//        return fmGraphicSignService.getPage(dto, pageParam);
//    }

    /**
     * 查询列表
     *
     * @param type
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
    public List<FmGraphicSignVO> getList(@RequestParam(required = false) String type) {
        return fmGraphicSignService.getList(type);
    }

//    /**
//     * 根据id查询数据
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/info/{id}")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
//    public FmGraphicSignVO getById(@PathVariable Long id) {
//        return fmGraphicSignService.getById(id);
//    }
//
//    /**
//     * 保存数据
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/save")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.INSERT)
//    public void save(@Validated(value = {FmGraphicSignDTO.Save.class}) @RequestBody FmGraphicSignDTO dto) {
//        fmGraphicSignService.save(dto);
//    }
//
//    /**
//     * 根据id删除数据
//     *
//     * @param id
//     * @return
//     */
//    @DeleteMapping("/delete/{id}")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.DELETE)
//    public void deleteById(@PathVariable("id") Long id) {
//        fmGraphicSignService.deleteById(id);
//    }
//
//    /**
//     * 根据idList批量删除数据
//     *
//     * @param idList
//     * @return
//     */
//    @DeleteMapping("/delete")
//    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.DELETE)
//    public void deleteByIdList(@RequestBody List<Long> idList) {
//        fmGraphicSignService.deleteByIdList(idList);
//    }
}
