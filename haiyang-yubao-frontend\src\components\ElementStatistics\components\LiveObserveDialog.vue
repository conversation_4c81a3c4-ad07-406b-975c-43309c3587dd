<template>
  <qx-dialog
    :title="title"
    :visible="dialogVisible"
    width="900px"
    class="live-observe-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <n-spin :show="isLoading">
        <div class="dialog-content">
          <n-form
            ref="formRef"
            :model="form"
            label-placement="left"
            label-width="auto"
            require-mark-placement="left"
            :show-feedback="false"
          >
            <n-grid :cols="24" :x-gap="12">
              <n-form-item-gi :span="16" label="数据时间:">
                <n-date-picker
                  style="width: 300px"
                  v-model:value="form.originalDatetime"
                  type="daterange"
                  :update-value-on-close="true"
                  clearable
                  :actions="[]"
                  @update:value="getStatisticsList"
                  @clear="clearHandler"
                />

                <qx-button
                  :class="{ primary: rangeIndex == 0, ml12: true }"
                  @click="changeTimeRange(3, 0)"
                  >3天
                </qx-button>
                <qx-button
                  :class="{ primary: rangeIndex == 1 }"
                  @click="changeTimeRange(5, 1)"
                  >5天
                </qx-button>
                <qx-button
                  :class="{ primary: rangeIndex == 0, ml12: true }"
                  @click="exportTable"
                  >导出表格
                </qx-button>
              </n-form-item-gi>
            </n-grid>
            <n-grid :col="24" :x-gap="12">
              <n-form-item-gi :span="7" label="统计要素:" path="element">
                <n-select
                  v-model:value="form.element"
                  :options="elementList"
                  label-field="name"
                  value-field="code"
                  @update:value="onSelectElement"
                />
              </n-form-item-gi>
              <template v-if="thresholdList.includes(form.element)">
                <n-form-item-gi
                  :span="stationType === 'oceanStation' ? 8 : 12"
                  label="统计阈值:"
                >
                  站点{{ getElementLabel().name }}连续超过
                  <n-input
                    v-model:value="form.threshold"
                    placeholdere="请输入"
                    style="width: 35%"
                    @blur="changeMarkPoints"
                  >
                    <template #suffix>
                      {{
                        [
                          'windWaveHeight',
                          'puPbg',
                          'ylpuPbg',
                          'buoydataYbg'
                        ].includes(form.element)
                          ? 'm'
                          : '级'
                      }}
                    </template>
                  </n-input>
                  的持续时间
                </n-form-item-gi>
              </template>
              <template v-if="stationType === 'oceanStation'">
                <n-form-item-gi :span="6" label="统计维度:" path="type">
                  <n-radio-group
                    v-model:value="form.dimension"
                    name="type"
                    @update:value="changeDimension"
                  >
                    <n-radio value="hour">整点</n-radio>
                    <n-radio value="minutes">分钟</n-radio>
                  </n-radio-group>
                </n-form-item-gi>
              </template>
            </n-grid>
            <n-grid :col="24" :x-gap="12">
              <n-form-item-gi :span="12" label="统计方式:">
                <n-radio-group
                  v-model:value="form.method"
                  name="radioGroup"
                  @update:value="onChangeMethod"
                >
                  <n-radio :value="1">单点</n-radio>
                  <n-radio :value="2">区域</n-radio>
                </n-radio-group>
                <n-select
                  v-if="form.method == 2"
                  v-model:value="form.oceanStationCodeList"
                  style="width: 50%"
                  :options="areaList"
                  label-field="relationCode"
                  value-field="code"
                  :consistent-menu-width="false"
                  multiple
                  :max-tag-count="1"
                  clearable
                  @blur="getStatisticsList"
                />
              </n-form-item-gi>
              <n-form-item-gi :span="9" label="展现形式:" path="type">
                <n-radio-group
                  v-model:value="form.type"
                  name="type"
                  @update:value="changeType"
                >
                  <n-radio :value="1">折线</n-radio>
                  <n-radio :value="2">柱图</n-radio>
                  <n-radio :value="3">表格</n-radio>
                </n-radio-group>
              </n-form-item-gi>
            </n-grid>
          </n-form>

          <div
            v-if="form.type == 3 && tableData.length"
            class="table-container"
          >
            <n-data-table
              :columns="columns"
              :data="cptTableData"
              :bordered="true"
              :max-height="300"
            />
          </div>
          <qx-no-data v-if="!tableData.length" />
          <div
            v-if="form.type != 3 && tableData.length"
            class="chart-container"
          >
            <qx-echarts :option="echartOption" />
          </div>
          <div
            v-if="form.threshold && form.dimension === 'hour'"
            class="statistics-tips"
          >
            {{ elementLabel }}连续超过{{ form.threshold }}
            {{
              ['windWaveHeight', 'puPbg', 'ylpuPbg', 'buoydataYbg'].includes(
                form.element
              )
                ? 'm'
                : '级'
            }}以上的时间共计{{ statisticsTime }}
            小时
          </div>
        </div>
      </n-spin>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { QxDialog } from 'src/components/QxDialog'
import { ref, computed, reactive, onMounted, Ref, nextTick, watch } from 'vue'
import { QxEcharts } from 'src/components/QxEcharts'
import ToolApi from 'src/requests/toolRequest'
import moment from 'moment'
import { groupBy } from 'lodash'
import { QxButton } from 'src/components/QxButton'
import { FormType, paramsType, DataItem, IResponseItem } from './types'

import { useMessage } from 'naive-ui'
import { NoDataError } from 'src/utils/errors/noDataError'
import { downloadCsvString, ICsvColumn, toCsvString } from 'src/utils/excel'
import { useMultiVectorSelect } from 'src/components/ElementStatistics/components/liveMonitoring-hooks/useMultiVectorSelect'

const emit = defineEmits(['visible', 'close'])
const message = useMessage()
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  },
  stationType: {
    type: String,
    default: ''
  },
  stationInfo: {
    type: Object,
    default: () => ({})
  }
})
const dialogVisible = computed({
  get: () => props.visible,
  set: val => {
    emit('visible', val)
  }
})

useMultiVectorSelect({ form: () => form })

const isLoading = ref(true)

const thresholdList = [
  'windWaveHeight',
  'windSpeed',
  'puPbg',
  'ylpuPbg',
  'buoydataWs',
  'buoydataYbg'
]
const title = computed(() => props.title + '-实况观测数据统计')
const stationType = computed(() => props.stationType)
const onClose = () => {
  emit('close', false)
}

let areaList = ref([])
let elementList = ref<any[]>([])
const form: FormType = reactive({
  dataTime: [
    computed(() =>
      moment(form.originalDatetime[0]).format('YYYY-MM-DD HH:mm:ss')
    ),
    computed(() =>
      moment(form.originalDatetime[1]).format('YYYY-MM-DD HH:mm:ss')
    )
  ],
  originalDatetime: [
    moment().startOf('days').valueOf(),
    moment().startOf('minutes').valueOf()
  ],
  oceanStationCodeList: [],
  method: 1,
  element: '',
  type: 1,
  dimension: 'hour',
  threshold: ''
})

function checkDatetime(value: (number | string)[]) {
  let startTime = moment(value[0]).startOf('minutes')
  let endTime = moment(value[1]).startOf('minutes').endOf('days')
  const currentTime = moment().startOf('minutes')
  if (endTime.isAfter(currentTime)) {
    endTime = currentTime
  }
  if (!form.originalDatetime) {
    return
  }
  form.originalDatetime[0] = startTime.valueOf()
  form.originalDatetime[1] = endTime.valueOf()
}

let elementLabel = ref('')

// 统计要素
function onSelectElement(val: string) {
  form.threshold = ''
  let result = elementList.value.find(item => item.code == val)
  elementLabel.value = result?.name || ''
  columns.value[columns.value.length - 1].key = val
  echartOption.dataset[0].dimensions = [form.element, 'time']
  echartOption.dataset[0].source = tableData.value

  if (form.method == 2) {
    let code = 'oceanStationCode'
    if (stationType.value == 'ocean') {
      code = 'oceanStationCode'
    } else if (stationType.value == 'buoyStation') {
      code = 'buoyStationCode'
    } else if (stationType.value == 'ship') {
      code = 'shipCode'
    }
    echartOption.dataset[0].dimensions.push(code)
  }
  echartOption.series.forEach(item => {
    item.encode.y = form.element
  })

  if (props.stationType === 'buoyStation') {
    buoyStatistics()
  }
}

function getElementLabel() {
  return elementList.value.find((item: any) => item.code == form.element)
}

// 切换统计方式
function onChangeMethod(val: number) {
  if (val === 1) {
    getStatisticsList()
  }
}

const mapper: Record<string, string> = {
  oceanStation: 'oceanStationName',
  buoyStation: 'buoyStationName',
  ship: 'shipName'
}
const columns = ref<any[]>([
  {
    title: '序号',
    render(row: any, rowIndex: number) {
      return rowIndex + 1
    }
  },
  {
    title: '站点名称',
    key: 'oceanStationName',
    render(row: any) {
      return mapper[props.stationType] ? row[mapper[props.stationType]] : ''
    }
  },
  {
    title: '数据时间',
    key: 'time'
  },
  {
    title: '监测值',
    key: form.element
  }
])
const tableData = ref<any[]>([])
const cptTableData = computed(() => {
  return tableData.value.filter(item => {
    return item[mapper[props.stationType]] != null
  })
})

let echartOption = reactive({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  xAxis: {
    type: 'time',
    axisLabel: {
      formatter(value: any) {
        return moment(value).format('MM-DD HH时')
      },
      rotate: 45
    }
  },
  yAxis: {
    type: 'value'
  },
  grid: {
    top: '10%',
    bottom: '15%',
    left: '6%',
    right: '5%'
  },
  dataset: [
    {
      dimensions: ['time', form.element],
      source: tableData.value
    }
  ],
  series: [
    {
      type: 'line',
      barWidth: '20px',
      encode: {
        y: form.element,
        x: 'time'
      },
      markPoint: {
        data: [
          {
            type: 'max' //最大值
          },
          {
            type: 'min' //最小值
          }
        ]
      }
    }
  ]
})

function changeDimension(val: string) {
  if (val === 'hour') {
    echartOption.xAxis.axisLabel.formatter = (value: any) => {
      return moment(value).format('MM-DD HH时')
    }
  } else {
    echartOption.xAxis.axisLabel.formatter = (value: any) => {
      return moment(value).format('MM-DD HH:mm')
    }
  }
  getStatisticsList()
}

// 切换展现形式
function changeType(type: number) {
  echartOption.series.forEach(item => {
    item.type = type == 1 ? 'line' : 'bar'
  })
  echartOption.dataset[0].source = tableData.value
}

async function getStationList() {
  areaList.value = []
  let result: any = null
  const params = {
    stationTypeCode: stationType.value
    // type: props.stationInfo.type
  }
  try {
    if (stationType.value == 'oceanStation') {
      // 是海洋站
      result = await ToolApi.getOceanStatio(params)
    } else if (stationType.value == 'buoyStation') {
      // 是浮标
      result = await ToolApi.getOceanStatio(params)
    } else if (stationType.value == 'ship') {
      // 船舶
      result = await ToolApi.getShipList()
    }
    if (result) {
      result.forEach((item: any) => {
        const type = item.typeName ? item.typeName : item.type
        item.relationCode = `[${type}]${item.code}-${item.name}`
      })
      areaList.value = result
    }
  } catch (e) {
    message.error('获取数据失败')
    console.error(e, 'getStationList--error')
  }
}

// 获取统计列表
async function getStatisticsList() {
  checkDatetime(form.originalDatetime)
  try {
    await nextTick()
    isLoading.value = true
    if (stationType.value === 'oceanStation') {
      await oceanStatistics()
    } else if (stationType.value == 'buoyStation') {
      await buoyStatistics()
    } else if (stationType.value === 'ship') {
      await shipStatistics()
    }
  } catch (e) {
    if (e instanceof Error) {
      message.error(e.message)
    } else {
      message.error('加载数据出错')
    }
  } finally {
    isLoading.value = false
  }
}

let rangeIndex: Ref<null | number> = ref(null)

function changeTimeRange(range: number, index: number) {
  let startTime = moment()
    .subtract(range - 1, 'days')
    .startOf('days')
  let endTime = moment().startOf('minutes')
  if (rangeIndex.value === index) {
    rangeIndex.value = null
    startTime = moment().startOf('days')
  } else {
    rangeIndex.value = index
  }
  form.originalDatetime = [startTime.valueOf(), endTime.valueOf()]
  getStatisticsList()
}

let statisticsTime = ref(0)

function changeMarkPoints() {
  let markPoints: any[] = [
    {
      type: 'max' //最大值
    },
    {
      type: 'min' //最小值
    }
  ]
  if (thresholdList.includes(form.element)) {
    let result = tableData.value.filter(
      (item: any) => item[form.element] >= form.threshold
    )
    markPoints = result.map((item: any) => {
      return {
        value: item[form.element],
        coord: [item.time, item[form.element]]
      }
    })
    statisticsTime.value = markPoints.length
  }
  echartOption.series.forEach(item => {
    item.markPoint.data = markPoints
  })
}

function clearHandler() {
  rangeIndex.value = null
}

// 海洋统计信息
async function oceanStatistics() {
  const params: paramsType = {
    oceanStationCodeList: [props.stationInfo.relationStation],
    relationStation: props.stationInfo.relationStation
  }
  if (form.dataTime?.length) {
    params.startTime = form.dataTime[0].value
    params.endTime = form.dataTime[1].value
  }
  if (form.method == 2) {
    params.oceanStationCodeList?.push(...form.oceanStationCodeList)
  }

  let result: any = null
  try {
    if (form.dimension === 'hour') {
      result = await ToolApi.getOceanStationStatisticsHour(params)
    } else {
      result = await ToolApi.getOceanStationStatisticsMinute(params)
    }
    formatChartOptions(result, 'oceanStationCode')
    checkNodata(result)
  } catch (e) {
    if (e instanceof NoDataError) throw e
    message.error('获取数据失败')
    tableData.value = []
  }
}

function checkNodata(result: IResponseItem[]) {
  if (
    !result.find(
      (i: IResponseItem) => i[form.element as keyof IResponseItem] !== null
    )
  ) {
    throw new NoDataError()
  }
}

function formatChartOptions(data: any, props: string) {
  let currentElementList = elementList.value.map((item: any) => item.value)
  data.forEach((item: DataItem) => {
    currentElementList.forEach((key: string) => {
      if (
        Object.prototype.hasOwnProperty.call(item, key) &&
        item[key] == 999.9
      ) {
        // 过滤无效值
        item[key] = null
      }
    })
  })
  columns.value[3].key = form.element
  tableData.value = data
  echartOption.dataset = [
    {
      dimensions: ['time', form.element],
      source: data
    }
  ]
  echartOption.series = [
    {
      barWidth: '10px',
      type: form.type == 1 ? 'line' : 'bar',
      encode: {
        y: form.element,
        x: 'time'
      },
      markPoint: {
        data: [
          {
            type: 'max'
          },
          {
            type: 'min' //最小值
          }
        ]
      }
    }
  ]
  if (form.method == 2) {
    const arr = groupBy(data, item => item[props])
    let series: any[] = []
    echartOption.dataset[0].dimensions.push(props)
    Object.keys(arr).forEach((item: string, index: number) => {
      let obj: any = {
        transform: {
          type: 'filter',
          config: { dimension: props, '=': item }
        }
      }
      echartOption.dataset.push(obj)
      series.push({
        name: `${arr[item][0][props.replace('Code', 'Name')]}(${item})`,
        type: form.type == 1 ? 'line' : 'bar',
        encode: {
          y: form.element,
          x: 'time'
        },
        datasetIndex: index + 1
      })
    })
    echartOption.series = series
  }
}

// 浮标统计信息
async function buoyStatistics() {
  const params: paramsType = {
    buoyStationCodeList: [props.stationInfo.relationStation],
    relationStation: props.stationInfo.relationStation,
    type: props.stationInfo.type,
    element: form.element
    // startTime: '2024-04-14 00:00:00',
    // endTime: '2024-04-14 23:00:00'
  }
  if (form.dataTime?.length) {
    params.startTime = form.dataTime[0].value
    params.endTime = form.dataTime[1].value
  }

  if (form.method == 2) {
    params.buoyStationCodeList?.push(...form.oceanStationCodeList)
  }
  console.log(params, 'params')
  return ToolApi.getBuoyStationStatistics(params)
    .then((res: any) => {
      formatChartOptions(res, 'buoyStationCode')
      checkNodata(res)
    })
    .catch(e => {
      if (e instanceof NoDataError) throw e
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
      console.error(e, 'buoyStatistics--error')
    })
}

// 船舶统计信息
async function shipStatistics() {
  const params: paramsType = {
    shipCodeList: [props.stationInfo.code]
    // startTime: '2000-04-14 00:00:00',
    // endTime: '2003-04-14 23:00:00'
  }
  if (form.dataTime?.length) {
    params.startTime = form.dataTime[0].value
    params.endTime = form.dataTime[1].value
  }

  if (form.method == 2) {
    params.shipCodeList?.push(...form.oceanStationCodeList)
  }

  return ToolApi.getShipStatistics(params)
    .then((res: any) => {
      formatChartOptions(res, 'shipCode')
      checkNodata(res)
    })
    .catch(e => {
      if (e instanceof NoDataError) throw e
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
      console.error(e, 'shipStatistics--error')
    })
}

onMounted(async () => {
  try {
    changeTimeRange(3, 0)
    elementList.value = []
    const params = {
      type: props.stationType
    }
    if (props.stationType === 'buoyStation') {
      params.type = props.stationInfo.type
    }
    let result = (await ToolApi.getELementList(params)) as any
    elementList.value = result
    form.element = result?.[0]?.code
    getStationList()
    getStatisticsList()
  } catch (e) {
    console.error(e, '获取统计要素失败')
  }
})

function exportTable() {
  const data = cptTableData.value
  const columns1: ICsvColumn[] = [
    {
      key: mapper[props.stationType],
      title: '站点名称',
      width: 100
    },
    {
      key: 'time',
      title: '数据时间',
      width: 100
    },
    {
      key: form.element,
      title: '监测值',
      width: 100
    }
  ]
  const s = toCsvString(data, columns1)
  downloadCsvString(s, `${title.value}.csv`)
}
</script>

<style scoped lang="scss">
.live-observe-dialog {
  .ml12 {
    margin-left: 12px;
  }

  .dialog-content {
    box-sizing: border-box;
    padding: 12px;
  }

  .n-grid {
    margin-bottom: 12px;
  }

  .element-list {
    display: flex;
    align-items: center;

    .element-item {
      padding: 5px 10px;
      background: rgba(28, 129, 248, 0.2);
      cursor: pointer;

      &.active {
        background: rgba(28, 129, 248, 1);
        color: #fff;
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 400px;
  }

  .chart {
    width: 100%;
    height: 400px;
  }

  .table-container {
    height: 450px;
  }

  .statistics-tips {
    text-align: center;
    padding: 12px;
    font-size: 14px;
  }
}
</style>
