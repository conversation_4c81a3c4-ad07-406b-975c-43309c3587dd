package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmGraphicTemplateTools;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-09 16:05:19
 */
public interface FmGraphicTemplateToolsDao extends BaseMapper<FmGraphicTemplateTools> {
    @Select("SELECT * FROM fm_graphic_template_tools WHERE template_id = #{templateId} order by id")
    List<FmGraphicTemplateTools> selectByTemplateId(Long templateId);

    @Delete("DELETE  FROM fm_graphic_template_tools where template_id = #{mainId}")
    void deleteByTemplateId(Long templateId);
    @Select("SELECT count(*) FROM fm_graphic_template_tools WHERE template_id = #{templateId}")
    int countByTemplateId(Long templateId);
}
