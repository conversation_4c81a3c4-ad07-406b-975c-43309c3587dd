package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmGraphicTemplateBDTO;
import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import cn.piesat.data.making.server.vo.FmGraphicTemplateBVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图形模板表服务接口
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:40
 */
public interface FmGraphicTemplateBService extends IService<FmGraphicTemplateB>{
        /**
         * 根据参数查询分页
         */
        PageResult<FmGraphicTemplateBVO> getPage(FmGraphicTemplateBDTO dto,PageParam pageParam);

        /**
         * 根据参数查询列表
         */
        List<FmGraphicTemplateBVO> getList(FmGraphicTemplateBDTO dto);
        /**
         * 父级列表
         */
        List<FmGraphicTemplateBVO> getListParent(FmGraphicTemplateBDTO dto);

        /**
         * 数查询列表
         */
        List<FmGraphicTemplateB> getListAll(FmGraphicTemplateBDTO dto);

        /**
         * 根据id查询数据
         */
        FmGraphicTemplateBVO getById(Long id);

        /**
         * 根据parentId查询数据
         */
        List<FmGraphicTemplateBVO> getByParentId(Long parentId);

        /**
         * 保存数据
         */
        void save(FmGraphicTemplateBDTO dto);

        /**
         * 批量保存数据
         */
        void saveList(List<FmGraphicTemplateBDTO> dtoList);

        /**
         * 根据id删除数据
         */
        void deleteById(Long id);

        /**
         * 根据idList批量删除数据
         */
        void deleteByIdList(List<Long> idList);

        /**
         * 上传base64格式图片
         */
        String uploadImageBase(String base64ImageData);
        /**
         * 上传图片
         */
        String uploadImageFile(MultipartFile file);

        /**
         * 更新模版状态
         */
        void updateStatus(FmGraphicTemplateBDTO dto);

        /**
         * 更新模版名称
         */
        void updateName(FmGraphicTemplateBDTO dto);
}
