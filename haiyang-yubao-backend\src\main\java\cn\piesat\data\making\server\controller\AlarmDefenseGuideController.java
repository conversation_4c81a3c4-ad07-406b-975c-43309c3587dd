package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.AlarmDefenseGuideDTO;
import cn.piesat.data.making.server.service.AlarmDefenseGuideService;
import cn.piesat.data.making.server.vo.AlarmDefenseGuideVO;
import cn.piesat.webconfig.validation.ValidatedGroup;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 警报防御指南
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:14
 */
@RestController
@RequestMapping("/alarmDefenseGuide")
public class AlarmDefenseGuideController {
    /**
     * 服务对象
     */
    @Autowired
    private AlarmDefenseGuideService alarmDefenseGuideService;

    /**
     * 查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报防御指南管理", operateType = OperateType.SELECT)
    public ArrayList<AlarmDefenseGuideVO> List(@RequestParam(required = false) Integer status) {
        return this.alarmDefenseGuideService.list(status);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报防御指南管理", operateType = OperateType.SELECT)
    public AlarmDefenseGuideDTO selectOne(@PathVariable Long id) {
        return this.alarmDefenseGuideService.info(id);
    }

    /**
     * 新增数据
     *
     * @param guideDTO 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报防御指南管理", operateType = OperateType.INSERT)
    public AlarmDefenseGuideDTO insert(@Validated(ValidatedGroup.Save.class) @RequestBody AlarmDefenseGuideDTO guideDTO) {

        return  this.alarmDefenseGuideService.saveInfo(guideDTO);
    }

    /**
     * 修改数据
     *
     * @param guideDTO 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报防御指南管理", operateType = OperateType.UPDATE)
    public void update(@Validated(ValidatedGroup.Update.class) @RequestBody AlarmDefenseGuideDTO guideDTO) {
        this.alarmDefenseGuideService.updateInfo(guideDTO);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报防御指南管理", operateType = OperateType.DELETE)
    public void delete(@PathVariable Long id) {
        this.alarmDefenseGuideService.deleteInfo(id);
    }

    /**
     * 修改防御指南状态
     */
    @PostMapping("/setOpen/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报防御指南管理", operateType = OperateType.UPDATE)
    public void setOpen(@PathVariable Long id){
        alarmDefenseGuideService.setOpen(id);
    }

    /**
     * 根据类型查询
     * @param type 警报类型 1 海浪 2风暴潮
     * @return
     */
    @GetMapping("/selectByType/{type}")
    public AlarmDefenseGuideDTO selectByType(@PathVariable Integer type){
        return alarmDefenseGuideService.selectByType(type);
    }
}

