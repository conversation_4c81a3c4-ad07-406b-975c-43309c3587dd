package cn.piesat.data.making.server.service.impl;

import cn.piesat.common.utils.SpringUtils;
import cn.piesat.data.making.server.dao.FmPushResultDao;
import cn.piesat.data.making.server.entity.FmPushResult;
import cn.piesat.data.making.server.service.FmPushResultService;
import cn.piesat.data.making.server.service.FmSenderService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

@Service
public class FmPushResultServiceImpl extends ServiceImpl<FmPushResultDao, FmPushResult> implements FmPushResultService {

    @Autowired
    private FmPushResultDao fmPushResultDaoImpl;

    @Override
    public void addFmPushResList(String pushChannel, String bizCode, String pushMsg, String pushContent,String fileName,String filePath) {
        List<Map<String, Object>> resList = fmPushResultDaoImpl.queryListMapByParam(bizCode);

        FmPushResult fmPushResult = null;

        List<FmPushResult> list = new ArrayList<>();

        for(Map<String,Object> res : resList){
            fmPushResult = new FmPushResult();

            fmPushResult.setBizCode(res.get("biz_code").toString());
            fmPushResult.setBizId(Long.valueOf(res.get("biz_id").toString()));
            fmPushResult.setBizName(res.get("biz_name").toString());
            fmPushResult.setRecId(res.get("rec_id")!=null?Long.valueOf(res.get("rec_id").toString()):null);
            fmPushResult.setRecName(res.get("rec_name").toString());

            fmPushResult.setPushChannel(res.get("push_channel").toString());
            fmPushResult.setPushMsg(pushMsg);
            fmPushResult.setPushContent(pushContent);

            if(fmPushResult.getPushChannel().equals("email")){
                fmPushResult.setPushChannelId(res.get("email").toString());
            }else if(fmPushResult.getPushChannel().equals("mobile")){
                fmPushResult.setPushChannelId(res.get("mobile").toString());
            }else if(fmPushResult.getPushChannel().equals("fax")){
                fmPushResult.setPushChannelId(res.get("fax_no").toString());
                fmPushResult.setPushMsg(fileName);
                fmPushResult.setPushContent(filePath);
            }


            fmPushResult.setPushTime(Calendar.getInstance().getTime());
            fmPushResult.setPushFlag("0");

            list.add(fmPushResult);
        }
        this.saveBatch(list);
    }

    public void updateFmPushResult(FmPushResult fmPushResult){
        this.updateById(fmPushResult);
    }

    @Override
    public void syncPushData() {
        LambdaQueryWrapper<FmPushResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(FmPushResult::getPushFlag, "1");

        List<FmPushResult> resList = this.list(queryWrapper);

        if(CollectionUtils.isNotEmpty(resList)){
            for(FmPushResult fmPushResult : resList){
                String pushChannelStr = fmPushResult.getPushChannel();

                String beanName = "fmSender"+pushChannelStr.substring(0,1).toUpperCase()+pushChannelStr.substring(1)+"ServiceImpl";
                FmSenderService fmSenderServiceImpl = (FmSenderService) SpringUtils.getBean(beanName);
                fmSenderServiceImpl.sender(fmPushResult);
            }
        }
    }
}
