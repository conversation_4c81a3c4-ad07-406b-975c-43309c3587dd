import {
  MaybeRefOrGetter,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  toRaw,
  toValue,
  watch
} from 'vue'
import OlMap from 'ol/Map'
import OlGraticule from 'ol/layer/Graticule'
import { Stroke } from 'ol/style'
import { Extent } from 'ol/extent'

export function useGraticule(opt: {
  map: MaybeRefOrGetter<OlMap>
  extent: MaybeRefOrGetter<number[]>
}) {
  const extentValue = toValue(opt.extent)
  const graticule = getGraticule(extentValue)
  const showGraticule = ref(false)

  // onMounted(() => {
  //   addGraticule()
  // })

  onUnmounted(() => {
    removeGraticule()
  })

  watch(opt.extent, val => {
    const value = toRaw(toValue(val))
    graticule.setExtent(value)
  })

  function addGraticule() {
    const map = toRaw(toValue(opt.map))
    map.addLayer(graticule)
  }

  function removeGraticule() {
    const map = toRaw(toValue(opt.map))
    map.removeLayer(graticule)
  }

  function toggleGraticule() {
    if (showGraticule.value) {
      removeGraticule()
    } else {
      addGraticule()
    }
    showGraticule.value = !showGraticule.value
  }

  return {
    showGraticule,
    toggleGraticule
  }
}

function getGraticule(extent: Extent | undefined) {
  return new OlGraticule({
    maxLines: 200, // 设置最大线条数量
    strokeStyle: new Stroke({
      lineDash: [5], // 设置虚线间隔
      width: 1.5, // 设置线段宽度
      color: 'blue' // 设置线段颜色
    }),
    extent,
    showLabels: true,
    wrapX: false
  })
}
