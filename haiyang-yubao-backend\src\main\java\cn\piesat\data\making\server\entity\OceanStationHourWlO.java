package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站整点-海浪特征值-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_hour_wl_o")
public class OceanStationHourWlO implements Serializable {

    private static final long serialVersionUID = 805639277651561714L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 监测日期
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 小时
     **/
    @JsonProperty("Hour")
    @TableField("hour")
    private String hour;
    /**
     * 小时质控符
     **/
    @JsonProperty("Hour_Qc2")
    @TableField("hour_qc2")
    private Integer hourQc2;
    /**
     * 00分测值
     **/
    @JsonProperty("measured_value_00")
    @TableField("measured_value_00")
    private String measuredValue00;
    /**
     * 00分测值质控符
     **/
    @JsonProperty("measured_value_00_Qc2")
    @TableField("measured_value_00_qc2")
    private Integer measuredValue00Qc2;
    /**
     * 01分测值
     **/
    @JsonProperty("measured_value_01")
    @TableField("measured_value_01")
    private String measuredValue01;
    /**
     * 01分测值质控符
     **/
    @JsonProperty("measured_value_01_Qc2")
    @TableField("measured_value_01_qc2")
    private Integer measuredValue01Qc2;
    /**
     * 02分测值
     **/
    @JsonProperty("measured_value_02")
    @TableField("measured_value_02")
    private String measuredValue02;
    /**
     * 02分测值质控符
     **/
    @JsonProperty("measured_value_02_Qc2")
    @TableField("measured_value_02_qc2")
    private Integer measuredValue02Qc2;
    /**
     * 03分测值
     **/
    @JsonProperty("measured_value_03")
    @TableField("measured_value_03")
    private String measuredValue03;
    /**
     * 03分测值质控符
     **/
    @JsonProperty("measured_value_03_Qc2")
    @TableField("measured_value_03_qc2")
    private Integer measuredValue03Qc2;
    /**
     * 04分测值
     **/
    @JsonProperty("measured_value_04")
    @TableField("measured_value_04")
    private String measuredValue04;
    /**
     * 04分测值质控符
     **/
    @JsonProperty("measured_value_04_Qc2")
    @TableField("measured_value_04_qc2")
    private Integer measuredValue04Qc2;
    /**
     * 05分测值
     **/
    @JsonProperty("measured_value_05")
    @TableField("measured_value_05")
    private String measuredValue05;
    /**
     * 05分测值质控符
     **/
    @JsonProperty("measured_value_05_Qc2")
    @TableField("measured_value_05_qc2")
    private Integer measuredValue05Qc2;
    /**
     * 06分测值
     **/
    @JsonProperty("measured_value_06")
    @TableField("measured_value_06")
    private String measuredValue06;
    /**
     * 06分测值质控符
     **/
    @JsonProperty("measured_value_06_Qc2")
    @TableField("measured_value_06_qc2")
    private Integer measuredValue06Qc2;
    /**
     * 07分测值
     **/
    @JsonProperty("measured_value_07")
    @TableField("measured_value_07")
    private String measuredValue07;
    /**
     * 07分测值质控符
     **/
    @JsonProperty("measured_value_07_Qc2")
    @TableField("measured_value_07_qc2")
    private Integer measuredValue07Qc2;
    /**
     * 08分测值
     **/
    @JsonProperty("measured_value_08")
    @TableField("measured_value_08")
    private String measuredValue08;
    /**
     * 08分测值质控符
     **/
    @JsonProperty("measured_value_08_Qc2")
    @TableField("measured_value_08_qc2")
    private Integer measuredValue08Qc2;
    /**
     * 09分测值
     **/
    @JsonProperty("measured_value_09")
    @TableField("measured_value_09")
    private String measuredValue09;
    /**
     * 09分测值质控符
     **/
    @JsonProperty("measured_value_09_Qc2")
    @TableField("measured_value_09_qc2")
    private Integer measuredValue09Qc2;
    /**
     * 10分测值
     **/
    @JsonProperty("measured_value_10")
    @TableField("measured_value_10")
    private String measuredValue10;
    /**
     * 10分测值质控符
     **/
    @JsonProperty("measured_value_10_Qc2")
    @TableField("measured_value_10_qc2")
    private Integer measuredValue10Qc2;
    /**
     * 11分测值
     **/
    @JsonProperty("measured_value_11")
    @TableField("measured_value_11")
    private String measuredValue11;
    /**
     * 11分测值质控符
     **/
    @JsonProperty("measured_value_11_Qc2")
    @TableField("measured_value_11_qc2")
    private Integer measuredValue11Qc2;
    /**
     * 12分测值
     **/
    @JsonProperty("measured_value_12")
    @TableField("measured_value_12")
    private String measuredValue12;
    /**
     * 12分测值质控符
     **/
    @JsonProperty("measured_value_12_Qc2")
    @TableField("measured_value_12_qc2")
    private Integer measuredValue12Qc2;
    /**
     * 13分测值
     **/
    @JsonProperty("measured_value_13")
    @TableField("measured_value_13")
    private String measuredValue13;
    /**
     * 13分测值质控符
     **/
    @JsonProperty("measured_value_13_Qc2")
    @TableField("measured_value_13_qc2")
    private Integer measuredValue13Qc2;
    /**
     * 14分测值
     **/
    @JsonProperty("measured_value_14")
    @TableField("measured_value_14")
    private String measuredValue14;
    /**
     * 14分测值质控符
     **/
    @JsonProperty("measured_value_14_Qc2")
    @TableField("measured_value_14_qc2")
    private Integer measuredValue14Qc2;
    /**
     * 15分测值
     **/
    @JsonProperty("measured_value_15")
    @TableField("measured_value_15")
    private String measuredValue15;
    /**
     * 15分测值质控符
     **/
    @JsonProperty("measured_value_15_Qc2")
    @TableField("measured_value_15_qc2")
    private Integer measuredValue15Qc2;
    /**
     * 16分测值
     **/
    @JsonProperty("measured_value_16")
    @TableField("measured_value_16")
    private String measuredValue16;
    /**
     * 16分测值质控符
     **/
    @JsonProperty("measured_value_16_Qc2")
    @TableField("measured_value_16_qc2")
    private Integer measuredValue16Qc2;
    /**
     * 17分测值
     **/
    @JsonProperty("measured_value_17")
    @TableField("measured_value_17")
    private String measuredValue17;
    /**
     * 17分测值质控符
     **/
    @JsonProperty("measured_value_17_Qc2")
    @TableField("measured_value_17_qc2")
    private Integer measuredValue17Qc2;
    /**
     * 18分测值
     **/
    @JsonProperty("measured_value_18")
    @TableField("measured_value_18")
    private String measuredValue18;
    /**
     * 18分测值质控符
     **/
    @JsonProperty("measured_value_18_Qc2")
    @TableField("measured_value_18_qc2")
    private Integer measuredValue18Qc2;
    /**
     * 19分测值
     **/
    @JsonProperty("measured_value_19")
    @TableField("measured_value_19")
    private String measuredValue19;
    /**
     * 19分测值质控符
     **/
    @JsonProperty("measured_value_19_Qc2")
    @TableField("measured_value_19_qc2")
    private Integer measuredValue19Qc2;
    /**
     * 20分测值
     **/
    @JsonProperty("measured_value_20")
    @TableField("measured_value_20")
    private String measuredValue20;
    /**
     * 20分测值质控符
     **/
    @JsonProperty("measured_value_20_Qc2")
    @TableField("measured_value_20_qc2")
    private Integer measuredValue20Qc2;
    /**
     * 21分测值
     **/
    @JsonProperty("measured_value_21")
    @TableField("measured_value_21")
    private String measuredValue21;
    /**
     * 21分测值质控符
     **/
    @JsonProperty("measured_value_21_Qc2")
    @TableField("measured_value_21_qc2")
    private Integer measuredValue21Qc2;
    /**
     * 22分测值
     **/
    @JsonProperty("measured_value_22")
    @TableField("measured_value_22")
    private String measuredValue22;
    /**
     * 22分测值质控符
     **/
    @JsonProperty("measured_value_22_Qc2")
    @TableField("measured_value_22_qc2")
    private Integer measuredValue22Qc2;
    /**
     * 23分测值
     **/
    @JsonProperty("measured_value_23")
    @TableField("measured_value_23")
    private String measuredValue23;
    /**
     * 23分测值质控符
     **/
    @JsonProperty("measured_value_23_Qc2")
    @TableField("measured_value_23_qc2")
    private Integer measuredValue23Qc2;
    /**
     * 24分测值
     **/
    @JsonProperty("measured_value_24")
    @TableField("measured_value_24")
    private String measuredValue24;
    /**
     * 24分测值质控符
     **/
    @JsonProperty("measured_value_24_Qc2")
    @TableField("measured_value_24_qc2")
    private Integer measuredValue24Qc2;
    /**
     * 25分测值
     **/
    @JsonProperty("measured_value_25")
    @TableField("measured_value_25")
    private String measuredValue25;
    /**
     * 25分测值质控符
     **/
    @JsonProperty("measured_value_25_Qc2")
    @TableField("measured_value_25_qc2")
    private Integer measuredValue25Qc2;
    /**
     * 26分测值
     **/
    @JsonProperty("measured_value_26")
    @TableField("measured_value_26")
    private String measuredValue26;
    /**
     * 26分测值质控符
     **/
    @JsonProperty("measured_value_26_Qc2")
    @TableField("measured_value_26_qc2")
    private Integer measuredValue26Qc2;
    /**
     * 27分测值
     **/
    @JsonProperty("measured_value_27")
    @TableField("measured_value_27")
    private String measuredValue27;
    /**
     * 27分测值质控符
     **/
    @JsonProperty("measured_value_27_Qc2")
    @TableField("measured_value_27_qc2")
    private Integer measuredValue27Qc2;
    /**
     * 28分测值
     **/
    @JsonProperty("measured_value_28")
    @TableField("measured_value_28")
    private String measuredValue28;
    /**
     * 28分测值质控符
     **/
    @JsonProperty("measured_value_28_Qc2")
    @TableField("measured_value_28_qc2")
    private Integer measuredValue28Qc2;
    /**
     * 29分测值
     **/
    @JsonProperty("measured_value_29")
    @TableField("measured_value_29")
    private String measuredValue29;
    /**
     * 29分测值质控符
     **/
    @JsonProperty("measured_value_29_Qc2")
    @TableField("measured_value_29_qc2")
    private Integer measuredValue29Qc2;
    /**
     * 30分测值
     **/
    @JsonProperty("measured_value_30")
    @TableField("measured_value_30")
    private String measuredValue30;
    /**
     * 30分测值质控符
     **/
    @JsonProperty("measured_value_30_Qc2")
    @TableField("measured_value_30_qc2")
    private Integer measuredValue30Qc2;
    /**
     * 31分测值
     **/
    @JsonProperty("measured_value_31")
    @TableField("measured_value_31")
    private String measuredValue31;
    /**
     * 31分测值质控符
     **/
    @JsonProperty("measured_value_31_Qc2")
    @TableField("measured_value_31_qc2")
    private Integer measuredValue31Qc2;
    /**
     * 32分测值
     **/
    @JsonProperty("measured_value_32")
    @TableField("measured_value_32")
    private String measuredValue32;
    /**
     * 32分测值质控符
     **/
    @JsonProperty("measured_value_32_Qc2")
    @TableField("measured_value_32_qc2")
    private Integer measuredValue32Qc2;
    /**
     * 33分测值
     **/
    @JsonProperty("measured_value_33")
    @TableField("measured_value_33")
    private String measuredValue33;
    /**
     * 33分测值质控符
     **/
    @JsonProperty("measured_value_33_Qc2")
    @TableField("measured_value_33_qc2")
    private Integer measuredValue33Qc2;
    /**
     * 34分测值
     **/
    @JsonProperty("measured_value_34")
    @TableField("measured_value_34")
    private String measuredValue34;
    /**
     * 34分测值质控符
     **/
    @JsonProperty("measured_value_34_Qc2")
    @TableField("measured_value_34_qc2")
    private Integer measuredValue34Qc2;
    /**
     * 35分测值
     **/
    @JsonProperty("measured_value_35")
    @TableField("measured_value_35")
    private String measuredValue35;
    /**
     * 35分测值质控符
     **/
    @JsonProperty("measured_value_35_Qc2")
    @TableField("measured_value_35_qc2")
    private Integer measuredValue35Qc2;
    /**
     * 36分测值
     **/
    @JsonProperty("measured_value_36")
    @TableField("measured_value_36")
    private String measuredValue36;
    /**
     * 36分测值质控符
     **/
    @JsonProperty("measured_value_36_Qc2")
    @TableField("measured_value_36_qc2")
    private Integer measuredValue36Qc2;
    /**
     * 37分测值
     **/
    @JsonProperty("measured_value_37")
    @TableField("measured_value_37")
    private String measuredValue37;
    /**
     * 37分测值质控符
     **/
    @JsonProperty("measured_value_37_Qc2")
    @TableField("measured_value_37_qc2")
    private Integer measuredValue37Qc2;
    /**
     * 38分测值
     **/
    @JsonProperty("measured_value_38")
    @TableField("measured_value_38")
    private String measuredValue38;
    /**
     * 38分测值质控符
     **/
    @JsonProperty("measured_value_38_Qc2")
    @TableField("measured_value_38_qc2")
    private Integer measuredValue38Qc2;
    /**
     * 39分测值
     **/
    @JsonProperty("measured_value_39")
    @TableField("measured_value_39")
    private String measuredValue39;
    /**
     * 39分测值质控符
     **/
    @JsonProperty("measured_value_39_Qc2")
    @TableField("measured_value_39_qc2")
    private Integer measuredValue39Qc2;
    /**
     * 40分测值
     **/
    @JsonProperty("measured_value_40")
    @TableField("measured_value_40")
    private String measuredValue40;
    /**
     * 40分测值质控符
     **/
    @JsonProperty("measured_value_40_Qc2")
    @TableField("measured_value_40_qc2")
    private Integer measuredValue40Qc2;
    /**
     * 41分测值
     **/
    @JsonProperty("measured_value_41")
    @TableField("measured_value_41")
    private String measuredValue41;
    /**
     * 41分测值质控符
     **/
    @JsonProperty("measured_value_41_Qc2")
    @TableField("measured_value_41_qc2")
    private Integer measuredValue41Qc2;
    /**
     * 42分测值
     **/
    @JsonProperty("measured_value_42")
    @TableField("measured_value_42")
    private String measuredValue42;
    /**
     * 42分测值质控符
     **/
    @JsonProperty("measured_value_42_Qc2")
    @TableField("measured_value_42_qc2")
    private Integer measuredValue42Qc2;
    /**
     * 43分测值
     **/
    @JsonProperty("measured_value_43")
    @TableField("measured_value_43")
    private String measuredValue43;
    /**
     * 43分测值质控符
     **/
    @JsonProperty("measured_value_43_Qc2")
    @TableField("measured_value_43_qc2")
    private Integer measuredValue43Qc2;
    /**
     * 44分测值
     **/
    @JsonProperty("measured_value_44")
    @TableField("measured_value_44")
    private String measuredValue44;
    /**
     * 44分测值质控符
     **/
    @JsonProperty("measured_value_44_Qc2")
    @TableField("measured_value_44_qc2")
    private Integer measuredValue44Qc2;
    /**
     * 415分测值
     **/
    @JsonProperty("measured_value_45")
    @TableField("measured_value_45")
    private String measuredValue45;
    /**
     * 45分测值质控符
     **/
    @JsonProperty("measured_value_45_Qc2")
    @TableField("measured_value_45_qc2")
    private Integer measuredValue45Qc2;
    /**
     * 46分测值
     **/
    @JsonProperty("measured_value_46")
    @TableField("measured_value_46")
    private String measuredValue46;
    /**
     * 46分测值质控符
     **/
    @JsonProperty("measured_value_46_Qc2")
    @TableField("measured_value_46_qc2")
    private Integer measuredValue46Qc2;
    /**
     * 47分测值
     **/
    @JsonProperty("measured_value_47")
    @TableField("measured_value_47")
    private String measuredValue47;
    /**
     * 47分测值质控符
     **/
    @JsonProperty("measured_value_47_Qc2")
    @TableField("measured_value_47_qc2")
    private Integer measuredValue47Qc2;
    /**
     * 48分测值
     **/
    @JsonProperty("measured_value_48")
    @TableField("measured_value_48")
    private String measuredValue48;
    /**
     * 48分测值质控符
     **/
    @JsonProperty("measured_value_48_Qc2")
    @TableField("measured_value_48_qc2")
    private Integer measuredValue48Qc2;
    /**
     * 49分测值
     **/
    @JsonProperty("measured_value_49")
    @TableField("measured_value_49")
    private String measuredValue49;
    /**
     * 49分测值质控符
     **/
    @JsonProperty("measured_value_49_Qc2")
    @TableField("measured_value_49_qc2")
    private Integer measuredValue49Qc2;
    /**
     * 50分测值
     **/
    @JsonProperty("measured_value_50")
    @TableField("measured_value_50")
    private String measuredValue50;
    /**
     * 50分测值质控符
     **/
    @JsonProperty("measured_value_50_Qc2")
    @TableField("measured_value_50_qc2")
    private Integer measuredValue50Qc2;
    /**
     * 51分测值
     **/
    @JsonProperty("measured_value_51")
    @TableField("measured_value_51")
    private String measuredValue51;
    /**
     * 51分测值质控符
     **/
    @JsonProperty("measured_value_51_Qc2")
    @TableField("measured_value_51_qc2")
    private Integer measuredValue51Qc2;
    /**
     * 52分测值
     **/
    @JsonProperty("measured_value_52")
    @TableField("measured_value_52")
    private String measuredValue52;
    /**
     * 52分测值质控符
     **/
    @JsonProperty("measured_value_52_Qc2")
    @TableField("measured_value_52_qc2")
    private Integer measuredValue52Qc2;
    /**
     * 53分测值
     **/
    @JsonProperty("measured_value_53")
    @TableField("measured_value_53")
    private String measuredValue53;
    /**
     * 53分测值质控符
     **/
    @JsonProperty("measured_value_53_Qc2")
    @TableField("measured_value_53_qc2")
    private Integer measuredValue53Qc2;
    /**
     * 54分测值
     **/
    @JsonProperty("measured_value_54")
    @TableField("measured_value_54")
    private String measuredValue54;
    /**
     * 54分测值质控符
     **/
    @JsonProperty("measured_value_54_Qc2")
    @TableField("measured_value_54_qc2")
    private Integer measuredValue54Qc2;
    /**
     * 55分测值
     **/
    @JsonProperty("measured_value_55")
    @TableField("measured_value_55")
    private String measuredValue55;
    /**
     * 55分测值质控符
     **/
    @JsonProperty("measured_value_55_Qc2")
    @TableField("measured_value_55_qc2")
    private Integer measuredValue55Qc2;
    /**
     * 56分测值
     **/
    @JsonProperty("measured_value_56")
    @TableField("measured_value_56")
    private String measuredValue56;
    /**
     * 56分测值质控符
     **/
    @JsonProperty("measured_value_56_Qc2")
    @TableField("measured_value_56_qc2")
    private Integer measuredValue56Qc2;
    /**
     * 57分测值
     **/
    @JsonProperty("measured_value_57")
    @TableField("measured_value_57")
    private String measuredValue57;
    /**
     * 57分测值质控符
     **/
    @JsonProperty("measured_value_57_Qc2")
    @TableField("measured_value_57_qc2")
    private Integer measuredValue57Qc2;
    /**
     * 58分测值
     **/
    @JsonProperty("measured_value_58")
    @TableField("measured_value_58")
    private String measuredValue58;
    /**
     * 58分测值质控符
     **/
    @JsonProperty("measured_value_58_Qc2")
    @TableField("measured_value_58_qc2")
    private Integer measuredValue58Qc2;
    /**
     * 59分测值
     **/
    @JsonProperty("measured_value_59")
    @TableField("measured_value_59")
    private String measuredValue59;
    /**
     * 59分测值质控符
     **/
    @JsonProperty("measured_value_59_Qc2")
    @TableField("measured_value_59_qc2")
    private Integer measuredValue59Qc2;
    /**
     * 高/低潮潮高
     **/
    @JsonProperty("day_max_min_tide_high_1")
    @TableField("day_max_min_tide_high_1")
    private String dayMaxMinTideHigh1;
    /**
     * 高/低潮潮高质控符
     **/
    @JsonProperty("day_max_min_tide_high_1_Qc2")
    @TableField("day_max_min_tide_high_1_qc2")
    private Integer dayMaxMinTideHigh1Qc2;
    /**
     * 高/低潮潮时
     **/
    @JsonProperty("day_max_min_tide_time_1")
    @TableField("day_max_min_tide_time_1")
    private String dayMaxMinTideTime1;
    /**
     * 高/低潮潮时质控符
     **/
    @JsonProperty("day_max_min_tide_time_1_Qc2")
    @TableField("day_max_min_tide_time_1_qc2")
    private Integer dayMaxMinTideTime1Qc2;
    /**
     * 高/低潮潮高
     **/
    @JsonProperty("day_max_min_tide_high_2")
    @TableField("day_max_min_tide_high_2")
    private String dayMaxMinTideHigh2;
    /**
     * 高/低潮潮高质控符
     **/
    @JsonProperty("day_max_min_tide_high_2_Qc2")
    @TableField("day_max_min_tide_high_2_qc2")
    private Integer dayMaxMinTideHigh2Qc2;
    /**
     * 高/低潮潮时
     **/
    @JsonProperty("day_max_min_tide_time_2")
    @TableField("day_max_min_tide_time_2")
    private String dayMaxMinTideTime2;
    /**
     * 高/低潮潮时质控符
     **/
    @JsonProperty("day_max_min_tide_time_2_Qc2")
    @TableField("day_max_min_tide_time_2_qc2")
    private Integer dayMaxMinTideTime2Qc2;
    /**
     * 高/低潮潮高
     **/
    @JsonProperty("day_max_min_tide_high_3")
    @TableField("day_max_min_tide_high_3")
    private String dayMaxMinTideHigh3;
    /**
     * 高/低潮潮高质控符
     **/
    @JsonProperty("day_max_min_tide_high_3_Qc2")
    @TableField("day_max_min_tide_high_3_qc2")
    private Integer dayMaxMinTideHigh3Qc2;
    /**
     * 高/低潮潮时
     **/
    @JsonProperty("day_max_min_tide_time_3")
    @TableField("day_max_min_tide_time_3")
    private String dayMaxMinTideTime3;
    /**
     * 高/低潮潮时质控符
     **/
    @JsonProperty("day_max_min_tide_time_3_Qc2")
    @TableField("day_max_min_tide_time_3_qc2")
    private Integer dayMaxMinTideTime3Qc2;
    /**
     * 高/低潮潮高
     **/
    @JsonProperty("day_max_min_tide_high_4")
    @TableField("day_max_min_tide_high_4")
    private String dayMaxMinTideHigh4;
    /**
     * 高/低潮潮高质控符
     **/
    @JsonProperty("day_max_min_tide_high_4_Qc2")
    @TableField("day_max_min_tide_high_4_qc2")
    private Integer dayMaxMinTideHigh4Qc2;
    /**
     * 高/低潮潮时
     **/
    @JsonProperty("day_max_min_tide_time_4")
    @TableField("day_max_min_tide_time_4")
    private String dayMaxMinTideTime4;
    /**
     * 高/低潮潮时质控符
     **/
    @JsonProperty("day_max_min_tide_time_4_Qc2")
    @TableField("day_max_min_tide_time_4_qc2")
    private Integer dayMaxMinTideTime4Qc2;
    /**
     * 高/低潮潮高
     **/
    @JsonProperty("day_max_min_tide_high_5")
    @TableField("day_max_min_tide_high_5")
    private String dayMaxMinTideHigh5;
    /**
     * 高/低潮潮高质控符
     **/
    @JsonProperty("day_max_min_tide_high_5_Qc2")
    @TableField("day_max_min_tide_high_5_qc2")
    private Integer dayMaxMinTideHigh5Qc2;
    /**
     * 高/低潮潮时
     **/
    @JsonProperty("day_max_min_tide_time_5")
    @TableField("day_max_min_tide_time_5")
    private String dayMaxMinTideTime5;
    /**
     * 高/低潮潮时质控符
     **/
    @JsonProperty("day_max_min_tide_time_5_Qc2")
    @TableField("day_max_min_tide_time_5_qc2")
    private Integer dayMaxMinTideTime5Qc2;
    /**
     * 高/低潮潮高
     **/
    @JsonProperty("day_max_min_tide_high_6")
    @TableField("day_max_min_tide_high_6")
    private String dayMaxMinTideHigh6;
    /**
     * 高/低潮潮高质控符
     **/
    @JsonProperty("day_max_min_tide_high_6_Qc2")
    @TableField("day_max_min_tide_high_6_qc2")
    private Integer dayMaxMinTideHigh6Qc2;
    /**
     * 高/低潮潮时
     **/
    @JsonProperty("day_max_min_tide_time_6")
    @TableField("day_max_min_tide_time_6")
    private String dayMaxMinTideTime6;
    /**
     * 高/低潮潮时质控符
     **/
    @JsonProperty("day_max_min_tide_time_6_Qc2")
    @TableField("day_max_min_tide_time_6_qc2")
    private Integer dayMaxMinTideTime6Qc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
}



