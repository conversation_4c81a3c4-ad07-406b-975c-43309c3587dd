package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.FmTyphoonCompletionBDTO;
import cn.piesat.data.making.server.entity.FmTyphoonCompletionB;
import cn.piesat.data.making.server.vo.FmTyphoonCompletionBVO;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 台风位置预警完成数据服务接口
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
public interface FmTyphoonCompletionBService extends IService<FmTyphoonCompletionB>{

        /**
         * 根据参数查询分页
         */
        PageResult<FmTyphoonCompletionBVO> getPage(FmTyphoonCompletionBDTO dto,PageParam pageParam);

        /**
         * 根据参数查询列表
         */
        List<FmTyphoonCompletionBVO> getList(FmTyphoonCompletionBDTO dto);

        /**
         * 根据id查询数据
         */
        FmTyphoonCompletionBVO getById(Long id);

        /**
         * 保存数据
         */
        void save(FmTyphoonCompletionBDTO dto);

        /**
         * 批量保存数据
         */
        void saveList(List<FmTyphoonCompletionBDTO> dtoList);

        /**
         * 根据id删除数据
         */
        void deleteById(Long id);

        /**
         * 根据idList批量删除数据
         */
        void deleteByIdList(List<Long> idList);

        FmTyphoonCompletionB getInfo(String typhoonNo, Date typhoonTime);
}
