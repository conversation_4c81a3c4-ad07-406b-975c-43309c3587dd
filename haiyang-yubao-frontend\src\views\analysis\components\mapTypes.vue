<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-02-13 10:24:28
 * @LastEditors: x<PERSON><PERSON><PERSON> xuli<PERSON><EMAIL>
 * @LastEditTime: 2025-04-29 11:02:21
 * @FilePath: \hainan-jianzai-web\src\views\analysis\components\mapTypes.vue
 * @Description: 预报分析右侧按钮及交互事件
-->
<template>
  <div class="map-types-list">
    <WindLayers
      ref="windComp"
      :map="map"
      name="预报分析"
      :key="windKey"
      v-if="currentType === 'oceanWave' && currentDataSource === 'nmefc'"
    ></WindLayers>
    <div
      v-for="item in mapTypeList.filter(i => i.name === '台风')"
      :key="item.value"
      class="map-types-item"
      :class="{ active: isTyphoonActive }"
      @click="isTyphoonActive = !isTyphoonActive"
    >
      <div class="map-type-item__label">{{ item.name }}</div>
      <div class="map-type-item__icon" :class="{ active: isTyphoonActive }">
        <img :src="isTyphoonActive ? item.activeIcon : item.icon" alt="" />
      </div>
    </div>
    <div
      v-for="item in mapTypeList.filter(i => i.name !== '台风')"
      :key="item.value"
      class="map-types-item"
      :class="{ active: currentType === item.value }"
      @click="onChangeType(item)"
    >
      <div class="map-type-item__label">{{ item.name }}</div>
      <div
        class="map-type-item__icon"
        :class="{ active: currentType === item.value }"
      >
        <img
          :src="currentType === item.value ? item.activeIcon : item.icon"
          alt=""
        />
      </div>
    </div>
  </div>
  <div
    v-if="currentModeTypeList.length && currentDataSource !== 'grid'"
    class="mode-type d-flex"
  >
    <div
      v-for="(item, index) in currentModeTypeList"
      :key="index"
      class="mode-type-item"
      :class="currentModeType == item.value ? 'active' : ''"
      @click="onChangeModeType(item)"
    >
      {{ item.name }}
    </div>
    <div v-if="currentType === 'oceanWave'" class="mode-type-item">
      <n-radio-group
        v-model:value="waveDirectionManifestation"
        @update:value="onWaveDirectionManifestationChanged"
      >
        <n-radio :value="WaveDirectionManifestation.ARROW">箭头</n-radio>
        <n-radio :value="WaveDirectionManifestation.PARTICLE_FLOW"
          >粒子流</n-radio
        >
      </n-radio-group>
    </div>
  </div>
  <div
    v-if="['seaWind', 'oceanCurrents'].includes(currentType)"
    class="particle-switch"
  >
    <n-switch
      v-model:value="particleSwitch"
      size="small"
      @update:value="onChangeParticle"
    />
    <div class="switch-label">粒子动画</div>
  </div>
  <div
    v-if="['seaWind'].includes(currentType)"
    style="right: 110px"
    class="particle-switch"
  >
    <n-switch v-model:value="showIsoline" size="small" />
    <div class="switch-label">气压等值线</div>
  </div>
  <Typhoon v-if="isTyphoonActive" ref="typhoonRef" :visible="isTyphoonActive" />

  <DataSource
    v-if="currentType && currentType !== 'stormSurge'"
    :value="currentDataSource"
    @change="changeDataSoure"
  />
  <StormSurgeProvincial
    :flag="stormSurgeFlag"
    :info="stormSurgeInfo"
    @update:flag="stormSurgeFlag = false"
  />
  <Legend
    v-if="showLegend"
    :url="legendUrl"
    :min="colorRampMin"
    :max="colorRampMax"
    :type="currentType"
    :mode-type="currentModeType"
    :is-typhoon-active="isTyphoonActive"
  />
  <QxTimeLine
    v-if="showTimeLine"
    ref="analysisTimeLine"
    class="analysis-time-line"
    @time-click="timeClick"
  />
  <div v-show="showInitTime" class="initiation-time">
    <label for="">起报时间:</label>
    <n-select
      v-model:value="forecastTime"
      :options="forecastTimeList"
      label-field="data_time"
      value-field="data_time"
      @update:value="onChangeForecastTime"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  inject,
  onMounted,
  nextTick,
  onBeforeUnmount,
  reactive,
  toRaw
} from 'vue'
import {
  DataSource,
  StormSurgeProvincial,
  mapTypeList,
  Legend,
  Typhoon,
  ITiffItem
} from './index'
import { QxTimeLine } from 'src/components/QxTimeLine'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import Point from 'ol/geom/Point.js'
import { Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import Feature from 'ol/Feature.js'
import { unByKey } from 'ol/Observable'
import GeoTIFFSource from 'ol/source/GeoTIFF.js'
import gisUtils from 'src/utils/gis'
import TileLayer from 'ol/layer/WebGLTile.js'
import { GeoJSON } from 'ol/format'
import Rainbow from 'src/utils/rainbowvis.js'
import { jet } from '../ts/common'
import eventBus from 'src/utils/eventBus'
import pointImg from 'src/assets/images/points/tide.png'
import Popup from 'src/utils/olPopup/ol-popup.js'
import 'src/utils/olPopup/ol-popup.css'
import Analysis from 'src/requests/analysis'
import moment from 'moment'
import { useMessage } from 'naive-ui'
import ProductApi from 'src/requests/product'
import WindLayers from 'src/components/Wind/Wind.vue'
import { useIsoline } from 'src/views/analysis/components/mapTypes-hooks/useIsoline'
import { WaveDirectionManifestation } from 'src/views/analysis/components/mapTypes-hooks/types'

const message = useMessage()
const map = inject<any>('map')
const emit = defineEmits(['changeParticle', 'currentTypeChanged'])
let showInitTime = ref(true) // 是否显示起报时间
let currentType = ref('seaWind')
const isTyphoonActive = ref(false)
let currentModeTypeList = ref<any[]>(mapTypeList[0].modeType)
let currentModeType = ref('refinement')
let currentDataSource = ref('nmefc')
let legendUrl = ref('')
let showLegend = ref(true)
let particleSwitch = ref(true)
const typhoonRef = ref<any>()
const { showIsoline } = useIsoline({
  map: () => toRaw(map.value),
  getCurrentTime: () => currentTime.value,
  getTimeRange: () => query,
  getCurrentType: () => currentType.value
})

const stationSource = new VectorSource()
const stationLayer = new VectorLayer({
  source: stationSource
})
// 海浪展现形式
const waveDirectionManifestation = ref(WaveDirectionManifestation.ARROW)
stationLayer.setProperties({ name: '预报分析' })
let fillMapLayer: any = null // 填色图层

function removeLayer() {
  let layers = map.value.getAllLayers()
  layers?.forEach((layer: any) => {
    const property = layer.getProperties()
    if (property.name === '预报分析') {
      map.value.removeLayer(layer)
    }
  })
  popup.hide()
  map.value.removeOverlay(popup)
  eventBus.emit('clearFlowField')
}

function onChangeParticle() {
  emit('changeParticle')
}

let forecastTimeList = ref<any[]>([])
let forecastTime = ref('')
let query = reactive({
  startTime: moment().format('YYYY-MM-DD 00:00:00'),
  endTime: moment().format('YYYY-MM-DD 23:59:59')
})

function getForecastTime() {
  let product = getProductId()
  console.log(product, 'product')
  let productId = product
  if (['seaWind'].includes(currentType.value)) {
    productId = product.tiff
  } else if (['seaTemp', 'salinity', 'oceanWave'].includes(currentType.value)) {
    if (currentDataSource.value !== 'grid') {
      productId = product[currentModeType.value]
    }
  } else if (currentType.value === 'oceanCurrents') {
    if (currentDataSource.value !== 'grid') {
      productId = product[currentModeType.value].tiff
    } else {
      productId = product.tiff
    }
  } else if (currentType.value === 'stormSurge') {
    productId = productId[currentModeType.value]
  }
  const params = {
    orderBy: 'data_time',
    sort: 'desc',
    _tz: 'GMT'
  }
  ProductApi.getForecastTime(productId, params)
    .then((res: any) => {
      if (res?.length) {
        forecastTimeList.value = res
        forecastTime.value = res[0].data_time
        let time = forecastTime.value.split(' ')[0]
        query.startTime = `${time} 00:00:00`
        query.endTime = `${time} 23:59:59`
        addLayer()
      } else {
        message.error('暂无数据')
      }
    })
    .catch(e => {
      console.error(e, 'eeee')
    })
}

function addLayer() {
  if (
    currentType.value === 'stormSurge' &&
    currentModeType.value === 'county'
  ) {
    addFloodplains()
  } else {
    getLayerStyle()
    // showTimeLine.value = false
    // showLegend.value = false
  }
}

function onChangeForecastTime(val: string) {
  removeLayer()
  let time = val.split(' ')[0]
  query.startTime = `${time} 00:00:00`
  query.endTime = `${time} 23:59:59`
  addLayer()
}

function onWaveDirectionManifestationChanged() {
  addLayer()
}

// 地图类型修改
function onChangeType(item: any) {
  showLegend.value = true
  showTimeLine.value = true
  showInitTime.value = true
  removeLayer()
  if (isTyphoonActive.value) {
    legendUrl.value = ''
  }
  if (currentType.value === item.value) {
    currentType.value = ''
    currentDataSource.value = ''
    showLegend.value = isTyphoonActive.value
    showTimeLine.value = false
  } else {
    currentType.value = item.value
    currentModeTypeList.value = item.modeType
    currentDataSource.value = 'nmefc'
    if (item.modeType?.length) {
      currentModeType.value = item.modeType?.[1].value
      if (currentType.value === 'stormSurge') {
        currentModeType.value = item.modeType?.[0].value
        if (currentModeType.value === 'county') {
          showTimeLine.value = false
          legendUrl.value = ''
        }
      }
    } else {
      currentModeType.value = ''
    }
    getForecastTime()
  }

  eventBus.emit('analysisClick')
  emit('currentTypeChanged', item.value)
}

// 绘制流场
function renderFlowField(url: string, png: string) {
  eventBus.emit('renderFlowField', {
    jsonUrl: url,
    imgUrl: png,
    colorList: colorRamp.value
  })
}

// 模式预报修改
function onChangeModeType(item: any) {
  removeLayer()
  currentModeType.value = item.value
  showTimeLine.value = true
  if (currentType.value === 'stormSurge' && item.value === 'county') {
    showTimeLine.value = false
    legendUrl.value = ''
  }

  getForecastTime()
}

let colorRampMin = ref(0)
let colorRampMax = ref(0)
let deepValue = ref(null)

function addFloodplains() {
  const productId = getProductId()
  const identity = productId[currentModeType.value]
  const params = {
    beginTime: query.startTime,
    endTime: query.endTime,
    _tz: 'GMT',
    orderBy: 'forecast_time',
    sort: 'asc'
  }
  Analysis.getProductGeojson(identity, params)
    .then((res: any) => {
      if (res?.length) {
        res.forEach((item: any) => {
          item.path = `${config.fileService}${item.file_path}`
        })
        renderFloodplains(res[0].path)
      }
    })
    .catch(e => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '获取数据失败')
      console.error(e, '漫滩')
    })
}

function renderFloodplains(filePath: string): void {
  fetch(filePath)
    .then((res: any) => res.json())
    .then((r: any) => {
      deepValue.value = r.area
      let arr = r.features.sort((a: any, b: any) => {
        return (
          a.properties['最大淹没深度(cm)'] + b.properties['最大淹没深度(cm)']
        )
      })
      const features = new GeoJSON().readFeatures(r)
      const rainbow = new Rainbow() // by default, range is 0 to 100
      rainbow.setSpectrum(['4b81f5', '71c45e', 'f9ea4d', 'dd3f25'])
      // rainbow.setSpectrum(jet)
      colorRampMin.value = arr[arr.length - 1].properties['最大淹没深度(cm)']
      colorRampMax.value = arr[0].properties['最大淹没深度(cm)']
      rainbow.setNumberRange(
        arr[arr.length - 1].properties['最大淹没深度(cm)'],
        arr[0].properties['最大淹没深度(cm)']
      )
      features.forEach((item: any) => {
        console.log(item.getProperties()['最大淹没深度(cm)'])
        const color =
          '#' + rainbow.colourAt(item.getProperties()['最大淹没深度(cm)'])
        item.setStyle(
          new Style({
            fill: new Fill({
              color
            }),
            stroke: new Stroke({
              color
            })
          })
        )
        stationSource.addFeature(item)
      })
      map.value.addLayer(stationLayer)
    })
    .catch(e => {
      console.error(e, 'fetch-data-error')
    })
}

const pointSource = new VectorSource()
const pointLayer = new VectorLayer({
  source: pointSource,
  declutter: true, //防止覆盖
  zIndex: 1
})
let stormSurgeFlag = ref(false) //风暴潮地图点 点击面板开关
let stormSurgeInfo = ref<any>(null)

// 渲染风暴潮
function renderStormSurge() {
  Analysis.getStationListofStormSurge({ stationTypeCode: 'oceanStation' })
    .then((res: any) => {
      res.forEach((item: any) => {
        const json = item.locationJson
        let point = JSON.parse(json)
        const geo = point.geometry
        const feature = new Feature({
          geometry: new Point(geo.coordinates)
        })
        const iconStyle = new Style({
          image: new Icon({
            anchor: [0.5, 1],
            src: pointImg,
            scale: [0.6, 0.6]
          }),
          text: new Text({
            text: item.name,
            font: 'bold 14px Calibri,sans-serif',
            fill: new Fill({
              color: 'black'
            }),
            stroke: new Stroke({
              color: 'white',
              width: 2
            }),
            offsetY: 10
          })
        })
        feature.setStyle([iconStyle])
        feature.setProperties(item)
        pointSource.addFeature(feature)
      })
      pointLayer.setProperties({ name: '预报分析' })
      map.value.addLayer(pointLayer)
    })
    .catch(e => {
      console.error(e)
    })
}

let colorRamp = ref<number[]>([])
let ettpTifInfo = ref<any>({})

// 渲染ETTP tiff 图层
async function renderTiff(url: string) {
  try {
    if (fillMapLayer) {
      map.value.removeLayer(fillMapLayer)
    }
    const sourceOptions: any = {
      url: url,
      max: null,
      min: null,
      bands: ['1'],
      nodata: ettpTifInfo.value.invalidValue,
      crossOrigin: 'anonymous'
    }
    const source = new GeoTIFFSource({
      sources: [sourceOptions],
      normalize: false, // 归一化
      interpolate: false, // 插值
      wrapX: false
    })
    fillMapLayer = new TileLayer({
      source: source
    })
    fillMapLayer.setProperties({ name: '预报分析' })
    const styleOptions = gisUtils.getTifStyle(ettpTifInfo.value)
    fillMapLayer.setStyle({
      color: styleOptions
    })
    map.value.addLayer(fillMapLayer)
  } catch (e) {
    console.error(e, 'renderTiff')
  }
}

// 获取产品标识
function getProductId() {
  let result: any = mapTypeList.find(
    (item: any) => item.value === currentType.value
  )
  if (result) {
    return result[currentDataSource.value]
  }
}

let mapDataList = ref<any[]>([])
const waveDirectionDataList = ref<ITiffItem[]>([])
const windComp = ref<InstanceType<typeof WindLayers> | undefined>()
const windKey = ref(0)
let flowFieldData = ref<any[]>([])

// 获取流场数据
async function getFlowFieldData(configGetter?: () => { json: string, png: string }) {
  removeLayer()
  const params = {
    beginTime: query.startTime,
    endTime: query.endTime,
    _tz: 'GMT',
    orderBy: 'forecast_time',
    sort: 'asc'
  }
  let productId = getProductId()
  let identity = { ...productId }

  if (currentType.value === 'oceanCurrents') {
    if (currentDataSource.value !== 'grid') {
      identity = productId[currentModeType.value]
    }
  }
  await Promise.all([
    Analysis.getProductGeojson(configGetter ? configGetter().json : identity.json, params),
    Analysis.getProductGeojson(configGetter ? configGetter().png : identity.png, params)
  ])
    .then((res: any) => {
      let mergedArr = res[0].reduce((acc: any, cur: any) => {
        let found = res[1].find(
          (item: any) => item.forecast_time === cur.forecast_time
        )
        if (found) {
          acc.push({
            ...{
              imgUrl: `${config.fileService}${found.file_path}`,
              jsonUrl: `${config.fileService}${cur.file_path}`
            },
            ...cur,
            ...found
          })
        }
        return acc
      }, [])
      flowFieldData.value = mergedArr
    })
    .catch(e => {
      message.error('获取数据失败')
      console.error(e)
    })
}

// 获取图层样式
function getLayerStyle() {
  let productId = getProductId()
  let params = productId
  if (['seaWind'].includes(currentType.value)) {
    params = productId.tiff
  } else if (['seaTemp', 'salinity', 'oceanWave'].includes(currentType.value)) {
    if (currentDataSource.value !== 'grid') {
      params = productId[currentModeType.value]
    }
  } else if (currentType.value === 'oceanCurrents') {
    if (currentDataSource.value !== 'grid') {
      params = productId[currentModeType.value].tiff
    } else {
      params = productId.tiff
    }
  } else if (currentType.value === 'stormSurge') {
    params = productId[currentModeType.value]
  }

  if (!params) {
    message.error('当前产品暂无数据')
    return false
  }

  Analysis.getIdentifybyProductId(params)
    .then(async (res: any) => {
      //数据源标识/图层标识/高度层(没有默认传NULL) 传参顺序
      const sourceSign = res.sourceIdentify
      const layerSign = res.layerIdentify
      const item = res.item
      // 获取样式
      const style: any = await Analysis.getLayerStyle(
        item,
        sourceSign,
        layerSign,
        'NULL'
      )
      ettpTifInfo.value = style[0]
      legendUrl.value = `${config.ettpService}${style[0]?.legend}` || ''
      let colorList = style[0]?.colorAttr.map((item: any) => item.color)
      colorRamp.value = colorList

      if (['oceanCurrents', 'seaWind'].includes(currentType.value)) {
        // let colorAttr = style[0]?.colorAttr
        style[0]?.colorAttr.forEach((item: any) => {
          item.opacity = 0.7
        })
        ettpTifInfo.value = style[0]
        await getFlowFieldData()
        getTiffData(params)
      } else if (['oceanWave'].includes(currentType.value)) {
        getTiffData(params)
        // 海浪分支
        if (waveDirectionManifestation.value === WaveDirectionManifestation.ARROW) {
          // 加载波向数据
          getWaveDirectionData(productId[`direction-${currentModeType.value}`])
        } else if (waveDirectionManifestation.value === WaveDirectionManifestation.PARTICLE_FLOW) {
          const pngfile = productId[`particle-flow-${currentModeType.value}-png`]
          const jsonfile = productId[`particle-flow-${currentModeType.value}-json`]
          await getFlowFieldData(() => ({
            json: jsonfile,
            png: pngfile
          }))

        }
      } else if (
        ['seaTemp', 'salinity'].includes(currentType.value)
      ) {
        getTiffData(params)
      } else if (currentType.value === 'stormSurge') {
        if (
          currentDataSource.value !== 'grid' &&
          currentModeType.value === 'provincial'
        ) {
          getTiffData(params)
          // renderStormSurge()
        }
      }
    })
    .catch(e => {
      message.error('获取数据失败')
      console.error(e, 'layer-style')
    })
}

// 获取tiff数据
function getTiffData(productId: string) {
  removeLayer()
  const params = {
    startTime: query.startTime,
    endTime: query.endTime,
    productId,
    _tz: 'GMT'
  }
  Analysis.getTiff(params)
    .then((res: any) => {
      if (res?.length) {
        res.forEach((item: any) => {
          item.filePath = `${config.fileService}${item.filePath}`
        })
        mapDataList.value = res
        let timeList = res.map((item: any) =>
          moment(item.forecastTime, 'YYYYMMDDHHmmss').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        )
        renderTimeLine(timeList)
        renderTiff(res[0].filePath as string)
      } else {
        message.warning('暂无数据')
        renderTimeLine([])
      }
    })
    .catch(e => {
      message.error('获取数据失败')
      console.error(e)
    })
}

function getWaveDirectionData(productId: string, index = 0) {
  // removeLayer()
  const params = {
    startTime: query.startTime,
    endTime: query.endTime,
    productId,
    _tz: 'GMT'
  }
  Analysis.getTiff(params)
    .then((res: any) => {
      if (res?.length) {
        res.forEach((item: any) => {
          item.filePath = `${config.fileService}${item.filePath}`
        })
        waveDirectionDataList.value = res
        let timeList = res.map((item: any) =>
          moment(item.forecastTime, 'YYYYMMDDHHmmss').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        )
        windKey.value++
        nextTick(() => {
          windComp.value?.createWindyLayer(res[index].filePath)
        })
      } else {
        message.warning('暂无数据')
        renderTimeLine([])
      }
      return res
    })
    .catch(e => {
      message.error('获取数据失败')
      console.error(e)
    })
}

/**
 * 时间轴部分
 */
const analysisTimeLine = ref()
let showTimeLine = ref(true)
const currentTime = ref('')
// 时间轴点击
function timeClick(time: { label: string }, index: number) {
  currentTime.value = time.label
  removeLayer()
  let url = mapDataList.value[index].filePath
  let png = ''
  if (
    currentType.value == 'seaWind' ||
    currentType.value == 'oceanCurrents' ||
    (currentType.value === 'oceanWave' &&
      waveDirectionManifestation.value ===
        WaveDirectionManifestation.PARTICLE_FLOW)
  ) {
    if (flowFieldData.value.length) {
      const jsonUrl = flowFieldData.value[index].jsonUrl
      png = flowFieldData.value[index].imgUrl
      renderFlowField(jsonUrl, png)
    }
  } else if (currentType.value == 'stormSurge') {
    if (currentModeType.value != 'county') {
      renderStormSurge()
    }
  } else if (currentType.value == 'oceanWave') {
    if (currentDataSource.value == 'nmefc') {
      const productId = getProductId()
      // 加载波向数据
      getWaveDirectionData(
        productId[`direction-${currentModeType.value}`],
        index
      )
    }
  }
  console.log(url, 'url----')
  renderTiff(url)
}

// 渲染时间轴
function renderTimeLine(timeList: any) {
  if (!timeList.length) {
    showTimeLine.value = false
  } else {
    showTimeLine.value = true
    nextTick(() => {
      analysisTimeLine.value.reRenderTimeLine(timeList)
      analysisTimeLine.value.changeTime({ label: timeList[0] }, 0)
    })
  }
}

// 切换数据源 智能网格grid | nmefc
function changeDataSoure(type: string) {
  currentDataSource.value = type
  getForecastTime()
  // getLayerStyle()
}

const popup = new Popup()
let clickKey: any = null
onMounted(() => {
  nextTick(() => {
    getForecastTime()

    eventBus.on('toolClick', () => {
      legendUrl.value = ''
      showTimeLine.value = false
      showInitTime.value = false
      currentType.value = ''
      removeLayer()
    })

    map.value.addOverlay(popup)

    clickKey = map.value?.on('click', (evt: any) => {
      const pixel = map.value.getEventPixel(evt.originalEvent)
      const feature = map.value.forEachFeatureAtPixel(
        pixel,
        function (feature: any) {
          return feature
        }
      )
      if (fillMapLayer) {
        const data = fillMapLayer?.getData(pixel)
        let typeResult = mapTypeList.find(
          (item: any) => item.value === currentType.value
        )
        popup.hide()
        if (data && parseInt(data) != 65535 && parseInt(data) != 999) {
          let formattedNum = data?.[0].toFixed(2)

          // 检查是否为-0.00
          if (formattedNum === '-0.00') {
            formattedNum = '0.00'
          }
          let html = `<div class="infoResult">
                <p class="title-popup">${typeResult?.unitLabel}:  ${formattedNum} ${typeResult?.unit}</p>
                </div>`
          popup.show(evt.coordinate, html)
        }
      }

      const val = feature?.getProperties()
      pointSource.forEachFeature((featureItem: any) => {
        featureItem.getStyle()[0].getText().getFill().setColor('block')
      })
      if (!val) {
        return
      }
      if (currentType.value === 'stormSurge') {
        if (currentModeType.value == 'provincial') {
          stormSurgeFlag.value = true
          stormSurgeInfo.value = val
          feature
            .getStyle()[0]
            .getText()
            .setFill(
              new Fill({
                color: '#ff0000'
              })
            )
          pointLayer.changed()
        } else {
          popup.hide()
          let html = `<div class="infoResult">
              <p class="title-popup">最大淹没深度(cm)：${
                val['最大淹没深度(cm)'] ? val['最大淹没深度(cm)'] : '--'
              }</p>
              <p class="title-popup">最大淹没范围(km²)：${
                deepValue.value ? deepValue.value : '--'
              }</p>
              </div>`
          popup.show(evt.coordinate, html)
        }
      } else if (isTyphoonActive.value) {
        typhoonRef.value.clickTyphoon(evt, map)
      }
    })
  })
})

onBeforeUnmount(() => {
  removeLayer()

  clickKey && unByKey(clickKey)
  clickKey = null
  eventBus.off('toolClick')
})
</script>

<style lang="scss">
.map-types-list {
  position: absolute;
  right: 20px;
  top: 48px;
  z-index: 4;

  .map-types-item {
    display: flex;
    align-items: center;
    justify-content: end;
    // background: rgba(81, 81, 81, 0.85);
    height: 25px;
    margin-bottom: 15px;
    position: relative;
    cursor: pointer;

    &.active {
      .map-type-item__label {
        background: rgba(81, 81, 81, 0.85);
        box-shadow: inset 0px 0px 11px 0px rgba(46, 46, 46, 0.61);
      }

      .map-type-item__icon {
        background: url(src/assets/images/analysis/<EMAIL>);
        background-size: 100% 100%;
      }
    }

    .map-type-item__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      background: rgba(93, 93, 93, 0.6);
      box-shadow: inset 0px 0px 4px 0px rgba(160, 160, 160, 0.25);
      border-radius: 19px;
      box-sizing: border-box;
      padding: 3px 39px 3px 11px;
    }

    .map-type-item__icon {
      width: 34px;
      height: 34px;
      background: #35414c;
      border-radius: 50%;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}

.mode-type {
  position: absolute;
  z-index: 3;
  bottom: 110px;
  right: 20px;
  border-radius: 26px 26px 26px 26px;
  background: rgba(250, 250, 250, 0.75);
  overflow: hidden;

  .mode-type-item {
    box-sizing: border-box;
    padding: 4px 46px;
    background: rgba(250, 250, 250, 0.75);
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #222222;
    cursor: pointer;

    &.active {
      background: #dc9b1d;
      border-radius: 26px 26px 26px 26px;
      color: #fff;
    }
  }
}

.initiation-time {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1;
  font-size: 20px;
  font-weight: bold /*  */;
  display: flex;
  align-items: center;

  label {
    flex-shrink: 0;
  }
}

.analysis-time-line {
  left: 20px !important;
}

.particle-switch {
  position: absolute;
  right: 20px;
  bottom: 180px;
  z-index: 3;
  display: flex;
  align-items: center;

  .switch-label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 14px;
  }
}
</style>
