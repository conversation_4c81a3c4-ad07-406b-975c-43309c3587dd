import { defineStore } from 'pinia'
export const useStackStore = defineStore('stack', {
  /**
   * 1.当创建、修改、删除要素的操作完成时，把当前地图所有的要素集合 push到 操作栈operateStack 中
    2.当点击 撤销 的时候，操作栈 operateStack  pop出栈，得到最近的一个 要素集合快照s，然后清空地图，添加到地图中，并把 要素集合快照s  push 压入 重做栈 redoStack  中。
    3.当点击 重做的时候，重做栈 redoStack  pop出栈，得到最近的一个 要素集合快照s，然后清空地图，添加到地图中，并把 要素集合快照s  push 压入 操作栈 operateStack  中。
   */

  actions: {
    // 当创建、修改、删除要素的操作完成时，把当前地图所有的要素集合 push到 操作栈operateStack 中
    operate(obj) {
      this.operateStack.push(obj)
    },
    //撤销（后退）
    redo() {
      const obj = this.operateStack.pop()
      this.redoStatck.push(obj)
      return obj
    },
    // 重做（前进）
    undo() {
      const obj = this.redoStatck.pop()
      this.operateStack.push(obj)
      return obj
    }
  },
  // 状态
  state() {
    return {
      operateStack: [], // 操作栈
      redoStatck: [] // 撤销栈
    }
  }
})
