<template>
  <!-- 预报单模板 -->
  <div class="forecast-temp d-flex">
    <Aside @select="selectHander" />
    <div class="content">
      <div class="content-header d-flex flex-justify-between flex-align-center">
        <h3>模板编辑</h3>
        <div class="btns">
          <qx-button class="primary" @click="onSave">保存</qx-button>
        </div>
      </div>
      <div class="main-container d-flex">
        <div class="column">
          <div class="column-title">模板预览</div>

          <div class="column-main">
            <div class="table-container">
              <div class="edit-wrap d-flex">
                <div
                  v-for="(item, index) in columns"
                  :key="item.key"
                  class="edit-item flex1 text-center"
                  :class="index <= 1 ? 'disabled' : ''"
                  @click="index <= 1 ? null : onEdit(item, index)"
                >
                  <i
                    class="icon"
                    :class="index <= 1 ? 'icon-edit-disabled' : 'icon-edit'"
                  ></i>
                </div>
              </div>
              <n-data-table
                v-loading="loading"
                :row-key="rowKey"
                class="qx-table"
                :data="tableData"
                :columns="columns"
                :single-line="false"
                :max-height="600"
                :checked-row-keys="checkedKeys"
                @update:checked-row-keys="handleCheck"
              ></n-data-table>
            </div>
            <div class="add-col" @click="onAddCol">
              <i class="icon icon-col-add"></i>
              添加列
            </div>
          </div>
        </div>
        <div v-if="isEdit" class="config">
          <Form
            :id="forecastTemplateId"
            :info="currentInfo"
            @save="onSaveColumn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Form, Aside } from './index'
import { useMessage, DataTableColumn } from 'naive-ui'
import Api from 'src/requests/forecast'
import { QxButton } from 'src/components/QxButton'
import { ref, h } from 'vue'
const message = useMessage()

// 表格渲染  start
const tableData = ref<any[]>([])
const columns = ref<any[]>(createCols())
const rowKey = (row: any) => row.id
const checkedKeys = ref<string[]>([])
const loading = ref(true)
const columnList = ref<any[]>([]) // 列

let forecastTemplateInfo = {} // 模板信息
let forecastTemplateId = '' //模板id

if (tableData.value.length) {
  tableData.value.forEach((item: any) => {
    Object.keys(item).forEach(key => {
      columns.value.push({
        title: item.name,
        key: key,
        align: 'center',
        className: ''
      })
    })
  })
}

function createCols(): DataTableColumn[] {
  return [
    {
      type: 'selection',
      width: 50
    },
    {
      title: '名称',
      key: 'name',
      align: 'center',
      className: ''
    }
  ]
}

// 定义一个函数，用于改变dataTableColumn中的属性值
function changeAttrs<T extends DataTableColumn>(
  // data为DataTableColumn类型
  data: T,
  // prop为data中的属性名
  prop: keyof T,
  // value为要改变的属性值
  value: T[keyof T]
) {
  // 如果data中存在prop属性，则将value赋值给data[prop]
  if (prop in data) {
    data[prop] = value
  }
  // @ts-ignore
  // 如果data中有children属性，且children的长度大于0，则遍历children中的每个元素，递归调用changeAttrs函数
  if (data.children?.length) {
    // @ts-ignore
    data.children.forEach((item: T) => {
      changeAttrs(item, prop, value)
    })
  }
}

// 表格渲染 end
// 侧边栏点击选中 start
function selectHander(params: any, option: any) {
  tableData.value = params
  loading.value = false
  if (option) {
    const id = option?.id
    forecastTemplateInfo = option
    forecastTemplateId = id
    getForecastTempInfo(id)
    checkedKeys.value = []
    columnList.value = []
    columns.value = createCols()
  }
}

function getForecastTempInfo(id: string) {
  checkedKeys.value = []
  Api.getForecastTempInfo(id)
    .then((res: any) => {
      if (res) {
        const { rowList = [] } = res
        const columnLists = res.columnList
        let areaCodes = rowList.map((item: any) => item.areaCode)

        areaCodes.forEach((areaCode: string) => {
          let result = tableData.value.find(item => item.code === areaCode)
          checkedKeys.value.push(result?.id)
        })
        columnList.value = columnLists

        columnLists.forEach((item: any) => {
          columns.value.push({
            title: renderHeader(
              item.columnName,
              item.display,
              true,
              columns.value.length
            )
          })
        })
      }
    })
    .catch(err => {
      console.log(err)
    })
}
// 侧边栏点击选中 end

// 表格头部编辑 start
const isEdit = ref(false) // 是否编辑
const editIndex = ref<null | number>(null) // 当前编辑下标

let currentInfo = ref({}) // 当前选中列信息
function onEdit(item: any, index: number) {
  isEdit.value = true
  currentInfo.value = columnList.value[index - 2]

  editIndex.value = index
  columns.value.forEach((col, idx) => {
    changeAttrs(col, 'className', '')
    if (idx === index) {
      changeAttrs(col, 'className', 'col-active')
    }
  })
}
// 表格头部编辑 end

// 添加列
function onAddCol() {
  // isEdit.value = true
  columns.value.push({
    title(column: any) {
      return renderHeader('列名', true, true, columns.value.length)
    },
    align: 'center',
    className: ''
  })
  currentInfo.value = {}
  columnList.value.push({
    columnName: '列名',
    display: true,
    forecastTemplateId: forecastTemplateId,
    id: ''
  })
}

// 渲染表头
function renderHeader(
  val: string,
  iconStatus = false,
  renderIcon = true,
  index = 0
) {
  return h(
    'div',
    { class: 'custom-header d-flex flex-align-center flex-justify-center' },
    [
      val,
      renderIcon
        ? h('i', {
            class: `icon ${iconStatus ? 'icon-eyes' : 'icon-eyes-close'}`,
            onClick: event => {
              changeIconStatus(event, index - 3)
            }
          })
        : ''
    ]
  )
}

// 渲染表头自定义icon
function changeIconStatus(event: Event, index: number) {
  const target = event.target as HTMLElement

  if (target.classList.contains('icon-eyes')) {
    target.classList.add('icon-eyes-close')
    target.classList.remove('icon-eyes')
  } else {
    target.classList.remove('icon-eyes-close')
    target.classList.add('icon-eyes')
  }
}

// 保存列信息
function onSaveColumn(info: any) {
  columns.value[editIndex.value!].title = renderHeader(
    info.columnName,
    true,
    true,
    columns.value.length
  )
  columns.value[editIndex.value!].className = ''
  info['forecastTemplateId'] = forecastTemplateId
  info['display'] = true

  // let index = columnList.value.findIndex(
  //   (item: any) =>
  //     item.elementCode === info.elementCode &&
  //     item.valueHandle === info.valueHandle
  // )
  // if (index === -1) {
  //   columnList.value.push(info)
  // } else {
  //   columnList.value[index] = info
  // }
  columnList.value[(editIndex.value as number) - 2] = info
  console.log(columnList.value, 'columnList.value')
  isEdit.value = false
}

function handleCheck(rowKeys: any) {
  console.log(rowKeys, 'rowKeys')
  checkedKeys.value = rowKeys
}

// 保存当前模板
function onSave() {
  if (!checkedKeys.value.length) {
    message.error('请选择预测区域')
    return false
  }

  if (!columnList.value.length) {
    message.error('请添加要素')
    return false
  }

  let rowList: any[] = []
  const { id } = forecastTemplateInfo as any
  console.log(checkedKeys.value, 'checkedKeys.value')
  checkedKeys.value.forEach((key: any) => {
    if (key) {
      let result = tableData.value.find((item: any) => item.id == key)
      let obj = {
        forecastTemplateId: id,
        areaCode: result?.code,
        areaId: result?.id
      }
      rowList.push(obj)
    }
  })
  const { forecastType, name, parentSort } = forecastTemplateInfo as any
  const params = {
    id,
    forecastType,
    name,
    sort: typeof parentSort==='number'?parentSort:1,
    rowList,
    columnList: columnList.value
  }
  Api.saveForecastTemp(params)
    .then((res: any) => {
      message.success('操作成功')
    })
    .catch(e => {
      let { msg = '' } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// 子组件可通过parent获取父组件变量
// defineExpose({
//   currentInfo
// })
</script>

<style lang="scss">
.forecast-temp {
  height: 100%;
  .content {
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;
    .content-header {
      box-sizing: border-box;
      padding: 9px 20px;
      width: 100%;
      background: url(src/assets/images/common/content-header.png) no-repeat;
      background-size: 100% 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      h3 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        line-height: 23px;
      }
    }
  }
  .main-container {
    height: calc(100% - 50px);
    .config {
      width: 320px;
      height: 100%;
      background: #fafafa;
      border-radius: 0px 0px 4px 0px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-top: none;
      border-bottom: none;
    }
    .column {
      height: 100%;
      flex: 1;
      box-sizing: border-box;
      padding: 18px;
      .column-title {
        margin-bottom: 14px;
      }
    }
  }
  .column-main {
    height: calc(100% - 28px);
    box-sizing: border-box;
    padding: 10px 25px 30px 50px;
    display: flex;
    background: #fafcfe;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e7f0fb;
    position: relative;
    .table-container {
      width: calc(100% - 53px);
      display: flex;
      flex-direction: column;
    }
    .qx-table {
      // flex: 1;
      height: calc(100% - 40px);
    }
    .add-col {
      cursor: pointer;
      height: calc(100% - 78px);
      padding: 0 10px;
      position: absolute;
      top: 58px;
      right: 25px;
      border-radius: 0px 0px 0px 0px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 12px;
      color: #000000;
      text-align: center;
      writing-mode: vertical-rl;
      text-orientation: upright;
    }
    .edit-wrap {
      margin-bottom: 9px;
    }
    .edit-item {
      padding: 8px 0;
      border: 1px solid rgba(0, 0, 0, 0.1);
      cursor: pointer;
      &:not(:nth-last-child(1)) {
        border-right: none;
      }
      &:nth-child(1) {
        width: 50px;
        flex: 0 1 auto !important;
      }
      &.disabled {
        cursor: not-allowed;
      }
    }
  }
  .col-active {
    background: rgba(50, 92, 240, 0.1) !important;
  }
  .custom-header {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-size: 14px;
    color: #222222;
    justify-content: center;
    i.icon {
      margin-left: 8px;
    }
  }
}
</style>
