package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ElementDTO;
import cn.piesat.data.making.server.entity.Element;
import cn.piesat.data.making.server.vo.ElementVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ElementMapper {

    ElementMapper INSTANCE = Mappers.getMapper(ElementMapper.class);

    /**
     * entity-->vo
     */
    ElementVO entityToVo(Element entity);

    /**
     * dto-->entity
     */
    Element dtoToEntity(ElementDTO dto);

    /**
     * entityList-->voList
     */
    List<ElementVO> entityListToVoList(List<Element> list);

    /**
     * dtoList-->entityList
     */
    List<Element> dtoListToEntityList(List<ElementDTO> dtoList);
}
