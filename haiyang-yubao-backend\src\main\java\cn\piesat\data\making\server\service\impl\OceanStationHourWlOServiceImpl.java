package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanStationHourWlODao;
import cn.piesat.data.making.server.entity.OceanStationHourWlO;
import cn.piesat.data.making.server.service.OceanStationHourWlOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-海浪特征值-原始数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanStationHourWlOServiceImpl extends ServiceImpl<OceanStationHourWlODao, OceanStationHourWlO>
        implements OceanStationHourWlOService {

    @Resource
    private OceanStationHourWlODao oceanStationHourWlODao;

    @Override
    public List<OceanStationHourWlO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList) {
        return oceanStationHourWlODao.getByStationNumListAndDateRange(startTime, endTime, stationNumList);
    }

    @Override
    public LocalDateTime getMaxCreateTime() {
        return oceanStationHourWlODao.getMaxCreateTime();
    }
}





