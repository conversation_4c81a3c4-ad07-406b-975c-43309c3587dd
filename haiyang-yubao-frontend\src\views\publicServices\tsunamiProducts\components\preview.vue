<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2025-03-26 10:04:12
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-02 15:38:13
 * @FilePath: /hainan-jianzai-web/src/views/publicServices/specialForecast/components/preview.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <qx-dialog
    title="预览"
    :visible="visible"
    width="906px"
    class="preview-special-dialog"
    @update:visible="onClose"
  >
    <template #content>
      <div class="report-content">
        <iframe
          :src="filePath"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </div>
    </template>
    <template #suffix>
      <div class="btn-group text-right">
        <qx-button class="cancel" @click="onClose">取消</qx-button>
        <qx-button class="primary" @click="onDownload">下载</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { QxDialog } from 'src/components/QxDialog'
import { QxButton } from 'src/components/QxButton'
import { useMessage } from 'naive-ui'
import PublicService from 'src/requests/publicService'
const message = useMessage()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  filePath: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:visible'])

function onClose() {
  emit('update:visible', false)
}

function onDownload() {
  PublicService.downloadTsunamiProduct(props.id)
    .then((res: any) => {
      const link = document.createElement('a')
      // @ts-ignore
      const url: any = window.URL || window.webkitURL || window.moxURL
      link.href = url.createObjectURL(
        new Blob([res.data], { type: res.data.type })
      )
      link.download = decodeURIComponent(res.fileName) //下载的文件名称
      link.click()
      window.URL.revokeObjectURL(url)
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data || {}
      message.error(msg || '下载失败')
    })
}
</script>

<style lang="scss">
.preview-special-dialog {
  .btn-group {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  .report-content {
    height: 700px;
  }
}
</style>
