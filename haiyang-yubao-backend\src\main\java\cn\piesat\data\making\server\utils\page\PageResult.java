package cn.piesat.data.making.server.utils.page;

import org.springframework.data.domain.Page;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 分页结果Bean
 * <AUTHOR>
 * @Date 2018/4/17
 * @Version V1.0.0
 * @Update 更新说明
 */
public class PageResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    private Integer pageNum;
    /**
     * 总页数
     */
    private Integer pages;
    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 数据总量
     */
    private Long totalCount;

    /**
     * 返回结果集
     */
    private List<T> pageResult;


    public PageResult() {

    }


    /**
     * @param pageResult 结果集合
     * @param pageNum    当前页码
     * @param pageSize   每页数量
     * @param totalCount 总条数
     */
    public PageResult(List<T> pageResult, Integer pageNum, Integer pageSize, Long totalCount) {
        this.pageNum = pageNum;
        this.pageResult = pageResult;
        this.totalCount = totalCount;
        this.pageSize = pageSize;

        this.pages = (int) Math.ceil((double) totalCount / pageSize);
    }


    /**
     * @param pageResult 结果集合
     * @param pageNum    当前页码
     * @param pages      每页数量
     * @param totalCount 总条数
     */
    public PageResult(Integer pageNum, Integer pages, Long totalCount, List<T> pageResult) {
        this.pageNum = pageNum;
        this.pages = pages;
        this.pageResult = pageResult;
        this.totalCount = totalCount;
    }

    /**
     * 根据JPA的Page构建PageResult结果
     *
     * @param page
     * @return
     */
    public static PageResult build(Page page) {
        PageResult pageResult = new PageResult();
        pageResult.setPageResult(page.getContent());
        pageResult.setPageNum((page.getNumber()));
        pageResult.setPageSize(page.getSize());
        pageResult.setTotalCount(page.getTotalElements());
        pageResult.setPages(page.getTotalPages());
        return pageResult;
    }


    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getPageResult() {
        return pageResult;
    }

    public void setPageResult(List<T> pageResult) {
        this.pageResult = pageResult;
    }
}
