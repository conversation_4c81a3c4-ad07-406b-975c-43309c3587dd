package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报产品记录表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_forecast_product_record_b")
public class ForecastProductRecord implements Serializable {

    private static final long serialVersionUID = -76322783833607045L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     **/
    @TableField("name")
    private String name;
    /**
     * 产品模板id
     **/
    @TableField("product_template_id")
    private Long productTemplateId;
    /**
     * 预报记录id
     **/
    @TableField("forecast_record_id")
    private String forecastRecordId;
    /**
     * 图形记录id
     **/
    @TableField("graphic_record_id")
    private String graphicRecordId;
    /**
     * 文件地址
     **/
    @TableField("file_url")
    private String fileUrl;
    /**
     * 推送任务id
     **/
    @TableField("push_task_id")
    private Long pushTaskId;
    /**
     * 创建人id
     **/
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建人
     **/
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人id
     **/
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;
    /**
     * 更新人
     **/
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}



