<template>
  <div class="common-aside publish-list">
    <div class="aside-header">
      <div class="title-wrap">
        <h3>发布列表</h3>
      </div>

      <Tab :active="tabIndex" :tab-list="tabList" @change="changeTab" />
    </div>
    <div class="aside-content">
      <div class="aside-filter">
        <n-form
          ref="formRef"
          label-width="auto"
          :model="form"
          label-placement="left"
          :show-feedback="false"
        >
          <template v-if="tabIndex === 2">
            <n-form-item label="数据来源">
              <n-radio-group
                v-model:value="form.sourceType"
                name="radiogroup"
                @update:value="
                  val => {
                    onChangeType(val, form.dataType)
                  }
                "
              >
                <n-radio :value="1">警报制作</n-radio>
                <n-radio :value="2">警报消息</n-radio>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="数据类型">
              <n-radio-group
                v-model:value="form.dataType"
                name="radiogroup"
                @update:value="
                  val => {
                    onChangeType(form.sourceType, val)
                  }
                "
              >
                <n-radio :value="1">海浪</n-radio>
                <n-radio :value="2">风暴潮</n-radio>
              </n-radio-group>
            </n-form-item>
          </template>
          <template v-if="tabIndex === 3">
            <n-form-item label="报告名称">
              <n-input
                v-model:value="form.keyword"
                placeholder="请输入"
              ></n-input>
            </n-form-item>
          </template>
          <template v-if="tabIndex !== 1">
            <n-form-item label="发布时间">
              <n-date-picker
                v-model:formatted-value="form.pushTime"
                type="daterange"
                :update-value-on-close="true"
                clearable
                :actions="[]"
                @update:formatted-value="onChangeTime"
              />
            </n-form-item>
          </template>
        </n-form>
      </div>
      <div class="table-container">
        <n-data-table
          v-loading="loading"
          :row-key="rowKey"
          class="qx-table"
          :data="tableData"
          :columns="columns"
          :max-height="600"
          :single-line="false"
          :row-props="rowProps"
        ></n-data-table>

        <n-pagination
          v-model:page="page"
          class="qx-pagination flex-justify-center"
          :page-count="totalPages"
          @update:page="onChangePage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tab } from 'src/components/Tab'
import { onMounted, reactive, ref, h } from 'vue'
import ProductApi from 'src/requests/productPublish'
import { useMessage } from 'naive-ui'
import Alarm from 'src/requests/alarm'
import { forecastColumns, alarmColumns, reportColumns } from './index'

const message = useMessage()
const emits = defineEmits(['row-click', 'changeTab'])
// tab start
const tabIndex = ref<number>(1)
const tabList = ref<any[]>([
  {
    label: '预报产品',
    id: 1
  },
  {
    label: '警报产品',
    id: 2
  },
  {
    label: '分析报告',
    id: 3
  }
])
async function changeTab(val: number) {
  tabIndex.value = val
  emits('changeTab', val)
  if (val === 1) {
    columns.value = forecastColumns
    getTableData()
  } else if (val === 2) {
    if (form.dataType === 1) {
      await getAlarmLevelList()
    } else {
      columns.value = alarmColumns
    }
    onChangeType(form.sourceType, form.dataType)
  } else {
    columns.value = reportColumns
  }
}
// tab end

/**
 * 列表 start
 */
const form = reactive({
  dataType: 1, //数据类型
  sourceType: 1, // 数据来源
  keyword: '', // 名称
  pushTime: null // 发布时间
})

let loading = ref(false)
const tableData = ref<any[]>([])
const page = ref(1)
let totalPages = ref(1) // 总页数
const columns = ref<any[]>([])
let alarmMakingColumns = ref<any[]>([])
const rowKey = (row: any) => row.id

function onChangeTime() {}
// 获取预报产品数据
function getTableData() {
  loading.value = true
  const params = {
    pageSize: 10,
    pageNum: 1
  }
  ProductApi.getForecastResult(params)
    .then((res: any) => {
      if (res) {
        const { pageResult } = res
        tableData.value = pageResult
        loading.value = false
        onRowClick(tableData.value[0], 0)
      }
    })
    .catch((e: any) => {
      let { msg } = e?.response?.data
      message.error(msg || '获取数据失败')
      loading.value = false
    })
}

// 警报级别
function getAlarmLevelList() {
  Alarm.getAlarmLevelList()
    .then((res: any) => {
      let obj: any = {
        title: '警报级别',
        align: 'center',
        render(row: any) {
          let result = res.find((item: any) => item.id == row.alarmLevel)
          let str = '--'
          if (result) {
            str = result.levelColor + '警报'
          }
          return h(
            'span',
            { class: 'alarm-level alarm-level-' + row.alarmLevel },
            str
          )
        }
      }

      let result: any[] = []

      alarmColumns.forEach((item: any) => {
        let obj = Object.assign({}, item)
        result.push(obj)
      })

      alarmMakingColumns.value = result
      alarmMakingColumns.value.splice(2, 0, obj)
      columns.value = alarmMakingColumns.value
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data
      message.error(msg || '获取警报级别失败')
      console.error(e, 'getAlarmLevelList')
    })
}
// 警报产品 单选切换 sourceType:数据来源 dataType:数据类型 1 海浪警报 2 风暴潮警报
function onChangeType(sourceType: number, dataType: number) {
  loading.value = true
  let funcName = ''
  if (sourceType === 1) {
    getAlarmLevelList()
    if (dataType === 1) {
      funcName = 'getSeaWaveAlarmPageList'
    } else {
      funcName = 'getStormSurgePageList'
    }
  } else {
    columns.value = alarmColumns
    if (dataType === 1) {
      funcName = 'getSeaWaveMessageList'
    } else {
      funcName = 'getStormSurgeMessageList'
    }
  }

  alarmCommon(funcName)
}

// 警报制作 公共调用
function alarmCommon(name: string) {
  tableData.value = []
  totalPages.value = 0
  const params = {
    pageNum: page.value,
    pageSize: 10
  }
  Alarm[name](params)
    .then((res: any) => {
      if (res) {
        const { pageResult = [] } = res
        if (pageResult.length) {
          tableData.value = pageResult
          totalPages.value = Number(res.pages)
          onRowClick(tableData.value[0], 0)
        } else {
          onRowClick(null, 0)
        }
      }
      loading.value = false
    })
    .catch((e: any) => {
      console.error(e, 'getAlarmList')
      message.error('获取数据失败')
      loading.value = false
    })
}

// 分页切换
function onChangePage(val: number) {
  page.value = val
  changeTab(tabIndex.value)
}

// 当前选中行下标
const currentRowIndex = ref<number | null>(null)
// 自定义行属性
function rowProps(row: any, rowIndex: number) {
  return {
    style: { cursor: 'pointer' },
    class: currentRowIndex.value === rowIndex ? 'row-active' : '',
    onClick() {
      onRowClick(row, rowIndex)
    }
  }
}
// 行点击事件
function onRowClick(row: any, rowIndex: number) {
  currentRowIndex.value = rowIndex
  emits('row-click', tabIndex.value, row, {
    sourceType: form.sourceType,
    dataType: form.dataType
  })
}

/**
 * 列表 end
 */

onMounted(() => {
  columns.value = forecastColumns
  getTableData()
})
</script>

<style lang="scss">
.publish-list {
  width: 448px;
  margin-right: 20px;
  border-radius: 8px;
  .aside-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    .title-wrap {
      box-sizing: border-box;
      padding: 18px 12px 20px 26px;
    }

    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
    }
  }
  .row-active {
    background: #e8f0fa;
    .n-data-table-td {
      background: transparent;
    }
  }

  .title-status {
    font-size: 11px;
    margin-top: 5px;
  }
  span.operate-btn {
    margin-right: 0;
    &.fail {
      color: #c52a29;
    }
    &.wait {
      color: #e79e42;
      padding: 0 10px;
      // border-left: 1px solid rgba(0, 0, 0, .1);
      // border-right: 1px solid rgba(0, 0, 0, .1);
    }
    &.published {
      color: #82b437;
    }
  }
  .status-list {
    display: flex;
    align-items: center;
    span {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
      margin-right: 20px;
      display: flex;
      align-items: center;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      i {
        display: inline-block;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        margin-right: 3px;
        &.fail {
          background: #c52a29;
        }
        &.wait {
          background: #e79e42;
          padding: 0 5px;
          border-left: 1px solid rgba(0, 0, 0, 0.1);
          border-right: 1px solid rgba(0, 0, 0, 0.1);
        }
        &.published {
          background: #82b437;
        }
      }
    }
  }
  .ht-tab-item {
    width: 25%;
    flex: 0 1 auto !important;
  }
  .aside-filter {
    box-sizing: border-box;
    padding: 20px 17px 0;
    margin-bottom: -20px;
    .n-form-item {
      margin-bottom: 10px;
    }
  }
  .qx-pagination {
    margin-top: 20px;
  }
  .aside-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .table-container {
    flex: 1;
  }
  .alarm-level {
    &-1 {
      color: #f90102;
    }
    &-2 {
      color: #fe7b0e;
    }
    &-3 {
      color: #fe7b0e;
    }
    &-4 {
      color: #0083fd;
    }
    &-5 {
      color: #ebeaef;
    }
  }
}
</style>
