package cn.piesat.data.making.server.vo;

import java.util.Date;

import java.io.Serializable;

/**
 * VO类
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:29
 */
public class FmIsolineDataVO implements Serializable {

    private static final long serialVersionUID = 483605865176511056L;

    private Long id;
    private String name;
    private String localGeo;
    private String localJson;
    private Date startReportTime;
    private Date forecastTime;
    private String dataSource;

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLocalGeo() {
        return localGeo;
    }

    public void setLocalGeo(String localGeo) {
        this.localGeo = localGeo;
    }

    public String getLocalJson() {
        return localJson;
    }

    public void setLocalJson(String localJson) {
        this.localJson = localJson;
    }

    public Date getStartReportTime() {
        return startReportTime;
    }

    public void setStartReportTime(Date startReportTime) {
        this.startReportTime = startReportTime;
    }

    public Date getForecastTime() {
        return forecastTime;
    }

    public void setForecastTime(Date forecastTime) {
        this.forecastTime = forecastTime;
    }
}
