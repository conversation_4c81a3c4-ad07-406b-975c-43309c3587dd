<!--
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-11-22 09:36:19
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2024-11-25 14:17:59
 * @FilePath: /hainan-jianzai-web/src/components/QxEcharts/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div
    ref="myChartsRef"
    :style="{ height: height, width: width }"
  />
</template>
<script setup lang="ts">
import { ECharts, init } from 'echarts'
import { ref, watch, onMounted, onBeforeUnmount,nextTick } from 'vue'

// 定义props
interface Props {
  width?: string
  height?: string
  option: any
}
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  option: () => ({})
})

const myChartsRef = ref<HTMLDivElement>()
let myChart: ECharts
// eslint-disable-next-line no-undef
let timer: string | number | NodeJS.Timeout | undefined

// 初始化echarts
const initChart = (): void => {
  if (myChart !== undefined) {
    myChart.dispose()
  }
  
  myChart = init(myChartsRef.value as HTMLDivElement)
  // 拿到option配置项，渲染echarts
  myChart?.setOption(props.option, true)
}

// 重新渲染echarts
const resizeChart = (): void => {
  timer = setTimeout(() => {
    if (myChart) {
      myChart.resize()
    }
  }, 500)
}

onMounted(() => {
  nextTick(()=>{
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
  clearTimeout(timer)
  timer = 0
})

watch(
  props.option,
  (val) => {
    initChart()
  },
  {
    deep: true
  }
)
</script>


