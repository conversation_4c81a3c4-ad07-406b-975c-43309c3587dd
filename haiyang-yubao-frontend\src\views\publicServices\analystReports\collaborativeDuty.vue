<template>
  <div class="duty-container">
    <div class="duty-title">
      <div class="duty-title__label">协同值班工作日报</div>
      <div class="duty-info">
        <span class="date">时间：2024-01-11</span>
        <span class="date">星期一</span>
        <qx-button class="primary">保存</qx-button>
        <qx-button class="warning">提交</qx-button>
      </div>
    </div>
    <div class="right-body">
      <Card title="人员信息">
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :model="personInfo"
          :show-feedback="false"
          require-mark-placement="left"
        >
          <n-grid :cols="3" :x-gap="12" :y-gap="10">
            <n-form-item-gi label="总队带班领导" path="user.name" :span="1">
              <n-input
                v-model:value="personInfo.leader"
                placeholder="输入总队带班领导"
                :disabled="true"
              />
            </n-form-item-gi>
            <n-form-item-gi label="总队值班处长" path="user.name">
              <n-input
                v-model:value="personInfo.director"
                placeholder="输入总值班处长"
                :disabled="true"
              />
            </n-form-item-gi>
            <n-form-item-gi label="总队值班干部" path="user.name">
              <n-input
                v-model:value="personInfo.director"
                placeholder="请输入总队值班干部"
                :disabled="true"
              />
            </n-form-item-gi>
            <n-form-item-gi label="总队值班员" path="user.name">
              <n-input
                v-model:value="personInfo.cadre"
                placeholder="输入总队值班员"
                :disabled="true"
              />
            </n-form-item-gi>
            <n-form-item-gi label="总值班员" path="user.name">
              <n-input
                v-model:value="personInfo.staff"
                placeholder="输入总值班员"
                :disabled="true"
              />
            </n-form-item-gi>
            <n-form-item-gi
              label="监测预报中心值班预报员"
              path="user.name"
              :required="true"
            >
              <n-input
                v-model:value="personInfo.forecaster"
                placeholder="输入监测预报中心值班预报员"
              />
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </Card>

      <Card title="涉嫌违法线索">
        <div class="info-card">
          <n-form
            label-placement="left"
            label-width="auto"
            :model="clueInfo"
            :show-feedback="false"
            inline
            require-mark-placement="left"
          >
            <n-grid :cols="2" :x-gap="12" :y-gap="10">
              <n-form-item-gi
                label="1、涉海线索"
                path="clueInfo.sea"
                :span="1"
                :required="true"
              >
                <n-input
                  v-model:value="clueInfo.sea"
                  type="textarea"
                  placeholder="注：包含时间、地点、涉嫌违法事项情况、处置情况。"
                  :disabled="true"
                />
              </n-form-item-gi>
              <n-form-item-gi
                label="2、涉渔线索"
                path="clueInfo.fishing"
                :span="1"
              >
                <n-input
                  v-model:value="clueInfo.fishing"
                  type="textarea"
                  placeholder="注：包含时间、地点、涉嫌违法事项情况、处置情况。"
                  :disabled="true"
                />
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </div>
      </Card>

      <Card title="预警信息发布">
        <div class="info-card">
          <n-form
            label-placement="left"
            label-width="auto"
            :model="clueInfo"
            inline
            :show-feedback="false"
          >
            <n-grid :cols="3" :x-gap="12" :y-gap="0">
              <n-form-item-gi label="1、海洋气象" path="clueInfo.sea" :span="1">
                <n-input
                  v-model:value="clueInfo.sea"
                  type="textarea"
                  placeholder="请输入内容"
                />
              </n-form-item-gi>
              <n-form-item-gi
                label="2、航行安全"
                path="clueInfo.fishing"
                :span="1"
              >
                <n-input
                  v-model:value="clueInfo.fishing"
                  type="textarea"
                  placeholder="请输入内容"
                  disabled
                />
              </n-form-item-gi>
              <n-form-item-gi
                label="3、禁航区域"
                path="clueInfo.fishing"
                :span="1"
              >
                <n-input
                  v-model:value="clueInfo.fishing"
                  type="textarea"
                  placeholder="请输入内容"
                  :disabled="true"
                />
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </div>
      </Card>

      <Card title="执法装备调度">
        <div class="textarea-wrap">
          <n-input
            v-model:value="value"
            type="textarea"
            placeholder="请输入内容"
            :disabled="true"
          />
        </div>
      </Card>

      <Card title="渔业船舶调度">
        <div class="textarea-wrap">
          <n-input
            v-model:value="value"
            type="textarea"
            placeholder="请输入内容"
            :disabled="true"
          />
        </div>
      </Card>

      <Card title="备注">
        <div class="textarea-wrap">
          <n-input
            v-model:value="value"
            type="textarea"
            placeholder="请输入备注"
            :disabled="true"
          />
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { QxButton } from 'src/components/QxButton'
import { ref } from 'vue'
import { Card } from './components'

const personInfo = ref({
  leader: '111', //总带班领导
  director: '222', //总值班处长
  cadre: '333', //总值班干部
  staff: '444', // 总值班员
  forecaster: '555' //测预报中心值班预报员
})
const clueInfo = ref({
  //涉海线索
  sea: '',
  //涉渔线索
  fishing: ''
})
const value = ref('')
</script>

<style lang="scss" scoped>
.duty-container {
  width: 100%;
  height: 100%;
  .duty-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .duty-title__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #222222;
      line-height: 21px;
    }
  }
  .date {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    margin-right: 10px;
  }
  .right-body {
    height: calc(100% - 53px);
    background: #ffffff;
    overflow-y: auto;
  }
}
</style>
