/*
 * @Author: 樊海玲 <EMAIL>
 * @Date: 2024-05-17 14:30:39
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-23 14:32:38
 * @FilePath: \hainan-jianzai-web\src\components\OpenlayersMap\openlayersInit.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Map, View } from 'ol'
import MousePosition from 'ol/control/MousePosition.js'
import { Tile as TileLayer } from 'ol/layer'
import XYZ from 'ol/source/XYZ'
import WMTS from 'ol/source/WMTS.js'
import WMTSTileGrid from 'ol/tilegrid/WMTS.js'
import { getWidth, getTopLeft } from 'ol/extent'
import { get } from 'ol/proj'
import { defaults as defaultControls } from 'ol/control'
import { defaults as defaultInteractions } from 'ol/interaction'
import proj4 from 'proj4'
import { register } from 'ol/proj/proj4'
import TileGrid from 'ol/tilegrid/TileGrid.js'
//+proj=longlat +ellps=GRS80 +no_defs +type=crs
/**
 * cesium 初始化
 * @param config
 * @param {String} config.elId cesium 渲染容器 id
 * @param {Boolean} config.debugShowFramesPerSecond 几何图形是否以3D模式绘制以节约GPU资源
 * @return {module:cesium.Viewer}
 */

export function init(options = {}) {
  const { elId = 'openlayers-map' } = options
  elId && delete options.elId
  // 实例化鼠标位置控件
  var mousePositionControl = new MousePosition({
    // coordinateFormat: createStringXY(4), // 坐标格式
    coordinateFormat: function (coordinate) {
      return (
        '经度：' +
        coordinate[0].toFixed(4) +
        '，纬度：' +
        coordinate[1].toFixed(4)
      )
    },
    projection: 'EPSG:4326', // 地图投影坐标系
    className: 'custom-mouse-position', // 坐标信息显示样式
    // 显示鼠标位置信息的目标容器
    target: document.getElementById('mouse-position'),
    undefinedHTML: '&nbsp' // 未定义坐标的标记
  })

  //WMTS图层数据
  var wmtsLayer
  //通过范围计算得到分辨率数组
  const projection = get('EPSG:4326')
  const projectionExtent = projection.getExtent()
  const size = getWidth(projectionExtent) / 256
  const resolutions = new Array(19)
  const matrixIds = new Array(19)
  for (let z = 0; z < 19; ++z) {
    // generate resolutions and matrixIds arrays for this WMTS
    resolutions[z] = size / Math.pow(2, z)
    matrixIds[z] = z
  }
  proj4.defs('EPSG:4490', '+proj=longlat +ellps=GRS80 +no_defs +type=crs')
  register(proj4)
  const resolutions1 = [
    0.703125, 0.3515625, 0.17578125, 0.087890625, 0.0439453125, 0.010986328125,
    0.0054931640625, 0.00274658203125, 0.001373291015625, 0.0006866455078125,
    0.00034332275390625, 0.000171661376953125, 8.58306884765625e-5,
    4.29153442382813e-5, 2.14576721191406e-5, 1.07288360595703e-5,
    5.36441802978516e-6, 2.68220901489258e-6, 1.34110450744629e-6
  ]
  const grid = new TileGrid({
    extent: [-180, -90, 180, 90],
    resolutions: resolutions1
  })
  const map = new Map({
    target: elId,
    layers: [
      // 40f07bc5dace757a11fdc4e823ee5ea2
      // 7842d4156bc725beb56aa76a239a447e
      // new TileLayer({
      //   title: '天地图矢量',
      //   source: new XYZ({
      //     url:
      //       config.mapService +
      //       `/DataServer?T=vec_c&x={x}&y={y}&l={z}&tk=40f07bc5dace757a11fdc4e823ee5ea2`,
      //     wrapX: true,
      //     crossOrigin: 'anonymous',
      //     projection: 'EPSG:4326' // 坐标
      //   })
      //   // crossOrigin: "Anonymous" // 图层是否允许跨域
      // }),
      // new TileLayer({
      //   title: '边界线',
      //   source: new XYZ({
      //     url:
      //       config.mapService +
      //       `/DataServer?T=ibo_c&x={x}&y={y}&l={z}&tk=40f07bc5dace757a11fdc4e823ee5ea2`,
      //     wrapX: true,
      //     projection: 'EPSG:4326', // 地图投影坐标系
      //     crossOrigin: 'anonymous'
      //   })
      // }),
      new TileLayer({
        title: '注记',
        properties: {
          name: '海南陆地'
        },
        zIndex: 7,
        // opacity: 0,
        source: new XYZ({
          // url:
          //   config.mapService +
          //   `/DataServer?T=cva_c&x={x}&y={y}&l={z}&tk=7842d4156bc725beb56aa76a239a447e`,
          // url: 'http://59.212.226.92:8095/gis2/rest/services/hy/%E7%9C%81%E9%A2%84%E6%8A%A5%E4%B8%AD%E5%BF%83%E4%B8%93%E9%A2%98%E5%9B%BE%E5%88%87%E7%89%87_%E6%B0%B4%E7%B3%BB/MapServer/tile/{z}/{y}/{x}?userno=admin&key=25f01ea8a93942cc8c41d82470fe84de',
          wrapX: true,
          projection: 'EPSG:4490',
          tileSize: 256, // 切片大小
          minZoom: 10, // 最小缩放级别
          maxZoom: 19, // 最大缩放级别
          // projection: 'EPSG:4490', // 地图投影坐标系
          crossOrigin: 'anonymous',
          tileGrid: grid,
          tileUrlFunction: tileCoord => {
            const z = tileCoord[0] + 1 // 缩放级别
            // const z = 0
            const y = tileCoord[1]
            const x = tileCoord[2]
            return `${config.landLayer}/gis2/rest/services/hy/%E7%9C%81%E9%A2%84%E6%8A%A5%E4%B8%AD%E5%BF%83%E4%B8%93%E9%A2%98%E5%9B%BE%E5%88%87%E7%89%87_%E9%99%86%E5%9C%B0/MapServer/tile/${z}/${x}/${y}?${config.mapParams}`
          }
        })
        // crossOrigin: "Anonymous" // 图层是否允许跨域
      }),
      /** 新地图 */
      new TileLayer({
        source: new WMTS({
          url: config.vecMapLayer,
          layer: 'vec',
          matrixSet: 'c',
          format: 'tiles',
          projection: projection,
          tileGrid: new WMTSTileGrid({
            origin: getTopLeft(projectionExtent),
            resolutions: resolutions,
            matrixIds: matrixIds
          }),
          crossOrigin: 'anonymous',
          style: 'default'
        })
      }),
      new TileLayer({
        zIndex: 8,
        source: new WMTS({
          url: config.cvaMapLayer,
          layer: 'cva',
          matrixSet: 'c',
          format: 'tiles',
          projection: projection,
          tileGrid: new WMTSTileGrid({
            origin: getTopLeft(projectionExtent),
            resolutions: resolutions,
            matrixIds: matrixIds
          }),
          crossOrigin: 'anonymous',
          style: 'default'
        })
      })
    ],
    view: new View({
      center: [110, 19.68], //视图中心位置 [116, 39]
      projection: 'EPSG:4326', //指定投影
      // projection: 'EPSG:4490', // 设置视图的坐标系为 EPSG:4490
      zoom: 8
    }),
    // controls: defaultControls().extend([mousePositionControl]),
    interactions: defaultInteractions({ doubleClickZoom: false }),
    controls: defaultControls({
      zoom: false
    }).extend([mousePositionControl])
  })

  return map
}

export default {
  init
}
