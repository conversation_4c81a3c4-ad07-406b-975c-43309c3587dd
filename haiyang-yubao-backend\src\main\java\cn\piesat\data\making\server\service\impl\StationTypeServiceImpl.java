package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.StationTypeDao;
import cn.piesat.data.making.server.dto.StationDTO;
import cn.piesat.data.making.server.entity.StationType;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.RegionService;
import cn.piesat.data.making.server.service.StationService;
import cn.piesat.data.making.server.service.StationTypeService;
import cn.piesat.data.making.server.vo.RegionVO;
import cn.piesat.data.making.server.vo.StationVO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 站点类型表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class StationTypeServiceImpl extends ServiceImpl<StationTypeDao, StationType> implements StationTypeService {

    @Resource
    private RegionService regionService;
    @Resource
    private StationService stationService;

    @Override
    public List<StationInfo> getTreeList(String name, String flag, String code) {
        //站点类型列表
        List<StationType> typeList = this.list(new LambdaUpdateWrapper<StationType>().eq(StringUtils.isNotBlank(code), StationType::getCode, code));
        if (CollectionUtils.isEmpty(typeList)) {
            return Collections.EMPTY_LIST;
        }
        //行政区列表
        List<RegionVO> regionList = regionService.getList();

        List<StationInfo> list = new ArrayList<>();
        //封装站点类型
        typeList.stream().forEach(type -> {
            StationInfo typeInfo = new StationInfo();
            typeInfo.setStationTypeCode(type.getCode());
            typeInfo.setId(type.getId());
            typeInfo.setCode(type.getCode());
            typeInfo.setName(type.getName());
            if (!"ship".equals(type.getCode())) {
                //封装海洋站、浮标站行政区
                regionList.stream().forEach(region -> {
                    StationInfo regionInfo = new StationInfo();
                    regionInfo.setId(region.getId());
                    regionInfo.setStationTypeCode(type.getCode());
                    regionInfo.setCode(region.getCode());
                    regionInfo.setName(region.getName());
                    if (StringUtils.isBlank(region.getParentCode())) {
                        regionInfo.setParentCode(type.getCode());
                    } else {
                        regionInfo.setParentCode(region.getParentCode());
                    }
                    list.add(regionInfo);
                });
            }
            list.add(typeInfo);
        });
        if (flag.equals("station")) {
            //站点列表
            StationDTO dto = new StationDTO();
            dto.setName(name);
            List<StationVO> stationList = stationService.getList(dto);
            //封装站点
            stationList.stream().forEach(station -> {
                StationInfo stationInfo = new StationInfo();
                stationInfo.setId(station.getId());
                stationInfo.setCode(station.getCode());
                stationInfo.setName(station.getName());
                if ("ship".equals(station.getStationTypeCode())) {
                    stationInfo.setParentCode(station.getStationTypeCode());
                } else {
                    stationInfo.setParentCode(station.getRegionCode());
                }
                stationInfo.setLocationJson(station.getLocationJson());
                stationInfo.setStationTypeCode(station.getStationTypeCode());
                stationInfo.setType(station.getType());
                stationInfo.setRelationStation(station.getRelationStation());
                stationInfo.setEnable(station.getEnable());
                list.add(stationInfo);
            });
        }
        return this.buildTree(list);
    }

    private List<StationInfo> buildTree(List<StationInfo> list) {
        List<StationInfo> treeList = new ArrayList<>();
        for (StationInfo info : list) {
            // 顶级类别
            if (info.getParentCode() == null) {
                treeList.add(info);
            } else {
                // 非顶级类别，递归查找其父类并添加到树中
                StationInfo parent = this.findByCode(list, info.getParentCode(), info.getStationTypeCode());
                if (parent != null) {
                    if (parent.getChildList() == null) {
                        parent.setChildList(new ArrayList<>());
                    }
                    parent.getChildList().add(info);
                }
            }
        }
        return treeList;
    }

    private StationInfo findByCode(List<StationInfo> list, String code, String stationTypeCode) {
        for (StationInfo info : list) {
            if (info.getCode() != null && info.getCode().equals(code) && info.getStationTypeCode() != null && info.getStationTypeCode().equals(stationTypeCode)) {
                return info;
            }
        }
        return null;
    }
}





