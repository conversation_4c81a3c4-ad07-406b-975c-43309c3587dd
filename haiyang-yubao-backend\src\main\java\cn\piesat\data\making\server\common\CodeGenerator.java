package cn.piesat.data.making.server.common;

import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang.StringUtils;

public class CodeGenerator {

    /**
     * 中文首字母拼音
     **/
    public static String getPinYinHeadChar(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        String convert = "";
        for (int j = 0; j < str.length(); j++) {
            char word = str.charAt(j);
            //提取字符的首字母
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            } else {
                convert += word;
            }
        }
        //去除字符中包含的空格
        //convert = convert.replace(" ","");
        //字符转小写
        return convert.toLowerCase();
    }
}