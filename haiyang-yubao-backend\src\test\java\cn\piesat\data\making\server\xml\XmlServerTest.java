package cn.piesat.data.making.server.xml;


import org.jsoup.Jsoup;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.xml.sax.Attributes;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import java.io.File;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;


public class XmlServerTest {

    public void testXPath(){
        try {
//            //String xmlPath = Thread.currentThread().getContextClassLoader().getResource("").getPath()+"alertxml/seawavetemplate.xml";
//            String xmlPath = "D:\\03.workspaces\\02.java\\58.hainan\\01.data-making-server\\src\\main\\resources\\alertxml\\seawavetemplate.xml";
//            //InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("classpath:alertxml/seawavetemplate.xml");
//            //Document doc = Jsoup.parse(inputStream,"utf-8","");
//            Document doc = Jsoup.parse(new File(xmlPath),"utf-8");
//
//            Element element = doc.getElementsByTag("sender").first();
//
//            System.out.println(element.text("123"));
//
//            System.out.println(doc.val());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void testXPath2(){
        try{
//            String xmlPath = "D:\\03.workspaces\\02.java\\58.hainan\\01.data-making-server\\src\\main\\resources\\alertxml\\seawavetemplate.xml";
//
//            SAXParserFactory factory = SAXParserFactory.newInstance();
//            SAXParser parser = factory.newSAXParser();
//
//            parser.parse(xmlPath,new DefaultHandler(){
//                public void startElement(String url, String localName, String qName, Attributes attributes){
//                    System.out.println(qName);
//                }
//            });
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    public void testXPath3(){
        try{
            String xmlPath = "D:\\03.workspaces\\02.java\\58.hainan\\01.data-making-server\\src\\main\\resources\\alertxml\\seawavetemplate.xml";

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new File(xmlPath));

            document.setXmlStandalone(true);

            XPathFactory xPathFactory = XPathFactory.newInstance();
            XPath xPath = xPathFactory.newXPath();

            Node node = (Node) xPath.evaluate("/alert/identifier", document, XPathConstants.NODE);

            //System.out.println(node);

            DateTimeFormatter dtf=DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String timestr = LocalDateTime.now().format(dtf);

            node.setTextContent("46000045000100_"+timestr);

            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.DOCTYPE_PUBLIC, "yes");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            //transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));
            String output = writer.getBuffer().toString();

            System.out.println("修改后的XML内容:");
            System.out.println(output);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ssXXX");
            System.out.println(sdf.format(Calendar.getInstance().getTime()));
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        XmlServerTest xmlServerTest = new XmlServerTest();

        xmlServerTest.testXPath3();
    }
}
