package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.ElementDTO;
import cn.piesat.data.making.server.entity.Element;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.ElementVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 要素字典表服务接口
 *
 * <AUTHOR>
 */
public interface ElementService extends IService<Element> {

    /**
     * 查询分页
     */
    PageResult<ElementVO> getPage(ElementDTO dto, PageParam pageParam);

    /**
     * 查询列表
     */
    List<ElementVO> getList();

    /**
     * 查询详情
     */
    ElementVO getInfoById(Long id);

    /**
     * 保存
     */
    void save(ElementDTO dto);

    /**
     * 删除
     */
    void deleteById(Long id);
}




