package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.dto.ForecastSpecialDTO;
import cn.piesat.data.making.server.entity.ForecastSpecial;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ForecastSpecialService extends IService<ForecastSpecial> {

    PageResult pageList(ForecastSpecialDTO dto);

    void saveInfo(ForecastSpecialDTO dto);

    void updateInfo(ForecastSpecialDTO dto);

    void updateStatus(Long id, Integer status);

    ForecastSpecialDTO info(Long id);

}
