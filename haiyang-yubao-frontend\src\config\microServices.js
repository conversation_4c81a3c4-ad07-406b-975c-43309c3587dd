const baseRouter = import.meta.env.BASE_URL

export const microServices = [
  {
    name: 'api-data-scrap',
    _prefix: '/api/dataScrap',
    host: 'http://data-scrap-api:28080', //数据汇集服务
    prefix: ''
  },
  {
    name: 'api-basic',
    _prefix: '/api/basic',
    host: 'http://sys-api-server:23080', //字典服务
    prefix: ''
  },
  {
    name: 'api-sys-admin',
    _prefix: '/api/sysadmin',
    host: 'http://sys-admin-api:25080', //数管服务
    prefix: ''
  },
  {
    name: 'product-push',
    _prefix: '/api/push',
    host: 'http://product-push-api:24080', //推送服务
    prefix: ''
  },
  {
    name: 'api-dispatch',
    _prefix: '/api/schedule',
    host: 'http://schedule-api-server:38080', //调度服务
    prefix: ''
  },
  {
    name: 'api-pers',
    _prefix: '/api/pers',
    host: 'http://security-ucenter-server:26080', //权限服务
    prefix: ''
  },
  {
    name: "product-push",
    _prefix: "/api/push",
    host: "http://product-push-api", //数据推送
    prefix: ''
  },
  {
    name: "data-scrap-mail",
    _prefix: "/api/mail",
    host: "http://data-scrap-mail", //邮件服务
    prefix: ''
  },
  {
    name: "api-monitor",
    _prefix: "/api/monitor",
    host: "http://data-scrap-mail", //邮件服务
    prefix: ''
  },{
    name: "api-alarm",
    _prefix: "/api/alarm",
    host: "http://visual-api-server:30080", //可视化服务
    prefix: ''
  }
]
