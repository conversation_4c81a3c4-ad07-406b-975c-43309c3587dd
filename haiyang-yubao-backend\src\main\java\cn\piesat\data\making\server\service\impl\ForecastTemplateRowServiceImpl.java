package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.ForecastTemplateRowDao;
import cn.piesat.data.making.server.entity.ForecastTemplateRow;
import cn.piesat.data.making.server.service.ForecastTemplateRowService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 预报模板-行定义服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastTemplateRowServiceImpl extends ServiceImpl<ForecastTemplateRowDao, ForecastTemplateRow>
        implements ForecastTemplateRowService {

    @Override
    public List<ForecastTemplateRow> getList(Long templateId) {
        return this.list(createQueryWrapper(templateId));
    }

    @Override
    public void saveList(Long templateId, List<ForecastTemplateRow> list) {
        LambdaQueryWrapper<ForecastTemplateRow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForecastTemplateRow::getForecastTemplateId, templateId);
        this.remove(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            this.saveBatch(list);
        }
    }

    private LambdaQueryWrapper<ForecastTemplateRow> createQueryWrapper(Long templateId) {
        LambdaQueryWrapper<ForecastTemplateRow> queryWrapper = new LambdaQueryWrapper<>();
        if (templateId != null) {
            queryWrapper.eq(ForecastTemplateRow::getForecastTemplateId, templateId);
        }
        return queryWrapper.orderByAsc(ForecastTemplateRow::getCreateTime);
    }
}





