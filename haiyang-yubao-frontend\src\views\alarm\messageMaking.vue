<template>
  <!-- 消息制作 -->
  <div class="message-making">
    <div class="alarm-info d-flex flex-justify-between flex-align-center">
      <div class="title d-flex flex-align-center">
        <i class="icon icon-notice"></i>警报消息
      </div>
      <div class="btns d-flex">
        <div class="btns d-flex flex-align-center">
          <div
            class="btn-item"
            :class="currentType === 1 ? 'active' : ''"
            @click="onChange(1)"
          >
            海浪
          </div>
          <div
            class="btn-item"
            :class="currentType === 2 ? 'active' : ''"
            @click="onChange(2)"
          >
            风暴潮
          </div>
        </div>
      </div>
    </div>
    <div v-loading="loading" class="loading-wrapper">
      <div class="history-alarm-info">
        <div class="column-header">
          <h3>历史报警信息</h3>
          <div class="btns d-flex">
            <n-popconfirm @positive-click="onDelete">
              <template #trigger>
                <qx-button>删除</qx-button>
              </template>
              是否删除该警报？
            </n-popconfirm>
            <!-- <qx-button @click="onDelete">删除</qx-button> -->
            <qx-button class="primary" @click="onCreate">新建</qx-button>
          </div>
        </div>

        <div class="history-alarms">
          <div
            v-if="!historyAlarms.length"
            class="d-flex flex-justify-center flex-align-center"
            style="height: 100px"
          >
            暂无数据
          </div>
          <template v-else>
            <div
              v-for="item in historyAlarms"
              :key="item.id"
              class="history-alarm-item"
              :class="[
                item.id === currentAlarm ? 'active' : '',
                item.status === 2 ? 'submited' : ''
              ]"
              @click="getCurrentAlarm(item)"
            >
              <div class="img-wrap">
                <div class="d-flex flex-align-center">
                  <img src="src/assets/images/alarm/sea-icon.png" alt="" />
                  <div class="info">
                    <h3>{{ currentType === 1 ? '海浪' : '风暴潮' }}</h3>
                    <span>{{
                      currentType === 1 ? 'SEA WAVE' : 'STORM SURGE'
                    }}</span>
                  </div>
                </div>
              </div>
              <div class="submit-time">
                <template v-if="item.status === 2">
                  <span>{{ item.releaseTime }}</span> 提交
                </template>
              </div>
              <div class="submit-status">
                <span>{{ item.status === 1 ? '未提交' : '已提交' }}</span>
                <i
                  class="icon"
                  :class="item.sourceType === 1 ? 'icon-cold' : 'icon-ocean'"
                ></i>
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="alarm-info-edit">
        <div class="column-header">
          <h3>警报消息{{ isEdit ? '编辑' : '新建' }}</h3>
          <div class="btns d-flex">
            <qx-button @click="onMicroblog">发送微博</qx-button>
            <qx-button @click="onSave">保存</qx-button>
            <qx-button class="primary" @click="onSubmit">提交</qx-button>
          </div>
        </div>

        <div class="alarm-info-form">
          <n-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="left"
          >
            <n-grid :cols="24" :x-gap="12">
              <n-form-item-gi :span="5" label="标题：" path="title">
                <n-input
                  v-model:value="model.title"
                  clearable
                  placeholder="请输入"
                  @blur="onCreateValidate"
                />
              </n-form-item-gi>
              <n-form-item-gi :span="5" label="发布时间：" path="releaseTime">
                <n-date-picker
                  v-model:formatted-value="model.releaseTime"
                  type="datetime"
                  clearable
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                  @update:value="changeReleaseTime"
                  @blur="onCreateValidate"
                />
              </n-form-item-gi>
              <n-form-item-gi :span="4" label="编号：" path="number">
                <n-input
                  v-model:value="model.number"
                  clearable
                  placeholder="请输入"
                  @blur="onCreateValidate"
                />
              </n-form-item-gi>
              <n-form-item-gi
                :span="4"
                label="签发："
                path="signUser"
                label-width="90px"
              >
                <n-select
                  v-model:value="model.signUser"
                  :options="issuerList"
                  label-field="name"
                  value-field="id"
                  clearable
                  filterable
                  @update:value="changeSignUser"
                  @blur="onCreateValidate"
                />
              </n-form-item-gi>
              <n-form-item-gi :span="4" label="预报员：" path="makeUser">
                <n-select
                  v-model:value="model.makeUser"
                  :options="userList"
                  label-field="name"
                  value-field="id"
                  clearable
                  filterable
                  @update:value="changeMakeUser"
                  @blur="onCreateValidate"
                />
              </n-form-item-gi>
            </n-grid>
            <n-grid :col="24" :x-gap="12">
              <n-form-item-gi :span="12" label="警报内容" path="alarmContent">
                <n-input
                  v-model:value="model.alarmContent"
                  placeholder="请输入"
                  type="textarea"
                  size="small"
                  :autosize="{
                    minRows: 6,
                    maxRows: 8
                  }"
                  @blur="onCreateValidate"
                />
              </n-form-item-gi>
              <!--              <n-form-item-gi :span="12" label="短信内容" path="smsContent">-->
              <!--                <n-input-->
              <!--                  v-model:value="model.smsContent"-->
              <!--                  placeholder="请输入"-->
              <!--                  type="textarea"-->
              <!--                  size="small"-->
              <!--                  :autosize="{-->
              <!--                    minRows: 6,-->
              <!--                    maxRows: 8-->
              <!--                  }"-->
              <!--                  @blur="onCreateValidate"-->
              <!--                />-->
              <!--              </n-form-item-gi>-->
            </n-grid>
          </n-form>
        </div>
      </div>
    </div>
  </div>

  <qx-dialog
    title="新建--警报消息"
    :visible="dialogVisible"
    width="365px"
    class="add-alarm-message"
    @update:visible="dialogVisible = false"
  >
    <template #content>
      <div class="form-container">
        <n-form
          ref="dialogFormRef"
          class="forecast-temp-form"
          :model="dialogForm"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="left"
        >
          <n-form-item label="影响要素" path="alarmType">
            <n-radio-group
              v-model:value="dialogForm.sourceType"
              name="radiogroup"
            >
              <n-radio :value="1">冷空气</n-radio>
              <n-radio :value="2">台风</n-radio>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            v-if="dialogForm.sourceType === 2"
            label="台风编号"
            path="typhoonNo"
          >
            <n-select
              v-model:value="dialogForm.typhoonNo"
              placeholder="请选择"
              :options="typhoonList"
              label-field="bhName"
              value-field="tfbh"
              @update:value="getTyphoonInfo"
            ></n-select>
          </n-form-item>
          <n-form-item
            v-if="dialogForm.sourceType === 2"
            label="台风时间"
            path="typhoonTime"
          >
            <n-select
              v-model:value="dialogForm.typhoonTime"
              placeholder="请选择"
              :options="typhoonTimeList"
              label-field="time"
              value-field="time"
            ></n-select>
          </n-form-item>
        </n-form>
      </div>
    </template>
    <template #suffix>
      <div class="btns text-center">
        <qx-button @click="dialogVisible = false">取消</qx-button>
        <qx-button class="primary" @click="onDialogSave">保存</qx-button>
      </div>
    </template>
  </qx-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Alarm from 'src/requests/alarm'
import { onMounted } from 'vue'
import PersApi from 'src/requests/pers'
import { useMessage } from 'naive-ui'
import ToolApi from 'src/requests/toolRequest'
import { QxDialog } from 'src/components/QxDialog'
import type { FormInst } from 'naive-ui'
import moment from 'moment'
import tagApi from 'src/requests/tag'

const message = useMessage()
const currentType = ref(1)
// 预报员
let userList = ref<any[]>([])
// 签发人
let issuerList = ref<any[]>([])

// 获取用户列表
function getUserList() {
  // 获取签发人
  tagApi
    .getUserList({
      tagId: '1904727438635282434'
    })
    .then((res: any) => {
      issuerList.value = res
    })
    .catch(() => {
      issuerList.value = []
    })
  // 获取预报员
  tagApi
    .getUserList({
      tagId: '1904788591580594177'
    })
    .then((res: any) => {
      userList.value = res
    })
    .catch(() => {
      userList.value = []
    })
}

// 历史预警
const historyAlarms = ref<any[]>([])
let currentAlarm = ref('') //选中的警报
let isEdit = ref(true)

type FormType = {
  [key: string]: any
}

type mapperType = {
  [key: number]: {
    [key: string]: string
  }
}
// mapper 使用说明：接口返回数据字段，格式一致，可以统一处理
let mapper: mapperType = {
  1: {
    list: 'getSeaWaveMessageList',
    detail: 'getAlarmInfoById',
    save: 'saveSeaWaveMessage',
    submit: 'submitSeaWaveMessage',
    update: 'updateSeaWaveMessage',
    delete: 'deleteAlarm'
  },
  2: {
    list: 'getStormSurgeMessageList',
    detail: 'getStormSurgeMessageInfoById',
    save: 'saveStormSurgeMessage',
    submit: 'submitStormSurgeMessage',
    update: 'updateStormSurgeMessage',
    delete: 'deleteStormSurgeMessage'
  }
}

const formRef = ref<FormInst | null>(null)
let model: FormType = reactive({
  title: '', // 标题
  releaseTime: null, // 发布时间
  signUser: null, // 签发
  signUserName: '', //签发人
  makeUser: null, // 制作
  makeUserName: '', //制作人
  alarmContent: '', //警报内容
  smsContent: '', //短信
  typhoonNo: '', //台风编号
  typhoonTime: '', //台风时间
  number: '' //警报编号
})
let loading = ref(false)

// 消息类型切换
function onChange(type: number) {
  currentType.value = type
  getHistoryAlarms()
}

// 发布时间改变
function changeReleaseTime(val: any) {
  if (val) {
    formRef.value?.restoreValidation()
  }
}

// 获取签发人名称
function changeSignUser(val: any) {
  model.signUserName = issuerList.value.find(i => i.id === val).name
}

// 获取预报员名称
function changeMakeUser(val: any) {
  model.makeUserName = getUserName(val)
}

function getUserName(val: any) {
  let result = userList.value.find(item => item.id === val)
  return result?.name
}

// 新建校验
function onCreateValidate() {
  if (model.sourceType == 2 && !model.typhoonNo) {
    message.warning('请点击新建操作后再编辑')
    formRef.value?.restoreValidation()
    return false
  }
}

// 获取历史警报列表
function getHistoryAlarms() {
  historyAlarms.value = []
  currentAlarm.value = ''
  onReset()

  let funcName = mapper[currentType.value].list
  const params = {
    pageNum: 1,
    pageSize: 12
  }
  Alarm[funcName](params)
    .then((res: any) => {
      if (res) {
        const { pageResult = [] } = res
        if (pageResult.length) {
          pageResult.forEach((item: any) => {
            item.releaseTime = item.releaseTime
              ? moment(item.releaseTime).format('MM月DD日 HH:mm')
              : null
          })
          historyAlarms.value = pageResult
          currentAlarm.value = pageResult?.[0].id
          getAlarmInfoById()
        }
      }
    })
    .catch((e: any) => {
      console.log(e, 'eeee')
      let { msg = null } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
}

function getCurrentAlarm(item: any) {
  currentAlarm.value = item.id
  getAlarmInfoById()
}

// 通过Id 获取详情
function getAlarmInfoById() {
  let funcName = mapper[currentType.value].detail
  Alarm[funcName](currentAlarm.value)
    .then((res: any) => {
      if (res) {
        Object.keys(res).forEach((item: any) => {
          if (model.hasOwnProperty(item)) {
            model[item] = res[item]
          }
        })
        model.sourceType = res.sourceType
        model.id = res.id
      }
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data
      message.error(msg || '获取数据失败')
    })
}

// 新建 start
let typhoonList = ref([])
let typhoonTimeList = ref([])

// 获取台风列表
function getTyList() {
  ToolApi.getTyList()
    .then((res: any) => {
      typhoonList.value = res.map((item: any) => {
        item.bhName = `${item.tfbh}-${item.name}`
        return item
      })
    })
    .catch(err => {
      let { msg = null } = err?.data
      message.error(msg || '获取数据失败')
    })
}

// 获取台风时间
function getTyphoonInfo(code: string) {
  ToolApi.getTyphoonInfo({ code })
    .then((res: any) => {
      typhoonTimeList.value = res
    })
    .catch(err => {
      let { msg = null } = err?.reponse?.data || err?.data || {}
      message.error(msg || '获取数据失败')
    })
}

// 新建弹窗警报消息 start
const dialogForm = reactive({
  typhoonNo: null, // 台风编号
  sourceType: 1, // 要素类型
  typhoonTime: null //台风时间
})
const rules = {
  typhoonNo: {
    required: true,
    message: '台风编号不能为空',
    trigger: 'change'
  },
  typhoonTime: {
    required: true,
    message: '台风时间不能为空',
    trigger: 'change'
  },
  title: {
    required: true,
    message: '标题不能为空',
    trigger: 'blur'
  },
  releaseTime: {
    required: true,
    message: '发布时间不能为空',
    trigger: ['blur', 'change']
  },
  signUser: {
    required: true,
    message: '签发不能为空',
    trigger: 'change'
  }, // 签发
  makeUser: {
    required: true,
    message: '预报员不能为空',
    trigger: 'change'
  },
  alarmContent: {
    required: true,
    message: '警报内容不能为空',
    trigger: 'blur'
  },
  smsContent: {
    required: true,
    message: '短信内容不能为空',
    trigger: 'blur'
  },
  number: {
    required: true,
    message: '编号不能为空',
    trigger: 'blur'
  }
}
let dialogVisible = ref(false)

// 新建弹窗
function onCreate() {
  dialogVisible.value = true
  getTyList()
}

// 弹窗确定
function onDialogSave() {
  isEdit.value = false
  onReset()

  model.typhoonNo = dialogForm.typhoonNo
  model.typhoonTime = dialogForm.typhoonTime
  model.sourceType = dialogForm.sourceType
  dialogVisible.value = false
}

// 新建弹窗警报消息 end

function onMicroblog() {
  const alarmContent = model.alarmContent
  Alarm.microblog({ describe: alarmContent, imgPath: '' })
    .then((res: any) => {
      console.log(res)
      message.success('发布成功')
    })
    .catch((e: any) => {
      console.warn(e)
      message.error('发布失败')
    })
}

// 保存
function onSave() {
  const params = JSON.parse(JSON.stringify(model))

  let funcName = mapper[currentType.value].save
  Alarm[funcName](params)
    .then((res: any) => {
      message.success('操作成功')
      getHistoryAlarms()
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// 提交
function onSubmit() {
  loading.value = true
  const params = JSON.parse(JSON.stringify(model))
  let funcName = mapper[currentType.value].submit
  Alarm[funcName](params)
    .then((res: any) => {
      message.success('操作成功')
      getHistoryAlarms()
      loading.value = false
    })
    .catch((e: any) => {
      loading.value = false
      let { msg = null } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// 删除
function onDelete() {
  let arr = []
  arr.push(currentAlarm.value)
  let funcName = mapper[currentType.value].delete
  Alarm[funcName](arr)
    .then((res: any) => {
      message.success('操作成功')
      getHistoryAlarms()
    })
    .catch((e: any) => {
      let { msg = null } = e?.response?.data
      message.error(msg || '操作失败')
    })
}

// 重置
function onReset() {
  model = reactive({
    title: '', // 标题
    releaseTime: null, // 发布时间
    signUser: null, // 签发
    signUserName: '', //签发人
    makeUser: null, // 制作
    makeUserName: '', //制作人
    alarmContent: '', //警报内容
    smsContent: '', //短信
    typhoonNo: '', //台风编号
    typhoonTime: '', //台风时间
    number: '' //警报编号
  })
}

onMounted(() => {
  getHistoryAlarms()
  getUserList()
})
</script>

<style lang="scss">
.message-making {
  box-sizing: border-box;
  padding: 20px 18px;
  display: flex;
  flex-direction: column;

  .alarm-info {
    box-sizing: border-box;
    padding: 13px 17px 12px 25px;
    background: linear-gradient(180deg, #ffffff 0%, rgba(0, 0, 0, 0) 100%);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;

    i.icon {
      margin-right: 8px;
    }

    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      line-height: 21px;
    }

    .btn-item {
      width: 90px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 300;
      font-size: 14px;
      color: rgba(34, 34, 34, 1);
      line-height: 16px;
      text-align: center;
      padding: 7px 0 6px;

      position: relative;

      &::before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: url(src/assets/images/alarm/border.png) no-repeat;
        background-size: 100% 100%;
      }

      &:nth-child(1)::before {
        transform: rotate(180deg);
      }

      &:nth-child(2) {
        margin-left: -10px;
      }

      &.active {
        color: #0073ce;

        &::before {
          background: url(src/assets/images/alarm/active-border.png) no-repeat;
          background-size: 100% 100%;
        }

        &:nth-child(1)::before {
          transform: rotate(0);
        }

        &:nth-child(2)::before {
          transform: rotate(180deg);
        }
      }
    }
  }

  .history-alarm-info {
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
  }

  .column-header {
    box-sizing: border-box;
    padding: 10px 17px 10px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    background-image: url(/src/assets/images/common/content-header.png);
    background-size: 100% 100%;
    border-radius: 8px 8px 0px 0px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    h3 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      line-height: 21px;
    }
  }

  .loading-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .history-alarms {
    box-sizing: border-box;
    padding: 18px 17px;
    display: flex;
    flex-direction: row-reverse;
    justify-content: start;
    background: #fafcfe;
    // border: 1px solid #e7f0fb;
    // margin-bottom: 28px;
    border-radius: 0px 0px 8px 8px;
    min-height: 114px;
    overflow-x: auto;

    .history-alarm-item {
      width: 144px;
      margin-right: 8px;
      background: #ffffff;
      box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      display: flex;
      flex-direction: column;

      &.submited {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          border: 12px solid #47bb52;
          border-bottom-color: transparent;
          border-left-color: transparent;
        }

        &::after {
          content: '';
          width: 5px;
          height: 2px;
          position: absolute;
          right: 2px;
          top: 4px;
          border: 2px solid #fff;
          border-bottom-color: transparent;
          border-left-color: transparent;
          transform: rotate(125deg);
        }
      }

      &.active {
        background: #dae8ff;
        border-radius: 4px;
        border: 1px solid #357fff;
      }

      // &:nth-last-child(1) {
      //   margin-right: 0;
      // }
      .img-wrap {
        display: flex;
        box-sizing: border-box;
        padding: 15px 16px 0 12px;
        flex: 1;
        margin-bottom: 10px;

        & > div {
          width: 100%;
          height: 40px;
          background: #0073ce;
          border-radius: 4px;
          border-top-left-radius: 30px;
          border-bottom-left-radius: 30px;
        }

        img {
          width: 47px;
          height: 47px;
        }

        .info {
          flex: 1;
          text-align: center;

          h3 {
            font-family: Noto Sans SC, Noto Sans SC;
            font-weight: bold;
            font-size: 16px;
            color: #ffffff;
            line-height: 19px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            padding: 3px 0;
          }

          span {
            font-family: Noto Sans SC, Noto Sans SC;
            font-weight: 400;
            font-size: 9px;
            color: #ffffff;
            line-height: 12px;
            padding: 3px 0;
          }
        }
      }

      .submit-time {
        font-family: Noto Sans SC, Noto Sans SC;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 14px;
        margin-bottom: 9px;
        text-align: center;

        span {
          color: #eb1d1d;
          margin-right: 5px;
        }
      }

      .submit-status {
        display: flex;
        align-items: center;

        span {
          flex: 1;
          background: rgba(0, 115, 206, 1);
          font-family: Noto Sans SC, Noto Sans SC;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 14px;
          display: inline-block;
          height: 26px;
          line-height: 26px;
          text-align: center;
        }
      }
    }
  }

  .visibility {
    visibility: hidden;
  }

  .alarm-info-edit {
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .alarm-info-form {
    box-sizing: border-box;
    padding: 32px 16px;
    background: #ffffff;
    flex: 1;
  }

  .warn-info {
    width: 100%;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 8px 11px;

    .btn {
      margin-top: 24px;
    }

    .n-input__border {
      border: none;
    }
  }
}
</style>
