package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.dto.SeaWaveAlarmDTO;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;
import cn.piesat.data.making.server.service.impl.OceanStationServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class SeaWaveAlarmServiceTest {

    @Autowired
    private SeaWaveAlarmService seaWaveAlarmServiceImpl;

    @Test
    public void testSave(){
        SeaWaveAlarm seaWaveAlarm = new SeaWaveAlarm();

        seaWaveAlarm.setCityAlarmContent("CityAlarmContent");
        seaWaveAlarm.setAlarmTime("alarmTime");

        seaWaveAlarmServiceImpl.save(seaWaveAlarm);
    }

    @Test
    public void testSave2(){
        ObjectMapper mapper = new ObjectMapper();
        TypeReference<SeaWaveAlarmDTO> typeRef = new TypeReference<SeaWaveAlarmDTO>() {
        };

        String jsonStr = "{\"historyAlarmLevel\":\"4\",\"alarmLevel\":1,\"title\":\"海浪I级警报\",\"releaseTime\":\"2025-07-11 16:00:00\",\"number\":\"2506-04\",\"lastNumber\":\"2506-01\",\"signUser\":\"1906526706074189826\",\"makeUser\":\"1906581439908478978\",\"summarize\":\"我是综述\",\"alarmContent\":\"受冷空气影响，预计今天中午到明天中午：北部湾南部、海南岛以东、海南岛以南附近海面有1.5-2.6米的中到轻浪。南沙以南到曾母暗沙附近、北部湾、琼州海峡附近海面有1.6-2.7米的中到轻浪。\",\"defenseGuide\":\"请上述海域作业船只提前做好防浪避浪工作，并关注我台的后续预报。\",\"smsContent\":\"海南省海洋预报台2025年07月11日00时发布海浪I级警报:受冷空气影响，预计今天中午到明天中午：北部湾南部、海南岛以东、海南岛以南附近海面有1.5-2.6米的中到轻浪。南沙以南到曾母暗沙附近、北部湾、琼州海峡附近海面有1.6-2.7米的中到轻浪。请上述海域作业船只提前做好防浪避浪工作，并关注我台的后续预报。\",\"alarmImages\":\"\",\"status\":2,\"makeUserName\":\"石海莹\",\"signUserName\":\"周涛\",\"typhoonNo\":\"\",\"typhoonTime\":\"\",\"sourceType\":1,\"pushTaskId\":\"1006512925252255744\",\"deleteFlag\":0,\"createTime\":\"2025-06-10 12:02:38\",\"updateTime\":\"2025-07-10 21:42:03\",\"wordFilePath\":\"/data/qixiang/Data/ZPEIYQ/DATA/PRO/ALERT/O/AHSW/2025/20250610/O_AHSW_L4_STP_20250610160000_V1_WAVE_20250610120227.docx\",\"smsFilePath\":\"/data/qixiang/Data/ZPEIYQ/DATA/PRO/ALERT/O/AHST/2025/20250610/O_AHST_L4_STP_20250610160000_V1_WAVE_20250610120238.txt\",\"htmlFilePath\":null,\"alarmArea\":\"\",\"display\":0,\"alarmTime\":\"预计今天中午到明天中午\",\"cityContent\":\"海南岛东部、海南岛南部的三亚、乐东、琼海、万宁、陵水近岸海域将出现1.1-1.9米的轻到小浪。海南岛西部、海南岛北部的儋州、昌江、东方、海口、澄迈、临高、文昌近岸海域将出现2.1-2.4米的中到小浪。\",\"stationWarning\":[],\"cityAlarmContent\":\"海南岛东部、海南岛南部的三亚、乐东、琼海、万宁、陵水近岸海域将出现1.1-1.9米的轻到小浪。海南岛西部、海南岛北部的儋州、昌江、东方、海口、澄迈、临高、文昌近岸海域将出现2.1-2.4米的中到小浪。\"}";
        try {
            SeaWaveAlarmDTO dto = mapper.readValue(jsonStr, new TypeReference<SeaWaveAlarmDTO>() {});
            seaWaveAlarmServiceImpl.saveInfo(dto);
            System.out.println(dto);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
