import { Polygon } from 'ol/geom'


export interface IPoint {
  x: number
  y: number
}

/**
 * 生成平滑多边形点集
 * @param points 原始多边形顶点数组（需至少3个点）
 * @param tension 平滑张力系数（0-1，值越大越平滑）
 * @param segments 每段曲线采样精度（点数）
 * @returns - 平滑后的顶点坐标数组
 */
export function smoothPolygon(
  points: IPoint[],
  tension = 0.2,
  segments = 20
): IPoint[] {
  if (points.length < 3) return [...points] // 至少需要3个点

  const smoothed = []
  const n = points.length

  // 生成贝塞尔曲线控制点并采样
  for (let i = 0; i < n; i++) {
    const prev = points[(i - 1 + n) % n]
    const current = points[i]
    const next = points[(i + 1) % n]
    const nextNext = points[(i + 2) % n]

    // 计算三次贝塞尔控制点
    const c1 = {
      x: current.x + (next.x - prev.x) * tension,
      y: current.y + (next.y - prev.y) * tension
    }

    const c2 = {
      x: next.x - (nextNext.x - current.x) * tension,
      y: next.y - (nextNext.y - current.y) * tension
    }

    // 在当前段生成采样点
    for (let t = 0; t <= segments; t++) {
      const param = t / segments
      smoothed.push(cubicBezier(current, c1, c2, next, param))
    }
  }

  return smoothed
}

/**
 * 三次贝塞尔曲线计算函数
 */
function cubicBezier(
  p0: IPoint,
  p1: IPoint,
  p2: IPoint,
  p3: IPoint,
  t: number
) {
  const u = 1 - t
  const tt = t * t
  const uu = u * u
  const uuu = uu * u
  const ttt = tt * t

  return {
    x: uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x,
    y: uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y
  }
}

/**
 * 适用于 Openlayer 的面绘制函数
 * @param coordinates
 * @param geometry
 */
export function geometryFunction(
  coordinates: number[][][],
  geometry: Polygon
): Polygon {
  const pnts: number[][] = JSON.parse(JSON.stringify(coordinates[0]))
  if (!geometry) {
    geometry = new Polygon([pnts])
  } else {
    const points = pnts.map(item => {
      return { x: item[0], y: item[1] }
    })
    const smoothPoints = smoothPolygon(points)
    geometry.setCoordinates([smoothPoints.map(i => [i.x, i.y])])
    geometry.setProperties({ $originPoints: coordinates })
  }
  return geometry
}
