import { DataTableColumns } from 'naive-ui'
import type { IOceanTableData } from './type'
import { Ref, ref, watch } from 'vue'
import VectorSource from 'ol/source/Vector'
import { Feature } from 'ol'
import { Style } from 'ol/style'

export function useOceanTable(opt: { vectorSourceGetter: () => VectorSource }) {
  const oceanData = ref<IOceanTableData[]>([])
  const checkedRowKeys = ref<string[]>([])
  const oceanRowKey: keyof IOceanTableData = 'oceanStationCode'

  watch(checkedRowKeys, val => {
    const source = opt.vectorSourceGetter()
    source.getFeatures().forEach((feature: Feature) => {
      const properties = feature.getProperties()
      if (val.find(i => i === properties[oceanRowKey])) {
        feature.setStyle(properties.$style)
      } else {
        feature.setStyle(emptyStyle())
      }
    })
  })
  return {
    oceanColumns: getOceanColumns(),
    oceanData,
    checkedRowKeys,
    rowKeyGetter: (row: IOceanTableData) => row[oceanRowKey],
    checkAllOcean: () =>
      checkAll({
        checkedRowKeys,
        oceanData,
        oceanRowKey
      })
  }
}

function getOceanColumns(): DataTableColumns<IOceanTableData> {
  return [
    {
      type: 'selection'
    },
    {
      title: '站位名称',
      key: 'oceanStationName'
    },
    {
      title: '站位编号',
      key: 'oceanStationCode'
    }
  ]
}

function checkAll(opt: {
  oceanData: Ref<IOceanTableData[]>
  checkedRowKeys: Ref<string[]>
  oceanRowKey: keyof IOceanTableData
}) {
  const arr: string[] = []
  opt.oceanData.value.forEach((ocean: IOceanTableData) => {
    const item = ocean[opt.oceanRowKey]
    if (item) {
      arr.push(item)
    }
  })
  opt.checkedRowKeys.value = arr
}

function emptyStyle() {
  return new Style({
    stroke: void 0,
    fill: void 0
  })
}
